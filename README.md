## Development

### Setup

To run server

```
cp .env.example .env
pip install uv
cd api
uv sync # syncs dependencies
uv run uvicorn server:app --host 0.0.0.0 --port 8000
```

To run UI

```
cd ui
npm install
npm run dev
```

### API

```
curl http://localhost:8000/api/docs
```

To deploy on API

```
copilot secret --init
PINECONE_API_KEY = <value>
AWS_ACCESS_KEY_ID = <value>
AWS_SECRET_ACCESS_KEY = <value>
OPENAI_API_KEY = <value>
copilot deploy --all

```
