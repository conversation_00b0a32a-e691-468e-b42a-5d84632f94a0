# Documentation for ui/lib/convertToCpp.ts

## 1. Overview

This script is responsible for converting massage program data, typically defined in a detailed JSON format, into a C++ compatible array of `T_OPERATION` structures. This C++ array is used by the OSIM massage chair firmware to execute the massage program.

The script handles:
- Parsing the input JSON which includes overall program settings, a list of program steps, and subroutine definitions.
- Mapping descriptive terms from the JSON (e.g., roller actions, positions, speeds, air/scent settings) to specific C++ constants and byte values as defined by the OSIM firmware specification.
- Generating initial setup commands for the C++ program (like `TRACK_SELECT` and `ASI_START`).
- Processing each program step, including expanding subroutine calls.
- Implementing logic to generate multiple C++ `T_OPERATION` lines from a single JSON `ProgramStep` if it involves distinct auxiliary actions (like seat programs or LED light changes) in addition to a main roller/timer action.
- Formatting the final output as a C++ array definition.

## 2. Input JSON Structure

The script primarily consumes data conforming to the `DetailedProgramData` interface. Key nested interfaces include:

### 2.1. `DetailedProgramData`
The root object for a program.
- `id`: Program ID.
- `program_title`: Name of the program.
- `overallSettings`: Contains global settings for the program. (See 2.2)
- `steps`: An array of `ProgramStep` objects. (See 2.3)
- `subRoutines`: An optional array of `Subroutine` definitions.

### 2.2. `OverallSettings`
Global settings for the massage program.
- `strength`: Overall massage strength (e.g., "STRONG", "MEDIUM", "WEAK"). Used for `TRACK_SELECT`.
- `position`: Default chair position (e.g., "Comfort Position"). (Currently not directly used for a specific C++ command in the initial setup).
- `leg_program`: Name of the foot/leg massage program (e.g., "Detox", "Relax"). Used for `ASI_START`.

### 2.3. `ProgramStep`
Defines a single step or action in the massage sequence.
- `step_number`: Identifier for the step.
- `roller_action_description`: Textual description of the main roller/3D/timer action. This is a key field for parsing.
- `air_action_description`: Description of air bag actions.
- `kneading_speed`, `tapping_speed`: Speeds for respective actions.
- `position_3d`: Array or string defining 3D roller position/depth.
- `width`: Roller width (e.g., "N", "M", "W").
- `seat_program`: ID or name of a seat massage program to activate.
- `notes`: Additional comments for the step.
- `scent`: Scent activation (e.g., "ON").
- `light`: LED light color (e.g., "255, 0, 0", "COLOUR339").
- `type`: Type of step ('action', 'subroutine', 'comment', 'program_note').
- `subroutine_id`: ID of a subroutine to call if `type` is 'subroutine'.

### 2.4. `Subroutine`
Defines a reusable sequence of `ProgramStep` objects.
- `name`: Name of the subroutine.
- `subroutineIdJson`: Unique ID for the subroutine.
- `steps`: An array of `ProgramStep` objects defining the subroutine's actions.

## 3. Core Conversion Functions

### 3.1. `convertJsonToCpp(jsonDataString, defaultProgramName, defaultProgramIndex)`
- **Purpose:** Main entry point of the script. Parses the input JSON string and orchestrates the conversion process.
- **Logic:**
    1. Parses `jsonDataString` into a `DetailedProgramData` object.
    2. Extracts `program_title` and `id` (used as `programName` and `programIndex`).
    3. Creates a `Map` (`subRoutinesMap`) from the `programData.subRoutines` array for easy lookup by `subroutineIdJson`.
    4. Calls `convertStepsToCpp` with the parsed steps, program info, subroutines map, and overall settings.
    5. Handles errors during parsing or conversion.

### 3.2. `convertStepsToCpp(steps, programName, programIndex, subRoutinesMap, overallSettings)`
- **Purpose:** Generates the complete C++ code string for the program array.
- **Logic:**
    1. **Header Generation:** Creates the C++ file header comments and `#include` directives. Defines the `T_OPERATION osim_pgX_course_tbl[] = { ... };` array structure.
    2. **Initial Operations (from `overallSettings`):**
        *   `TRACK_SELECT`:
            *   Determines `trackStrength` based on `overallSettings.strength`. Intended mapping: "STRONG" -> `STRONG`, "MEDIUM" -> `MEDIUM`, "WEAK" -> `WEAK`, else `LIGHT`.
            *   *Note: Recent test generations have shown this sometimes defaults to `MEDIUM` even when "STRONG" is specified in JSON, suggesting a potential runtime issue with `overallSettings` access or a subtle bug.*
        *   `ASI_START`:
            *   Determines `asiStartMode` based on `overallSettings.leg_program`.
            *   Uses `LEG_PROGRAM_NAME_TO_KEY_MAP` (e.g., `"detox": "auto6"`) to map descriptive names to keys for `FOOT_PROGRAM_MAP` (e.g., `auto6: "MODE_AUTO6"`).
            *   *Note: Recent test generations have shown this sometimes defaults to `MODE_AUTO` or a constructed `MODE_LEGPROGRAMNAME` instead of the correctly mapped `MODE_AUTOX` value, indicating potential issues with the mapping application at runtime or the comprehensiveness of `LEG_PROGRAM_NAME_TO_KEY_MAP`.*
    3. **Main Step Processing:**
        *   Creates `extendedSubRoutinesMap` to pass `overallSettings` to `generateOperationObjectsFromSteps` (as a workaround for context).
        *   Calls `generateOperationObjectsFromSteps` to convert all main program steps.
    4. **End Marker:** Appends the `0xff,...` program end marker.
    5. **Formatting:** Formats each `OperationObject` into a C++ array line with aligned parameters and comments.

### 3.3. `generateOperationObjectsFromSteps(steps, subRoutinesMap, isSubroutineCall)`
- **Purpose:** Converts an array of `ProgramStep` objects into an array of `OperationObject` instances, which directly map to lines in the C++ `T_OPERATION` array. This is the core logic for step-by-step translation.
- **Logic:**
    1. Iterates through each `ProgramStep`.
    2. **Handles Special Step Types:**
        *   `comment`: Creates a comment-only `OperationObject`.
        *   `program_note`: Creates a comment-only `OperationObject`.
        *   `subroutine`:
            *   Finds the subroutine definition in `subRoutinesMap`.
            *   Adds start/end comments for the subroutine.
            *   Recursively calls `generateOperationObjectsFromSteps` for the subroutine's steps.
    3. **Handles 'action' Step Types (Multi-Line Generation):**
        *   Initializes a temporary `localOps` array for the current `ProgramStep`.
        *   **Seat Program:** If `step.seat_program` is present, an `OperationObject` for `CPP_CMD_SEATM_START` is created and added to `localOps`. `parseByte8Value` is used to get the seat program mode.
        *   **LED Light:** If `step.light` is present, an `OperationObject` for `CPP_CMD_LED_COLOR` is created and added to `localOps`. `parseByte8Value` is used to get the LED color value/constant.
        *   **Main Action from `roller_action_description`:**
            *   If `step.roller_action_description` is present and non-empty:
                *   `mainCommand = detectCommandType(mainActionDescription, step)` is called.
                *   `alreadyHandledByField` check: This boolean flag is true if `mainCommand` is `SEATM_START` (and `step.seat_program` was present) OR if `mainCommand` is `LED_COLOR` (and `step.light` was present). This prevents duplicating these commands if they are also inferable from the description keywords after being explicitly handled.
                *   If `mainCommand` is a valid action (not a comment type) AND `!alreadyHandledByField`:
                    *   The full set of `parseByteXValue` functions (2 through 8) are called to determine all parameters for this `mainCommand`.
                    *   An `OperationObject` for this `mainCommand` and its parameters is created and added to `localOps`.
                *   If `mainCommand` is a comment type AND `localOps` is currently empty (meaning no seat/LED op was generated for this step), a comment line is added to `localOps`.
        *   **Empty/Note-Only Actions:** If `localOps` remains empty after the above (e.g., an action step with only `notes`, or a truly empty action), a warning comment or a comment from `step.notes` is generated.
        *   All `OperationObject`s in `localOps` are then added to the main `operationObjects` list.
    4. Returns the final array of `OperationObject`s.

## 4. Command and Parameter Parsing

### 4.1. `detectCommandType(description, step)`
- **Purpose:** Determines the primary C++ command (Byte 1 of `T_OPERATION`) based on the `description` string and the `ProgramStep` object.
- **Logic:**
    - Returns `CPP_CMD_TIMER` if `description` is null/empty.
    - Checks for non-actionable comment patterns (e.g., "No. X ～ Y ... 繰り返し").
    - **Order of Checks (Prioritized):**
        1.  Keywords for `LED_COLOR` (e.g., "led", "color") - *only if `!step.light`*.
        2.  Keywords for `SEATM_START` (e.g., "seat program start") - *only if `!step.seat_program`*.
        3.  3D Control commands (`THREED_CTRL_ENABLE`/`DISABLE`).
        4.  `COOLDOWN`.
        5.  Vibration commands (`VIB_SET`, `VIB_TIME`).
        6.  Positional movements (`POS_UP`, `POS_DOWN`).
        7.  3D position commands (`POS_3D_UP`, `POS_3D_DOWN`, `POS_3D`).
        8.  Width commands (`WIDTH_SET`).
        9.  Rotation commands (`ROTATION` - checks for "cyc" not part of a timer phrase).
        10. Timer commands (`TIMER` - checks for "timer", "hold", "s", "sec", or "cyc" within a timer context).
        11. Roller Actions (`CPP_CMD_POS`): Iterates through `OPERATION_MAP` keys.
        12. Positional keywords (`CPP_CMD_POS`): Checks for "まで", " to ", "move" along with `POSITION_MAP` keys.
        13. Subroutine keyword (`CPP_CMD_COMMENT`).
    - Defaults to `CPP_CMD_TIMER` if no other command is identified.
- **Recent Change:** The direct checks for `step.light` and `step.seat_program` fields were removed from the top of this function. It now infers `LED_COLOR` or `SEATM_START` based on keywords in the `description` string *only if* the corresponding `step.light` or `step.seat_program` fields are not already set. This allows `generateOperationObjectsFromSteps` to handle explicit fields first.

### 4.2. Byte-Specific Parsing Functions (`parseByteXValue`)

These functions are responsible for determining the value for each byte (parameter) of the `T_OPERATION` C++ structure, based on the detected `command` and the `ProgramStep` data.

#### 4.2.1. `parseByte2Value(command, step)` (Byte 2: Position/Time/Cycles/etc.)
- **`POS`, `POS_UP`, `POS_DOWN`:** Uses `parsePositionFromDescription`.
- **`POS_3D`, `POS_3D_UP`, `POS_3D_DOWN`:**
    - Looks for `FIT_POSX` patterns (e.g., "FIT_POS5").
    - Looks for `3D := X` patterns.
    - Extracts the first numeric value as a fallback.
- **`TIMER`:** Extracts duration from patterns like "Xs", "Xsec", "Xcyc". Defaults to "1".
- **`ROTATION`:** Extracts cycle count from "Xcyc". Defaults to "1".
- **`POS_UP`/`POS_DOWN` (turn-based):** Extracts turn count from "Xturn". Defaults to "1".
- Defaults to `CPP_DEFAULT_PARAM` ("0").

#### 4.2.2. `parseByte3Value(command, step)` (Byte 3: Kneading Speed / 3D Adjust)
- Uses `step.kneading_speed` if it's a number or can be parsed from a string.
- For 3D commands, can parse `position -/+ X` for adjustments (though this seems less used now).
- Defaults to `CPP_DEFAULT_PARAM` ("0").

#### 4.2.3. `parseByte4Value(command, step)` (Byte 4: Tapping Speed / OSI Speed / Vib Strength)
- Uses `step.tapping_speed` if it's a number or can be parsed from a string.
- For `VIB_SET`/`VIB_TIME`, extracts strength from "strength := X".
- Defaults to `CPP_DEFAULT_PARAM` ("0").

#### 4.2.4. `parseByte5Value(command, step)` (Byte 5: Width + Direction)
- **Direction:** Infers "REW+" (upward) prefix if `description` contains "up", "もみ上げ", or "rewind" for `POS`, `POS_UP`, `POS_DOWN`, `TIMER` commands. No prefix for downward or directionless. `ROTATION` and `WIDTH_SET` typically don't use a direction prefix here.
- **Width:** Uses `step.width` and maps it via `ROLLER_WIDTH_DIRECTION_MAP` (e.g., "N" -> "PN").
- Combines direction prefix and mapped width (e.g., "REW+PN").
- Returns `CPP_DEFAULT_PARAM` ("0") if no width is specified.

#### 4.2.5. `parseByte6And7Value(step)` (Bytes 6 & 7: Airbag & Scent)
- Calls `parseAirAction(step.air_action_description, step.scent)`.

#### 4.2.6. `parseByte8Value(command, step, overallSettings)` (Byte 8: Movement Mode / Program ID / Color)
- **`CPP_CMD_LED_COLOR`:**
    - Uses `step.light`. Tries `LED_COLOR_MAP` first, then `COLOURX` pattern, then raw value.
    - If `step.light` is null, tries to parse `COLOURX` from `step.roller_action_description`.
- **`CPP_CMD_ASI_START`:** (This is for the initial `ASI_START` generated by `convertStepsToCpp`, not typically determined by `detectCommandType` from a step description).
    - Uses `overallSettings.leg_program`. Maps descriptive names (e.g., "Relax", "Detox") via `LEG_PROGRAM_NAME_TO_KEY_MAP` to "autoX" keys, then uses `FOOT_PROGRAM_MAP`.
    - Fallback constructs `MODE_LEGPROGRAMNAME` or defaults to `MODE_AUTO2` (previously `MODE_AUTO1`).
- **`CPP_CMD_SEATM_START`:**
    - Uses `step.seat_program`. Normalizes the string (removes "sm_", "auto", "#", "zlb", spaces) and tries to form an "autoX" key for `SEAT_PROGRAM_MAP` (e.g., "ZLB #53" -> "auto53").
    - If not found, constructs `SM_XXX` from the (cleaned) original `step.seat_program` string.
    - If `step.seat_program` is null, tries to parse `SM_AUTOX` from `step.roller_action_description`.
- **Roller Action Commands (`POS`, `TIMER`, `ROTATION`, etc.):** Calls `detectOperationType(step.roller_action_description)` to get the specific movement mode (e.g., `M_KNEAD`, `M_TAP_ROLPART`).
- Defaults to `CPP_DEFAULT_PARAM` ("0").

### 4.3. Other Helper Functions

- **`parseAirAction(airActionDesc, scentDesc)`:**
    - Parses `airActionDesc` for keywords like "shoulder", "arm", "left", "right" and combines bitmasks from `AIR_SCENT_CONFIG_MAP`.
    - Parses `scentDesc` for "left", "right", or general "on" and combines bitmasks.
    - Appends "+NOHOLD" if any air/scent code is generated, unless "hold" is specified in the descriptions.
- **`parsePositionFromDescription(description)`:** Iterates `POSITION_MAP` to find matching position constants in the description.
- **`detectOperationType(description)`:** Simpler parser for Byte 8 movement mode, iterates `OPERATION_MAP`. Used by `parseByte8Value`.
- **`parseWidth(width)`:** Maps "N"/"M"/"W" to "PN"/"PM"/"PW".
- **`parse3DPosition(position3d)`:** Parses the `step.position_3d` array/string into a C++ style 3D position string and depth.

## 5. Constant Maps

- **`OPERATION_MAP`:** Maps roller action keywords (Japanese & English) to `M_XXX` C++ movement constants (for Byte 8).
- **`POSITION_MAP`:** Maps position keywords to `POS_XXX` C++ constants (for Byte 2).
- **`THREE_D_POSITION_MAP`:** Maps 3D named positions to `FIT_POSX` constants (for Byte 2).
- **`ROLLER_WIDTH_DIRECTION_MAP`:** Maps "N"/"M"/"W" to "PN"/"PM"/"PW" (for Byte 5).
- **`AIR_SCENT_CONFIG_MAP`:** Maps air/scent keywords to their bitmask values (for Bytes 6 & 7).
- **`FOOT_PROGRAM_MAP`:** Maps "autoX" keys to `MODE_AUTOX` foot program constants (for Byte 8 of `ASI_START`).
- **`LEG_PROGRAM_NAME_TO_KEY_MAP` (in `convertStepsToCpp`):** Helper to map descriptive leg program names from `overallSettings` to "autoX" keys for `FOOT_PROGRAM_MAP`.
- **`LED_COLOR_MAP`:** Maps color names to `COLOUR_XXX` constants (for Byte 8 of `LED_COLOR`).
- **`SEAT_PROGRAM_MAP`:** Maps "autoX" keys to `SM_AUTOX` seat program constants (for Byte 8 of `SEATM_START`).

## 6. Known Issues / Areas for Future Improvement

1.  **`TRACK_SELECT` Strength & `ASI_START` Mode:** Despite logic appearing correct in the code for mapping `overallSettings.strength` and `overallSettings.leg_program` to the initial `TRACK_SELECT` and `ASI_START` commands, recent generated outputs have not always reflected these settings accurately. This may indicate a runtime issue with how `overallSettings` is passed or accessed during generation for these specific initial commands, or a subtle persistent bug.
2.  **Complex Description Parsing:** The script currently identifies one primary command from `roller_action_description`. Human-written C++ often breaks down a single complex description into multiple sequential C++ `T_OPERATION` lines. For example, "3D N→N+3 肩上　もみ下げ　2cyc" might become a `POS_3D_UP` line followed by a `ROTATION` line. Implementing this level of parsing is the most significant planned enhancement.
3.  **Handling of "Repeat" Comments:** Descriptions like "No.X ～ Y 1回　繰り返し" are currently output as C++ comments. True execution would require implementing loop structures or duplicating the specified steps.
4.  **Subroutine Parameter Overrides:** The system does not currently support calling a subroutine with modified parameters (e.g., "Subroutine X with width N"). Subroutines are expanded as defined.
5.  **Comprehensive Error Handling & Validation:** While some error handling exists (e.g., for JSON parsing), more robust validation of input data against expected formats and values could be added.

## 7. C++ `T_OPERATION` Structure (Reference from OSIM Document)

Each line in the generated C++ array corresponds to a `T_OPERATION` struct:

```c
typedef struct {
    UCHAR command;        // Byte 1: Action command (e.g., POS, TIMER)
    UCHAR pos_time;       // Byte 2: Roller position / 3D position / Timer seconds / No. of knead cycles
    int8  knead_speed;    // Byte 3: Kneading speed
    UCHAR tap_speed;      // Byte 4: Tapping speed / OSI speed
    UCHAR width_flag;     // Byte 5: Kneading direction + roller width (e.g., REW+PN)
    uint16 air_bag;       // Bytes 6 & 7: Airbag & Scent on/off (bitmask)
    UCHAR movement;       // Byte 8: Movement mode (M_KNEAD, M_TAP, etc.) / Program ID / Color
} T_OPERATION;