# Plan: Two-Tiered Program Ontology and Segmentation v5 - Enhanced Purpose Tag Generation

## 1. Objective

The goal of this initiative is to create a rich, multi-layered, and queryable library of massage program segments with **semantically rich, program-specific purpose tags**. This will be achieved by analyzing our existing programs and breaking them down at two levels of granularity:

1.  **Macro-Phases:** The three primary structural phases of a program (`front`, `main`, `cooling`).
2.  **Micro-Chunks:** Smaller, thematically coherent, AI-discovered segments of steps that exist *within* the macro-phases.

To preserve critical context, we will implement a **Hierarchical Segment Link**, ensuring every micro-chunk is linked to its parent macro-phase. We will also define a **Segment Interface Contract** (entry/exit states) for every segment to enable the generation of seamless, state-aware transitions.

### 1.1. Enhanced Purpose Tag Generation

**Problem Identified:** Current segmentation produces overly generic purpose tags:
- 88% of segments use only 4 generic tags (`deep_therapy`, `tension_release`, `invigoration`, `circulation_boost`)
- Rich program context (target demographics, therapeutic objectives, acupressure points) is underutilized
- Only 13 distinct purpose tags generated vs. 30+ needed for semantic richness

**Solution:** Enhanced AI prompts with comprehensive purpose tag taxonomy and program context injection.

## 2. Final Data Model

We will create a new `program_segments` table. A single program will be represented by multiple entries: three for its parent macro-phases, and numerous child entries for the micro-chunks discovered within them.

### 2.1. Pydantic Schema (`api/schemas/program_schemas.py`)

```python
from sqlalchemy import Column, Integer, ForeignKey, String, JSON
from sqlalchemy.orm import relationship
from core.database import Base

class ProgramSegment(Base):
    __tablename__ = "program_segments"

    id = Column(Integer, primary_key=True, index=True)
    original_program_id = Column(Integer, ForeignKey("programs.id"), nullable=False, index=True)

    # --- Hierarchy and Granularity ---
    phase = Column(String, index=True, nullable=False) # e.g., "front", "main", "cooling", "discovered"
    parent_segment_id = Column(Integer, ForeignKey("program_segments.id"), nullable=True, index=True) # NULL for macro-phases

    # --- The Segment Interface Contract ---
    # Example: {"position": 18, "width": "W", "kneading_speed": 125, "tapping_speed": 0, "airbags": "shoulders"}
    entry_state = Column(JSON, nullable=False)
    # Example: {"position": 12, "width": "M", "kneading_speed": 100, "tapping_speed": 0, "airbags": "off"}
    exit_state = Column(JSON, nullable=False)

    # --- Rich Ontology Tags ---
    # Example: ["tension_release", "circulation_boost"]
    purpose_tags = Column(JSON)
    # Example: ["percussive", "deep_kneading"]
    technique_tags = Column(JSON)
    # Example: ["lumbar", "shoulders"]
    body_part_tags = Column(JSON)

    # --- Objective Metrics ---
    # Example: 8
    intensity_score = Column(Integer)
    # Example: 45
    duration_seconds = Column(Integer)

    # --- Core Data ---
    steps = Column(JSON, nullable=False)

    # --- Relationships ---
    original_program = relationship("Program")
    parent_segment = relationship("ProgramSegment", remote_side=[id], backref="child_segments")
```

## 3. The AI Agent Architecture

The process will use a chain of three specialized AI agents.

### 3.1. Agent 1: Macro-Phase Segmentation Agent
*   **Purpose:** To identify the three primary `front`, `main`, and `cooling` phases.
*   **Output:** Boundaries for the three macro-phases.

### 3.2. Agent 2: Micro-Chunk Discovery Agent
*   **Purpose:** To discover smaller, thematically coherent chunks *within* a larger block of steps.
*   **Output:** Boundaries for conceptual chunks within a given macro-phase.

### 3.3. Agent 3: Ontology & Interface Tagging Agent
*   **Purpose:** To analyze any segment of steps and assign its complete metadata.
*   **Output:** An object containing ontology tags (`purpose`, `technique`, etc.) and the `entry_state`/`exit_state` contract.

## 4. The Backfill Process Flow

A new script, `scripts/backfill_ontology_segments.py`, will orchestrate this process. For each program:

1.  **Fetch Program:** Retrieve the program's `steps` and `program_description`.
2.  **Macro-Phase Segmentation (Agent 1):** Identify the boundaries for `front`, `main`, and `cooling`.
3.  **Iterate Through Macro-Phases:** For each macro-phase (e.g., the `main` phase):
    a.  **Tag the Macro-Phase (Agent 3):** Call the **Ontology & Interface Tagging Agent** to get its complete metadata (tags and entry/exit states).
    b.  **Save Macro-Phase and Get ID:** Save this segment to the DB with `phase = 'main'` and `parent_segment_id = NULL`. **Crucially, retrieve the new `id` of this parent segment.**
    c.  **Discover Micro-Chunks (Agent 2):** Get the boundaries of all smaller chunks within the macro-phase steps.
    d.  **Iterate and Tag Micro-Chunks:** For each `discovered` chunk:
        i.  **Tag the Micro-Chunk (Agent 3):** Get its specific metadata.
        ii. **Save Micro-Chunk with Parent Link:** Save this small segment to the DB with `phase = 'discovered'` and set its `parent_segment_id` to the ID retrieved in step 3.b.
4.  **Vector Embedding:** Generate and upload a vector embedding to Pinecone for every segment (both macro and micro).

## 5. Strategic Benefits

This final, hybrid architecture is exceptionally robust:
*   **Hierarchical Context:** The AI can now make context-aware decisions (e.g., "find an intense chunk *from a main phase*").
*   **Seamless Transitions:** The `entry_state`/`exit_state` contract allows for the precise, calculated generation of "bridge" steps.
*   **Maximum Flexibility:** Supports both simple, phase-based refactoring of the old service and complex, creative synthesis for the new one.