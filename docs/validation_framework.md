# Purpose Tag Validation Framework

## Overview

This framework provides validation mechanisms to ensure the enhanced AI segmentation agents generate semantically rich, contextually appropriate purpose tags that align with OSIM program documentation and therapeutic objectives.

## 1. Validation Metrics

### 1.1. Tag Diversity Score
Measures the variety of purpose tags being generated across all segments.

```python
def calculate_tag_diversity_score(all_purpose_tags: List[List[str]]) -> float:
    """Calculate diversity score based on unique tag distribution."""
    
    # Flatten all tags and count frequencies
    all_tags = [tag for tags in all_purpose_tags for tag in tags]
    tag_counts = Counter(all_tags)
    
    # Calculate Shannon diversity index
    total_tags = len(all_tags)
    diversity_score = 0
    
    for count in tag_counts.values():
        proportion = count / total_tags
        diversity_score -= proportion * math.log2(proportion)
    
    # Normalize to 0-1 scale (higher is better)
    max_possible_diversity = math.log2(len(tag_counts))
    normalized_score = diversity_score / max_possible_diversity if max_possible_diversity > 0 else 0
    
    return normalized_score

# Target: > 0.7 (indicating good tag diversity)
```

### 1.2. Context Alignment Score
Measures how well purpose tags align with program context.

```python
def calculate_context_alignment_score(
    purpose_tags: List[str], 
    program_context: Dict[str, Any]
) -> float:
    """Calculate alignment between purpose tags and program context."""
    
    alignment_score = 0
    max_score = 10
    
    # Program type alignment (weight: 3)
    program_type_tags = PROGRAM_PURPOSE_MAPPING.get(program_context['program_type'], [])
    type_alignment = len(set(purpose_tags) & set(program_type_tags))
    alignment_score += min(type_alignment * 3, 3)
    
    # Demographic alignment (weight: 2)
    demographic_tags = DEMOGRAPHIC_PURPOSE_MAPPING.get(program_context['target_demographic'], [])
    demo_alignment = len(set(purpose_tags) & set(demographic_tags))
    alignment_score += min(demo_alignment * 2, 2)
    
    # Acupressure alignment (weight: 2)
    acupressure_alignment = len(set(purpose_tags) & set(program_context['acupressure_benefits']))
    alignment_score += min(acupressure_alignment * 2, 2)
    
    # Penalty for generic tags (weight: -3)
    generic_tags = ['deep_therapy', 'tension_release', 'invigoration']
    generic_penalty = len(set(purpose_tags) & set(generic_tags)) * 0.5
    alignment_score -= generic_penalty
    
    return max(alignment_score / max_score, 0)

# Target: > 0.6 (indicating good context alignment)
```

### 1.3. Semantic Specificity Score
Measures how specific and meaningful the purpose tags are.

```python
def calculate_semantic_specificity_score(purpose_tags: List[str]) -> float:
    """Calculate semantic specificity of purpose tags."""
    
    # Define specificity levels
    specificity_weights = {
        # High specificity (program/demographic specific)
        'skin_beautification': 1.0,
        'athletic_recovery': 1.0,
        'executive_wellness': 1.0,
        'maternal_health': 1.0,
        'computer_strain_relief': 1.0,
        'respiratory_enhancement': 1.0,
        
        # Medium specificity (therapeutic modality specific)
        'posture_correction': 0.8,
        'lymphatic_drainage': 0.8,
        'spinal_alignment': 0.8,
        'joint_mobility': 0.8,
        'cardiovascular_support': 0.8,
        
        # Low specificity (general wellness)
        'muscle_recovery': 0.6,
        'flexibility_improvement': 0.6,
        'pain_relief': 0.6,
        'stress_relief': 0.6,
        
        # Very low specificity (generic)
        'tension_release': 0.3,
        'circulation_boost': 0.3,
        'relaxation': 0.3,
        
        # Minimal specificity (overly generic)
        'deep_therapy': 0.1,
        'invigoration': 0.1
    }
    
    if not purpose_tags:
        return 0
    
    total_specificity = sum(specificity_weights.get(tag, 0.5) for tag in purpose_tags)
    return total_specificity / len(purpose_tags)

# Target: > 0.7 (indicating high semantic specificity)
```

## 2. Validation Rules

### 2.1. Context Consistency Rules
```python
def validate_context_consistency(
    purpose_tags: List[str], 
    program_context: Dict[str, Any]
) -> List[str]:
    """Validate purpose tags against program context and return violations."""
    
    violations = []
    
    # Rule 1: Beauty programs should include beauty-related tags
    if program_context['program_type'] == 'Beauty':
        beauty_tags = ['skin_beautification', 'anti_aging_therapy', 'cellulite_reduction']
        if not any(tag in purpose_tags for tag in beauty_tags):
            violations.append("Beauty program missing beauty-specific purpose tags")
    
    # Rule 2: Sports programs should include athletic tags
    if program_context['program_type'] == 'Sports':
        athletic_tags = ['athletic_recovery', 'muscle_recovery', 'performance_enhancement']
        if not any(tag in purpose_tags for tag in athletic_tags):
            violations.append("Sports program missing athletic-specific purpose tags")
    
    # Rule 3: Office worker demographics should include relevant tags
    if program_context['target_demographic'] == 'office_workers':
        office_tags = ['computer_strain_relief', 'posture_correction', 'neck_stiffness_relief']
        if not any(tag in purpose_tags for tag in office_tags):
            violations.append("Office worker program missing workplace-specific tags")
    
    # Rule 4: Acupressure benefits should be reflected
    if program_context['acupressure_benefits']:
        if not any(tag in purpose_tags for tag in program_context['acupressure_benefits']):
            violations.append("Program with acupressure points missing related therapeutic benefits")
    
    # Rule 5: Avoid generic tag overuse
    generic_tags = ['deep_therapy', 'tension_release', 'invigoration']
    generic_count = sum(1 for tag in purpose_tags if tag in generic_tags)
    if generic_count > 1:
        violations.append(f"Overuse of generic tags: {generic_count} generic tags found")
    
    return violations
```

### 2.2. Therapeutic Logic Rules
```python
def validate_therapeutic_logic(
    purpose_tags: List[str], 
    technique_tags: List[str],
    intensity_score: int
) -> List[str]:
    """Validate therapeutic logic consistency."""
    
    violations = []
    
    # Rule 1: High intensity should align with intensive purposes
    if intensity_score >= 8:
        intensive_purposes = ['athletic_recovery', 'deep_therapy', 'muscle_recovery']
        if not any(tag in purpose_tags for tag in intensive_purposes):
            violations.append("High intensity segment missing intensive therapeutic purposes")
    
    # Rule 2: Relaxation purposes should not have high intensity
    relaxation_purposes = ['relaxation', 'stress_relief', 'sleep_preparation']
    if any(tag in purpose_tags for tag in relaxation_purposes) and intensity_score >= 7:
        violations.append("Relaxation purposes inconsistent with high intensity")
    
    # Rule 3: Air compression techniques should align with circulation purposes
    if 'air_compression' in technique_tags:
        circulation_purposes = ['circulation_boost', 'lymphatic_drainage', 'cardiovascular_support']
        if not any(tag in purpose_tags for tag in circulation_purposes):
            violations.append("Air compression technique missing circulation-related purposes")
    
    return violations
```

## 3. Validation Pipeline

### 3.1. Real-time Validation
```python
async def validate_segment_ontology(
    ontology: SegmentOntology,
    program_context: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """Perform comprehensive validation of segment ontology."""
    
    validation_results = {
        'is_valid': True,
        'scores': {},
        'violations': [],
        'recommendations': []
    }
    
    # Calculate validation scores
    validation_results['scores']['semantic_specificity'] = calculate_semantic_specificity_score(
        ontology.purpose_tags
    )
    
    if program_context:
        validation_results['scores']['context_alignment'] = calculate_context_alignment_score(
            ontology.purpose_tags, program_context
        )
        
        # Check context consistency
        context_violations = validate_context_consistency(
            ontology.purpose_tags, program_context
        )
        validation_results['violations'].extend(context_violations)
    
    # Check therapeutic logic
    logic_violations = validate_therapeutic_logic(
        ontology.purpose_tags, 
        ontology.technique_tags,
        ontology.intensity_score
    )
    validation_results['violations'].extend(logic_violations)
    
    # Determine overall validity
    min_specificity = 0.5
    min_alignment = 0.4
    
    if validation_results['scores']['semantic_specificity'] < min_specificity:
        validation_results['is_valid'] = False
        validation_results['recommendations'].append(
            "Increase semantic specificity by using more specific purpose tags"
        )
    
    if program_context and validation_results['scores']['context_alignment'] < min_alignment:
        validation_results['is_valid'] = False
        validation_results['recommendations'].append(
            "Improve context alignment by incorporating program-specific purposes"
        )
    
    if validation_results['violations']:
        validation_results['is_valid'] = False
    
    return validation_results
```

### 3.2. Batch Validation for Analysis
```python
def analyze_segmentation_quality(segments: List[Dict[str, Any]]) -> Dict[str, Any]:
    """Analyze overall segmentation quality across multiple segments."""
    
    all_purpose_tags = [segment['purpose_tags'] for segment in segments]
    
    analysis = {
        'total_segments': len(segments),
        'unique_purpose_tags': len(set(tag for tags in all_purpose_tags for tag in tags)),
        'tag_diversity_score': calculate_tag_diversity_score(all_purpose_tags),
        'generic_tag_usage': {},
        'context_alignment_distribution': [],
        'recommendations': []
    }
    
    # Analyze generic tag usage
    generic_tags = ['deep_therapy', 'tension_release', 'invigoration', 'circulation_boost']
    for tag in generic_tags:
        usage_count = sum(1 for tags in all_purpose_tags if tag in tags)
        analysis['generic_tag_usage'][tag] = {
            'count': usage_count,
            'percentage': (usage_count / len(segments)) * 100
        }
    
    # Generate recommendations
    if analysis['tag_diversity_score'] < 0.6:
        analysis['recommendations'].append(
            "Low tag diversity detected. Consider expanding purpose tag vocabulary."
        )
    
    total_generic_usage = sum(
        analysis['generic_tag_usage'][tag]['percentage'] 
        for tag in generic_tags
    )
    if total_generic_usage > 200:  # More than 50% average usage
        analysis['recommendations'].append(
            "High generic tag usage detected. Focus on context-specific purposes."
        )
    
    return analysis
```

## 4. Implementation Integration

### 4.1. Enhanced Segmentation Pipeline
```python
async def enhanced_segmentation_with_validation(
    steps: List[Dict[str, Any]],
    program_context: Dict[str, Any]
) -> Dict[str, Any]:
    """Run segmentation with integrated validation."""
    
    # Get enhanced ontology
    ontology = await get_enhanced_segment_ontology(steps, program_context)
    
    # Validate results
    validation_results = await validate_segment_ontology(ontology, program_context)
    
    # If validation fails, attempt improvement
    if not validation_results['is_valid']:
        logger.warning(f"Validation failed: {validation_results['violations']}")
        
        # Attempt to improve with fallback context guidance
        improved_ontology = await improve_ontology_with_context(
            ontology, program_context, validation_results
        )
        
        # Re-validate
        validation_results = await validate_segment_ontology(
            improved_ontology, program_context
        )
        
        ontology = improved_ontology
    
    return {
        'ontology': ontology,
        'validation': validation_results
    }
```

## 5. Success Metrics

### 5.1. Target Improvements
- **Tag Diversity**: Increase from 13 unique tags to 30+ unique tags
- **Generic Tag Usage**: Reduce from 88% to <30% of segments
- **Context Alignment**: Achieve >60% average alignment score
- **Semantic Specificity**: Achieve >70% average specificity score

### 5.2. Quality Indicators
- Programs with specific therapeutic objectives show aligned purpose tags
- Demographic-specific programs include relevant lifestyle tags
- Acupressure point data is reflected in therapeutic benefits
- Intensity and technique patterns align with purpose classifications
