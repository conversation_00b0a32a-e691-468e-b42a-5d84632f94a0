# Enhanced Segmentation Implementation Guide

## Overview

This guide provides step-by-step instructions for implementing the enhanced AI segmentation system that generates rich, diverse purpose tags for OSIM massage chair program segments.

## 1. Implementation Summary

### 1.1. Problem Addressed
- **Current Issue**: 88% of segments use only 4 generic purpose tags (`deep_therapy`, `tension_release`, `invigoration`, `circulation_boost`)
- **Root Cause**: AI agents not leveraging rich program context (target demographics, therapeutic objectives, acupressure points)
- **Impact**: Poor semantic richness, limited queryability, missed therapeutic nuances

### 1.2. Solution Components
1. **Comprehensive Purpose Tag Taxonomy** (40+ specific tags)
2. **Enhanced AI Agent Prompts** (context-driven, simplified)
3. **Program Context Injection Strategy** (demographic mapping, acupressure benefits)
4. **Validation Framework** (diversity, alignment, specificity metrics)

## 2. File Structure and Components

### 2.1. New Documentation Files
```
docs/
├── purpose_tag_taxonomy.md           # 40+ purpose tag vocabulary
├── enhanced_tagging_agent_prompt.md  # New tagging agent prompt
├── simplified_agent_prompts.md       # Simplified macro/micro prompts
├── context_injection_strategy.md     # Context processing strategy
├── validation_framework.md           # Quality validation system
└── enhanced_segmentation_implementation_guide.md  # This file
```

### 2.2. New Implementation Files
```
api/services/segmentation/
├── enhanced_tagging_agent.py         # Enhanced tagging with context
├── context_processor.py              # Context extraction utilities
└── validation_service.py             # Validation framework
```

### 2.3. Modified Existing Files
```
api/services/segmentation/
├── tagging_agent.py                  # Updated with enhanced prompt
├── macro_phase_agent.py              # Simplified prompt
└── micro_chunk_agent.py              # Simplified prompt
```

## 3. Step-by-Step Implementation

### Step 1: Create Context Processing Service
```bash
# Create context processor
touch api/services/segmentation/context_processor.py
```

**Implementation**: Copy the context processing functions from `enhanced_tagging_agent.py`:
- `extract_program_context()`
- `extract_demographic_from_objective()`
- `parse_acupressure_benefits()`
- `get_context_guided_purposes()`

### Step 2: Create Validation Service
```bash
# Create validation service
touch api/services/segmentation/validation_service.py
```

**Implementation**: Implement validation functions from `validation_framework.md`:
- `calculate_tag_diversity_score()`
- `calculate_context_alignment_score()`
- `validate_segment_ontology()`

### Step 3: Update Existing Agent Prompts

#### 3.1. Update Tagging Agent
**File**: `api/services/segmentation/tagging_agent.py`

Replace the existing system prompt with the enhanced version from `enhanced_tagging_agent_prompt.md`:

```python
# Replace the system_prompt in ontology_tagging_agent with:
system_prompt="""
You are a certified massage therapist and OSIM massage chair programming expert specializing in therapeutic purpose identification...
[Use the full enhanced prompt from enhanced_tagging_agent_prompt.md]
"""
```

#### 3.2. Update Macro Phase Agent
**File**: `api/services/segmentation/macro_phase_agent.py`

Replace with simplified prompt from `simplified_agent_prompts.md`.

#### 3.3. Update Micro Chunk Agent
**File**: `api/services/segmentation/micro_chunk_agent.py`

Replace with simplified prompt from `simplified_agent_prompts.md`.

### Step 4: Integrate Context Injection

#### 4.1. Modify Segmentation Service
**File**: `api/services/segmentation_service.py`

Add context extraction and injection:

```python
from .context_processor import extract_program_context
from .enhanced_tagging_agent import get_enhanced_segment_ontology

async def process_program_segments(program_id: int):
    # Fetch program with description
    program = await get_program_with_description(program_id)
    
    # Extract program context
    program_context = extract_program_context(program)
    
    # Use enhanced tagging with context
    for segment in segments:
        ontology = await get_enhanced_segment_ontology(
            segment['steps'], 
            program_context
        )
        # Save with enhanced ontology
```

#### 4.2. Update Database Queries
Ensure program queries include `program_description` field:

```python
def get_program_with_description(program_id: int):
    return session.query(Program).options(
        selectinload(Program.program_description)
    ).filter(Program.id == program_id).first()
```

### Step 5: Implement Validation Pipeline

#### 5.1. Add Validation to Segmentation
```python
from .validation_service import validate_segment_ontology

async def process_segment_with_validation(steps, program_context):
    # Get enhanced ontology
    ontology = await get_enhanced_segment_ontology(steps, program_context)
    
    # Validate results
    validation_results = await validate_segment_ontology(ontology, program_context)
    
    # Log validation issues
    if not validation_results['is_valid']:
        logger.warning(f"Validation issues: {validation_results['violations']}")
    
    return ontology, validation_results
```

#### 5.2. Add Quality Monitoring
```python
async def analyze_segmentation_batch_quality(program_ids: List[int]):
    """Analyze quality across multiple programs."""
    all_segments = []
    
    for program_id in program_ids:
        segments = await get_program_segments(program_id)
        all_segments.extend(segments)
    
    quality_analysis = analyze_segmentation_quality(all_segments)
    
    # Log quality metrics
    logger.info(f"Tag diversity score: {quality_analysis['tag_diversity_score']}")
    logger.info(f"Unique tags: {quality_analysis['unique_purpose_tags']}")
    
    return quality_analysis
```

## 4. Testing and Validation

### 4.1. Unit Tests
Create tests for each component:

```python
# test_context_processor.py
def test_extract_program_context():
    program_data = {
        'name': 'Beauty Program',
        'program_description': {
            'target_group_objective': 'For people who desire skin beautification',
            'targeted_acupressure_points': ['Lung Transporter - respiratory enhancement']
        }
    }
    
    context = extract_program_context(program_data)
    
    assert context['program_type'] == 'Beauty'
    assert 'skin_beautification' in get_context_guided_purposes(context)
    assert 'respiratory_enhancement' in context['acupressure_benefits']

# test_validation_service.py
def test_context_alignment_score():
    purpose_tags = ['skin_beautification', 'anti_aging_therapy']
    program_context = {'program_type': 'Beauty', 'target_demographic': 'general'}
    
    score = calculate_context_alignment_score(purpose_tags, program_context)
    
    assert score > 0.6  # Should have good alignment
```

### 4.2. Integration Tests
Test the full pipeline:

```python
async def test_enhanced_segmentation_pipeline():
    # Test with actual program data
    program = await get_program_by_name('Beauty')
    segments = await process_program_segments(program.id)
    
    # Validate enhanced purpose tags
    for segment in segments:
        assert len(segment.purpose_tags) >= 2
        assert not all(tag in ['deep_therapy', 'tension_release'] for tag in segment.purpose_tags)
        
        # Check for program-specific tags
        if 'Beauty' in program.name:
            beauty_tags = ['skin_beautification', 'anti_aging_therapy']
            assert any(tag in segment.purpose_tags for tag in beauty_tags)
```

### 4.3. Quality Validation
Run quality analysis on existing data:

```python
async def validate_enhancement_effectiveness():
    # Analyze before enhancement
    old_segments = await get_segments_with_old_tags()
    old_diversity = calculate_tag_diversity_score([s.purpose_tags for s in old_segments])
    
    # Re-process with enhanced system
    new_segments = await reprocess_segments_with_enhancement()
    new_diversity = calculate_tag_diversity_score([s.purpose_tags for s in new_segments])
    
    # Validate improvement
    assert new_diversity > old_diversity * 1.5  # 50% improvement
    
    # Check generic tag reduction
    old_generic_usage = calculate_generic_tag_usage(old_segments)
    new_generic_usage = calculate_generic_tag_usage(new_segments)
    
    assert new_generic_usage < old_generic_usage * 0.5  # 50% reduction
```

## 5. Deployment Strategy

### 5.1. Phased Rollout
1. **Phase 1**: Deploy enhanced tagging agent for new segments only
2. **Phase 2**: Validate quality improvements with sample data
3. **Phase 3**: Backfill existing segments with enhanced tags
4. **Phase 4**: Full deployment with monitoring

### 5.2. Monitoring and Metrics
Track key metrics post-deployment:

```python
# Daily quality monitoring
async def daily_quality_check():
    recent_segments = await get_segments_created_since(days_ago=1)
    
    quality_metrics = {
        'tag_diversity': calculate_tag_diversity_score([s.purpose_tags for s in recent_segments]),
        'generic_usage': calculate_generic_tag_usage(recent_segments),
        'context_alignment': calculate_average_context_alignment(recent_segments),
        'validation_failures': count_validation_failures(recent_segments)
    }
    
    # Alert if quality degrades
    if quality_metrics['tag_diversity'] < 0.6:
        send_alert("Tag diversity below threshold")
    
    return quality_metrics
```

### 5.3. Rollback Plan
If quality degrades:
1. **Immediate**: Switch back to original tagging agent
2. **Analysis**: Review validation failures and prompt issues
3. **Fix**: Update prompts or context processing
4. **Re-deploy**: Test and re-deploy enhanced system

## 6. Expected Outcomes

### 6.1. Quantitative Improvements
- **Tag Diversity**: Increase from 13 to 30+ unique purpose tags
- **Generic Tag Usage**: Reduce from 88% to <30% of segments
- **Context Alignment**: Achieve >60% average alignment score
- **Semantic Specificity**: Achieve >70% average specificity score

### 6.2. Qualitative Improvements
- Purpose tags reflect actual program therapeutic objectives
- Demographic-specific needs captured in purpose classification
- Acupressure benefits properly attributed to segments
- Better queryability for therapeutic program discovery

### 6.3. Business Impact
- Enhanced user experience through better program recommendations
- Improved therapeutic program discovery and matching
- More accurate representation of OSIM's therapeutic expertise
- Foundation for advanced AI-driven program generation
