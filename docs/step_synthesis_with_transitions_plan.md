# Plan: Enhanced Step Synthesis with Smooth Transitions

**Version:** 1.0
**Date:** 2024-07-26
**Author:** AI Assistant

## 1. Objective

To enhance the `StepSynthesisService` by introducing a mechanism for creating smooth transitions between different massage program segments. This aims to eliminate abrupt jumps in massage parameters (e.g., speed, position, intensity) when stitching segments from different source programs, leading to a more natural and pleasant user experience.

## 2. Background

The existing `StepSynthesisService` (as detailed in `step_synthesis_plan.md`) segments multiple massage programs into "front," "main," and "cooling" sections. It then selects the best segment for each part based on a target sequence and concatenates them. While effective, this direct concatenation can lead to jarring transitions.

## 3. Proposed Enhancement: Transition Smoothing

We will introduce a dedicated "Transition Agent" and associated logic to generate 1-2 intermediate steps between the concatenated segments. These transition steps will be designed to gradually interpolate key massage parameters.

## 4. Key Components & Implementation Details

### 4.1. `TransitionStep` Pydantic Model

A Pydantic model to structure the output of the transition-generating agent.

```python
class TransitionStep(BaseModel):
    steps: List[HighEndProductStep] = Field(..., description="List of transition steps")
    rationale: Optional[str] = Field(None, description="Rationale for the transition design")
```

### 4.2. `format_step_description` Helper Function

A utility function to consistently format `HighEndProductStep` objects into a string representation for agent prompts. This function has been refactored into the `api/services/step_synthesis_service.py` file for cleaner code and is used by multiple agents.

```python
def format_step_description(step: HighEndProductStep) -> str:
    parts = []
    if step.roller_action_description:
        parts.append(f"Roller: {step.roller_action_description}")
    # ... (other fields like air, kneading_speed, tapping_speed, position_3d, width, type, subroutine_id)
    return " | ".join(parts)
```

This helper is used in:

- `analyze_and_segment_steps` for preparing input to `segmentation_analyzer`.
- `create_transition_steps` for preparing input to `transition_agent`.

### 4.3. `transition_agent`

An AI agent (`pydantic-ai.Agent`) specifically designed to create smooth transitions.

- **Input:** The last step of the preceding segment and the first step of the succeeding segment, along with (optionally) full example programs for contextual reference.
- **Output:** A `TransitionStep` object containing 1-2 `HighEndProductStep`s.
- **System Prompt:** Guides the agent to focus on gradual changes in numeric values (speed, intensity), logical position changes, and maintaining a natural flow.
- **Few-shot/Contextual Learning:** The agent will be provided with full example programs (nicely formatted using `format_step_description`) to understand the desired style and smoothness of transitions.

### 4.4. `create_transition_steps` Async Function

This function orchestrates the call to the `transition_agent`:

```python
async def create_transition_steps(
    last_step: HighEndProductStep,
    first_step: HighEndProductStep,
    example_programs: List[Program] # Used for providing context
) -> List[HighEndProductStep]:
    # 1. Format last_step and first_step using format_step_description.
    # 2. Format example_programs' steps using format_step_description.
    # 3. Construct a prompt for the transition_agent including the target steps and example context.
    # 4. Call transition_agent.run().
    # 5. Return the generated transition steps.
```

### 4.5. Modifications to `StepSynthesisService.synthesize_steps`

The main `synthesize_steps` method will be updated:

1. After selecting the best front, main, and cooling segments.
2. Before concatenating the main segment to the front segment, call `create_transition_steps` with the last step of `front_steps` and the first step of `main_steps`. Insert the returned transition steps.
3. Similarly, before concatenating the cooling segment to the (now extended) main segment, call `create_transition_steps` with the last step of the current sequence and the first step of `cooling_steps`. Insert these transition steps.
4. Renumber all steps in the final combined sequence.

## 5. Benefits

- **Improved User Experience:** Provides a smoother, more professional-feeling massage program.
- **Reduced Abruptness:** Eliminates sudden changes in massage parameters.
- **Enhanced Realism:** Generated programs will more closely resemble professionally designed sequences.

## 6. Future Considerations

- Allow configurable number of transition steps.
- Explore more sophisticated interpolation logic beyond simple linear changes if needed.
- Fine-tune the `transition_agent`'s prompt for optimal performance.

## 7. Related Plans & Documents

This plan builds upon and relates to the following documents in the `/docs` directory:

- [Step Synthesis Plan](./step_synthesis_plan.md)
- [Semantic Search Plan](./semantic_search_plan.md)
- [Sequence Generation Plan V4](./sequence_generation_plan_v4.md)
- [Sequence Generation Plan V3](./sequence_generation_plan_v3.md)
- [Sequence Generation Plan (Original)](./sequence_generation_plan.md)
- [Token Refresh Plan](./token-refresh-plan.md)
