# Plan: AI-Powered Massage Step Synthesis

## 1. Goal

To analyze the detailed `steps` from the top 3 relevant massage programs (retrieved based on user needs) and synthesize a new, coherent sequence of steps conforming to the `HighEndProductSteps` schema using AI agents.

## 2. Approach

Implement an agent-based workflow within a dedicated service file (`api/services/step_synthesis_service.py`), keeping the synthesis logic separate from the main `ChatService`. This approach leverages agent delegation for modularity and maintainability.

## 3. Supporting Schemas

The following Pydantic models will be defined or confirmed in `api/schemas/chat_schemas.py`:

- **`HighEndProductStep` (Existing):** Defines the structure for a single step (roller action, speed, position, etc.).
- **`HighEndProductSteps` (Existing):** Contains `steps: List[HighEndProductStep]`. This is the final output format.
- **`SegmentedSteps` (New):** Holds the output of the segmentation agent.

  ```python
  from pydantic import BaseModel, Field
  from typing import List
  # Assuming HighEndProductStep is defined elsewhere or imported

  class SegmentedSteps(BaseModel):
      front_steps: List[HighEndProductStep] = Field(default_factory=list, description="Steps corresponding to the Front Sequence")
      main_steps: List[HighEndProductStep] = Field(default_factory=list, description="Steps corresponding to the Main Sequence")
      cooling_steps: List[HighEndProductStep] = Field(default_factory=list, description="Steps corresponding to the Cooling Sequence")
  ```

- **`StitchingInput` (New):** Structures the input for the stitching agent.

  ```python
  from pydantic import BaseModel
  # Assuming SegmentedSteps and TargetSequence are defined elsewhere or imported

  class StitchingInput(BaseModel):
      segmented_program_1: SegmentedSteps
      segmented_program_2: SegmentedSteps
      segmented_program_3: SegmentedSteps
      target_sequence: TargetSequence # To guide the stitching process
  ```

## 4. Agent Definitions

The following agents will be defined in `api/services/step_synthesis_service.py`:

- **`segmentation_agent`**:
  - **Input:** `program_steps: List[HighEndProductStep]`, `target_sequence: TargetSequence`.
  - **Output:** `SegmentedSteps`.
  - **System Prompt:** "Analyze the provided `program_steps` and the descriptions in the `target_sequence` (Front, Main, Cooling). Divide the `program_steps` into three corresponding lists: `front_steps`, `main_steps`, and `cooling_steps`. Base the division on the likely boundaries implied by the `target_sequence` descriptions and the logical flow of the steps (e.g., initial warm-up, main body work, concluding cool-down). Output the result using the `SegmentedSteps` schema."
- **`stitching_agent`**:
  - **Input:** `stitching_input: StitchingInput`.
  - **Output:** `HighEndProductSteps`.
  - **System Prompt:** "You are an expert massage program choreographer. Combine the steps from the three segmented programs (`segmented_program_1`, `segmented_program_2`, `segmented_program_3`) provided in the `stitching_input`. Use the `target_sequence` also provided in the input as a guide for the overall objective and flow. Create a _single_, coherent, and effective new sequence of steps. Blend techniques, adjust timings/intensities logically, ensure smooth transitions between segments (front -> main -> cooling), and re-number the steps sequentially in the final output. Output the final combined sequence using the `HighEndProductSteps` schema."
- **`orchestration_agent`**:
  - **Input:** `top_programs: List[Program]`, `target_sequence: TargetSequence`. (Where `Program` includes its steps).
  - **Output:** `HighEndProductSteps`.
  - **Tools:**
    - `segment_program_steps(program_steps: List[HighEndProductStep], target_sequence: TargetSequence) -> SegmentedSteps`: This tool will internally call `segmentation_agent.run()`.
    - `stitch_segments(stitching_input: StitchingInput) -> HighEndProductSteps`: This tool will internally call `stitching_agent.run()`.
  - **System Prompt:** "Orchestrate the creation of a new massage program. Given the `top_programs` (list of program data including their steps) and `target_sequence`: 1. For each of the top 3 programs, use the `segment_program_steps` tool to divide its steps based on the `target_sequence`. 2. Collect the three `SegmentedSteps` results. 3. Prepare the `StitchingInput` using the three segmented results and the original `target_sequence`. 4. Use the `stitch_segments` tool with the prepared input to generate the final `HighEndProductSteps`."

## 5. Workflow Implementation

- A primary function or class method (e.g., `synthesize_steps(programs: List[Program], target: TargetSequence) -> HighEndProductSteps`) will be created in `api/services/step_synthesis_service.py`.
- This function will orchestrate the process:
  1.  Extract steps from the top 3 input `programs`. Handle subroutine steps (e.g., inline them before segmentation).
  2.  Prepare input for and run the `orchestration_agent`.
  3.  The `orchestration_agent` uses its tools (`segment_program_steps`, `stitch_segments`) which delegate calls to the `segmentation_agent` and `stitching_agent` respectively.
  4.  Return the final `HighEndProductSteps` generated by the `stitching_agent` (via the `orchestration_agent`).

## 6. Workflow Diagram

```mermaid
graph TD
    subgraph Step Synthesis Service (api/services/step_synthesis_service.py)
        I[Input: Top 3 Programs + TargetSequence] --> O(Orchestration Function/Class: synthesize_steps);

        subgraph Agents
            OA(Orchestration Agent);
            SA(Segmentation Agent);
            TA(Stitching Agent);
        end

        O -- Calls --> OA;
        OA -- Uses Tool --> F1(Segment Tool: segment_program_steps);
        F1 -- Calls --> SA;
        SA -- Returns --> F1[SegmentedSteps (x3)];
        F1 -- Returns --> OA;

        OA -- Uses Tool --> F2(Stitch Tool: stitch_segments);
        F2 -- Input: StitchingInput --> TA;
        TA -- Returns --> F2[HighEndProductSteps];
        F2 -- Returns --> OA;

        OA -- Returns --> O[Final HighEndProductSteps];
    end

    style SA fill:#f9d,stroke:#333,stroke-width:1px
    style TA fill:#f9d,stroke:#333,stroke-width:1px
    style OA fill:#ccf,stroke:#333,stroke-width:1px
```

## 7. Translation

Handling potential language differences in step descriptions (e.g., Japanese/English mix) is deferred. A separate translation function/tool can be added later if required. The initial focus is on the structural synthesis.

## 8. Next Steps

Proceed with implementing the schemas, agents, and orchestration logic as outlined above.
