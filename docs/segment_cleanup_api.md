# Program Segments Cleanup API

## Overview

The segment cleanup API provides a comprehensive way to clean up program segments and segmentation jobs, following the same pattern as the bulk program cleanup procedure. This API handles both database cleanup and vector database (Pinecone) cleanup.

## Endpoint

```
DELETE /api2/segments/clear
```

**Authentication:** Admin only (requires `@require_admin` decorator)

## Features

- ✅ **Hierarchical Cleanup**: Properly handles parent-child relationships (micro-chunks before macro-phases)
- ✅ **Vector Database Cleanup**: Removes embeddings from Pinecone namespace
- ✅ **Flexible Filtering**: Support for product-level or program-level cleanup
- ✅ **Comprehensive Statistics**: Detailed reporting of cleanup operations
- ✅ **Safe Operations**: Proper transaction handling with rollback on errors
- ✅ **Sequence Reset**: Optionally resets database sequences for complete cleanup

## Request Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `product_id` | integer | No | Filter cleanup to specific product only |
| `program_id` | integer | No | Filter cleanup to specific program only |

**Note:** If both parameters are provided, `program_id` takes precedence. If neither is provided, ALL segments and jobs are cleared.

## Response Schema

```json
{
  "success": true,
  "message": "Successfully cleared segments and segmentation jobs",
  "statistics": {
    "segments_deleted": 106,
    "micro_chunks_deleted": 93,
    "macro_phases_deleted": 13,
    "jobs_deleted": 9,
    "vectors_deleted_from_pinecone": 106,
    "sequences_reset": true
  },
  "filters_applied": {
    "product_id": null,
    "program_id": null
  }
}
```

## Usage Examples

### 1. Clear All Segments and Jobs

```bash
curl -X DELETE "http://localhost:8000/api2/segments/clear" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 2. Clear Segments for Specific Product

```bash
curl -X DELETE "http://localhost:8000/api2/segments/clear?product_id=1" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 3. Clear Segments for Specific Program

```bash
curl -X DELETE "http://localhost:8000/api2/segments/clear?program_id=15" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## Cleanup Process

The API follows this sequence:

1. **Validation & Filtering**: Apply product_id or program_id filters if provided
2. **Count Collection**: Get statistics before deletion for reporting
3. **Pinecone Cleanup**: Remove vectors from `program_segments` namespace in batches
4. **Database Cleanup**: 
   - Delete segmentation jobs (no dependencies)
   - Delete micro-chunks (child segments)
   - Delete macro-phases (parent segments)
5. **Sequence Reset**: Reset auto-increment sequences if clearing all data
6. **Transaction Commit**: Commit all changes or rollback on error

## Error Handling

- **Database Errors**: Automatic rollback with detailed error messages
- **Pinecone Errors**: Continues with database cleanup even if vector deletion fails
- **Permission Errors**: Returns 403 for non-admin users
- **Validation Errors**: Returns 400 for invalid parameters

## Safety Considerations

⚠️ **WARNING**: This operation is irreversible. Always backup your data before running cleanup operations.

- Use `product_id` or `program_id` filters for targeted cleanup
- Test with specific programs first before clearing all data
- Monitor the response statistics to verify expected deletion counts
- Consider the impact on running segmentation jobs

## Related Endpoints

- `GET /api2/segments/stats` - Get current segment statistics before cleanup
- `GET /api2/programs/{program_id}/segments` - View segments for a specific program
- `POST /api2/programs/{program_id}/segmentation/start` - Start new segmentation after cleanup

## Implementation Notes

This API follows the same pattern as `clear_programs_and_subroutines()` in the program cleanup:

- Uses bulk delete operations for performance
- Handles foreign key relationships properly
- Provides comprehensive logging
- Returns structured response with statistics
- Supports both filtered and complete cleanup operations
