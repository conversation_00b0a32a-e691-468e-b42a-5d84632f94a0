## 1. Introduction & Motivation

The current system for recommending programs relies on keyword matching (`ILIKE`) within specific fields (`program_title`, `logic_technique`, `program_description` JSON elements) using a complex SQL query (`_query_programs` in `api/utils/program_helpers.py`). While this provides some relevance based on explicit term presence and weighting, it has limitations:

- **Semantic Gaps:** It struggles with synonyms, related concepts, and understanding the user's underlying _intent_ if they don't use the exact keywords present in the program data.
- **Nuance:** It cannot easily match based on the overall _style_ or _sequence_ of a program, only on specific keywords found within its description fields.
- **Maintainability:** The complex SQL query with manual weighting can become difficult to maintain and tune as program data evolves.

This plan outlines the implementation of a new system leveraging semantic search via vector embeddings to address these limitations and provide more relevant, nuanced program recommendations based on user concerns.

## 2. Current System Overview

- **Input:** User message -> `massage_agent` -> Structured `UserMassageRequest` (containing `concerns_list`).
- **Process:** `_query_programs` function constructs a raw SQL query.
- **Search:** Uses PostgreSQL `ILIKE ANY` against predefined text and JSON fields in the `programs` table.
- **Scoring:** Calculates a `relevance_score` based on weighted matches across different fields.
- **Output:** Top 9 programs sorted by relevance score.

## 3. Proposed System Overview

The new system will replace the direct SQL keyword search with a multi-step process involving AI generation and vector similarity search:

1.  **Concern Input:** User's message is processed (potentially still by `massage_agent` or a refined version) to extract key concerns.
2.  **Target Sequence Generation:** A new dedicated AI Agent ("Concern-to-Sequence Agent") takes the user's concerns and a few relevant program examples (few-shot learning) to generate a textual representation of an _ideal_ program sequence tailored to the user's request. This sequence will mimic the format used for embedding existing programs.
3.  **Query Embedding:** The generated target sequence is embedded using an OpenAI embedding model.
4.  **Vector Search:** The resulting query vector is used to search against a pre-computed **Pinecone index** containing vector embeddings for all existing programs. The search identifies programs with the most semantically similar sequences based on cosine similarity (or another chosen metric).
5.  **Result Retrieval:** The IDs of the top N most similar programs are retrieved from the Pinecone search results.
6.  **Output:** Details of the matched programs are fetched from the **PostgreSQL database** using the retrieved IDs and presented to the user.

## 4. Detailed Design & Implementation

### 4.1. Data Preparation & Representation (for Embedding Existing Programs)

- **Source Fields for Text:** Extract relevant text from the `programs` table for each program to form the document to be embedded:
  - `name` (String) - The program's primary name.
  - `program_description` -> `'target_group_objective'` (Text from JSON)
  - `program_description` -> `'signature_moves'` (List of strings from JSON)
- **Source Fields for Metadata:** Identify fields to store as metadata alongside the vector in Pinecone (useful for filtering or retrieving context without hitting the DB):
  - `category_id` (Integer) - The program's category.
  - `product_id` (Integer) - The associated product.
  - Potentially others like `program_title` if needed for display later.
- **Concatenation Format (for Text Embedding):** Define a consistent text format to combine the _text_ source fields into a single string for embedding. Example:
  ```
  Name: [name]
  Objective: [target_group_objective text]
  Signature Moves: [move1, move2, ...]
  ```
  **\*Note:** Ensure this format aligns with the expected output of the "Concern-to-Sequence Agent" (Section 4.4) and the structure of the few-shot examples (`api/data/example_sequences/example_program_sequence.md`). If the agent generates more detailed sequences based on the current examples, this simpler format might lead to suboptimal semantic matching. Consider revising the examples or the agent's prompt if necessary.\*
- **Implementation:** A Python function will be created to fetch a program's data, generate this text representation, and prepare the metadata dictionary.

### 4.2. Embedding Generation & Upserting to Pinecone

- **Model:** Use OpenAI's `text-embedding-3-small` model (1536 dimensions) as a starting point due to its balance of performance and cost.
- **Initial Population Strategy:**
  - **Bulk Upsert:** A dedicated bulk process, triggered by an admin endpoint, is required for the initial population and for refreshing all embeddings efficiently.
- **Ongoing Updates (Asynchronous):**
  - **Trigger:** The `PUT /api2/programs/{program_id}` API endpoint, specifically when the `program_description` field is modified. The endpoint checks for this change and schedules the background task.
  - **Mechanism:** Schedules the `upsert_program_embedding` background task (see Section 4.6).
  - **Background Task Action (Single Program):**
    1.  Receives the `program_id`.
    2.  Fetches the program's latest data from PostgreSQL.
    3.  Generates the text representation (using `format_program_for_embedding`).
    4.  Calls the OpenAI API to get the embedding vector.
    5.  Prepares the vector data for Pinecone: `id` (string `program.id`), `values`, `metadata`.
    6.  Uses the Pinecone client to `upsert` the single vector into the `programs` namespace.
- **Bulk Upsert Process:**
  - **Initiation:** Triggered via the dedicated admin API endpoint `POST /api2/programs/bulk-embedding`.
  - **Execution:** The endpoint schedules the `batch_upsert_program_embeddings` background task.
  - **Steps (in Background Task):**
    1. Receives a list of `program_ids` (potentially filtered by product/category/limit by the API endpoint based on request parameters).
    2. Processes these IDs in batches (e.g., default size 10, configurable via API request).
    3. For each batch:
       a. Fetches the corresponding `Program` objects from PostgreSQL.
       b. Generates a list of text representations (`format_program_for_embedding`).
       c. Calls OpenAI `embeddings.create` _once_ with the list of texts for the batch.
       d. Prepares a list of Pinecone vector data (`id`, `values`, `metadata`) for the batch.
       e. Calls Pinecone `index.upsert` _once_ with the list of vectors and the `programs` namespace.
    4. Logs progress and handles errors robustly.
    5. **Filtering Note:** The query retrieving `program_ids` for the bulk task (in `get_programs_for_bulk_embedding`) specifically filters to include only programs where `program_description` contains non-empty `programme_sequence` and `signature_moves` fields.

### 4.3. Pinecone Index Usage & Namespacing

- **Purpose:** To store and efficiently search program embeddings, keeping them separate from file embeddings.
- **Environment Setup:** Pinecone API key and environment name are configured in application settings (`settings.PINECONE_API_KEY`, `settings.PINECONE_INDEX_NAME`).
- **Index Usage:** The **existing Pinecone index**, specified by `settings.PINECONE_INDEX_NAME`, will be utilized. No new index creation is required for this feature.
- **Namespacing:** Program embeddings will be stored within a dedicated **namespace** (e.g., `"programs"`) inside the existing index. This isolates them from file embeddings, which use `source_id` as their namespace (as seen in `api/utils/pinecone_ops.py`).
- **Implementation:** The application will use the Pinecone client to interact with the specified index and namespace.

  ```python
  # Example using pinecone-client v3+
  from pinecone import Pinecone
  from config import settings # Assuming settings holds config

  # Initialize client (ensure API key/env are set via env vars or settings)
  pc = Pinecone(api_key=settings.PINECONE_API_KEY)
  index_name = settings.PINECONE_INDEX_NAME
  program_namespace = "programs" # Define the dedicated namespace

  # Check if the index exists (optional but good practice)
  if index_name not in pc.list_indexes().names:
    # Log an error or raise an exception - the index should already exist
    # as it's used by the file RAG system.
    raise EnvironmentError(f"Pinecone index '{index_name}' not found. Please ensure it exists and is configured in settings.")

  # Get the index object for later use
  pinecone_index = pc.Index(index_name)

  print(f"Using existing Pinecone index '{index_name}' for program embeddings in namespace '{program_namespace}'.")
  ```

### 4.4. Concern-to-Sequence Agent

- **Purpose:** To translate user concerns into a target program sequence representation suitable for embedding and searching.
- **Input:**
  - User's core concern(s) (e.g., extracted by `massage_agent`).
  - Few-shot examples: Text representations (using format from 4.1) of 2-3 existing programs that are generally relevant to the _type_ of concern (strategy for selecting these examples needs definition - static or dynamic?).
- **Output:** A single text string mimicking the program representation format (from 4.1), tailored to the user's specific concern.
- **Implementation:**
  - Define a prompt for an LLM (e.g., using LangChain or directly interacting with OpenAI API).
  - The prompt will instruct the model to generate a sequence based on the concern, using the provided examples as a structural/stylistic guide.
  - This agent needs careful prompt engineering and testing.

### 4.5. API Endpoint Modifications (`chat_handler` in `chat_program_routes.py` and `_query_programs_embedding` in `api/utils/program_helpers.py`)

1.  **Input Processing:** Retain or adapt the initial user message processing and history loading.
2.  **Agent Orchestration:**
    - Call the `massage_agent` (or equivalent) to extract concerns.
    - _New Step:_ Call the "Concern-to-Sequence Agent" with the concerns and selected few-shot examples.
    - _New Step:_ Take the generated sequence text from the agent.
3.  **Query Embedding:** Call the OpenAI Embedding API with the generated sequence text to get the `query_embedding`.
4.  **Vector Search Query (Pinecone):**

    - Replace the PostgreSQL vector query logic.
    - Initialize the Pinecone client and get a reference to the `program-embeddings` index.
    - Use the `pinecone_index.query()` method:

      ```python
      # ... inside chat_handler async context ...

      # Ensure pinecone_index is initialized (see 4.4 example)
      # Assume pinecone_index = pc.Index("program-embeddings")

      # Convert numpy array/list to list if necessary
      query_vector = query_embedding.tolist() if hasattr(query_embedding, 'tolist') else list(query_embedding)
      top_k = 9 # Number of results to retrieve
      program_namespace = "programs" # Define the namespace for program embeddings

      try:
        query_response = pinecone_index.query(
          vector=query_vector,
          top_k=top_k,
          namespace=program_namespace, # Specify the namespace
          include_metadata=True # Or False if metadata not needed here
          # Optional: filter={"category_id": "some_category"} # Metadata filtering still possible within namespace
        )
        # query_response contains a list of matches, e.g.:
        # query_response.matches -> [{'id': 'program_id_1', 'score': 0.95, 'metadata': {...}}, ...]

        matched_ids = [match.id for match in query_response.matches]

        # Now fetch full program details from PostgreSQL using matched_ids
        if matched_ids:
          # Use your existing DB session (db)
          # Need to adjust for async if using async session
          matched_programs_query = db.query(Program).filter(Program.id.in_([int(id) for id in matched_ids]))
          # Ensure order matches Pinecone scores if desired
          matched_programs_list = matched_programs_query.all()
          # Create a mapping or re-sort based on Pinecone results
          programs_dict = {str(p.id): p for p in matched_programs_list}
          # Order results based on Pinecone score
          ordered_programs = [programs_dict[match.id] for match in query_response.matches if match.id in programs_dict]
          # Format 'ordered_programs' for the API response
          matched_programs_data = [format_program_for_response(p) for p in ordered_programs] # Define formatting function
        else:
          matched_programs_data = []

      except Exception as e:
        # Handle Pinecone query errors (log appropriately)
        print(f"Pinecone query failed: {e}")
        matched_programs_data = []

      # ... rest of the response formatting ...
      ```

5.  **Response Formatting:** Adapt the streaming response to yield the results based on the Pinecone search followed by PostgreSQL lookup (e.g., `type: 'programs'`, content being the list of matched program details). Ensure the structure stored in the assistant message (`assistant_message.content`) reflects the new retrieval method.

### 4.6. Background Task Implementation

- **Framework:** FastAPI BackgroundTasks.
- **Task Definitions:**
  - `upsert_program_embedding(program_id: int)`: Handles single program upsert triggered by the `PUT` endpoint.
  - `batch_upsert_program_embeddings(program_ids: List[int], batch_size: int)`: Handles bulk upserts triggered by the `POST` endpoint.
- **Logic:** Implement the steps outlined in 4.2 for both single and bulk tasks. Include error handling and logging.
- **Integration:**
  - Single task (`upsert_program_embedding`): Triggered by the `update_program_endpoint` in `program_routes.py` after a successful update involving `program_description`.
  - Bulk task (`batch_upsert_program_embeddings`): Triggered by the `schedule_bulk_embedding` endpoint in `program_routes.py`.

## 5. Infrastructure Requirements

- PostgreSQL Database (for storing program metadata, chat history, etc.).
- **Pinecone Account:** API Key and Environment details.
- **Pinecone Index:** Access to the existing, configured index specified in `settings.PINECONE_INDEX_NAME`. Program embeddings will utilize a dedicated namespace (e.g., `"programs"`) within this index.
- Background Task Queue Infrastructure (e.g., Redis/RabbitMQ broker for Celery/RQ, or FastAPI BackgroundTasks).
- Worker processes for the background task queue (if using Celery/RQ).
- OpenAI API Key and necessary budget/quota.

## 6. Open Questions / Future Considerations

- **Few-Shot Example Selection:** How are the examples for the Concern-to-Sequence Agent chosen? Yes the examples are static, now located in `api/data/example_sequences/example_program_sequence.md`
