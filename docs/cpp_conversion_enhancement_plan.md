# Plan for Enhancing `ui/lib/convertToCpp.ts`

This plan outlines the steps to modify `ui/lib/convertToCpp.ts` to improve its C++ code generation, aiming to align its output more closely with detailed, human-converted C++ massage programs. The primary reference for C++ structure and parameters is the OSIM document: `api/data/Developing massage program Rev1_08April2020.docx`.

The strategy is to infer C++ parameters by enhancing the parsing logic for existing JSON fields within the `ProgramStep` interface (e.g., `roller_action_description`, `notes`, `light`, `seat_program`).

## I. Update Data Structures (Interfaces, Constants, and Mappings)

1.  **C++ Command Constants:**
    *   Define TypeScript constants in `ui/lib/convertToCpp.ts` for all "Action commands" listed in `Table 1` of the OSIM document. Examples: `CPP_CMD_LED_COLOR`, `CPP_CMD_POS_UP`, `CPP_CMD_THREED_CTRL_DISABLE`, `CPP_CMD_SEATM_START`.
2.  **Operation Mode Map (`OPERATION_MAP`):**
    *   Expand or replace the existing `OPERATION_MAP` with entries from `Table 5 (byte 8 define)` in the OSIM document. This will include all movement types like `M_KNEAD_TAP_ROLPART`, `M_OSI`, `M_ROLPART`, etc.
3.  **Position Map (`POSITION_MAP`):**
    *   Expand or replace the existing `POSITION_MAP` with entries from `Table 2 (Roller position)` in the OSIM document (e.g., `POS_NECK_UPPER`, `POS_SHDBLADE_LOWER`).
4.  **New Mappings to Create in `ui/lib/convertToCpp.ts`:**
    *   `THREE_D_POSITION_MAP`: Based on `Table 3 (3D position)` (e.g., `FIT_POS0`-`FIT_POS30`, `FIT_POS_SHD`).
    *   `ROLLER_WIDTH_DIRECTION_MAP`: To handle Byte 5 logic (page 12 of OSIM doc), combining kneading direction (Up/`REW` vs. Down) and width (`PN`, `PM`, `PW`).
    *   `AIR_SCENT_CONFIG_MAP`: For Byte 6 & 7 logic (page 13 of OSIM doc), mapping keywords (shoulder, arm, scent, left, right, nohold) to their respective bitmask values.
    *   `FOOT_PROGRAM_MAP`: Based on `Table 6 (Foot massage program)`.
    *   `LED_COLOR_MAP`: Based on `Table 7 (LED colour)`.
    *   `SEAT_PROGRAM_MAP`: Based on `Table 8 (Seat Massage program)`.

## II. Enhance Parsing and Logic Functions in `ui/lib/convertToCpp.ts`

1.  **`detectCommandType(description: string | null, step: ProgramStep): string`:**
    *   Significantly refactor this function.
    *   It must identify the correct Byte 1 C++ command (from `Table 1` of OSIM doc) by parsing `step.roller_action_description` and potentially other `ProgramStep` fields (e.g., `step.light` for `LED_COLOR`, `step.seat_program` for `SEATM_START`, `step.notes`).
    *   Example inferences:
        *   "roller moves up by N turns" -> `POS_UP`
        *   "3D = FIT_POS5" -> `POS_3D`
        *   Description containing "LED" or `step.light` having a value -> `LED_COLOR`
2.  **`detectOperationType(description: string | null): string`:**
    *   Update to use the new, comprehensive `OPERATION_MAP` (derived from `Table 5` of OSIM doc) to determine the correct Byte 8 value for roller actions based on `step.roller_action_description`.
3.  **Parameter Parsing Functions (New and Modified):**
    *   Develop or modify functions to parse specific values for each of the 8 bytes of the `T_OPERATION` C++ struct. These functions will take the detected command and the `ProgramStep` object as input and infer values from `ProgramStep` fields based on the OSIM document's rules.
    *   **`parseByte2Value(command: string, step: ProgramStep): string`**:
        *   If `command` is `POS`, use `POSITION_MAP`.
        *   If `POS_3D`, use `THREE_D_POSITION_MAP`.
        *   If `TIMER`, `POS_UP`, `POS_DOWN`, `ROTATION`, etc., extract numeric value from `step.roller_action_description` or other relevant fields (e.g., `step.starting_timing` if applicable for timer, or parsing numbers from description).
    *   **`parseByte3Value(command: string, step: ProgramStep): string` (Kneading Speed / 3D Adjust):**
        *   Primarily use `step.kneading_speed`.
        *   If `command` is `POS_3D`, handle potential 3D adjustment values (e.g., "neutral position-2") if specified in `step.roller_action_description` or `step.position_3d`.
    *   **`parseByte4Value(command: string, step: ProgramStep): string` (Tapping Speed / OSI Speed / Vib Strength):**
        *   Primarily use `step.tapping_speed`.
        *   Handle vibration strength if `command` is `VIB_SET` or `VIB_TIME`, inferring from `step.roller_action_description`.
    *   **`parseByte5Value(step: ProgramStep): string` (Width + Direction):**
        *   Combine kneading direction (inferred from keywords like "up"/"down", "もみ上げ"/"もみ下げ" in `step.roller_action_description`) and `step.width` using the `ROLLER_WIDTH_DIRECTION_MAP` to produce the correct byte value (e.g., `REW+PN`).
    *   **`parseByte6And7Value(step: ProgramStep): string` (Airbag & Scent):**
        *   Use `AIR_SCENT_CONFIG_MAP`. Parse `step.air_action_description` for airbag locations (shoulder, arm, left, right) and `step.scent` for scent activation. Implement `NOHOLD` logic based on keywords in descriptions or a default behavior.
    *   **`parseByte8Value(command: string, step: ProgramStep): string` (Movement/Program/Color):**
        *   If it's a roller action command (like `POS`, `TIMER`), use `detectOperationType` based on `step.roller_action_description`.
        *   If `command` is `ASI_START`, use `FOOT_PROGRAM_MAP` (likely with `step.seat_program` or by parsing ID from description).
        *   If `command` is `LED_COLOR`, use `LED_COLOR_MAP` (likely with `step.light` or by parsing color name/ID from description).
        *   If `command` is `SEATM_START`, use `SEAT_PROGRAM_MAP` (likely with `step.seat_program` or by parsing ID from description).
4.  **`generateOperationObjectsFromSteps(...)`:**
    *   This core function will orchestrate the process:
        1.  Call `detectCommandType` for the current `ProgramStep`.
        2.  Based on the command, call the respective byte-parsing functions (`parseByte2Value`, `parseByte3Value`, etc.).
        3.  Assemble the results into an `OperationObject` that mirrors the 8-byte `T_OPERATION` C++ structure.
    *   The `OperationObject` interface might need minor adjustments to clearly map its fields to Byte1 through Byte8.

## III. Main Conversion Function Adjustments in `ui/lib/convertToCpp.ts`

1.  **`convertStepsToCpp(...)`:**
    *   **Program End Marker**: Modify the logic that adds `PG_END`. The final line of the `osim_pgX_course_tbl` array must be `{0xff,0xff,0xff,0xff,0xff,0xffff,0xff}` as specified on page 3, line 109 of the OSIM document.
    *   **`TRACK_SELECT` Parameter**: The human-converted code uses `STRONG` as a parameter for `TRACK_SELECT`. The current script maps `overallSettings.strength` "STRONG" to "HEAVY". This should be changed to output `STRONG` (assuming `STRONG` is a valid C++ constant defined elsewhere in the C++ project). If not, the correct C++ constant for "strong" intensity needs to be identified and used.

## IV. High-Level Processing Flow (Mermaid Diagram)

```mermaid
graph TD
    A[Start: For each ProgramStep in JSON] --> K{Is Step a Comment/Subroutine Header/Footer?};
    K -- Yes --> L[Add as C++ Comment to output list];
    K -- No --> B{Determine Byte 1 Command (Action Command) using `detectCommandType` by parsing `step.roller_action_description`, `step.light`, `step.seat_program`, etc.};
    B --> C[Parse Byte 2 (Roller/3D Pos, Time, Cycles) based on Command & `step` fields];
    B --> D[Parse Byte 3 (Knead Speed / 3D Adjust) based on Command & `step` fields];
    B --> E[Parse Byte 4 (Tap/OSI Speed, Vib Strength) based on Command & `step` fields];
    B --> F[Parse Byte 5 (Width + Direction) based on `step.roller_action_description` & `step.width`];
    B --> G[Parse Byte 6 & 7 (Airbag & Scent) based on `step.air_action_description` & `step.scent`];
    B --> H[Parse Byte 8 (Movement Type / Foot Prog / LED Color / Seat Prog) based on Command, `step.roller_action_description`, etc.];
    I[Assemble C++ T_OPERATION struct fields from parsed Bytes 1-8] --> J[Add formatted C++ line to output list];
    C --> I;
    D --> I;
    E --> I;
    F --> I;
    G --> I;
    H --> I;
    L --> M[Next ProgramStep or End of Steps];
    J --> M;
    M -- End of Steps --> N[Add Program End Marker `0xff,...` to output list];
    N --> O[Return complete C++ string];
```

This plan aims for a comprehensive enhancement of the C++ conversion logic. Implementation will require careful parsing of string descriptions and mapping to the detailed byte structure.