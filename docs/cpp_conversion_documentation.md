# Documentation: JSON to C++ Massage Program Conversion

This document details the current implementation of the logic within [`ui/lib/convertToCpp.ts`](ui/lib/convertToCpp.ts:1) for converting JSON-defined massage program steps into a C++ array format suitable for OSIM massage chairs. It also outlines the next major planned enhancement.

**Primary Reference Document:** OSIM `Developing massage program Rev1_08April2020.docx` (located at [`api/data/Developing massage program Rev1_08April2020.docx`](api/data/Developing%20massage%20program%20Rev1_08April2020.docx)).

## 1. Overall Goal

The primary goal of the [`convertToCpp.ts`](ui/lib/convertToCpp.ts:1) script is to transform a massage program defined in a structured JSON format (specifically, an array of `ProgramStep` objects and associated settings) into a C-style array of `T_OPERATION` structs. This C++ array is then used by the massage chair's firmware.

## 2. Key Input Data Structures (from [`ui/lib/convertToCpp.ts`](ui/lib/convertToCpp.ts:1))

The conversion process primarily uses the following TypeScript interfaces:

*   **`ProgramStep`**: Represents a single step or event in the massage program. Key fields include:
    *   `step_number: string | null`
    *   `roller_action_description: string | null`: Textual description of the roller action. Crucial for inferring commands and parameters.
    *   `air_action_description: string | null`: Description of airbag operations.
    *   `kneading_speed: number | string | null`
    *   `tapping_speed: number | string | null`
    *   `position_3d: (string | number | null)[] | string | null`: 3D roller settings.
    *   `width: string | null`: Roller width (N, M, W).
    *   `seat_program: string | null`: Identifier for a seat massage sub-program.
    *   `notes: string | null`
    *   `scent: string | null`
    *   `light: string | null`: LED light setting.
    *   `type: 'action' | 'subroutine' | 'comment' | 'program_note'`: Type of the step.
    *   `subroutine_id?: string`: Identifier if the step is a call to a subroutine.
*   **`Subroutine`**: Defines a reusable sequence of `ProgramStep`s.
    *   `name: string`
    *   `subroutineIdJson: string`
    *   `steps: ProgramStep[]`
*   **`OverallSettings`**: Contains global settings for the program.
    *   `strength: string` (e.g., "STRONG", "MEDIUM")
    *   `leg_program: string` (e.g., "MODE_AUTO2")
*   **`DetailedProgramData`**: The top-level interface for the input JSON, containing metadata, `OverallSettings`, an array of `ProgramStep`s, and an optional array of `Subroutine`s.

## 3. Core C++ Structure (`T_OPERATION`)

As per the OSIM document (page 3), each line in the generated C++ array corresponds to a `T_OPERATION` struct, which consists of 8 bytes:

```c
typedef struct {
    UCHAR command;        // Byte 1: Action command
    UCHAR pos_time;       // Byte 2: Roller position / 3D position / Timer seconds / No. of knead cycles / VibTime
    int8  knead_speed;    // Byte 3: Kneading speed / 3D position adjustment
    UCHAR tap_speed;      // Byte 4: Tapping speed / OSI speed / Vibration strength
    UCHAR width_flag;     // Byte 5: Kneading direction + roller width
    uint16 air_bag;       // Byte 6 & 7: 16-bit Airbag & Scent on/off
    UCHAR movement;       // Byte 8: Kneading/tapping/OSI/rolling type / Foot program / LED colour / Seat program
} T_OPERATION;
```

The script generates each field of this struct (represented as `param1` through `param6Mode` in the `OperationObject` interface, where `param1` maps to `pos_time`, `param2` to `knead_speed`, etc., and `command` maps to `command`).

## 4. Conversion Process Overview

The conversion is orchestrated by the `convertJsonToCpp` function:

1.  Parses the input JSON string into a `DetailedProgramData` object.
2.  Extracts program metadata, steps, subroutines, and overall settings.
3.  Calls `convertStepsToCpp` to generate the main C++ array string.

The `convertStepsToCpp` function:
1.  Generates the C++ file header and array declaration (`T_OPERATION osim_pgX_course_tbl[] = { ... }`).
2.  Adds initial setup lines based on `OverallSettings`:
    *   `TRACK_SELECT`: Sets program strength (e.g., `STRONG`).
    *   `ASI_START`: Initializes a leg/foot program (e.g., `MODE_AUTO2`).
3.  Calls `generateOperationObjectsFromSteps` to process the main program steps and any nested subroutines.
4.  Appends a program end marker: `{0xff,0xff,0xff,0xff,0xff,0xffff,0xff}`.
5.  Formats each `OperationObject` into a C++ array line.

The `generateOperationObjectsFromSteps` function is the core logic:
1.  Iterates through each `ProgramStep`.
2.  Handles comments and subroutine calls (recursively calling itself for subroutine steps).
3.  For action steps, it determines the C++ command (Byte 1) and then parses values for Bytes 2-8.

## 5. Detailed Breakdown of Current Implementation in `ui/lib/convertToCpp.ts`

### 5.1. Constants and Mappings

Numerous constant strings and mapping objects are defined to translate JSON data and textual descriptions into C++ specific codes. These are primarily derived from tables in the OSIM document.

*   **C++ Command Constants (Lines 431-451):** e.g., `CPP_CMD_TIMER`, `CPP_CMD_POS`, `CPP_CMD_LED_COLOR`. Correspond to Byte 1 "Action commands" from OSIM Table 1.
*   **`OPERATION_MAP` (Lines 65-90):** Maps textual descriptions of roller actions (e.g., "もみ上げ", "tap", "knead_rolpart") to C++ movement codes for Byte 8 (OSIM Table 5).
*   **`POSITION_MAP` (Lines 93-126):** Maps textual descriptions of body positions (e.g., "肩", "POS_WAIST_UPPER1") to C++ position constants for Byte 2 (OSIM Table 2).
*   **`THREE_D_POSITION_MAP` (Lines 129-142):** Maps 3D position names (e.g., "FIT_POS5") for Byte 2 (OSIM Table 3).
*   **`ROLLER_WIDTH_DIRECTION_MAP` (Lines 145-153):** Maps width strings ("N", "M", "W") to C++ codes (`PN`, `PM`, `PW`). Direction is handled in `parseByte5Value`. (OSIM Byte 5, page 12).
*   **`AIR_SCENT_CONFIG_MAP` (Lines 156-167):** Maps air/scent keywords to bitmask values for Bytes 6 & 7 (OSIM page 13). Includes specific codes for shoulder, arm (left/right/both), and scent (left/right/both).
*   **`FOOT_PROGRAM_MAP` (Lines 170-180):** Maps foot program identifiers to C++ codes for Byte 8 (OSIM Table 6).
*   **`LED_COLOR_MAP` (Lines 183-193):** Maps color names/codes to C++ constants for Byte 8 (OSIM Table 7).
*   **`SEAT_PROGRAM_MAP` (Lines 196-207):** Maps seat program identifiers to C++ codes for Byte 8 (OSIM Table 8).

### 5.2. Command Detection (Byte 1)

*   **`detectCommandType(description: string | null, step: ProgramStep): string` (Lines 280-377):**
    *   This crucial function determines the primary C++ "Action Command" (Byte 1) for a given `ProgramStep`.
    *   It analyzes `step.roller_action_description`, `step.light`, and `step.seat_program`.
    *   It uses a series of checks (regex, string inclusion) in a specific order of priority to identify commands like `LED_COLOR`, `SEATM_START`, `POS_3D_UP`, `TIMER`, `ROTATION`, `POS`, etc.
    *   If no specific command is matched, it defaults to `CPP_CMD_TIMER`.
    *   It can also identify steps that should be treated purely as C++ comments (`CPP_CMD_AS_COMMENT`).

### 5.3. Parameter Parsing (Bytes 2-8)

Once the Byte 1 command is determined, the following functions parse the respective parameters:

*   **Byte 2: `pos_time` - Parsed by `parseByte2Value(command: string, step: ProgramStep): string` (Lines 474-514)**
    *   If `command` is `POS`, `POS_UP`, `POS_DOWN`: Uses `parsePositionFromDescription` (based on `POSITION_MAP`) to get roller position.
    *   If `command` is `POS_3D`, `POS_3D_UP`, `POS_3D_DOWN`: Extracts 3D position (e.g., `FIT_POSX`) or numeric pulse/depth value from `step.roller_action_description`.
    *   If `command` is `TIMER` or `ROTATION`: Extracts numeric duration (seconds) or cycle count from `step.roller_action_description`.
    *   Defaults to `CPP_DEFAULT_PARAM` ('0').

*   **Byte 3: `knead_speed` - Parsed by `parseByte3Value(command: string, step: ProgramStep): string` (Lines 517-534)**
    *   Primarily uses `step.kneading_speed`.
    *   If `command` involves 3D, it can also parse 3D adjustment values from `step.roller_action_description`.
    *   Defaults to `CPP_DEFAULT_PARAM` ('0').

*   **Byte 4: `tap_speed` - Parsed by `parseByte4Value(command: string, step: ProgramStep): string` (Lines 537-554)**
    *   Primarily uses `step.tapping_speed`.
    *   If `command` is `VIB_SET` or `VIB_TIME`, it parses vibration strength from `step.roller_action_description`.
    *   Defaults to `CPP_DEFAULT_PARAM` ('0').

*   **Byte 5: `width_flag` - Parsed by `parseByte5Value(command: string, step: ProgramStep): string` (Lines 557-584)**
    *   Determines roller width (`PN`, `PM`, `PW`) from `step.width` via `ROLLER_WIDTH_DIRECTION_MAP`.
    *   Determines kneading direction prefix (`REW+` for up/rewind) from keywords in `step.roller_action_description` (e.g., "up", "もみ上げ").
    *   Combines them: e.g., `REW+PN`.
    *   For `ROTATION` commands, it typically omits the `REW+` prefix, using just the width code, aligning with OSIM examples.
    *   If width is not applicable or specified, defaults to `CPP_DEFAULT_PARAM` ('0').

*   **Bytes 6 & 7: `air_bag` - Parsed by `parseByte6And7Value(step: ProgramStep): string` (Lines 587-589), which calls `parseAirAction(airActionDesc: string | null, scentDesc: string | null = null): string` (Lines 391-428)**
    *   `parseAirAction` uses `AIR_SCENT_CONFIG_MAP` to build a 16-bit hex code.
    *   It interprets keywords for shoulder, arm, and scent (with left/right/both variations) from `step.air_action_description` and `step.scent`.
    *   Appends `+NOHOLD` if any air/scent action is active, unless "hold" is explicitly mentioned in the descriptions.
    *   Defaults to `CPP_DEFAULT_HEX_PARAM` ('0X0000').

*   **Byte 8: `movement` - Parsed by `parseByte8Value(command: string, step: ProgramStep, overallSettings?: OverallSettings): string` (Lines 592-639)**
    *   If `command` is `LED_COLOR`: Uses `LED_COLOR_MAP` based on `step.light` or parses `COLOURXXX` from description.
    *   If `command` is `ASI_START`: Uses `FOOT_PROGRAM_MAP` based on `overallSettings.leg_program` or description, defaulting to `MODE_AUTO1` (though `MODE_AUTO2` is used in the initial setup block if no `leg_program` is specified).
    *   If `command` is `SEATM_START`: Uses `SEAT_PROGRAM_MAP` based on `step.seat_program` or description, defaulting to `SM_AUTO23`.
    *   For roller action commands (`POS`, `TIMER`, `ROTATION`, etc.): Calls `detectOperationType(step.roller_action_description): string` (Lines 250-277), which uses `OPERATION_MAP` to find the C++ movement code (e.g., `M_TAP`, `M_KNEAD_ROLPART`).
    *   Defaults to `CPP_DEFAULT_PARAM` ('0').

### 5.4. Main Orchestration and Output Formatting

*   **`generateOperationObjectsFromSteps(...)` (Lines 641-744):**
    *   Iterates `ProgramStep` objects.
    *   Handles `type: 'comment'`, `type: 'program_note'`, and `type: 'subroutine'` (with recursion for subroutine steps).
    *   For `type: 'action'`, it calls `detectCommandType` and then all the `parseByteXValue` functions to populate an `OperationObject`.
    *   A workaround passes `overallSettings` via the `subRoutinesMap` for use in `parseByte8Value` for `ASI_START` commands in main steps.

*   **`convertStepsToCpp(...)` (Lines 747-799):**
    *   Constructs the C++ file header.
    *   Adds initial `TRACK_SELECT` (strength from `overallSettings.strength`, defaults to `LIGHT`, uses `STRONG` if specified) and `ASI_START` (mode from `overallSettings.leg_program` via `FOOT_PROGRAM_MAP`, defaults to `MODE_AUTO2`).
    *   Calls `generateOperationObjectsFromSteps` for the main program body.
    *   Appends the `0xff,...` end-of-array marker.
    *   Formats each `OperationObject` into a tab-delimited C++ line with padding for alignment and includes the step description as a comment.

*   **`convertJsonToCpp(...)` (Lines 802-838):**
    *   The main entry point that parses the JSON string, prepares the `subRoutinesMap`, and invokes `convertStepsToCpp`.

## 6. Next Major Step: Structural Alignment (Multi-Line Generation from Single `ProgramStep`)

### 6.1. The Challenge

The current implementation primarily maps one `ProgramStep` from the JSON input to a single `T_OPERATION` line in the C++ output (unless it's a comment or subroutine invocation). However, human-written C++ programs often use multiple `T_OPERATION` lines to represent a single conceptual step or a combination of simultaneous/sequential actions described in one original program instruction.

**Example:** A single instruction "Activate seat program SM_AUTO23 and perform a 3-second shoulder tap" would ideally translate to:
```cpp
SEATM_START,    0,                      0,      0,      0,      0X0000,             SM_AUTO23,          // Comment for seat program
TIMER,          3,                      0,      120,    0,      0X00C0+NOHOLD,      M_TAP,              // Comment for shoulder tap
```
The current script would likely generate only one of these lines, based on which command `detectCommandType` prioritizes from the combined description.

### 6.2. Proposed Strategy

To address this, the `generateOperationObjectsFromSteps` function needs to be enhanced to potentially produce multiple `OperationObject` instances from a single `ProgramStep`.

1.  **Identify Multi-Action Patterns:**
    *   **Explicit Fields:** If a `ProgramStep` has `step.seat_program` filled AND a non-trivial `step.roller_action_description`, it implies two C++ lines. Similarly for `step.light` + roller action.
    *   **Complex Descriptions:** The `step.roller_action_description` itself might describe sequential actions (e.g., "3D up 3 pulses, then rotate 2 cycles"). This requires more sophisticated parsing of the description.

2.  **Modification of `generateOperationObjectsFromSteps`:**
    *   For each `ProgramStep`, instead of directly creating one `OperationObject`, the function could first analyze the step for multiple distinct actions.
    *   **Approach A (Iterative Pushing):**
        *   Check for a seat program: If `step.seat_program` exists, create and add a `SEATM_START` `OperationObject` to a temporary list for the current step.
        *   Check for LED color: If `step.light` exists, create and add an `LED_COLOR` `OperationObject`.
        *   Process `step.roller_action_description`: Determine the primary command and parameters for the roller/timer/3D action. Create and add this `OperationObject`. This part might need to ensure it doesn't re-process keywords already handled (e.g., if "seat program" was in the description and also in `step.seat_program`).
        *   Push all generated objects from the temporary list to the main `operationObjects` array.
    *   **Approach B (Command List from `detectCommandType`):**
        *   Refactor `detectCommandType` to return an array of `DetectedCommandInfo` objects, each specifying a command and the relevant part of the description or `ProgramStep` field that triggered it.
        *   `generateOperationObjectsFromSteps` would then iterate over this array, creating an `OperationObject` for each detected command. This is cleaner but requires more significant changes to `detectCommandType`.

3.  **Refining `roller_action_description` Parsing:**
    *   If a description like "3D up 3, then rotate 2 cycles" is encountered, the system needs to parse it into two distinct conceptual actions. This might involve splitting the string by keywords like "then", "and then", or specific punctuation, and processing each part.

This enhancement will allow the generated C++ code to more closely mirror the structure and granularity of human-programmed massage sequences, leading to more complex and nuanced program translations.