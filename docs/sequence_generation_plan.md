# Massage Sequence Generation Refactoring Plan

Based on the analysis of the existing code (`api/views/chat_views.py`), user requirements, example sequences, subroutines (`ui/src/features/sequence-editor/mockdata.ts`), focus areas (`api/schemas/sequence_schemas.py`), and action types (`api/models/sequence_models.py`), this plan outlines the steps to refactor the massage sequence generation logic.

**1. Define Necessary Mappings:**

- **Focus Area to Initial Roll Action:** Create a hardcoded Python dictionary mapping `FocusAreas` enum values to specific `ActionType` enum values representing the initial rolling target.
  - _Example:_ `{ FocusAreas.SHOULDER: ActionType.ROLL_SHOULDER_UPPER, FocusAreas.WAIST: ActionType.ROLL_WAIST_UPPER, ... }`
- **Body Part (ActionType) to Subroutines:** Create a hardcoded Python dictionary mapping specific `ActionType` values (the ones representing body part locations like `ROLL_SHOULDER_LOWER_1`, `ROLL_WAIST`, etc.) to a list of suitable subroutine IDs (e.g., `'SUB_01'`, `'SUB_06'`). This mapping will likely be based on analyzing the subroutine names and intended targets.
  - _Example:_ `{ ActionType.ROLL_SHOULDER_LOWER_1: ['SUB_01', 'SUB_06'], ActionType.ROLL_WAIST: ['SUB_09', 'SUB_11', ...], ... }`
- **Subroutine Definitions:** Ensure the subroutine definitions (currently in `ui/src/features/sequence-editor/mockdata.ts`) are accessible to the backend logic. This might involve moving them to a shared JSON file, a database table, or directly into a Python structure within the API code.

**2. Refactor Sequence Generation Logic (within `api/views/chat_views.py` or a new dedicated module):**

- **Replace `theoretical_foundation_expert` and `massage_programme_planner`:** The new logic is more direct and doesn't rely on these abstract planning steps.
- **Create a New Main Generation Function (e.g., `generate_massage_sequence_v2`):**
  - **Input:** User requirements (`SequenceParameters`), LLM instance, Subroutine data, Mappings.
  - **Output:** A list of `SequenceRow` objects.
  - **Steps:**
    1.  **Start Segment:**
        - Add `Seat Vibration` action (param 2 or 3, randomly chosen).
        - Add `Start Leg program` action (e.g., "Deep Relief").
    2.  **Middle Segment (Loop):**
        - Initialize `current_time = 0`, `current_body_part = None`.
        - Loop while `current_time < (total_duration - end_segment_duration)` (Estimate `end_segment_duration`, e.g., 2 minutes).
        - **Determine Target:**
          - If `sequence_params.focus_areas` provided: Use the _Focus Area to Initial Roll Action_ map.
          - If no focus area: Randomly select a `Roll to...` `ActionType`.
        - **Roll to Target:** Add the determined `Roll to...` action. Update `current_body_part`.
        - **Action at Target:**
          - **LLM Decision:** Ask LLM: use subroutine _or_ generate basic actions for `current_body_part`? Provide suitable subroutine IDs.
          - **If Subroutine:**
            - Use _Body Part to Subroutines_ map to get suitable IDs.
            - If found, randomly select one ID.
            - Retrieve and append subroutine actions.
          - **If Basic Actions (or no suitable subroutine):**
            - Call LLM to generate a short sequence of basic actions (`Cycle`, `Timer`, etc.).
            - Append generated basic actions.
        - **Update Time:** Increment `current_time` (requires estimating action durations).
    3.  **End Segment:**
        - **Repeat Loop:** Add `Repeat loop starts`. Call LLM to generate basic actions.
        - **Cooldown:** Add `Last min cooldown starts`. Call LLM to generate basic actions.
    4.  **Return:** Return the complete list of `SequenceRow` objects.

**3. Update LLM Interaction:**

- Modify `massage_programme_generator` (or create new) for structured output based on the new logic.
- Design specific prompts for:
  - Deciding between subroutine vs. basic actions.
  - Generating basic action sequences (middle, repeat, cooldown).
- `use_example` flag might still be useful for format guidance.

**4. Integration:**

- Update `sequence_handler` to call `generate_massage_sequence_v2`.
- Ensure `_save_sequence_to_database` correctly processes the output.

**Visual Plan (Mermaid):**

```mermaid
graph TD
    subgraph Input
        direction LR
        In1[User Params: FocusAreas, Duration, etc.]
        In2[Subroutine Definitions]
        In3[Mappings: Focus->Roll, Part->Subs]
    end

    subgraph Generation Process
        A[Start: generate_massage_sequence_v2] --> B(Add Seat Vibration);
        B --> C(Add Leg Program);
        C --> D{Target Body Part?};
        D -- User Focus --> E[Map FocusArea to Roll Action];
        D -- No Focus --> F[Randomly Select Roll Action];
        E --> G[Add Roll Action];
        F --> G;
        G --> H{Loop While Time < End Segment Start};
        H -- Yes --> I{LLM: Subroutine or Basic Actions?};
        I -- Subroutine --> J[Get Suitable Subroutines for Current Part];
        J --> K{Found?};
        K -- Yes --> L[Randomly Choose Subroutine];
        L --> M[Add Subroutine Actions];
        K -- No --> N[LLM: Generate Basic Actions];
        I -- Basic Actions --> N;
        N --> O[Add Basic Actions];
        M --> P[Update Time/State];
        O --> P;
        P --> H;
        H -- No --> Q[Add Repeat Loop Start];
        Q --> R[LLM: Generate Repeat Loop Actions];
        R --> S[Add Repeat Loop Actions];
        S --> T[Add Cooldown Start];
        T --> U[LLM: Generate Cooldown Actions];
        U --> V[Add Cooldown Actions];
    end

    subgraph Output
        direction LR
        Out1[List of SequenceRow Objects]
    end

    Input --> A;
    V --> Out1;

    style I fill:#ccf,stroke:#333,stroke-width:2px
    style N fill:#ccf,stroke:#333,stroke-width:2px
    style R fill:#ccf,stroke:#333,stroke-width:2px
    style U fill:#ccf,stroke:#333,stroke-width:2px
```

**Key Changes from Current Approach:**

- Removes abstract "theoretical foundation" and "sequence plan" steps.
- Directly generates sequence based on defined segments (Start, Middle, End).
- Introduces explicit mappings for focus areas and subroutines.
- Uses LLM for targeted decisions (subroutine vs. basic actions) and generation of specific action blocks.
- Integrates predefined subroutines directly.
