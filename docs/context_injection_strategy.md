# Context Injection Strategy for Enhanced Purpose Tag Generation

## Overview

This document outlines the strategy for effectively injecting rich program context into the AI segmentation agents to generate semantically meaningful purpose tags that reflect the nuanced therapeutic objectives described in OSIM program documentation.

## 1. Available Context Sources

### 1.1. Program Description JSON Structure
```json
{
  "program_name": "Beauty",
  "intensity": 4,
  "recommended_position": "Zero-Gravity",
  "target_group_objective": "For people who desire skin beautification...",
  "programme_sequence": {
    "Front Sequence": "Massage starting Shoulder to Butt area...",
    "Main Sequence": "Main massage area is 1) Shoulder blade area...",
    "Cooling Sequence": "Back massage that combined high to low-intensity..."
  },
  "targeted_acupressure_points": [
    "1) Shoulder Well (肩井) - Local point for occipital headache...",
    "2) Lung Transporter (肺俞) - Stop phlegm, relieve bronchial asthma..."
  ],
  "signature_moves": [
    "1) Slow rolling with mid 3D at shoulder blade area",
    "2) Slow but Deep kneading use lower roller..."
  ],
  "limitations": ""
}
```

### 1.2. Additional Context Fields
- `logic_technique`: Detailed program logic and therapeutic progression
- `source_notes`: Additional contextual information
- `program_title`: Full program title with descriptive elements

## 2. Context Processing Pipeline

### 2.1. Pre-Processing Context Extraction
```python
def extract_program_context(program_data):
    """Extract and structure program context for agent injection."""
    context = {
        'program_name': program_data.get('name', ''),
        'therapeutic_objective': None,
        'target_demographic': None,
        'acupressure_benefits': [],
        'signature_techniques': [],
        'intensity_level': None
    }
    
    # Extract from program_description JSON
    if program_data.get('program_description'):
        desc = program_data['program_description']
        context.update({
            'therapeutic_objective': desc.get('target_group_objective', ''),
            'target_demographic': extract_demographic(desc.get('target_group_objective', '')),
            'acupressure_benefits': parse_acupressure_points(desc.get('targeted_acupressure_points', [])),
            'signature_techniques': desc.get('signature_moves', []),
            'intensity_level': desc.get('intensity', 5)
        })
    
    return context
```

### 2.2. Demographic Extraction Patterns
```python
DEMOGRAPHIC_PATTERNS = {
    'office_workers': ['office worker', 'desk work', 'computer', 'sitting'],
    'executives': ['CEO', 'C-Suite', 'executive', 'management'],
    'athletes': ['sports', 'athletic', 'workout', 'exercise', 'active lifestyle'],
    'mothers': ['mum', 'mother', 'maternal', 'pregnancy', 'postpartum'],
    'seniors': ['elderly', 'senior', 'age-related', 'older adults'],
    'drivers': ['driver', 'driving', 'long distance'],
    'shoppers': ['shopping', 'walking', 'standing', 'retail']
}
```

### 2.3. Acupressure Point Benefit Mapping
```python
ACUPRESSURE_BENEFITS = {
    'Shoulder Well': ['headache_relief', 'neck_stiffness_relief'],
    'Lung Transporter': ['respiratory_enhancement', 'asthma_relief'],
    'Kidney Transporter': ['lumbar_support', 'pain_relief'],
    'Heart Transporter': ['cardiovascular_support', 'heart_wellness'],
    'Wind Pool': ['headache_relief', 'mental_clarity'],
    'Celestial Pillar': ['neck_stiffness_relief', 'stress_relief']
}
```

## 3. Agent-Specific Context Injection

### 3.1. Ontology & Interface Tagging Agent Context
```python
def create_tagging_context_prompt(steps, program_context):
    """Create context-rich prompt for purpose tag generation."""
    
    context_prompt = f"""
**PROGRAM THERAPEUTIC CONTEXT:**
Program Name: {program_context['program_name']}
Therapeutic Objective: {program_context['therapeutic_objective']}
Target Demographic: {program_context['target_demographic']}
Intensity Level: {program_context['intensity_level']}/10

**ACUPRESSURE THERAPEUTIC BENEFITS:**
{format_acupressure_benefits(program_context['acupressure_benefits'])}

**SIGNATURE THERAPEUTIC TECHNIQUES:**
{format_signature_techniques(program_context['signature_techniques'])}

**PURPOSE TAG SELECTION GUIDANCE:**
Based on the program context above, prioritize purpose tags that align with:
1. The specific therapeutic objective described
2. The target demographic's typical needs
3. The acupressure points being targeted
4. The signature techniques being employed

Avoid generic tags like 'deep_therapy' unless specifically indicated by context.
"""
    
    return context_prompt
```

### 3.2. Macro-Phase Agent Context
```python
def create_macro_phase_context_prompt(steps, program_context):
    """Create context for macro-phase boundary detection."""
    
    context_prompt = f"""
**PROGRAM THERAPEUTIC PROGRESSION:**
{program_context.get('programme_sequence', {})}

**EXPECTED PHASE CHARACTERISTICS:**
Based on "{program_context['program_name']}" program for {program_context['target_demographic']}:
- Front Phase: {predict_front_characteristics(program_context)}
- Main Phase: {predict_main_characteristics(program_context)}
- Cooling Phase: {predict_cooling_characteristics(program_context)}
"""
    
    return context_prompt
```

## 4. Context-Driven Purpose Tag Selection

### 4.1. Program Name → Purpose Mapping
```python
PROGRAM_PURPOSE_MAPPING = {
    'Beauty': ['skin_beautification', 'anti_aging_therapy', 'circulation_boost'],
    'Sports': ['athletic_recovery', 'muscle_recovery', 'performance_enhancement'],
    'CEO': ['executive_wellness', 'computer_strain_relief', 'stress_relief'],
    'Healthy Mum': ['maternal_health', 'pelvic_care', 'hormonal_balance'],
    'Shoulder Blade': ['office_worker_relief', 'respiratory_enhancement', 'posture_correction'],
    'Lumbar': ['lumbar_support', 'pain_relief', 'spinal_alignment']
}
```

### 4.2. Demographic → Purpose Mapping
```python
DEMOGRAPHIC_PURPOSE_MAPPING = {
    'office_workers': ['computer_strain_relief', 'posture_correction', 'neck_stiffness_relief'],
    'executives': ['executive_wellness', 'stress_relief', 'mental_clarity'],
    'athletes': ['athletic_recovery', 'muscle_recovery', 'performance_enhancement'],
    'mothers': ['maternal_health', 'pelvic_care', 'stress_relief'],
    'seniors': ['senior_mobility', 'joint_mobility', 'circulation_boost'],
    'drivers': ['driver_fatigue_relief', 'lumbar_support', 'circulation_boost']
}
```

## 5. Implementation Strategy

### 5.1. Enhanced Agent Prompt Integration
1. **Pre-process program context** before calling agents
2. **Inject structured context** into agent prompts
3. **Provide purpose tag guidance** based on context
4. **Validate tag selection** against context relevance

### 5.2. Fallback Strategy
```python
def get_fallback_purposes(program_context):
    """Provide fallback purpose tags when context is limited."""
    
    fallbacks = []
    
    # Program name fallbacks
    if program_context['program_name']:
        fallbacks.extend(PROGRAM_PURPOSE_MAPPING.get(
            extract_program_type(program_context['program_name']), 
            ['relaxation', 'tension_release']
        ))
    
    # Demographic fallbacks
    if program_context['target_demographic']:
        fallbacks.extend(DEMOGRAPHIC_PURPOSE_MAPPING.get(
            program_context['target_demographic'],
            ['stress_relief']
        ))
    
    return fallbacks[:3]  # Limit to 3 fallback tags
```

### 5.3. Validation Framework
```python
def validate_purpose_tags(tags, program_context):
    """Validate that purpose tags align with program context."""
    
    validation_score = 0
    
    # Check program alignment
    expected_tags = PROGRAM_PURPOSE_MAPPING.get(
        extract_program_type(program_context['program_name']), []
    )
    validation_score += len(set(tags) & set(expected_tags)) * 2
    
    # Check demographic alignment  
    demo_tags = DEMOGRAPHIC_PURPOSE_MAPPING.get(
        program_context['target_demographic'], []
    )
    validation_score += len(set(tags) & set(demo_tags)) * 1
    
    # Penalize generic tags
    generic_tags = ['deep_therapy', 'tension_release', 'invigoration']
    validation_score -= len(set(tags) & set(generic_tags)) * 0.5
    
    return validation_score
```

## 6. Expected Outcomes

### 6.1. Improved Tag Diversity
- Reduce reliance on generic tags from 88% to <30%
- Increase unique purpose tag count from 13 to 30+
- Generate program-specific therapeutic purposes

### 6.2. Enhanced Semantic Meaning
- Tags reflect actual program therapeutic objectives
- Demographic-specific purposes captured
- Acupressure benefits properly attributed

### 6.3. Better Query Capabilities
- Users can find segments by specific therapeutic need
- Program recommendations based on purpose alignment
- Therapeutic progression analysis enabled
