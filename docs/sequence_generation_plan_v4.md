# Sequence Generation v4 Plan (Approach B: Iterative + Decision Tool)

**Goal:** Refactor the sequence generation to use an iterative loop where each step's action (Roll, Subroutine, or Basic) is determined by a dedicated LLM tool call (`PlanNextStep`), improving decision-making based on the current sequence state and aiming for better structural similarity to manual examples.

**Core Concept:** Instead of using separate LLM calls for deciding between subroutine/basic and generating basic actions, consolidate the step-by-step decision logic into a single, more powerful tool call within the iterative generation loop.

**Proposed `PlanNextStep` Tool Schema (Conceptual):**

This tool schema defines the expected output from the LLM at each decision point.

```python
# Conceptual Schema
from pydantic import BaseModel, Field
from typing import Optional, List, Literal
from models.sequence_models import ActionType
from schemas.sequence_schemas import SequenceParameters

class PlanNextStepOutput(BaseModel):
    """
    Schema for the LLM's decision on the next step in sequence generation (V4).
    """
    next_action_type: Literal["roll", "subroutine", "basic"] = Field(
        description="The type of action block to perform next."
    )
    target_roll_action: Optional[ActionType] = Field(
        default=None,
        description="If 'roll', the target body part ActionType."
    )
    chosen_subroutine_id: Optional[str] = Field(
        default=None,
        description="If 'subroutine', the ID of the chosen subroutine (e.g., 'SUB_01')."
    )
    num_basic_steps: Optional[int] = Field(
        default=None,
        description="If 'basic', the number of basic steps to generate (e.g., 2-5)."
    )
    reasoning: Optional[str] = Field(
        default=None,
        description="Brief justification for the chosen next action."
    )
```

**Implementation Plan (v4):**

1.  **Phase 1: Define Tool Schema**

    - **Action:** Formally define the `PlanNextStepOutput` Pydantic model.
    - **File:** `api/schemas/sequence_schemas.py`.
    - **Status:** Done.

2.  **Phase 2: Create `sequence_service_v4.py`**

    - **Action:** Copy `api/services/sequence_service_v3.py` to `api/services/sequence_service_v4.py`. Rename functions (`generate_massage_sequence_v4`, etc.).
    - **File:** `api/services/sequence_service_v4.py`.
    - **Status:** Done.

3.  **Phase 3: Implement Tool-Based Decision Loop**

    - **Action:** Modify the middle segment `while` loop in `generate_massage_sequence_v4`:
      - Remove v3 interleaving logic and `_decide_subroutine_vs_basic_v3`.
      - Inside the loop: Gather state, prepare prompt for `PlanNextStepOutput` tool, bind tool, invoke LLM.
      - Parse the tool call result.
      - Execute the decided action (Roll, Subroutine, or Basic) based on the tool output.
      - Update state (`current_time_seconds`, `current_body_part`, `last_action_block_type`).
    - **File:** `api/services/sequence_service_v4.py`.
    - **Status:** Done.

4.  **Phase 4: Adapt Helper Functions & Prompts**

    - **Action:** Ensure `_generate_basic_action_sequence_v4` exists and is called correctly. Rename `_determine_target_roll_action` to `_determine_target_roll_action_v4`. Update logging messages. The prompt for the `PlanNextStep` tool call is crucial and may need further refinement based on testing.
    - **File:** `api/services/sequence_service_v4.py`.
    - **Status:** Done.

5.  **Phase 5: Integration and Testing**
    - **Action:** Update `api/views/chat_views.py` to import and call `sequence_handler_v4`. Test generation, compare outputs, and iterate on prompts/logic as needed.
    - **File:** `api/views/chat_views.py`.
    - **Status:** Pending.

**v4 Middle Segment Flow (Mermaid):**

```mermaid
graph TD
    A[Start Middle Loop Iteration] --> B[Gather Current State];
    B --> C[Prepare Prompt & Bind PlanNextStep Tool];
    C --> D[Invoke LLM];
    D --> E{Parse PlanNextStep Tool Result};
    E --> F{Next Action Type?};
    F -- Roll --> G[Determine/Add Roll Action];
    F -- Subroutine --> H[Parse ID & Add Subroutine Action];
    F -- Basic --> I[Call Generate Basic Actions];
    I --> J[Add Basic Actions];
    G --> K[Update State (Time, Location, Last Type)];
    H --> K;
    J --> K;
    K --> L{Time < Limit?};
    L -- Yes --> A;
    L -- No --> M[End Middle Segment];
```
