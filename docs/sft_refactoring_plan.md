# Plan: Refactoring SFT Program Generation into a Separate Service

This document outlines the plan to refactor the SFT (Fine-Tuned Model) program generation logic from `ChatService` into a dedicated service. This will improve modularity, testability, and maintainability.

## 1. Current State & Goal

The SFT logic for direct program step generation is currently implemented within `ChatService.process_chat_message`. The goal is to extract this into a new `SFTProgramGenerationService`.

## 2. New Service Definition

### 2.1. Interface (Optional but Recommended)
*   **File**: `api/services/sft_program_generation_interface.py`
*   **Content**:
    ```python
    from abc import ABC, abstractmethod
    from typing import Optional
    from schemas.chat_schemas import MassageProgramDetails, HighEndProductSteps

    class SFTProgramGenerationInterface(ABC):
        @abstractmethod
        async def generate_steps(
            self,
            program_details: MassageProgramDetails,
            sft_model_name: str
        ) -> Optional[HighEndProductSteps]:
            """
            Generates massage program steps using a specified SFT model.

            Args:
                program_details: Extracted user preferences and massage details.
                sft_model_name: The name/identifier of the SFT model to use.

            Returns:
                HighEndProductSteps if generation is successful, otherwise None or raises an error.

            Raises:
                SFTProgramGenerationError: If there's an issue during SFT model invocation or output processing.
            """
            pass
    ```

### 2.2. Implementation & Factory
*   **File**: `api/services/sft_program_generation_service.py`
*   **Content**:
    ```python
    import json
    import logging
    from typing import Optional

    from schemas.chat_schemas import MassageProgramDetails, HighEndProductSteps, HighEndProductStep
    from services.chat_agents import sft_program_generator_agent_factory
    from .sft_program_generation_interface import SFTProgramGenerationInterface # Assuming interface is in a separate file

    logger = logging.getLogger(__name__)

    class SFTProgramGenerationError(Exception):
        """Custom exception for SFT program generation errors."""
        pass

    class SFTProgramGenerationService(SFTProgramGenerationInterface):
        def _construct_sft_prompt(self, details: MassageProgramDetails) -> str:
            # This method will be moved from ChatService
            prompt_lines = [
                "You are an expert massage program designer. Based on the following user preferences, generate a complete, step-by-step massage program sequence in the required JSON format (HighEndProductSteps).",
                "User Preferences:",
            ]
            if details.target_market:
                prompt_lines.append(f"- Target User/Market: {details.target_market}")
            if details.duration:
                prompt_lines.append(f"- Requested Duration: {details.duration} minutes. Design the program steps to fit this duration.")
            if details.focus_area:
                prompt_lines.append(f"- Key Focus Areas: {', '.join(details.focus_area)}")
            if details.problem_desired_outcome:
                prompt_lines.append(f"- Main Goal/Desired Outcome: {details.problem_desired_outcome}")

            if details.program_flow:
                flow_parts = []
                if details.program_flow.highlights:
                    flow_parts.append(f"Highlights: {details.program_flow.highlights}")
                if details.program_flow.signature_moves:
                    flow_parts.append(f"Signature Moves to Incorporate: {details.program_flow.signature_moves}")
                if details.program_flow.logic_techniques:
                    flow_parts.append(f"Preferred Logic/Techniques: {details.program_flow.logic_techniques}")
                if details.program_flow.intensity:
                    flow_parts.append(f"Overall Intensity: {details.program_flow.intensity}")
                if flow_parts:
                    prompt_lines.append(f"- Program Flow Preferences: {'; '.join(flow_parts)}")

            if details.massage_settings:
                settings_parts = []
                if details.massage_settings.warm_air is not None:
                    settings_parts.append(f"Warm Air: {'On' if details.massage_settings.warm_air else 'Off'}")
                if details.massage_settings.vibration is not None:
                    settings_parts.append(f"Vibration: {'On' if details.massage_settings.vibration else 'Off'}")
                if details.massage_settings.air_bags is not None:
                    settings_parts.append(f"Air Bags: {'On' if details.massage_settings.air_bags else 'Off'}")
                # Add other settings as needed
                if settings_parts:
                    prompt_lines.append(f"- Specific Massage Settings: {'; '.join(settings_parts)}")
            
            prompt_lines.append("\nGenerate the full program steps now.")
            return "\n".join(prompt_lines)

        async def generate_steps(
            self,
            program_details: MassageProgramDetails,
            sft_model_name: str
        ) -> Optional[HighEndProductSteps]:
            logger.info(f"SFT Service: Attempting direct generation with model: {sft_model_name}")
            try:
                sft_agent = sft_program_generator_agent_factory(sft_model_name)
                logger.info(f"SFT Service: Agent for {sft_model_name} instantiated.")

                sft_prompt = self._construct_sft_prompt(program_details)
                logger.debug(f"SFT Service: Constructed prompt: {sft_prompt}")

                sft_agent_run_result = await sft_agent.run(sft_prompt)
                raw_output = sft_agent_run_result.output
                
                sft_generated_steps: Optional[HighEndProductSteps] = None
                
                if isinstance(raw_output, HighEndProductSteps):
                    sft_generated_steps = raw_output
                    logger.info(f"SFT Service: Model returned HighEndProductSteps object directly.")
                elif isinstance(raw_output, str):
                    try:
                        logger.info(f"SFT Service: Model returned JSON string, parsing...")
                        parsed_json = json.loads(raw_output)
                        
                        if isinstance(parsed_json, dict) and "steps" in parsed_json:
                            sft_generated_steps = HighEndProductSteps(**parsed_json)
                            logger.info(f"SFT Service: Successfully parsed JSON string into HighEndProductSteps with {len(sft_generated_steps.steps)} steps")
                        elif isinstance(parsed_json, list):
                            logger.info(f"SFT Service: Model returned array of {len(parsed_json)} steps, wrapping.")
                            sft_generated_steps = HighEndProductSteps(steps=[HighEndProductStep(**step) for step in parsed_json])
                            logger.info(f"SFT Service: Successfully wrapped {len(sft_generated_steps.steps)} steps")
                        else:
                            logger.error(f"SFT Service: Unexpected JSON structure. Type: {type(parsed_json)}")
                            raise SFTProgramGenerationError(f"SFT model returned unexpected JSON structure: expected either {{'steps': [...]}} or [...], got {type(parsed_json)}")
                            
                    except (json.JSONDecodeError, TypeError, ValueError) as parse_err:
                        logger.error(f"SFT Service: Failed to parse SFT JSON output: {parse_err}. Raw output: {raw_output[:500]}") # Log snippet of raw output
                        raise SFTProgramGenerationError(f"SFT model returned invalid JSON: {parse_err}")
                else:
                    logger.error(f"SFT Service: Model returned unexpected type: {type(raw_output)}. Raw output: {str(raw_output)[:500]}")
                    raise SFTProgramGenerationError(f"SFT model returned unexpected data type: {type(raw_output)}")

                if not sft_generated_steps or not sft_generated_steps.steps:
                    logger.warning(f"SFT Service: Model {sft_model_name} did not produce valid steps.")
                    # Consider if this should be an error or return None. For now, error.
                    raise SFTProgramGenerationError(f"SFT model {sft_model_name} failed to generate program steps or returned empty steps.")
                
                logger.info(f"SFT Service: Model {sft_model_name} generated {len(sft_generated_steps.steps)} steps.")
                return sft_generated_steps

            except SFTProgramGenerationError: # Re-raise specific errors
                raise
            except Exception as e:
                logger.error(f"SFT Service: Unexpected error during step generation with {sft_model_name}: {e}", exc_info=True)
                raise SFTProgramGenerationError(f"Unexpected error in SFT generation service: {e}") from e


    class SFTProgramGenerationFactory:
        @staticmethod
        def create_default_service() -> SFTProgramGenerationInterface:
            return SFTProgramGenerationService()
    ```

## 3. Refactor `api/services/chat_service.py`

### 3.1. Imports
*   Add:
    ```python
    from .sft_program_generation_service import ( # Adjust path if interface is separate
        SFTProgramGenerationInterface,
        SFTProgramGenerationFactory,
        SFTProgramGenerationError
    )
    ```

### 3.2. `ChatService.__init__`
*   Modify signature:
    ```python
    def __init__(
        self,
        db: Session,
        user: User,
        step_synthesis_service: StepSynthesisInterface,
        sft_program_generation_service: SFTProgramGenerationInterface # New
    ):
    ```
*   Add in body:
    ```python
    self.sft_program_generation_service = sft_program_generation_service
    ```

### 3.3. `ChatService.create_with_default_services`
*   Modify:
    ```python
    @classmethod
    def create_with_default_services(cls, db: Session, user: User) -> "ChatService":
        step_synthesis_service = StepSynthesisFactory.create_default_service()
        sft_program_service = SFTProgramGenerationFactory.create_default_service() # New
        return cls(db, user, step_synthesis_service, sft_program_service) # Pass new service
    ```

### 3.4. `ChatService.process_chat_message`
*   **Remove SFT Logic**: Delete the block responsible for SFT agent instantiation, prompt construction, agent execution, and output parsing (lines ~227-270 in the previously reviewed version).
*   **Call New Service**:
    ```python
    # Inside 'if chat_config.use_sft_direct_generation and chat_config.sft_model_name:'
    try:
        logger.info(f"Attempting SFT direct generation via service with model: {chat_config.sft_model_name} for chat {chat.id}")
        yield { # Keep this event
            "message_id": random.randint(1, 1000000),
            "type": "generating_with_sft", # New event type for UI
            "slug": chat.slug,
            "content": f"Generating program steps with specialized model: {chat_config.sft_model_name}...",
        }

        sft_generated_steps = await self.sft_program_generation_service.generate_steps(
            program_details=structured_output, # This is MassageProgramDetails
            sft_model_name=chat_config.sft_model_name
        )

        if sft_generated_steps and sft_generated_steps.steps: # Service ensures steps are present if it doesn't raise error
            logger.info(f"SFT service returned {len(sft_generated_steps.steps)} steps for chat {chat.id}")
            
            # Logic for pseudo_target_sequence and saving remains largely the same
            sft_program_name = f"SFT: {structured_output.problem_desired_outcome or 'Custom Program'}"
            # ... (rest of pseudo_target_sequence_for_sft creation) ...
            # ... (call to _save_synthesized_program_and_message) ...
            # ... (yield "synthesized_steps" or "sft_generated_steps" event) ...
            # Note: The event type for successful SFT generation was "sft_generated_steps" in the original plan.
            # The _save_synthesized_program_and_message method saves a message with role "synthesized_steps".
            # We should ensure consistency or use a distinct role/event for SFT if needed.
            # For now, let's assume the existing "synthesized_steps" role/event is acceptable for SFT-generated programs too.
            # If not, the _save_synthesized_program_and_message and SSE event type would need adjustment.
            # The plan used "sft_generated_steps" as the SSE type, which is good for client-side distinction.
            # The DB role could remain "synthesized_steps" but with the "SFT Generated: " prefix in source_notes.

            yield {
                "message_id": sft_steps_message.id, # Assuming sft_steps_message is the result of saving
                "type": "sft_generated_steps", # Distinct SSE event type
                "slug": chat.slug,
                "content": sft_steps_message.content, 
                "program_id": sft_steps_message.program_id,
            }
        # else: # No else needed here if service raises error on failure/no steps

    except SFTProgramGenerationError as sft_err: # Catch specific error from the service
        logger.error(f"SFT generation service failed for chat {chat.id}: {sft_err}", exc_info=True)
        yield {
            "message_id": random.randint(1, 1000000),
            "type": "error", 
            "slug": chat.slug,
            "content": f"Failed to generate program with specialized model: {str(sft_err)}",
        }
        self._save_db_messages("error", f"SFT Generation Service Error: {str(sft_err)}", chat.id)
    # The broader 'except Exception as processing_err:' will catch other unexpected errors in this block
    ```

### 3.5. Remove `_construct_sft_prompt` from `ChatService`
*   This method is now part of `SFTProgramGenerationService`.

## 4. Update `sft_integration_plan.md`
*   This current document (`sft_refactoring_plan.md`) will serve as the updated plan. The original `sft_integration_plan.md` can be archived or replaced.

This refactoring will make the `ChatService` cleaner by delegating the SFT-specific logic to a specialized service, adhering to the Single Responsibility Principle.