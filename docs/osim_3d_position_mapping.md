# OSIM uDream 3D Position Mapping Documentation

## Overview
This document provides the comprehensive 3D position mapping for OSIM uDream massage chairs, extracted from official OSIM documentation. This mapping is used for accurate body part targeting in massage program synthesis and segmentation analysis.

## Device Information
- **Device**: OSIM uDream
- **Version**: v6.1
- **Date**: 16-Mar-20
- **Intensity Levels**: WEAK, MID (MEDIUM), STRONG
- **Movement Types**: Rolling Down (Roller change to big size), Rolling Up (Roller change to small size)

## Important Notes
- Position values are **discrete numbers**, not ranges
- Each body part maps to specific position values from the 3D position grid
- The mapping varies by intensity level (WEAK, MID, STRONG)
- Shoulder positioning percentages are used for precise targeting in STRONG and WEAK modes

## Standard 3D Position Mapping Table (MID/MEDIUM Intensity)

### Head and Neck Region
| Positions | Japanese Name | English Translation | FIT_POS Code | Shoulder Position |
|-----------|---------------|---------------------|--------------|-------------------|
| 0, 1, 2, 3 | 頭(頭上) | Head (Upper Head) | FIT_POS_HEAD | shoulder_pos -24 |
| 0, 1, 2, 3 | 首上(頭) | Upper Neck (Head) | FIT_POS_HEAD | shoulder_pos -15 |
| 0 | 首上 | Upper Neck | FIT_POS_NECK_UP | shoulder_pos -4 |
| 0, 1, 2, 3, 4, 5 | 首 | Neck | FIT_POS_NECK | shoulder_pos -3 |

### Shoulder Region
| Positions | Japanese Name | English Translation | FIT_POS Code | Shoulder Position |
|-----------|---------------|---------------------|--------------|-------------------|
| 13, 14, 15, 16, 17, 18, 19 | 肩上 | Upper Shoulder | FIT_POS_SHD2 | shoulder_pos -2 |
| 17, 18, 19, 20, 21, 22, 24 | 肩 | Shoulder | FIT_POS_SHD | shoulder_pos |
| 14 | 肩下 | Lower Shoulder | FIT_POS_MID3 | shoulder_pos*8% |
| 10 | 肩下2 | Lower Shoulder 2 | FIT_POS_MID2 | shoulder_pos*16% |

### Upper Back Region
| Positions | Japanese Name | English Translation | FIT_POS Code | Shoulder Position |
|-----------|---------------|---------------------|--------------|-------------------|
| 10 | 肩甲骨上 | Upper Shoulder Blade | FIT_POS_MID2 | shoulder_pos*36% |
| 10 | 肩甲骨 | Shoulder Blade | FIT_POS_MID2 | shoulder_pos*48% |
| 8 | 肩甲骨下 | Lower Shoulder Blade | FIT_POS_MID | shoulder_pos*58% |
| 9 | 背上 | Upper Back | FIT_POS_MID | shoulder_pos*68% |

### Mid and Lower Back Region
| Positions | Japanese Name | English Translation | FIT_POS Code | Shoulder Position |
|-----------|---------------|---------------------|--------------|-------------------|
| 5 | 背中 | Mid Back | FIT_POS_WAIST | shoulder_pos*80% |
| 2, 3, 4, 5, 8, 11, 14 | 腰上1(背下) | Upper Waist 1 (Lower Back) | FIT_POS_WAIST | shoulder_pos*92% |
| 2, 3, 4, 5, 8, 11, 14 | 腰(腰上) | Waist (Upper Waist) | FIT_POS_WAIST | shoulder_pos*100% |
| 5, 6, 7, 8, 13, 18, 24 | 尻(腰上) | Hips (Upper Waist) | FIT_POS_HIPS | UPDOWN_MAX |

## STRONG Intensity Position Mapping

### Head and Neck Region (STRONG)
| Positions | Japanese Name | English Translation | FIT_POS Code | Shoulder Position |
|-----------|---------------|---------------------|--------------|-------------------|
| 0, 1, 2, 3, 4 | 頭(頭上) | Head (Upper Head) | FIT_POS_HEAD | shoulder_pos -24 |
| 0, 1, 2, 3 | 首上(頭) | Upper Neck (Head) | FIT_POS_HEAD | shoulder_pos -15 |
| 0, 1, 2, 3, 4 | 首上 | Upper Neck | FIT_POS_NECK_UP | shoulder_pos -4 |
| 1, 2, 3, 4, 5, 6, 7 | 首 | Neck | FIT_POS_NECK | shoulder_pos -3 |

### Shoulder Region (STRONG)
| Positions | Japanese Name | English Translation | FIT_POS Code | Shoulder Position |
|-----------|---------------|---------------------|--------------|-------------------|
| 14, 15, 16, 17, 18, 19, 20 | 肩上 | Upper Shoulder | FIT_POS_SHD2 | shoulder_pos -2 |
| 19, 20, 21, 22, 23, 24 | 肩 | Shoulder | FIT_POS_SHD | shoulder_pos |
| 15 | 肩下 | Lower Shoulder | FIT_POS_MID3 | shoulder_pos*8% |
| 13 | 肩下2 | Lower Shoulder 2 | FIT_POS_MID2 | shoulder_pos*16% |

### Upper Back Region (STRONG)
| Positions | Japanese Name | English Translation | FIT_POS Code | Shoulder Position |
|-----------|---------------|---------------------|--------------|-------------------|
| 13 | 肩甲骨上 | Upper Shoulder Blade | FIT_POS_MID2 | shoulder_pos*36% |
| 13 | 肩甲骨 | Shoulder Blade | FIT_POS_MID2 | shoulder_pos*48% |
| 11 | 肩甲骨下 | Lower Shoulder Blade | FIT_POS_MID | shoulder_pos*58% |
| 11 | 背上 | Upper Back | FIT_POS_MID | shoulder_pos*68% |

### Mid and Lower Back Region (STRONG)
| Positions | Japanese Name | English Translation | FIT_POS Code | Shoulder Position |
|-----------|---------------|---------------------|--------------|-------------------|
| 9 | 背中 | Mid Back | FIT_POS_WAIST | shoulder_pos*80% |
| 5, 6, 7, 8, 12, 16, 20 | 腰上1(背下) | Upper Waist 1 (Lower Back) | FIT_POS_WAIST | shoulder_pos*92% |
| 5, 6, 7, 8, 13, 18, 22 | 腰(腰上) | Waist (Upper Waist) | FIT_POS_WAIST | shoulder_pos*100% |
| 7, 8, 9, 10, 15, 20, 24 | 尻(腰上) | Hips (Upper Waist) | FIT_POS_HIPS | UPDOWN_MAX |

## WEAK Intensity Position Mapping

### Head and Neck Region (WEAK)
| Positions | Japanese Name | English Translation | FIT_POS Code | Shoulder Position |
|-----------|---------------|---------------------|--------------|-------------------|
| 0, 1, 2 | 頭(頭上) | Head (Upper Head) | FIT_POS_HEAD | shoulder_pos -24 |
| 0, 1, 2, 3 | 首上(頭) | Upper Neck (Head) | FIT_POS_HEAD | shoulder_pos -15 |
| 0 | 首上 | Upper Neck | FIT_POS_NECK_UP | shoulder_pos -4 |
| 0, 1, 2, 3, 4 | 首 | Neck | FIT_POS_NECK | shoulder_pos -3 |

### Shoulder Region (WEAK)
| Positions | Japanese Name | English Translation | FIT_POS Code | Shoulder Position |
|-----------|---------------|---------------------|--------------|-------------------|
| 12, 13, 14, 15, 16, 17, 18 | 肩上 | Upper Shoulder | FIT_POS_SHD2 | shoulder_pos -2 |
| 14, 15, 16, 17, 18, 19, 20 | 肩 | Shoulder | FIT_POS_SHD | shoulder_pos |
| 11 | 肩下 | Lower Shoulder | FIT_POS_MID3 | shoulder_pos*8% |
| 8 | 肩下2 | Lower Shoulder 2 | FIT_POS_MID2 | shoulder_pos*16% |

### Upper Back Region (WEAK)
| Positions | Japanese Name | English Translation | FIT_POS Code | Shoulder Position |
|-----------|---------------|---------------------|--------------|-------------------|
| 8 | 肩甲骨上 | Upper Shoulder Blade | FIT_POS_MID2 | shoulder_pos*36% |
| 8 | 肩甲骨 | Shoulder Blade | FIT_POS_MID2 | shoulder_pos*48% |
| 6 | 肩甲骨下 | Lower Shoulder Blade | FIT_POS_MID | shoulder_pos*58% |
| 6 | 背上 | Upper Back | FIT_POS_MID | shoulder_pos*68% |

### Mid and Lower Back Region (WEAK)
| Positions | Japanese Name | English Translation | FIT_POS Code | Shoulder Position |
|-----------|---------------|---------------------|--------------|-------------------|
| 4 | 背中 | Mid Back | FIT_POS_WAIST | shoulder_pos*80% |
| 1, 2, 3, 4, 6, 8, 10 | 腰上1(背下) | Upper Waist 1 (Lower Back) | FIT_POS_WAIST | shoulder_pos*92% |
| 1, 2, 3, 4, 6, 8, 10 | 腰(腰上) | Waist (Upper Waist) | FIT_POS_WAIST | shoulder_pos*100% |
| 3, 4, 5, 6, 10, 14, 18 | 尻(腰上) | Hips (Upper Waist) | FIT_POS_HIPS | UPDOWN_MAX |

## Position Mapping for Programming

```python
# MID/MEDIUM Intensity (Standard)
OSIM_3D_POSITION_MAPPING = {
    # Head and Neck
    "head": {"positions": [0, 1, 2, 3], "fit_pos": "FIT_POS_HEAD", "region": "head"},
    "neck_upper_head": {"positions": [0, 1, 2, 3], "fit_pos": "FIT_POS_HEAD", "region": "neck"},
    "neck_upper": {"positions": [0], "fit_pos": "FIT_POS_NECK_UP", "region": "neck"},
    "neck": {"positions": [0, 1, 2, 3, 4, 5], "fit_pos": "FIT_POS_NECK", "region": "neck"},
    
    # Shoulders
    "shoulder_upper": {"positions": [13, 14, 15, 16, 17, 18, 19], "fit_pos": "FIT_POS_SHD2", "region": "shoulders"},
    "shoulder": {"positions": [17, 18, 19, 20, 21, 22, 24], "fit_pos": "FIT_POS_SHD", "region": "shoulders"},
    "shoulder_lower": {"positions": [14], "fit_pos": "FIT_POS_MID3", "region": "shoulders"},
    "shoulder_lower_2": {"positions": [10], "fit_pos": "FIT_POS_MID2", "region": "shoulders"},
    
    # Back regions
    "shoulder_blade_upper": {"positions": [10], "fit_pos": "FIT_POS_MID2", "region": "upper_back"},
    "shoulder_blade": {"positions": [10], "fit_pos": "FIT_POS_MID2", "region": "upper_back"},
    "shoulder_blade_lower": {"positions": [8], "fit_pos": "FIT_POS_MID", "region": "upper_back"},
    "upper_back": {"positions": [9], "fit_pos": "FIT_POS_MID", "region": "upper_back"},
    "mid_back": {"positions": [5], "fit_pos": "FIT_POS_WAIST", "region": "mid_back"},
    
    # Lower back and hips
    "lower_back": {"positions": [2, 3, 4, 5, 8, 11, 14], "fit_pos": "FIT_POS_WAIST", "region": "lower_back"},
    "waist": {"positions": [2, 3, 4, 5, 8, 11, 14], "fit_pos": "FIT_POS_WAIST", "region": "lumbar"},
    "hips": {"positions": [5, 6, 7, 8, 13, 18, 24], "fit_pos": "FIT_POS_HIPS", "region": "hips"}
}
```

## Usage Notes

### Key Points:
1. **Discrete Values**: Position values are specific numbers, not ranges
2. **Intensity Variations**: Each intensity level (WEAK, MID, STRONG) has different position mappings
3. **Multiple Positions**: A body part can be targeted by multiple discrete position values
4. **Overlapping Regions**: Some positions may target multiple body parts (e.g., position 8 might target both lower back and hips)

### Programming Examples
```python
# Get body parts for a specific position with intensity
body_parts = get_body_parts_from_position(15, intensity="strong")  # Returns regions that include position 15

# Check if a position targets a specific body part
if 15 in OSIM_3D_POSITION_MAPPING_STRONG["shoulder_upper"]["positions"]:
    print("Position 15 targets upper shoulder in STRONG mode")

# Get all positions for a body region
shoulder_positions = OSIM_3D_POSITION_MAPPING["shoulder"]["positions"]  # [17, 18, 19, 20, 21, 22, 24]
```

### Intensity Characteristics
| Intensity | Coverage | Precision | Use Case |
|-----------|----------|-----------|----------|
| WEAK | Gentle, focused positions | Basic targeting | Comfort, relaxation |
| MID | Standard coverage | Balanced targeting | General therapy |
| STRONG | Extended positions | Enhanced precision | Intensive treatment |

## Movement Patterns

### Rolling Direction
- **Rolling Down (to big size)**: Initial positioning and coverage expansion
- **Rolling Up (to small size)**: Focused treatment and precision targeting

### Position Transitions
- Smooth transitions between adjacent position values
- Consider intensity when planning position sequences
- Use discrete values for precise body part targeting

## Integration Guidelines

When using this mapping:
1. Always check the specific position values, not ranges
2. Consider intensity level for accurate body part identification
3. Handle overlapping regions appropriately
4. Use shoulder positioning percentages for fine-tuning in STRONG/WEAK modes

## Future Considerations

- Position values may be refined based on clinical feedback
- Additional intensity levels may be introduced
- Machine learning models can use these discrete mappings for pattern recognition
- Cross-reference with therapeutic outcomes for optimization
