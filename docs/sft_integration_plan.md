# Plan: Integrating Fine-Tuned Model (SFT) for Direct Program Step Generation

This document outlines the plan to modify the `ChatService` to integrate a fine-tuned model (SFT) for directly generating massage program steps, as an alternative to the existing semantic search and stitching flow.

## 1. Goal

To allow the system to use a specified SFT model for generating full massage program steps when indicated by the `ChatConfig`, using the output of the initial `massage_agent` ([`MassageProgramDetails`](api/schemas/chat_schemas.py:414)) as input.

## 2. Context

-   **New `ChatConfig` fields (already added in [`api/models/chat_models.py`](api/models/chat_models.py:1)):**
    -   `sft_model_name: Optional[str]`
    -   `use_sft_direct_generation: bool` (defaults to `False`)
-   **SFT Agent Invocation:** A new factory function `sft_program_generator_agent_factory(model_name: str) -> Agent` has been added to [`api/services/chat_agents.py`](api/services/chat_agents.py:1). This factory creates a Pydantic-AI `Agent` configured with an `OpenAIModel` (using the provided `model_name`) and expects `HighEndProductSteps` as its output schema.

## 3. Proposed Changes in `api/services/chat_service.py`

The primary modifications will be within the `process_chat_message` method of the `ChatService`.

### 3.1. Conditional Logic after `massage_agent`

After the `massage_agent` successfully returns `structured_output` (which is an instance of `MassageProgramDetails`):

```python
# Existing code:
# ...
# --- 5. Process Agent Output ---
if structured_output and isinstance(structured_output, MassageProgramDetails):
    logger.info(
        f"Massage agent returned structured output for chat {chat.id}: {structured_output.focus_area}"
    )
    try:
        massage_program_details_message = self._save_db_messages(
            "massage_program_details",
            json.dumps(structured_output.model_dump()),
            chat.id,
        )
        yield {
            "message_id": massage_program_details_message.id,
            "type": "massage_program_details",
            "slug": chat.slug,
            "content": json.dumps(structured_output.model_dump()),
        }

        # NEW CONDITIONAL LOGIC STARTS HERE
        if chat_config.use_sft_direct_generation and chat_config.sft_model_name:
            logger.info(f"Attempting SFT direct generation with model: {chat_config.sft_model_name} for chat {chat.id}")
            # 5s_sft. Generate Steps directly using SFT model
            # (Implementation details in section 3.2)
        else:
            logger.info(f"Proceeding with semantic search and stitching for chat {chat.id}")
            # 5a. Generate Target Sequence (Existing logic)
            generated_target_sequence = await self._generate_target_sequence(
                structured_output
            )
            # ... rest of the existing semantic search and stitching flow ...

    except Exception as processing_err: # Broader catch for this block
        # ... existing error handling ...
# ...
```

### 3.2. SFT Direct Generation Path

This new block will be executed if `use_sft_direct_generation` is true.

```python
            # NEW: Inside the 'if chat_config.use_sft_direct_generation:' block
            try:
                # 5s_sft.1. Instantiate SFT Agent
                sft_agent = sft_program_generator_agent_factory(chat_config.sft_model_name)
                logger.info(f"SFT Agent for {chat_config.sft_model_name} instantiated for chat {chat.id}")

                # 5s_sft.2. Construct Prompt for SFT Agent
                # This prompt will use the 'structured_output' (MassageProgramDetails)
                sft_prompt = self._construct_sft_prompt(structured_output)
                logger.debug(f"Constructed SFT prompt for chat {chat.id}: {sft_prompt}")

                # 5s_sft.3. Run SFT Agent
                # Create a "generating_with_sft" event
                yield {
                    "message_id": random.randint(1, 1000000), # Or use a more stable ID if possible
                    "type": "generating_with_sft",
                    "slug": chat.slug,
                    "content": f"Generating program steps with specialized model: {chat_config.sft_model_name}...",
                }

                sft_agent_run_result = await sft_agent.run(sft_prompt)
                sft_generated_steps: Optional[HighEndProductSteps] = sft_agent_run_result.output

                if sft_generated_steps and sft_generated_steps.steps:
                    logger.info(f"SFT model {chat_config.sft_model_name} generated {len(sft_generated_steps.steps)} steps for chat {chat.id}")

                    # 5s_sft.4. Save SFT-Generated Program and Message
                    # We need a way to distinguish SFT-generated programs.
                    # For now, we'll use the MassageProgramDetails to create a "TargetSequence-like" object
                    # for naming and description, or adapt _save_synthesized_program_and_message.

                    # Create a simple TargetSequence-like structure for naming/description
                    sft_program_name = f"SFT: {structured_output.problem_desired_outcome or 'Custom Program'}"
                    sft_program_objective = f"Directly generated by SFT model {chat_config.sft_model_name} based on user request: {structured_output.model_dump_json(indent=2, exclude_none=True)[:200]}..."

                    # This part needs careful consideration:
                    # Option A: Create a minimal TargetSequence on the fly
                    # Option B: Adapt _save_synthesized_program_and_message or create a new helper
                    # For now, let's assume we create a minimal structure.
                    # This is a placeholder; the actual TargetSequence schema is more complex.
                    pseudo_target_sequence_for_sft = TargetSequence(
                        name=sft_program_name,
                        objective=sft_program_objective,
                        programme_sequence=ProgrammeSequenceDetail( # Dummy values, as SFT generates full steps
                            front_sequence="Generated by SFT",
                            main_sequence="Generated by SFT",
                            cooling_sequence="Generated by SFT"
                        ),
                        targeted_acupressure_points=[], # SFT output is steps, not these high-level details
                        signature_moves=[] # SFT output is steps
                    )


                    sft_steps_message = self._save_synthesized_program_and_message( # Re-using this, might need adjustment
                        product_id=chat_config.product_id,
                        target_sequence=pseudo_target_sequence_for_sft, # Pass the pseudo-sequence
                        steps=sft_generated_steps,
                        chat_id=chat.id,
                        source_note_prefix="SFT Generated: " # Add a prefix to source_notes
                    )

                    yield {
                        "message_id": sft_steps_message.id,
                        "type": "sft_generated_steps", # New event type
                        "slug": chat.slug,
                        "content": sft_steps_message.content, # JSON string of steps
                        "program_id": sft_steps_message.program_id,
                    }
                else:
                    logger.warning(f"SFT model {chat_config.sft_model_name} did not return valid steps for chat {chat.id}.")
                    raise ChatServiceError(f"SFT model {chat_config.sft_model_name} failed to generate program steps.")

            except Exception as sft_err:
                logger.error(f"SFT direct generation failed for chat {chat.id}: {sft_err}", exc_info=True)
                # Yield specific SFT error
                yield {
                    "message_id": random.randint(1, 1000000),
                    "type": "error", # Can be a more specific error type e.g., "sft_error"
                    "slug": chat.slug,
                    "content": f"Failed to generate program with specialized model: {str(sft_err)}",
                }
                # Optionally save an error message to DB
                self._save_db_messages("error", f"SFT Generation Error: {str(sft_err)}", chat.id)

```

### 3.3. New Helper Method: `_construct_sft_prompt`

This method will take `MassageProgramDetails` and format it into a string prompt suitable for the SFT agent.

```python
    def _construct_sft_prompt(self, details: MassageProgramDetails) -> str:
        """Constructs a detailed prompt for the SFT program generation agent."""
        prompt_lines = [
            "You are an expert massage program designer. Based on the following user preferences, generate a complete, step-by-step massage program sequence in the required JSON format (HighEndProductSteps).",
            "User Preferences:",
        ]
        if details.target_market:
            prompt_lines.append(f"- Target User/Market: {details.target_market}")
        if details.duration:
            prompt_lines.append(f"- Requested Duration: {details.duration} minutes. Design the program steps to fit this duration.")
        if details.focus_area:
            prompt_lines.append(f"- Key Focus Areas: {', '.join(details.focus_area)}")
        if details.problem_desired_outcome:
            prompt_lines.append(f"- Main Goal/Desired Outcome: {details.problem_desired_outcome}")

        if details.program_flow:
            flow_parts = []
            if details.program_flow.highlights:
                flow_parts.append(f"Highlights: {details.program_flow.highlights}")
            if details.program_flow.signature_moves:
                flow_parts.append(f"Signature Moves to Incorporate: {details.program_flow.signature_moves}")
            if details.program_flow.logic_techniques:
                flow_parts.append(f"Preferred Logic/Techniques: {details.program_flow.logic_techniques}")
            if details.program_flow.intensity:
                flow_parts.append(f"Overall Intensity: {details.program_flow.intensity}")
            if flow_parts:
                prompt_lines.append(f"- Program Flow Preferences: {'; '.join(flow_parts)}")

        if details.massage_settings:
            settings_parts = []
            if details.massage_settings.warm_air is not None:
                settings_parts.append(f"Warm Air: {'On' if details.massage_settings.warm_air else 'Off'}")
            if details.massage_settings.vibration is not None:
                settings_parts.append(f"Vibration: {'On' if details.massage_settings.vibration else 'Off'}")
            if details.massage_settings.air_bags is not None:
                settings_parts.append(f"Air Bags: {'On' if details.massage_settings.air_bags else 'Off'}")
            # Add other settings as needed
            if settings_parts:
                prompt_lines.append(f"- Specific Massage Settings: {'; '.join(settings_parts)}")
        
        prompt_lines.append("\nGenerate the full program steps now.")
        return "\n".join(prompt_lines)

```

### 3.4. Modify `_save_synthesized_program_and_message` (or create a new one)

The existing `_save_synthesized_program_and_message` method might need a slight modification to handle programs generated by SFT, perhaps by adding a parameter to indicate the source or by adjusting how `source_notes` or `logic_technique` are populated.

A simple approach is to add an optional `source_note_prefix` parameter:

```python
    def _save_synthesized_program_and_message(
        self,
        product_id: int,
        target_sequence: TargetSequence, # This will be a pseudo-TargetSequence for SFT
        steps: HighEndProductSteps,
        chat_id: int,
        source_note_prefix: str = "" # New parameter
    ) -> Message:
        generated_category = self._get_or_create_program_category("generated programs") # Or "sft generated programs"

        # ... (column mapping logic remains the same) ...

        program = Program(
            product_id=product_id,
            user_id=self.user.id,
            name=target_sequence.name, # Will use the SFT-derived name
            logic_technique=target_sequence.objective, # Will use SFT-derived objective
            program_description=target_sequence.model_dump(), # Store the pseudo-sequence or MassageProgramDetails
            category_id=generated_category.id,
            steps=[s.model_dump() for s in steps.steps],
            column_mapping_used=dynamic_column_mapping,
            source_notes=f"{source_note_prefix}Generated by AI. Based on: {target_sequence.objective[:200]}...", # Modified
            current_version_number=initial_version_number,
        )
        # ... (rest of the method remains the same) ...
```

### 3.5. Import `sft_program_generator_agent_factory`

Add the import at the top of `api/services/chat_service.py`:

```python
from services.chat_agents import (
    massage_agent,
    sequence_generator_agent,
    sft_program_generator_agent_factory, # Add this
)
# Also ensure TargetSequence and ProgrammeSequenceDetail are imported from schemas if not already
from schemas.chat_schemas import (
    MassageProgramDetails,
    TargetSequence,
    HighEndProductSteps,
    HighEndProductStep,
    ProgrammeSequenceDetail, # Ensure this is imported
)
```

## 4. New SSE Event Type

A new SSE event type `sft_generated_steps` will be introduced for the client to handle these directly generated steps.
Another event `generating_with_sft` will inform the UI that this specific process has started.

## 5. Error Handling

-   Errors during SFT agent instantiation or execution will be caught.
-   A specific error message will be yielded to the client via SSE.
-   An error message will be saved to the database.

## 6. Flow Diagram Update (Conceptual)

The main flow diagram would now have a branch after "MassageAgent provides `MassageProgramDetails`":

```mermaid
sequenceDiagram
    participant Client
    participant Router as chat_program_routes.py
    participant Service as chat_service.py
    participant DB as Database
    participant MassageAgent
    participant SFT_Agent
    participant SeqGenAgent as SequenceGeneratorAgent
    participant Pinecone
    participant StepSynthService as StepSynthesisService

    Client->>+Router: POST /api2/chat/program (ChatRequest)
    Router->>+Service: create_with_default_services()
    Router->>Service: process_chat_message(ChatRequest)
    Service->>+DB: _get_or_create_chat_and_config()
    DB-->>-Service: Chat, ChatConfig
    Service->>DB: _load_chat_history()
    DB-->>Service: Message History
    Service->>DB: _save_db_messages(user_message)

    Service->>+MassageAgent: run_stream(message, history)
    loop Streaming Response from MassageAgent
        MassageAgent-->>Service: delta (text token)
        Service-->>Router: yield {"type": "token", "content": delta, ...}
        Router-->>Client: SSE: data: {"type": "token", ...}
    end
    MassageAgent-->>-Service: Full Response, StructuredOutput (MassageProgramDetails), ToolMessages
    Service->>DB: _save_db_messages(assistant_response, tool_call, tool_return)
    Service-->>Router: yield {"type": "massage_program_details", ...}
    Router-->>Client: SSE: data: {"type": "massage_program_details", ...}

    alt ChatConfig.use_sft_direct_generation is TRUE
        Service->>Service: _construct_sft_prompt(MassageProgramDetails)
        Service->>SFT_Agent: sft_program_generator_agent_factory(ChatConfig.sft_model_name)
        Service-->>Router: yield {"type": "generating_with_sft", ...}
        Router-->>Client: SSE: data: {"type": "generating_with_sft", ...}
        Service->>+SFT_Agent: run(sft_prompt)
        SFT_Agent-->>-Service: AgentRunResult[HighEndProductSteps]
        alt SFT Agent Success
            Service->>+DB: _save_synthesized_program_and_message (with SFT steps, pseudo_target_sequence, source_note_prefix="SFT Generated: ")
            DB-->>-Service: Saved Program, Message
            Service-->>Router: yield {"type": "sft_generated_steps", ...}
            Router-->>Client: SSE: data: {"type": "sft_generated_steps", ...}
        else SFT Agent Failure
            Service-->>Router: yield {"type": "error", "content": "SFT generation failed..."}
            Router-->>Client: SSE: data: {"type": "error", ...}
            Service->>DB: _save_db_messages("error", "SFT Generation Error...")
        end
    else ChatConfig.use_sft_direct_generation is FALSE (Existing Flow)
        Service->>+SeqGenAgent: _generate_target_sequence(MassageProgramDetails)
        SeqGenAgent-->>-Service: AgentRunResult[TargetSequence]
        Service->>DB: _save_db_messages(target_sequence)
        Service-->>Router: yield {"type": "target_sequence", ...}
        Router-->>Client: SSE: data: {"type": "target_sequence", ...}

        Service->>Service: _perform_semantic_search(TargetSequence, product_id)
        Service->>Pinecone: query(...)
        Pinecone-->>Service: Matched Program IDs
        Service->>+DB: Fetch Programs by ID
        DB-->>-Service: Fetched Programs
        Service-->>Router: yield {"type": "programs", ...}
        Router-->>Client: SSE: data: {"type": "programs", ...}
        Service->>DB: _save_db_messages(programs_data)

        alt Fetched Programs available
            Service-->>Router: yield {"type": "synthesizing", ...}
            Router-->>Client: SSE: data: {"type": "synthesizing", ...}
            Service->>+StepSynthService: synthesize_steps(FetchedPrograms, TargetSequence.output)
            StepSynthService-->>-Service: SynthesizedSteps (HighEndProductSteps)
            alt SynthesizedSteps available
                Service->>+DB: _save_synthesized_program_and_message(SynthesizedSteps, TargetSequence)
                DB-->>-Service: Saved Program, Message
                Service-->>Router: yield {"type": "synthesized_steps", ...}
                Router-->>Client: SSE: data: {"type": "synthesized_steps", ...}
            else No SynthesizedSteps
                Service-->>Router: yield {"type": "error", "content": "Synthesis failed..."}
                Router-->>Client: SSE: data: {"type": "error", ...}
            end
        else No Fetched Programs
             Service-->>Router: yield {"type": "error", "content": "Cannot synthesize: No relevant programs..."}
             Router-->>Client: SSE: data: {"type": "error", ...}
        end
    end

    alt Overall Error Occurs (e.g., HTTPException, ChatServiceError)
        Service-->>Router: yield {"type": "error", "content": error_details}
        Router-->>Client: SSE: data: {"type": "error", ...}
    end
    Service-->>-Router: (async generator completes)
    Router-->>-Client: (SSE stream ends)
```

## 7. Testing Considerations

-   Test with `use_sft_direct_generation = True` and a valid `sft_model_name`.
    -   Verify SFT agent is called.
    -   Verify correct prompt construction.
    -   Verify `sft_generated_steps` event is sent.
    -   Verify program is saved correctly with SFT distinction.
-   Test with `use_sft_direct_generation = True` but an invalid/failing `sft_model_name` or SFT agent error.
    -   Verify error is handled gracefully and reported.
-   Test with `use_sft_direct_generation = False`.
    -   Verify the existing semantic search and stitching flow is executed.
-   Test with `use_sft_direct_generation = True` but `sft_model_name` is `None` or empty.
    -   Verify it falls back to the semantic search and stitching flow (current plan).
-   Ensure logging is adequate for debugging both paths.

This plan provides a comprehensive approach to integrating the SFT model for direct program step generation.