# Sequence Generation v3 Plan

**Goal:** Modify the sequence generation logic to produce massage sequences that are structurally more similar to the provided manual examples (`lumbar_program.md`, `ns_program.md`) in terms of step count and the interleaving of subroutines, basic actions, and rolling movements.

**Analysis Summary (v2 vs Manual):**

- **Length Discrepancy:** Generated sequences (v2) are shorter than manual examples (100+ steps). Likely due to overestimated action durations and fixed end segment time.
- **Subroutine Clustering:** Generated sequences (v2) cluster `SUBROUTINE` actions consecutively more often than manual examples. Likely due to the generation loop logic not enforcing interleaving after a subroutine if the roller location doesn't change.

**Proposed Plan (v3):**

1.  **Phase 1: Refine Duration Estimation**

    - **Action:** Analyze `lumbar_program.md` and `ns_program.md` to calculate more realistic average durations for each `ActionType`. Pay close attention to `ROLL_*` actions and the effective duration of `SUBROUTINE` blocks.
    - **File:** Update the `ACTION_DURATION_ESTIMATES` dictionary in `api/utils/sequence_utils.py`.
    - **Rationale:** More accurate time estimates will lead to sequences with a step count closer to the target duration.

2.  **Phase 2: Adjust Segment Timing & Structure**

    - **Action:** Modify the fixed `end_segment_duration_seconds` in `generate_massage_sequence_v3` (the new function based on v2). Consider making it a smaller percentage of the total duration or dynamically calculating it.
    - **File:** `api/services/sequence_service_v3.py` (new file/function).
    - **Rationale:** Prevents the middle segment from being cut short unnecessarily.

3.  **Phase 3: Implement Hybrid Interleaving in Middle Segment**

    - **Action:** Modify the middle segment loop logic in `generate_massage_sequence_v3`.
      - **After a `SUBROUTINE` is added:** _Force_ the next step to be either:
        - A `Roll to` action (chosen based on focus areas or randomly).
        - OR Call `_generate_basic_action_sequence` to add 1-3 basic actions (e.g., `Timer`, `3D`, `Width change`). The choice (Roll vs. Basic) could be random or guided.
      - **After `basic actions` are added or after a `Roll to` action:** Use the existing `_decide_subroutine_vs_basic` LLM call to determine whether the _next_ block should be a subroutine or more basic actions.
    - **File:** `api/services/sequence_service_v3.py` (new file/function).
    - **Rationale:** Directly addresses subroutine clustering by enforcing a different action type after each subroutine, mimicking manual examples.

4.  **Phase 4: Refine LLM Prompts**

    - **Action:** Update the system prompts used in `_generate_basic_action_sequence` and `_decide_subroutine_vs_basic` (or copies if creating v3 versions). Incorporate insights from manual examples and desired structure.
    - **File:** `api/services/sequence_service_v3.py`.
    - **Rationale:** Better prompts guide the LLM towards desired output structure.

5.  **Phase 5: Integration and Testing**
    - **Action:** Create `api/services/sequence_service_v3.py`. Update `api/views/chat_views.py` to call the new `sequence_handler_v3`.
    - Generate sequences using v3, compare against manual examples and v2 outputs.
    - Iteratively refine logic and prompts based on results.
    - **Files:** `api/services/sequence_service_v3.py`, `api/views/chat_views.py`.

**Proposed v3 Middle Segment Flow (Mermaid):**

```mermaid
graph TD
    A[Start Middle Loop Iteration] --> B{Target Location Changed?};
    B -- Yes --> C[Add Roll Action];
    B -- No --> D[Current Location];
    C --> D;
    D --> E{Previous Action was Subroutine?};
    E -- Yes --> F{Force Interleaving: Roll or Basic?};
    F -- Roll --> G[Determine Next Roll Target];
    F -- Basic --> H[Generate 1-3 Basic Actions];
    G --> C;
    H --> I[Add Basic Actions];
    E -- No --> J[LLM Decide: Subroutine or Basic?];
    J -- Subroutine --> K[Select & Add Subroutine Action];
    J -- Basic --> L[Generate 2-5 Basic Actions];
    K --> M[End Iteration / Check Time];
    L --> I;
    I --> M;
    M --> A;
```
