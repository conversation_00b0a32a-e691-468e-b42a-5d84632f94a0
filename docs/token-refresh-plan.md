# Plan: Proactive Cognito Token Refresh in Next.js UI

## Problem Description

The Next.js web application UI, using AWS Cognito via Amplify for authentication, experiences '401 Unauthenticated' errors from the backend API after user inactivity. The UI currently doesn't automatically handle token expiry, requiring a manual refresh.

## Analysis

- **Authentication Flow:**
  - `ui/src/providers/app-provider.tsx`: Configures Amplify Auth and uses the `<Authenticator>` component.
  - `ui/src/hooks/auth.hooks.ts`: The `useUser` hook fetches the initial session (`fetchAuthSession`) and user details, storing the access token in a Zustand store (`useAuthStore`).
  - `ui/src/services/user.service.ts`: An Axios instance (`api`) is created. A _request_ interceptor reads the `accessToken` directly from the Zustand store (`useAuthStore.getState().accessToken`) and adds it to the `Authorization` header for every outgoing request.
- **Root Cause:** The Axios request interceptor uses the potentially stale token from the Zustand store without verifying its validity or attempting a refresh before the API call.

## Proposed Solution

Leverage Amplify's built-in session management within the Axios request flow to proactively manage token validity and refresh. Implement global error handling for unrecoverable authentication issues.

### Detailed Steps

1.  **Modify Axios Request Interceptor (`ui/src/services/user.service.ts`):**

    - Make the interceptor `async`.
    - Before each request, call `fetchAuthSession({ forceRefresh: false })` from `aws-amplify/auth`. This function automatically handles checking token validity and refreshing using the refresh token if necessary.
    - Extract the `accessToken` from the `tokens` object returned by the successful `fetchAuthSession` call.
    - Attach this fresh `accessToken` to the `Authorization` header (`Bearer ${accessToken}`).
    - **(Optional but recommended):** Update the Zustand store (`setAccessToken`) with the potentially new token.
    - Handle potential errors from `fetchAuthSession` (e.g., if the refresh token is also invalid). Let these errors propagate to be caught by the response interceptor or global error handling.

2.  **Implement Global Error Handling (Axios Response Interceptor):**
    - Add an Axios _response_ interceptor to the `api` instance in `ui/src/services/user.service.ts`.
    - Catch errors globally, specifically looking for responses with a `401` status code.
    - If a 401 occurs (indicating an unrecoverable session issue even after the request interceptor's attempt):
      - Trigger a full sign-out (e.g., using `signOut` from Amplify or a dedicated mutation/store action).
      - Clear user state in Zustand (`setUser(null)`, `setAccessToken(null)`).
      - Clear relevant React Query cache (`queryClient.clear()`).
      - Redirect the user to the login page or ensure the `<Authenticator>` takes over.

### Mermaid Diagram: Proposed Flow

```mermaid
sequenceDiagram
    participant UI Component
    participant Axios Interceptor (Request)
    participant Amplify Auth
    participant Zustand Store
    participant Backend API
    participant Axios Interceptor (Response)
    participant SignOut Logic

    UI Component->>Axios Interceptor (Request): Initiate API Call (e.g., userService.getAccount)
    Axios Interceptor (Request)->>Amplify Auth: fetchAuthSession()
    alt Access Token Valid or Refresh Successful
        Amplify Auth-->>Axios Interceptor (Request): Return Session (Current or Refreshed Tokens)
        Axios Interceptor (Request)->>Zustand Store: (Optional) setAccessToken(new token)
        Axios Interceptor (Request)->>Backend API: API Request with Fresh Bearer Token
        Backend API-->>Axios Interceptor (Response): API Response (e.g., 200 OK)
        Axios Interceptor (Response)-->>UI Component: Return Data
    else fetchAuthSession Fails (e.g., Refresh Token Invalid)
        Amplify Auth-->>Axios Interceptor (Request): Throw Error
        Axios Interceptor (Request)->>Axios Interceptor (Response): Error Bubbles Up
        Axios Interceptor (Response)->>SignOut Logic: Trigger Sign Out (Handle Error)
        SignOut Logic->>Zustand Store: Clear User/Token
        SignOut Logic->>UI Component: Redirect/Show Auth UI
        Axios Interceptor (Response)-->>UI Component: Return Error
    else API Returns 401 (Unexpected)
        Backend API-->>Axios Interceptor (Response): 401 Unauthorized
        Axios Interceptor (Response)->>SignOut Logic: Trigger Sign Out (Handle 401)
        SignOut Logic->>Zustand Store: Clear User/Token
        SignOut Logic->>UI Component: Redirect/Show Auth UI
        Axios Interceptor (Response)-->>UI Component: Return Error
    end
```

## Summary

This plan centralizes token management within Axios interceptors. The request interceptor proactively ensures a fresh token using Amplify's `fetchAuthSession`, while the response interceptor handles unrecoverable errors by forcing a sign-out, creating a more resilient authentication experience.
