# Plan: UI Support for MassageProgramDetails from `tool_call` Stream Event

**Objective:** Implement UI components to display structured `MassageProgramDetails` received via a real-time `tool_call` stream event from the backend. This implementation will focus on real-time display and will not include persistence or loading from chat history for this specific component at this stage.

## Phase 1: Frontend Type Definitions ([`ui/types/index.ts`](ui/types/index.ts))

1.  **Define TypeScript Interfaces:**
    *   Create TypeScript interfaces corresponding to the Python Pydantic models in [`api/schemas/chat_schemas.py`](api/schemas/chat_schemas.py:414):
        *   `ProgramFlowDetails`
        *   `MassageSettingsDetails`
        *   `MassageProgramDetails` (embedding `ProgramFlowDetails` and `MassageSettingsDetails`)
2.  **Update `ChatStreamResponse` Interface:**
    *   Add `'tool_call'` to the `ChatStreamResponse.type` union.
    *   Specify that `ChatStreamResponse.content` for `type: 'tool_call'` will be a `string` (which is the JSON representation of `MassageProgramDetails`).
3.  **Update `Message` Interface:**
    *   Add a new role variant: `| 'tool_call'` to `Message.role`.
    *   Add an optional field: `massageProgramDetails?: MassageProgramDetails;` to store the parsed, structured data.

## Phase 2: Frontend Logic Update ([`ui/src/features/chat/chat-container.tsx`](ui/src/features/chat/chat-container.tsx))

1.  **Enhance `handleMessageSuccess` Function:**
    *   Add a `case 'tool_call':` block to the `switch` statement that handles incoming stream events.
    *   **Inside this case:**
        *   Parse the `response.content` (JSON string) into the `MassageProgramDetails` TypeScript interface.
        *   Locate the corresponding message in the `messages` state array using `response.message_id`. If no message with this ID exists, create a new message object.
        *   Set the `role` of this message object to `'tool_call'`.
        *   Populate the `massageProgramDetails` field of the message object with the newly parsed data.
        *   Consider clearing any standard `content` (text) for this message if the `MassageProgramDetailsCard` is intended to be the exclusive visual representation for this type of message.

## Phase 3: Create `MassageProgramDetailsCard.tsx` Component

1.  **New Component File:** Create a new React component file at [`ui/src/features/chat/MassageProgramDetailsCard.tsx`](ui/src/features/chat/MassageProgramDetailsCard.tsx).
2.  **Props:** This component will accept `details: MassageProgramDetails` as its primary prop.
3.  **Rendering Logic:**
    *   The card will iterate through the fields of the `MassageProgramDetails` object (e.g., Target Market, Duration, Focus Area, Problem/Desired Outcome, Program Flow, Massage Settings).
    *   For each field, it will display:
        *   A descriptive label (e.g., "Target Market:").
        *   The actual value of the field from the `details` prop.
        *   A clear visual indicator (e.g., text like "Given" accompanied by a checkmark icon ✓, or "Not Given" with a cross icon ✗; alternatively, background colors can be used as shown in the provided spreadsheet image) to denote whether the field contains a meaningful value or is `null`, `undefined`, or an empty collection.
    *   For nested objects like `program_flow` and `massage_settings`, the card will recursively render their sub-fields, applying the same "Given" / "Not Given" indication logic.

## Phase 4: Render `MassageProgramDetailsCard` ([`ui/src/features/chat/chat-container.tsx`](ui/src/features/chat/chat-container.tsx))

1.  **Import:** Import the newly created `MassageProgramDetailsCard` component into [`chat-container.tsx`](ui/src/features/chat/chat-container.tsx).
2.  **Conditional Rendering:** Within the JSX part of `chat-container.tsx` where messages are mapped and rendered:
    *   Add a condition: If `message.role === 'tool_call'` AND `message.massageProgramDetails` is populated, then render the `<MassageProgramDetailsCard details={message.massageProgramDetails} />`.

## Mermaid Diagram: Frontend Flow (Real-time)

```mermaid
graph TD
    A[EventSource Receives Stream Event] -- type: 'tool_call' --> B(handleMessageSuccess in chat-container.tsx);
    B -- response.content (JSON string of MassageProgramDetails) --> C{Parse JSON};
    C -- Success --> D{Update/Create Message in `messages` state with `role: 'tool_call'` and parsed `massageProgramDetails`};
    D --> E[React Renders Message List];
    E -- Iterate `messages` --> F{Is message.role === 'tool_call' AND message.massageProgramDetails exists?};
    F -- Yes --> G[Render MassageProgramDetailsCard with `massageProgramDetails`];
    G --> H[Card displays details + Given/Not Given status for each field];
    F -- No --> I[Render other message types];
    C -- Parse Error --> J(Log error);