from decouple import config, UndefinedValueError


class Settings:
    try:
        OPENAI_API_KEY = config("OPENAI_API_KEY")
        GEMINI_API_KEY = config("GEMINI_API_KEY")
        PINECONE_API_KEY = config("PINECONE_API_KEY")
        PINECONE_INDEX_NAME = config("PINECONE_INDEX_NAME", default="keoni-rag-local")
        AWS_ACCESS_KEY_ID = config("AWS_ACCESS_KEY_ID")
        AWS_SECRET_ACCESS_KEY = config("AWS_SECRET_ACCESS_KEY")
        AWS_REGION = config("AWS_REGION", default="us-east-1")
        AWS_S3_BUCKET_NAME = config("AWS_S3_BUCKET_NAME", default="osimuploads")

        # SQS Configuration
        SEGMENTATION_QUEUE_URL = config("SEGMENTATION_QUEUE_URL")
        SEGMENTATION_DLQ_URL = config("SEGMENTATION_DLQ_URL")

        # Segmentation Processing Configuration
        MAX_CONCURRENT_PHASES = config("MAX_CONCURRENT_PHASES", default=3, cast=int)
        MAX_CONCURRENT_CHUNKS = config("MAX_CONCURRENT_CHUNKS", default=5, cast=int)
        MAX_CONCURRENT_PROGRAMS = config("MAX_CONCURRENT_PROGRAMS", default=2, cast=int)
        MAX_MESSAGES_PER_BATCH = config("MAX_MESSAGES_PER_BATCH", default=2, cast=int)
        DATABASE_USERNAME = config("DATABASE_USERNAME")
        DATABASE_PASSWORD = config("DATABASE_PASSWORD")
        DATABASE_ENDPOINT = config("DATABASE_ENDPOINT")
        DATABASE_NAME = config("DATABASE_NAME")
        COGNITO_USER_POOL_ID = config("COGNITO_USER_POOL_ID")
        COGNITO_CLIENT_ID = config("COGNITO_CLIENT_ID")
        # Debug if found, otherwise use False
        DEBUG = config("DEBUG", default=False, cast=bool)

        # Sequence generation settings
        SEQUENCE_MODEL = config("SEQUENCE_MODEL", default="gpt-4")
        SEQUENCE_TEMPERATURE = config("SEQUENCE_TEMPERATURE", default=0, cast=float)
        SEQUENCE_MAX_TOKENS = config("SEQUENCE_MAX_TOKENS", default=2000, cast=int)
        SEQUENCE_TOP_P = config("SEQUENCE_TOP_P", default=1.0, cast=float)
        SEQUENCE_FREQUENCY_PENALTY = config(
            "SEQUENCE_FREQUENCY_PENALTY", default=0.0, cast=float
        )
        SEQUENCE_PRESENCE_PENALTY = config(
            "SEQUENCE_PRESENCE_PENALTY", default=0.0, cast=float
        )

        PROGRAM_EMBEDDING_NAMESPACE = "programs"
        PROGRAM_SEGMENTS_EMBEDDING_NAMESPACE = "program_segments"
        EMBEDDING_MODEL = "text-embedding-3-small"
        LOGFIRE_TOKEN = config(
            "LOGFIRE_TOKEN",
            default="pylf_v1_us_0PptBqK11TFPCHLbVSvTYhB38n7JTHZ4nbPdLMvHR8FD",
        )
        EXCLUDED_CATEGORIES = [
            "ai programs",
            "generated programs",
        ]

    except UndefinedValueError as e:
        raise ValueError(f"Missing required environment variable: {str(e)}")


settings = Settings()
