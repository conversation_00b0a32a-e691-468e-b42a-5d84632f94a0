[project]
name = "test-boto"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "alembic>=1.14.1",
    "asyncpg>=0.30.0",
    "boto3>=1.36.4",
    "gunicorn>=23.0.0",
    "httpx>=0.28.1",
    "langchain>=0.3.15",
    "langchain-aws>=0.2.11",
    "langchain-cli>=0.0.35",
    "langchain-community>=0.3.15",
    "langchain-openai>=0.3.2",
    "langchain-pinecone>=0.2.2",
    "langchain-unstructured>=0.1.6",
    "langgraph>=0.2.73",
    "langserve>=0.3.1",
    "openpyxl>=3.1.5",
    "pinecone>=5.4.2",
    "psycopg2-binary>=2.9.10",
    "pydantic-ai-slim[anthropic,logfire,openai,vertexai]>=0.1.4",
    "pypdf>=5.3.0",
    "pytest>=8.3.4",
    "pytest-asyncio>=0.25.2",
    "python-decouple>=3.8",
    "python-jose>=3.3.0",
    "python-multipart>=0.0.20",
    "sqlalchemy>=2.0.37",
    "unstructured[doc,pptx,xlsx]>=0.16.20",
    "uvicorn>=0.34.0",
]

[tool.pytest.ini_options]
asyncio_mode = "auto"
asyncio_default_fixture_loop_scope = "function"
