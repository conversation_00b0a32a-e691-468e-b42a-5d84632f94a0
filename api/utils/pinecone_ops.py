import os
import secrets
import logging
import shutil
import json
from config import settings
from sqlalchemy.orm import Session
from pinecone import Pinecone
from langchain_openai import OpenAIEmbeddings
from langchain_community.document_loaders import (
    PyPDFLoader,
    UnstructuredExcelLoader,
    UnstructuredCSVLoader,
)
from langchain_unstructured import UnstructuredLoader
from langchain.text_splitter import RecursiveCharacterTextSplitter
from models import File
from langchain_pinecone import PineconeVectorStore

logger = logging.getLogger(__name__)
openai_embeddings = OpenAIEmbeddings(api_key=settings.OPENAI_API_KEY)
pc = Pinecone(api_key=settings.PINECONE_API_KEY)


def get_pinecone_index():
    return pc.Index(settings.PINECONE_INDEX_NAME)


def get_vectordb():
    return PineconeVectorStore(index=get_pinecone_index(), embedding=openai_embeddings)


# Helper function to sanitize metadata for <PERSON>cone
def sanitize_metadata(metadata):
    """
    Sanitize metadata to ensure it only contains types supported by Pinecone:
    strings, numbers, booleans, or lists of strings.
    """
    result = {}
    for key, value in metadata.items():
        # Skip complex nested structures like 'links'
        if key in ["links"] and isinstance(value, dict):
            result[key] = json.dumps(value)

        # Convert link_urls and link_texts to simple lists
        elif key == "link_urls" and isinstance(value, list):
            result[key] = value
        elif key == "link_texts" and isinstance(value, list):
            result[key] = value
        # Handle other types
        elif isinstance(value, (str, int, float, bool)):
            result[key] = value
        elif isinstance(value, list) and all(isinstance(x, str) for x in value):
            result[key] = value
        else:
            # Convert other values to strings
            try:
                result[key] = str(value)
            except:
                # If conversion fails, skip this field
                logger.warning(f"Skipping metadata field {key} with unsupported type")

    return result


async def upsert_file(db: Session, source_id: int, file: File, temp_path: str):
    permanent_dir = os.path.join(os.path.dirname(temp_path), "permanent_files")
    os.makedirs(permanent_dir, exist_ok=True)

    # Create a more permanent file path with proper extension
    file_ext = os.path.splitext(file.name)[1]
    permanent_path = os.path.join(permanent_dir, f"{file.id}{file_ext}")
    # Copy the temp file to the permanent location
    shutil.copy2(temp_path, permanent_path)

    try:
        # Load document based on file type
        if file.name.endswith(".pdf"):
            loader = PyPDFLoader(permanent_path)
        elif file.name.endswith((".xlsx", ".xls")):
            loader = UnstructuredExcelLoader(permanent_path)
        elif file.name.endswith(".csv"):
            # Try different encodings for CSV files
            encodings_to_try = ["utf-8", "latin-1", "cp1252", "iso-8859-1"]
            for encoding in encodings_to_try:
                try:
                    loader = UnstructuredCSVLoader(
                        permanent_path,
                        encoding=encoding,
                    )
                    documents = loader.load()
                    break
                except UnicodeDecodeError:
                    continue
                except Exception as e:
                    logger.error(f"Error loading CSV with {encoding}: {e}")
                    continue
            else:
                raise Exception("Failed to load CSV with any supported encoding")
        elif file.name.lower().endswith((".doc", ".docx", ".ppt", ".pptx")):
            try:
                # For Office documents, try direct loading first
                loader = UnstructuredLoader(permanent_path)
                logger.info(f"Processing Office document: {permanent_path}")
            except Exception as e:
                logger.error(f"Error with UnstructuredLoader: {e}")
                raise Exception(f"Failed to process Office document: {str(e)}")
        else:
            loader = UnstructuredLoader(permanent_path)

        # Store extracted documents in json format
        extracted_data = []
        documents = loader.load()
        ids = []

        # Process documents and sanitize metadata
        processed_documents = []
        for doc in documents:
            doc.id = f"{source_id}-{file.id}-{secrets.token_hex(8)}"
            ids.append(doc.id)

            # Create a clean copy of metadata
            sanitized_metadata = sanitize_metadata(doc.metadata)
            sanitized_metadata["source_id"] = str(source_id)
            sanitized_metadata["file_id"] = str(file.id)
            sanitized_metadata["file_name"] = file.name

            # Create a new document with sanitized metadata for Pinecone
            doc.metadata = sanitized_metadata

            # Store original metadata for extraction
            if doc.page_content:
                extracted_data.append(
                    {"page_content": doc.page_content, "metadata": doc.metadata}
                )

            processed_documents.append(doc)

        text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=1000, chunk_overlap=200
        )
        splits = text_splitter.split_documents(processed_documents)
        vectordb = get_vectordb()
        ids = await vectordb.aadd_documents(
            ids=ids,
            documents=splits,
            namespace=source_id,
        )
        file.status = "active"
        file.vector_ids = ids
        file.extracted = extracted_data
        db.add(file)
        db.commit()
        logger.info(f"File {file.name} upserted successfully")

    except Exception as e:
        file.status = "failed"
        db.add(file)
        db.commit()
        logger.error(f"Error upserting file: {e}", exc_info=True)

    finally:
        # Clean up the temporary files
        try:
            os.remove(temp_path)
            if os.path.exists(permanent_path):
                os.remove(permanent_path)
        except Exception as e:
            logger.error(f"Error removing temporary files: {e}", exc_info=True)


def delete_file(file_id: str, source_id: str):
    prefix = f"{source_id}-{file_id}-"
    index = get_pinecone_index()
    for ids in index.list(prefix=prefix, namespace=str(source_id)):
        index.delete(ids=ids, namespace=str(source_id))


def delete_source(source_id: str):
    get_pinecone_index().delete(delete_all=True, namespace=str(source_id))
