Sub uDiva4_LegProgram_Macro()

''Revision_09 : Add condition for [Rotation] must end with position [PN/PM/PW]
''Revision_10 : Change ROLLING UP/DOWN BY DISTANCE (CM) Byte_B space condition by remove [LEN] -> cell3.value < 5
''Revision_11 : Return to Neutral Position(Waist /Waist Upper) : Remove all the [ROL]. Width set to "0"

    '''Declare variables'''
    Dim Byte_A, Byte_B, Byte_C, Byte_D, Byte_E, Byte_F, Byte_G As String
    Dim TapRoll As String
    Dim tTest As String, tTestR As Boolean, num As String, actionCycTimerDistance As String, isEnd As Boolean, stepNo <PERSON> String, subNum As Integer
    Dim cell1, cell2, cell3, cell4, cell5, cell6, cell7, cell8, cell9 As Range
    Dim StartRow, StartCol, NextRow, Row As Integer
    Dim desiredfont As String
        
    StartRow = 4
    StartCol = 2
    Row = 1
    
    desiredfont = "FangSong"
   
    isEnd = False

    Worksheets("Program").Select
    Sheets("Program Converted").Cells.Clear
    ActiveSheet.Cells(StartRow, StartCol).Select

    If ActiveCell.Value = "Start" Then
        Do While Not isEnd
            Worksheets("Program").Select
            StartRow = StartRow + 1
            ActiveSheet.Cells(StartRow, StartCol).Select

            ' Assign the corresponding cell values to variables
            Set cell1 = ActiveSheet.Cells(StartRow, 1)
            Set cell2 = ActiveSheet.Cells(StartRow, 2)
            Set cell3 = ActiveSheet.Cells(StartRow, 3)
            Set cell4 = ActiveSheet.Cells(StartRow, 4)
            Set cell5 = ActiveSheet.Cells(StartRow, 5)
            Set cell6 = ActiveSheet.Cells(StartRow, 6)
            Set cell7 = ActiveSheet.Cells(StartRow, 7)
            Set cell8 = ActiveSheet.Cells(StartRow, 8)
            Set cell9 = ActiveSheet.Cells(StartRow, 9)

            If ActiveCell.Value = "End" Then

                isEnd = True

            Else
                ' Check Step Number'
                stepNo = cell1.Value
                tTest = cell1.Value
                tTestR = IsNumeric(tTest)

        '///////////////////////////////////////////////Cant_Recog_Starts//////////////////////////////////////////////
            If Not (cell2 = "Roll to Upper limit" Or cell2 = "Roll to Neck Upper 1" Or cell2 = "Roll to Neck Upper" _
            Or cell2 = "Roll to Neck" Or cell2 = "Roll to Shoulder Upper" Or cell2 = "Roll to Shoulder" _
            Or cell2 = "Roll to Shoulder Lower" Or cell2 = "Roll to Shoulder Lower 1" Or cell2 = "Roll to Shoulder Blade Upper" _
            Or cell2 = "Roll to Shoulder Blade" Or cell2 = "Roll to Shoulder Blade Lower" Or cell2 = "Roll to Shoulder Blade Lower 1" _
            Or cell2 = "Roll to Back Upper" Or cell2 = "Roll to Back" Or cell2 = "Roll to Back Lower" _
            Or cell2 = "Roll to Waist Upper" Or cell2 = "Roll to Waist" Or cell2 = "Timer (Sec)" _
            Or cell2 = "Rolling up by distance (CM)" Or cell2 = "Rolling down by distance (CM)" Or cell2 = "Width change" _
            Or cell2 = "Cycle" Or cell2 = "3D:Return back to Neutral Position (Waist)" _
            Or cell2 = "3D:Return back to Neutral Position (Waist Upper)" Or cell2 = "3D:Return back to Neutral Position" _
            Or cell2 = "3D: Set 3D value" _
            Or cell2 = "Last min cooldown starts" Or cell2 = "Repeat loop starts" Or cell2 = "Subroutine" _
            Or cell2 = "Start Leg program: Energize" Or cell2 = "Start Leg program: Relax" Or cell2 = "Start Leg program: Balance" _
            Or cell2 = "Start Leg program: Circulation" Or cell2 = "Start Leg program: Sleep" _
            Or cell2 = "Start Leg program: Night" Or cell2 = "Start Leg program: Deep Relief" Or cell2 = "Start Leg program: Destress" _
            Or cell2 = "Start Leg program: Sleep Soft" Or cell2 = "End" Or cell2 = "Seat Vibration") Then
                       
              MsgBox "Please select an appropriate command from the dropdown list."
              Exit Sub
            End If
        '/////////////////////////////////////////////// Cant_Recog_Ends //////////////////////////////////////////////
        '===== ROLL TO POSITION - START ===============================================================================
        
                If cell2 = "Roll to Upper limit" Or cell2 = "Roll to Neck Upper 1" Or cell2 = "Roll to Neck Upper" _
                Or cell2 = "Roll to Neck" Or cell2 = "Roll to Shoulder Upper" Or cell2 = "Roll to Shoulder" _
                Or cell2 = "Roll to Shoulder Lower" Or cell2 = "Roll to Shoulder Lower 1" Or cell2 = "Roll to Shoulder Blade Upper" _
                Or cell2 = "Roll to Shoulder Blade" Or cell2 = "Roll to Shoulder Blade Lower" Or cell2 = "Roll to Shoulder Blade Lower 1" _
                Or cell2 = "Roll to Back Upper" Or cell2 = "Roll to Back" Or cell2 = "Roll to Back Lower" _
                Or cell2 = "Roll to Waist Upper" Or cell2 = "Roll to Waist" Then
                    Byte_A = "POS" & "," & Space(10)
                    Byte_E = "0" & "," & Space(4)
                    Byte_F = "0" & "," & Space(4)
                    
                    If cell2 = "Roll to Upper limit" Then                ' ----01 UPLIMIT
                    Byte_B = "POS_UPLMIT" & "," & Space(15)
                    
                    ElseIf cell2 = "Roll to Neck Upper 1" Then           '-----02 NECK UPPER1
                    Byte_B = "POS_NECK_UPPER1" & "," & Space(10)
                                            
                    ElseIf cell2 = "Roll to Neck Upper" Then             '-----03 NECK UPPER
                    Byte_B = "POS_NECK_UPPER" & "," & Space(11)
                    
                    ElseIf cell2 = "Roll to Neck" Then                   '-----04 NECK
                    Byte_B = "POS_NECK" & "," & Space(17)
                    
                    ElseIf cell2 = "Roll to Shoulder Upper" Then         '-----05 SHOULDER UPPER
                    Byte_B = "POS_SHOULDER_UPPER" & "," & Space(7)
                        
                    ElseIf cell2 = "Roll to Shoulder" Then               '-----06 SHOULDER
                    Byte_B = "POS_SHOULDER" & "," & Space(13)
                    
                    ElseIf cell2 = "Roll to Shoulder Lower" Then         '-----07 SHOULDER LOWER
                    Byte_B = "POS_SHOULDER_LOWER" & "," & Space(7)
                    
                    ElseIf cell2 = "Roll to Shoulder Lower 1" Then       '-----08 SHOULDER LOWER
                    Byte_B = "POS_SHOULDER_LOWER1" & "," & Space(6)
                    
                    ElseIf cell2 = "Roll to Shoulder Blade Upper" Then   '-----09 SHOULDER LOWER
                    Byte_B = "POS_SHDBLADE_UPPER" & "," & Space(7)
                    
                    ElseIf cell2 = "Roll to Shoulder Blade" Then         '-----10 SHOULDER BLADE
                    Byte_B = "POS_SHDBLADE" & "," & Space(13)
                    
                    ElseIf cell2 = "Roll to Shoulder Blade Lower" Then   '-----11 SHOULDER BLADE LOWER
                    Byte_B = "POS_SHDBLADE_LOWER" & "," & Space(7)
                
                    ElseIf cell2 = "Roll to Shoulder Blade Lower 1" Then '-----12 SHOULDER BLADE LOWER
                    Byte_B = "POS_SHDBLADE_LOWER_DOWN" & "," & Space(2)
                    
                    ElseIf cell2 = "Roll to Back Upper" Then             '-----13 BACK UPPER
                    Byte_B = "POS_BACK_UPPER" & "," & Space(11)
                    
                    ElseIf cell2 = "Roll to Back" Then                   '-----14 BACK
                    Byte_B = "POS_BACK" & "," & Space(17)
                    
                    ElseIf cell2 = "Roll to Back Lower" Then             '-----15 BACK LOWER
                    Byte_B = "POS_BACK_LOWER" & "," & Space(11)
                    
                    ElseIf cell2 = "Roll to Waist Upper" Then            '-----16 WAIST UPPER
                    Byte_B = "POS_WAIST_UPPER" & "," & Space(10)
                    
                    ElseIf cell2 = "Roll to Waist" Then                  '-----17 WAIST (DNLIMIT)
                    Byte_B = "POS_WAIST" & "," & Space(16)
                    
                    End If
                
        '---------- CHECK KNEADING/TAPPING SPEED -----------------------------------
                    
                    If cell4 = "" Then
                      MsgBox "Please select kneading/tapping/rolling action."
                        Exit Sub
                      
                        ElseIf cell4 = "Slow Knead" Then
                            Byte_C = "ML" & "," & Space(3)
                            Byte_D = "0" & "," & Space(4)
                            Byte_G = "M_MOMIROL"
                    
                        ElseIf cell4 = "Fast Knead" Then
                            Byte_C = "MH" & "," & Space(3)
                            Byte_D = "0" & "," & Space(4)
                            Byte_G = "M_MOMIROL"
                        
                        ElseIf cell4 = "Slow Tap" Then
                            Byte_C = "0" & "," & Space(4)
                            Byte_D = "TL" & "," & Space(3)
                            Byte_G = "M_TATAKIROL"

                        ElseIf cell4 = "Fast Tap" Then
                            Byte_C = "0" & "," & Space(4)
                            Byte_D = "TH" & "," & Space(3)
                            Byte_G = "M_TATAKIROL"
                        
                        ElseIf cell4 = "Slow Knead and Tap" Then
                            Byte_C = "ML" & "," & Space(3)
                            Byte_D = "TL" & "," & Space(3)
                            Byte_G = "M_MOMITATAKIROL"
                        
                        ElseIf cell4 = "Fast Knead and Tap" Then
                            Byte_C = "MH" & "," & Space(3)
                            Byte_D = "TH" & "," & Space(3)
                            Byte_G = "M_MOMITATAKIROL"
                        
                        ElseIf cell4 = "Slow Knead & Fast Tap" Then
                            Byte_C = "ML" & "," & Space(3)
                            Byte_D = "TH" & "," & Space(3)
                            Byte_G = "M_MOMITATAKIROL"
                        
                        ElseIf cell4 = "Fast Knead & Slow Tap" Then
                            Byte_C = "MH" & "," & Space(3)
                            Byte_D = "TL" & "," & Space(3)
                            Byte_G = "M_MOMITATAKIROL"
                        
                        ElseIf cell4 = "Rolling" Then
                            Byte_C = "0" & "," & Space(4)
                            Byte_D = "0" & "," & Space(4)
                            Byte_G = "M_ROL"
                        End If
        
                '*********** CHECK PROHIBITED CELL - START *******************************************
                          If cell3 <> "" Or cell6 <> "" Then
                    MsgBox "Please do not enter any value for [Cycle],  [Width set] ."
                        Exit Sub
                        End If
                '*********** CHECK PROHIBITED CELL - END *********************************************
        
                  End If
        '===== ROLL TO POSITION - END ================================================================================
        '===== TIMER - START =================================================================================================
        
                If cell2 = "Timer (Sec)" Then
                 Byte_A = "TIMER" & "," & Space(8)
                 Byte_F = "0" & "," & Space(4)
                                
                    If cell2 = "" Then
                    MsgBox "Please select an appropriate command from the dropdown list."
                    Exit Sub
                 
                    ElseIf Len(cell3.Value) = 1 Then
                    Byte_B = cell3.Value & "," & Space(24)
                 
                    Else
                    Byte_B = cell3.Value & "," & Space(23)
                    End If
                  
         '----------------- CHECK KNEADING/TAPPING SPEED -----------------------------------
                    
                        If cell4 = "" Then
                            Byte_C = "0" & "," & Space(4)
                            Byte_D = "0" & "," & Space(4)
                            Byte_G = "0"
                            
                            ElseIf cell4 = "Rolling" Then
                            MsgBox "Do not use [Rolling] for Timer"
                            Exit Sub
                    
                            ElseIf cell4 = "Slow Knead" Then
                                Byte_C = "ML" & "," & Space(3)
                                Byte_D = "0" & "," & Space(4)
                                Byte_G = "M_MOMI"
                    
                            ElseIf cell4 = "Fast Knead" Then
                                Byte_C = "MH" & "," & Space(3)
                                Byte_D = "0" & "," & Space(4)
                                Byte_G = "M_MOMI"
                            
                            ElseIf cell4 = "Slow Tap" Then
                                Byte_C = "0" & "," & Space(4)
                                Byte_D = "TL" & "," & Space(3)
                                Byte_G = "M_TATAKI"

                            ElseIf cell4 = "Fast Tap" Then
                                Byte_C = "0" & "," & Space(4)
                                Byte_D = "TH" & "," & Space(3)
                                Byte_G = "M_TATAKI"
                        
                            ElseIf cell4 = "Slow Knead and Tap" Then
                                Byte_C = "ML" & "," & Space(3)
                                Byte_D = "TL" & "," & Space(3)
                                Byte_G = "M_MOMITATAKI"
                            
                            ElseIf cell4 = "Fast Knead and Tap" Then
                                Byte_C = "MH" & "," & Space(3)
                                Byte_D = "TH" & "," & Space(3)
                                Byte_G = "M_MOMITATAKI"
                            
                            ElseIf cell4 = "Slow Knead & Fast Tap" Then
                                Byte_C = "ML" & "," & Space(3)
                                Byte_D = "TH" & "," & Space(3)
                                Byte_G = "M_MOMITATAKI"
                            
                            ElseIf cell4 = "Fast Knead & Slow Tap" Then
                                Byte_C = "MH" & "," & Space(3)
                                Byte_D = "TL" & "," & Space(3)
                                Byte_G = "M_MOMITATAKI"
                            End If

        '----------------- CHECK WIDTH -----------------------------------
                                
                                If cell6 = "" Then
                                    Byte_E = "0" & "," & Space(4)
                                    
                                    ElseIf cell6 = "Narrow" Then
                                    Byte_E = "PN" & "," & Space(3)
                                    
                                    ElseIf cell6 = "Mid" Then
                                    Byte_E = "PM" & "," & Space(3)
                                    
                                    ElseIf cell6 = "Wide" Then
                                    Byte_E = "PW" & "," & Space(3)
                                End If
                                
                '*********** CHECK PROHIBITED CELL - START ******************************************
                          If cell5 <> "" Then
                    MsgBox "Please do not enter any value for [3D position] ."
                        Exit Sub
                        End If
                '*********** CHECK PROHIBITED CELL - END ********************************************
                
                    End If
        '===== TIMER - END =================================================================================================
        '===== ROLLING UP BY DISTANCE (CM) - START =========================================================================
                
                If cell2 = "Rolling up by distance (CM)" Then
                    Byte_A = "POS_UP" & "," & Space(7)
                    Byte_E = "0" & "," & Space(4)
                    Byte_F = "0" & "," & Space(4)
                
                '----------------- CHECK ROLLING DISTANCE -----------------------------------------
                    If cell3 = "" Then
                        MsgBox "Please key in the distance in CM."
                        Exit Sub
                        
                        ElseIf cell3.Value < 5 Then
                            Byte_B = cell3.Value / 0.5 & "," & Space(24)
                 
                        Else
                            Byte_B = cell3.Value / 0.5 & "," & Space(23)
                    End If
                '----------------- CHECK KNEADING/TAPPING SPEED -----------------------------------
                    If cell4 = "" Then
                      MsgBox "Please select kneading/tapping/rolling action."
                        Exit Sub
                      
                        ElseIf cell4 = "Slow Knead" Then
                            Byte_C = "ML" & "," & Space(3)
                            Byte_D = "0" & "," & Space(4)
                            Byte_G = "M_MOMIROL"
                    
                        ElseIf cell4 = "Fast Knead" Then
                            Byte_C = "MH" & "," & Space(3)
                            Byte_D = "0" & "," & Space(4)
                            Byte_G = "M_MOMIROL"
                        
                        ElseIf cell4 = "Slow Tap" Then
                            Byte_C = "0" & "," & Space(4)
                            Byte_D = "TL" & "," & Space(3)
                            Byte_G = "M_TATAKIROL"

                        ElseIf cell4 = "Fast Tap" Then
                            Byte_C = "0" & "," & Space(4)
                            Byte_D = "TH" & "," & Space(3)
                            Byte_G = "M_TATAKIROL"
                        
                        ElseIf cell4 = "Slow Knead and Tap" Then
                            Byte_C = "ML" & "," & Space(3)
                            Byte_D = "TL" & "," & Space(3)
                            Byte_G = "M_MOMITATAKIROL"
                        
                        ElseIf cell4 = "Fast Knead and Tap" Then
                            Byte_C = "MH" & "," & Space(3)
                            Byte_D = "TH" & "," & Space(3)
                            Byte_G = "M_MOMITATAKIROL"
                        
                        ElseIf cell4 = "Slow Knead & Fast Tap" Then
                            Byte_C = "ML" & "," & Space(3)
                            Byte_D = "TH" & "," & Space(3)
                            Byte_G = "M_MOMITATAKIROL"
                        
                        ElseIf cell4 = "Fast Knead & Slow Tap" Then
                            Byte_C = "MH" & "," & Space(3)
                            Byte_D = "TL" & "," & Space(3)
                            Byte_G = "M_MOMITATAKIROL"
                        
                        ElseIf cell4 = "Rolling" Then
                            Byte_C = "0" & "," & Space(4)
                            Byte_D = "0" & "," & Space(4)
                            Byte_G = "M_ROL"
                        End If
                        
                '*********** CHECK PROHIBITED CELL - START *******************************************
                          If cell5 <> "" Or cell6 <> "" Then
                    MsgBox "Please do not enter any value for [3D position], [Width set] ."
                        Exit Sub
                        End If
                '*********** CHECK PROHIBITED CELL - END *********************************************
                
            End If
        '===== ROLLING UP BY DISTANCE (CM) - END =========================================================================
        '===== ROLLING DOWN BY DISTANCE (CM) - START =====================================================================
                
                If cell2 = "Rolling down by distance (CM)" Then
                    Byte_A = "POS_DOWN" & "," & Space(5)
                    Byte_E = "0" & "," & Space(4)
                    Byte_F = "0" & "," & Space(4)
                
                '----------------- CHECK ROLLING DISTANCE -----------------------------------------
                    If cell3 = "" Then
                        MsgBox "Please key in the distance in CM."
                        Exit Sub
                        
                        ElseIf cell3.Value < 5 Then
                            Byte_B = cell3.Value / 0.5 & "," & Space(24)
                 
                        Else
                            Byte_B = cell3.Value / 0.5 & "," & Space(23)
                    End If
                '----------------- CHECK KNEADING/TAPPING SPEED -----------------------------------
                    If cell4 = "" Then
                      MsgBox "Please select kneading/tapping/rolling action."
                        Exit Sub
                      
                        ElseIf cell4 = "Slow Knead" Then
                            Byte_C = "ML" & "," & Space(3)
                            Byte_D = "0" & "," & Space(4)
                            Byte_G = "M_MOMIROL"
                    
                        ElseIf cell4 = "Fast Knead" Then
                            Byte_C = "MH" & "," & Space(3)
                            Byte_D = "0" & "," & Space(4)
                            Byte_G = "M_MOMIROL"
                        
                        ElseIf cell4 = "Slow Tap" Then
                            Byte_C = "0" & "," & Space(4)
                            Byte_D = "TL" & "," & Space(3)
                            Byte_G = "M_TATAKIROL"

                        ElseIf cell4 = "Fast Tap" Then
                            Byte_C = "0" & "," & Space(4)
                            Byte_D = "TH" & "," & Space(3)
                            Byte_G = "M_TATAKIROL"
                        
                        ElseIf cell4 = "Slow Knead and Tap" Then
                            Byte_C = "ML" & "," & Space(3)
                            Byte_D = "TL" & "," & Space(3)
                            Byte_G = "M_MOMITATAKIROL"
                        
                        ElseIf cell4 = "Fast Knead and Tap" Then
                            Byte_C = "MH" & "," & Space(3)
                            Byte_D = "TH" & "," & Space(3)
                            Byte_G = "M_MOMITATAKIROL"
                        
                        ElseIf cell4 = "Slow Knead & Fast Tap" Then
                            Byte_C = "ML" & "," & Space(3)
                            Byte_D = "TH" & "," & Space(3)
                            Byte_G = "M_MOMITATAKIROL"
                        
                        ElseIf cell4 = "Fast Knead & Slow Tap" Then
                            Byte_C = "MH" & "," & Space(3)
                            Byte_D = "TL" & "," & Space(3)
                            Byte_G = "M_MOMITATAKIROL"
                        
                        ElseIf cell4 = "Rolling" Then
                            Byte_C = "0" & "," & Space(4)
                            Byte_D = "0" & "," & Space(4)
                            Byte_G = "M_ROL"
                        End If
                        
                '*********** CHECK PROHIBITED CELL - START *******************************************
                          If cell5 <> "" Or cell6 <> "" Then
                    MsgBox "Please do not enter any value for [3D position], [Width set] ."
                        Exit Sub
                        End If
                '*********** CHECK PROHIBITED CELL - END *********************************************
                
            End If
        '===== ROLLING DOWN BY DISTANCE (CM) - END =========================================================================
        '===== WIDTH CHANGE - START ========================================================================================
                
                If cell2 = "Width change" Then
                    Byte_A = "WIDTH_SET" & "," & Space(4)
                    Byte_B = "0" & "," & Space(24)
                    Byte_C = "MH" & "," & Space(3)
                    Byte_D = "0" & "," & Space(4)
                    Byte_F = "0" & "," & Space(4)
                    Byte_G = "M_MOMI"
                    
                    If cell6 = "" Then
                        MsgBox "Please select width [Narrow / Mid / Wide]"
                        Exit Sub
                                    
                        ElseIf cell6 = "Narrow" Then
                            Byte_E = "PN" & "," & Space(3)
                     
                        ElseIf cell6 = "Mid" Then
                            Byte_E = "PM" & "," & Space(3)
                                    
                        ElseIf cell6 = "Wide" Then
                            Byte_E = "PW" & "," & Space(3)
                End If
                
                '*********** CHECK PROHIBITED CELL - START *******************************************
                          If cell3 <> "" Or cell4 <> "" Or cell5 <> "" Then
                    MsgBox "Please do not enter any value for [Cycle], [Speed], [3D position]."
                        Exit Sub
                        End If
                '*********** CHECK PROHIBITED CELL - END *********************************************
            
            End If
        '===== WIDTH CHANGE - END ========================================================================================
        '===== CYCLE - START =============================================================================================
                
                If cell2 = "Cycle" Then
                    Byte_A = "ROTATION" & "," & Space(5)
                    Byte_F = "0" & "," & Space(4)
        
        '----------------- CHECK NO.OF CYCLE DISTANCE -----------------------------------------
                    If cell3 = "" Then
                        MsgBox "Please key in the no.of cycle."
                        Exit Sub
                        
                        ElseIf Len(cell3.Value) = 1 Then
                            Byte_B = cell3.Value & "," & Space(24)
                 
                        Else
                            Byte_B = cell3.Value & "," & Space(23)
                    End If
       '----------------- CHECK KNEADING/TAPPING SPEED -----------------------------------
                       If cell4 = "" Then
                           MsgBox "Please select action."
                           Exit Sub
                         
                           ElseIf cell4 = "Slow Tap" Or cell4 = "Fast Tap" Then
                               MsgBox "Unable to perform tapping alone; must be performed with any kneading action."
                               Exit Sub
                               
                               ElseIf cell4 = "Rolling" Then
                                   MsgBox "Unable to perform rolling for cycle command."
                                   Exit Sub
                           
                                   ElseIf cell4 = "Slow Knead" Then
                                       Byte_C = "ML" & "," & Space(3)
                                       Byte_D = "0" & "," & Space(4)
                                       Byte_G = "M_MOMI"
                               
                                   ElseIf cell4 = "Fast Knead" Then
                                       Byte_C = "MH" & "," & Space(3)
                                       Byte_D = "0" & "," & Space(4)
                                       Byte_G = "M_MOMI"
    
                                   ElseIf cell4 = "Slow Knead and Tap" Then
                                       Byte_C = "ML" & "," & Space(3)
                                       Byte_D = "TL" & "," & Space(3)
                                       Byte_G = "M_MOMITATAKI"
                                   
                                   ElseIf cell4 = "Fast Knead and Tap" Then
                                       Byte_C = "MH" & "," & Space(3)
                                       Byte_D = "TH" & "," & Space(3)
                                       Byte_G = "M_MOMITATAKI"
                                   
                                   ElseIf cell4 = "Slow Knead & Fast Tap" Then
                                       Byte_C = "ML" & "," & Space(3)
                                       Byte_D = "TH" & "," & Space(3)
                                       Byte_G = "M_MOMITATAKI"
                                   
                                   ElseIf cell4 = "Fast Knead & Slow Tap" Then
                                       Byte_C = "MH" & "," & Space(3)
                                       Byte_D = "TL" & "," & Space(3)
                                       Byte_G = "M_MOMITATAKI"
                                   End If
                         
        '----------------- CHECK WIDTH -----------------------------------
                                   If cell6 = "" Then
                                       MsgBox "Please select width [Narrow / Mid / Wide]"
                                       Exit Sub
                                           
                                       ElseIf cell6 = "Narrow" Then
                                       Byte_E = "PN" & "," & Space(3)
                                           
                                       ElseIf cell6 = "Mid" Then
                                       Byte_E = "PM" & "," & Space(3)
                                           
                                       ElseIf cell6 = "Wide" Then
                                       Byte_E = "PW" & "," & Space(3)
                                   End If
                                   
                '*********** CHECK PROHIBITED CELL - START *******************************************
                          If cell5 <> "" Then
                    MsgBox "Please do not enter any value for [3D position]."
                        Exit Sub
                        End If
                '*********** CHECK PROHIBITED CELL - END *********************************************
                
                End If
                    
        '===== CYCLE - END =============================================================================================
        '===== 3D RETURN BACK TO NEUTRAL POSITION (WAIST) - START ======================================================
                
                If cell2 = "3D:Return back to Neutral Position (Waist)" Then
                    Byte_A = "POS_3D" & "," & Space(7)
                    Byte_B = "FIT_POS_WAIST" & "," & Space(12)
                    Byte_E = "0" & "," & Space(4)
                    Byte_F = "0" & "," & Space(4)
                    
               '---------- CHECK KNEADING/TAPPING SPEED -----------------------------------
                    If cell4 = "" Then
                        Byte_C = "0" & "," & Space(4)
                        Byte_D = "0" & "," & Space(4)
                        Byte_G = "0"
                      
                        ElseIf cell4 = "Rolling" Then
                            MsgBox "Unable to perform rolling for this command"
                            Exit Sub
                            
                            ElseIf cell4 = "Slow Knead" Then
                                Byte_C = "ML" & "," & Space(3)
                                Byte_D = "0" & "," & Space(4)
                                Byte_G = "M_MOMI"
                        
                            ElseIf cell4 = "Fast Knead" Then
                                Byte_C = "MH" & "," & Space(3)
                                Byte_D = "0" & "," & Space(4)
                                Byte_G = "M_MOMI"
                            
                            ElseIf cell4 = "Slow Tap" Then
                                Byte_C = "0" & "," & Space(4)
                                Byte_D = "TL" & "," & Space(3)
                                Byte_G = "M_TATAKI"
    
                            ElseIf cell4 = "Fast Tap" Then
                                Byte_C = "0" & "," & Space(4)
                                Byte_D = "TH" & "," & Space(3)
                                Byte_G = "M_TATAKI"
                            
                            ElseIf cell4 = "Slow Knead and Tap" Then
                                Byte_C = "ML" & "," & Space(3)
                                Byte_D = "TL" & "," & Space(3)
                                Byte_G = "M_MOMITATAKI"
                            
                            ElseIf cell4 = "Fast Knead and Tap" Then
                                Byte_C = "MH" & "," & Space(3)
                                Byte_D = "TH" & "," & Space(3)
                                Byte_G = "M_MOMITATAKI"
                            
                            ElseIf cell4 = "Slow Knead & Fast Tap" Then
                                Byte_C = "ML" & "," & Space(3)
                                Byte_D = "TH" & "," & Space(3)
                                Byte_G = "M_MOMITATAKI"
                            
                            ElseIf cell4 = "Fast Knead & Slow Tap" Then
                                Byte_C = "MH" & "," & Space(3)
                                Byte_D = "TL" & "," & Space(3)
                                Byte_G = "M_MOMITATAKI"
                        End If
                        
                '*********** CHECK PROHIBITED CELL - START *******************************************
                          If cell3 <> "" Or cell6 <> "" Then
                    MsgBox "Please do not enter any value for [Cycle], [Width set] ."
                        Exit Sub
                        End If
                '*********** CHECK PROHIBITED CELL - END *********************************************
                  
                  End If
        '===== 3D RETURN BACK TO NEUTRAL POSITION (WAIST) - END ========================================================
        '===== 3D RETURN BACK TO NEUTRAL POSITION (WAIST UPPER) - START ================================================
                
                If cell2 = "3D:Return back to Neutral Position (Waist Upper)" Then
                    Byte_A = "POS_3D" & "," & Space(7)
                    Byte_B = "FIT_POS_WAIST_UPPER" & "," & Space(6)
                    Byte_E = "0" & "," & Space(4)
                    Byte_F = "0" & "," & Space(4)
                    
               '---------- CHECK KNEADING/TAPPING SPEED -----------------------------------
                    If cell4 = "" Then
                        Byte_C = "0" & "," & Space(4)
                        Byte_D = "0" & "," & Space(4)
                        Byte_G = "0"
                      
                        ElseIf cell4 = "Rolling" Then
                            MsgBox "Unable to perform rolling for this command"
                            Exit Sub
                            
                            ElseIf cell4 = "Slow Knead" Then
                                Byte_C = "ML" & "," & Space(3)
                                Byte_D = "0" & "," & Space(4)
                                Byte_G = "M_MOMI"
                        
                            ElseIf cell4 = "Fast Knead" Then
                                Byte_C = "MH" & "," & Space(3)
                                Byte_D = "0" & "," & Space(4)
                                Byte_G = "M_MOMI"
                            
                            ElseIf cell4 = "Slow Tap" Then
                                Byte_C = "0" & "," & Space(4)
                                Byte_D = "TL" & "," & Space(3)
                                Byte_G = "M_TATAKI"
    
                            ElseIf cell4 = "Fast Tap" Then
                                Byte_C = "0" & "," & Space(4)
                                Byte_D = "TH" & "," & Space(3)
                                Byte_G = "M_TATAKI"
                            
                            ElseIf cell4 = "Slow Knead and Tap" Then
                                Byte_C = "ML" & "," & Space(3)
                                Byte_D = "TL" & "," & Space(3)
                                Byte_G = "M_MOMITATAKI"
                            
                            ElseIf cell4 = "Fast Knead and Tap" Then
                                Byte_C = "MH" & "," & Space(3)
                                Byte_D = "TH" & "," & Space(3)
                                Byte_G = "M_MOMITATAKI"
                            
                            ElseIf cell4 = "Slow Knead & Fast Tap" Then
                                Byte_C = "ML" & "," & Space(3)
                                Byte_D = "TH" & "," & Space(3)
                                Byte_G = "M_MOMITATAKI"
                            
                            ElseIf cell4 = "Fast Knead & Slow Tap" Then
                                Byte_C = "MH" & "," & Space(3)
                                Byte_D = "TL" & "," & Space(3)
                                Byte_G = "M_MOMITATAKI"
                         End If
                         
                '*********** CHECK PROHIBITED CELL - START *******************************************
                          If cell3 <> "" Or cell6 <> "" Then
                    MsgBox "Please do not enter any value for [Cycle], [Width set] ."
                        Exit Sub
                        End If
                '*********** CHECK PROHIBITED CELL - END *********************************************
                  
                  End If
        '===== 3D RETURN BACK TO NEUTRAL POSITION (WAIST UPPER)- END ===================================================
        '===== 3D RETURN BACK TO NEUTRAL POSITION - START ==============================================================
                If cell2 = "3D:Return back to Neutral Position" Then
                    Byte_A = "POS_3D" & "," & Space(7)
                    Byte_E = "0" & "," & Space(4)
                    Byte_F = "0" & "," & Space(4)
                    
        '----------------- CHECK FIT POS POSITION  -----------------------------------------
                    If cell5 = "" Then
                        MsgBox "Please provide the 3D value."
                        Exit Sub
                        
                        ElseIf Len(cell5.Value) = 1 Then
                            Byte_B = "FIT_POS" & cell5.Value & "," & Space(17)
                 
                        Else
                            Byte_B = "FIT_POS" & cell5.Value & "," & Space(16)
                    End If
                    
        '---------- CHECK KNEADING/TAPPING SPEED -----------------------------------
                    If cell4 = "" Then
                        Byte_C = "0" & "," & Space(4)
                        Byte_D = "0" & "," & Space(4)
                        Byte_G = "0"
                      
                        ElseIf cell4 = "Rolling" Then
                            MsgBox "Unable to perform rolling for this command"
                            Exit Sub
                            
                            ElseIf cell4 = "Slow Knead" Then
                                Byte_C = "ML" & "," & Space(3)
                                Byte_D = "0" & "," & Space(4)
                                Byte_G = "M_MOMI"
                        
                            ElseIf cell4 = "Fast Knead" Then
                                Byte_C = "MH" & "," & Space(3)
                                Byte_D = "0" & "," & Space(4)
                                Byte_G = "M_MOMI"
                            
                            ElseIf cell4 = "Slow Tap" Then
                                Byte_C = "0" & "," & Space(4)
                                Byte_D = "TL" & "," & Space(3)
                                Byte_G = "M_TATAKI"
    
                            ElseIf cell4 = "Fast Tap" Then
                                Byte_C = "0" & "," & Space(4)
                                Byte_D = "TH" & "," & Space(3)
                                Byte_G = "M_TATAKI"
                            
                            ElseIf cell4 = "Slow Knead and Tap" Then
                                Byte_C = "ML" & "," & Space(3)
                                Byte_D = "TL" & "," & Space(3)
                                Byte_G = "M_MOMITATAKI"
                            
                            ElseIf cell4 = "Fast Knead and Tap" Then
                                Byte_C = "MH" & "," & Space(3)
                                Byte_D = "TH" & "," & Space(3)
                                Byte_G = "M_MOMITATAKI"
                            
                            ElseIf cell4 = "Slow Knead & Fast Tap" Then
                                Byte_C = "ML" & "," & Space(3)
                                Byte_D = "TH" & "," & Space(3)
                                Byte_G = "M_MOMITATAKI"
                            
                            ElseIf cell4 = "Fast Knead & Slow Tap" Then
                                Byte_C = "MH" & "," & Space(3)
                                Byte_D = "TL" & "," & Space(3)
                                Byte_G = "M_MOMITATAKI"
                        End If
                        
                '*********** CHECK PROHIBITED CELL - START *******************************************
                          If cell3 <> "" Or cell6 <> "" Then
                    MsgBox "Please do not enter any value for [Cycle],[Width set] ."
                        Exit Sub
                        End If
                '*********** CHECK PROHIBITED CELL - END *********************************************
                    
                    End If
        '===== 3D RETURN BACK TO NEUTRAL POSITION - END ================================================================
        '===== 3D : SET 3D VALUE - START ===============================================================================
                
                If cell2 = "3D: Set 3D value" Then
                    Byte_A = "POS_3D" & "," & Space(7)
                    Byte_E = "0" & "," & Space(4)
                    Byte_F = "0" & "," & Space(4)
                    
        '----------------- CHECK FIT POS POSITION  -----------------------------------------
                    If cell5 = "" Then
                        MsgBox "Please provide the 3D value."
                        Exit Sub
                        
                        ElseIf Len(cell5.Value) = 1 Then
                            Byte_B = "FIT_POS" & cell5.Value & "," & Space(17)
                 
                        Else
                            Byte_B = "FIT_POS" & cell5.Value & "," & Space(16)
                    End If
                    
        '---------- CHECK KNEADING/TAPPING SPEED -----------------------------------
                    If cell4 = "" Then
                        Byte_C = "0" & "," & Space(4)
                        Byte_D = "0" & "," & Space(4)
                        Byte_G = "0"
                      
                        ElseIf cell4 = "Rolling" Then
                            MsgBox "Unable to perform rolling for this command"
                            Exit Sub
                            
                            ElseIf cell4 = "Slow Knead" Then
                                Byte_C = "ML" & "," & Space(3)
                                Byte_D = "0" & "," & Space(4)
                                Byte_G = "M_MOMI"
                        
                            ElseIf cell4 = "Fast Knead" Then
                                Byte_C = "MH" & "," & Space(3)
                                Byte_D = "0" & "," & Space(4)
                                Byte_G = "M_MOMI"
                            
                            ElseIf cell4 = "Slow Tap" Then
                                Byte_C = "0" & "," & Space(4)
                                Byte_D = "TL" & "," & Space(3)
                                Byte_G = "M_TATAKI"
    
                            ElseIf cell4 = "Fast Tap" Then
                                Byte_C = "0" & "," & Space(4)
                                Byte_D = "TH" & "," & Space(3)
                                Byte_G = "M_TATAKI"
                            
                            ElseIf cell4 = "Slow Knead and Tap" Then
                                Byte_C = "ML" & "," & Space(3)
                                Byte_D = "TL" & "," & Space(3)
                                Byte_G = "M_MOMITATAKI"
                            
                            ElseIf cell4 = "Fast Knead and Tap" Then
                                Byte_C = "MH" & "," & Space(3)
                                Byte_D = "TH" & "," & Space(3)
                                Byte_G = "M_MOMITATAKI"
                            
                            ElseIf cell4 = "Slow Knead & Fast Tap" Then
                                Byte_C = "ML" & "," & Space(3)
                                Byte_D = "TH" & "," & Space(3)
                                Byte_G = "M_MOMITATAKI"
                            
                            ElseIf cell4 = "Fast Knead & Slow Tap" Then
                                Byte_C = "MH" & "," & Space(3)
                                Byte_D = "TL" & "," & Space(3)
                                Byte_G = "M_MOMITATAKI"
                        End If
                        
                '*********** CHECK PROHIBITED CELL - START *******************************************
                          If cell3 <> "" Or cell6 <> "" Then
                    MsgBox "Please do not enter any value for [Cycle],[Width set] ."
                        Exit Sub
                        End If
                '*********** CHECK PROHIBITED CELL - END *********************************************
                    
                    End If
        '===== 3D : SET 3D VALUE - END =================================================================================
        '===== LAST MIN COOLDOWN STARTS - START=========================================================================
                
                If cell2 = "Last min cooldown starts" Then
                    Byte_A = "COOLDOWN" & "," & Space(5)
                    Byte_B = "60" & "," & Space(23)
                    Byte_C = "0" & "," & Space(4)
                    Byte_D = "0" & "," & Space(4)
                    Byte_E = "0" & "," & Space(4)
                    Byte_F = "0" & "," & Space(4)
                    Byte_G = "0"
                
                '*********** CHECK PROHIBITED CELL - START *******************************************
                          If cell3 <> "" Or cell4 <> "" Or cell5 <> "" Or cell6 <> "" Then
                    MsgBox "Please do not enter any value for [Cycle], [Speed], [3D position], [Width set]."
                        Exit Sub
                        End If
                '*********** CHECK PROHIBITED CELL - END *********************************************
                
                End If
                
        '===== LAST MIN COOLDOWN STARTS - END ==========================================================================
        '===== REPEAT LOOP STARTS - START===============================================================================
                
                If cell2 = "Repeat loop starts" Then
                    Byte_A = "POS_RET" & "," & Space(6)
                    Byte_B = "0" & "," & Space(24)
                    Byte_C = "0" & "," & Space(4)
                    Byte_D = "0" & "," & Space(4)
                    Byte_E = "0" & "," & Space(4)
                    Byte_F = "0" & "," & Space(4)
                    Byte_G = "0"
                
                '*********** CHECK PROHIBITED CELL - START *******************************************
                          If cell3 <> "" Or cell4 <> "" Or cell5 <> "" Or cell6 <> "" Then
                    MsgBox "Please do not enter any value for [Cycle], [Speed], [3D position], [Width set]."
                        Exit Sub
                        End If
                '*********** CHECK PROHIBITED CELL - END *********************************************
                
                End If
                
        '===== REPEAT LOOP STARTS - END ================================================================================
        '===== SUBROUTINE - START=======================================================================================
                If cell2 = "Subroutine" Then
                    Byte_A = "COMPLEX" & "," & Space(6)
                    Byte_B = "1" & "," & Space(24)
                    Byte_C = "0" & "," & Space(4)
                    Byte_D = "0" & "," & Space(4)
                    Byte_E = "0" & "," & Space(4)
                    Byte_F = "0" & "," & Space(4)
                    
                    If cell3 = "" Then
                        MsgBox "Please provide subroutine number."
                        Exit Sub
                        
                    ElseIf Len(cell3.Value) = 1 Then
                        Byte_G = "SUB_0" & cell3.Value
                 
                    Else
                        Byte_G = "SUB_" & cell3.Value
                End If
                
            '*********** CHECK PROHIBITED CELL - START *******************************************
                   '       If cell4 <> "" Or cell5 <> "" Or cell6 <> "" Then
                   ' MsgBox "Please do not enter any value for [Speed], [3D position], [Width set]."
                     '   Exit Sub
                    '    End If
            '*********** CHECK PROHIBITED CELL - END *********************************************
            
            End If
        '===== SUBROUTINE - END ========================================================================================
        '===== START LEG PROGRAM - START================================================================================
                
                If cell2 = "Start Leg program: Energize" Then           ' -------------- Energize
                    Byte_A = "FOOT_ON" & "," & Space(6)
                    Byte_B = "0" & "," & Space(24)
                    Byte_C = "0" & "," & Space(4)
                    Byte_D = "0" & "," & Space(4)
                    Byte_E = "0" & "," & Space(4)
                    Byte_F = "0" & "," & Space(4)
                    Byte_G = "ASI_MODE_AUTO1"
                    
                    ElseIf cell2 = "Start Leg program: Relax" Then      ' -------------- Relax
                    Byte_A = "FOOT_ON" & "," & Space(6)
                    Byte_B = "0" & "," & Space(24)
                    Byte_C = "0" & "," & Space(4)
                    Byte_D = "0" & "," & Space(4)
                    Byte_E = "0" & "," & Space(4)
                    Byte_F = "0" & "," & Space(4)
                    Byte_G = "ASI_MODE_AUTO2"
                    
                    ElseIf cell2 = "Start Leg program: Balance" Then     ' -------------- Balance
                    Byte_A = "FOOT_ON" & "," & Space(6)
                    Byte_B = "0" & "," & Space(24)
                    Byte_C = "0" & "," & Space(4)
                    Byte_D = "0" & "," & Space(4)
                    Byte_E = "0" & "," & Space(4)
                    Byte_F = "0" & "," & Space(4)
                    Byte_G = "ASI_MODE_AUTO3"
                    
                    ElseIf cell2 = "Start Leg program: Circulation" Then  ' -------------- Circulation
                    Byte_A = "FOOT_ON" & "," & Space(6)
                    Byte_B = "0" & "," & Space(24)
                    Byte_C = "0" & "," & Space(4)
                    Byte_D = "0" & "," & Space(4)
                    Byte_E = "0" & "," & Space(4)
                    Byte_F = "0" & "," & Space(4)
                    Byte_G = "ASI_MODE_AUTO4"
                    
                    ElseIf cell2 = "Start Leg program: Sleep" Then         ' -------------- Sleep
                    Byte_A = "FOOT_ON" & "," & Space(6)
                    Byte_B = "0" & "," & Space(24)
                    Byte_C = "0" & "," & Space(4)
                    Byte_D = "0" & "," & Space(4)
                    Byte_E = "0" & "," & Space(4)
                    Byte_F = "0" & "," & Space(4)
                    Byte_G = "ASI_MODE_AUTO5"
                    
                    ElseIf cell2 = "Start Leg program: Night" Then         ' -------------- Night
                    Byte_A = "FOOT_ON" & "," & Space(6)
                    Byte_B = "0" & "," & Space(24)
                    Byte_C = "0" & "," & Space(4)
                    Byte_D = "0" & "," & Space(4)
                    Byte_E = "0" & "," & Space(4)
                    Byte_F = "0" & "," & Space(4)
                    Byte_G = "ASI_MODE_AUTO6"
                    
                    ElseIf cell2 = "Start Leg program: Deep Relief" Then   ' -------------- Deep Relief
                    Byte_A = "FOOT_ON" & "," & Space(6)
                    Byte_B = "0" & "," & Space(24)
                    Byte_C = "0" & "," & Space(4)
                    Byte_D = "0" & "," & Space(4)
                    Byte_E = "0" & "," & Space(4)
                    Byte_F = "0" & "," & Space(4)
                    Byte_G = "ASI_MODE_AUTO7"
                    
                    ElseIf cell2 = "Start Leg program: Destress" Then      ' -------------- Destress
                    Byte_A = "FOOT_ON" & "," & Space(6)
                    Byte_B = "0" & "," & Space(24)
                    Byte_C = "0" & "," & Space(4)
                    Byte_D = "0" & "," & Space(4)
                    Byte_E = "0" & "," & Space(4)
                    Byte_F = "0" & "," & Space(4)
                    Byte_G = "ASI_MODE_AUTO8"
                    
                     ElseIf cell2 = "Start Leg program: Sleep Soft" Then      ' -------------- Sleep Soft
                    Byte_A = "FOOT_ON" & "," & Space(6)
                    Byte_B = "0" & "," & Space(24)
                    Byte_C = "0" & "," & Space(4)
                    Byte_D = "0" & "," & Space(4)
                    Byte_E = "0" & "," & Space(4)
                    Byte_F = "0" & "," & Space(4)
                    Byte_G = "ASI_MODE_AUTO9"
                    
                '*********** CHECK PROHIBITED CELL - START *******************************************
                          If cell3 <> "" Or cell4 <> "" Or cell5 <> "" Or cell6 <> "" Then
                    MsgBox "Please do not enter any value for [Cycle], [Speed], [3D position], [Width set]."
                        Exit Sub
                        End If
                '*********** CHECK PROHIBITED CELL - END *********************************************
                
                End If

        '===== START LEG PROGRAM - END ================================================================================
        '===== SEAT VIBRATION - START==================================================================================
                If cell2 = "Seat Vibration" Then
                    Byte_A = "SEAT_VIB_ON" & "," & Space(2)
                    Byte_B = "0" & "," & Space(24)
                    Byte_C = "0" & "," & Space(4)
                    Byte_D = "0" & "," & Space(4)
                    Byte_E = "0" & "," & Space(4)
                    Byte_F = "0" & "," & Space(4)
                    Byte_G = "SEAT_VIB_MODE_AUTO" & cell3.Value
                    
                    If cell3 = "" Then
                        MsgBox "Please provide seat vibration program number."
                        Exit Sub
                End If
                
                '*********** CHECK PROHIBITED CELL - START *******************************************
                          If cell4 <> "" Or cell5 <> "" Or cell6 <> "" Then
                    MsgBox "Please do not enter any value for  [Speed], [3D position], [Width set]."
                        Exit Sub
                        End If
                '*********** CHECK PROHIBITED CELL - END *********************************************
            End If
        '===== SEAT VIBRATION - END ===================================================================================
        
           
       
        
        
        '///////////////////////////////////////////////Converted_Starts///////////////////////////////////////////////

                Worksheets("Program Converted").Select
                ActiveSheet.Cells(Row, 1).Value = Byte_A & Byte_B & Byte_C & Byte_D & Byte_E & Byte_F & Byte_G & "," & "//" & stepNo
                Row = Row + 1
                Sheets("Program Converted").Cells.Font.Name = desiredfont '(Convert to FangSong font type)
                
        '////////////////////////////////////////////////Converted_Ends//////////////////////////////////////////////
        End If 'If (ActiveCell.Value = "End")
    
     Loop

   Worksheets("Program Converted").Select
   MsgBox "Program conversion completed, please copy the codes into the cpp file"
   Exit Sub
        
End If 'If (ActiveCell.Value = "Start")

End Sub

             Function IsWholeNumber(num As Variant) As Boolean
                 IsWholeNumber = (num = Int(num))
             End Function





