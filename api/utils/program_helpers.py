from typing import Dict, Any, List
import json

from models import Program
from config import settings
from utils.osim_position_mapping import get_body_parts_from_position

# --- Helper Function for Response Formatting ---
def _format_program_for_response(
    program: Program, similarity_score: float = None
) -> Dict[str, Any]:
    """
    Converts Program object to a dictionary suitable for JSON response.

    Args:
        program: The Program object to format
        similarity_score: Optional similarity score from vector search
    """
    # Extract values from program_description JSON for consistency with previous SQL output
    program_description = program.program_description or {}

    # Basic program data
    result = {
        "id": program.id,
        "name": program.name,
        "program_title": program.program_title,
        "target_objective_text": program_description.get("target_group_objective", ""),
        "target_objective_json": program_description.get("target_group_objective", ""),
        "intensity_text": program_description.get("intensity", ""),
        "intensity_json": program_description.get("intensity", ""),
        "programme_sequence": program_description.get("programme_sequence", {}),
        "signature_moves": program_description.get("signature_moves", []),
        "targeted_acupressure_points": program_description.get(
            "targeted_acupressure_points", []
        ),
        "step_count": len(program.steps),
        "product_id": program.product_id,
        "category_id": program.category_id,
    }

    # Convert similarity score to a relevance_score format (0-100 range)
    # Pinecone cosine similarity ranges from -1 to 1, with 1 being most similar
    if similarity_score is not None:
        # Scale to 0-100 range, assuming cosine similarity from -1 to 1
        # Normalize from 0-1 range (typical for cosine) to 0-100
        relevance_score = int(min(max(similarity_score, 0), 1) * 100)

        # Add semantic match indicators based on which program components likely contributed
        # to the match based on similarity score
        # Higher scores give more detailed match info
        if relevance_score > 85:
            result["matched_objective_concerns"] = (
                f"Semantic match ({relevance_score}%)"
            )
            result["matched_move_concerns"] = f"Semantic match ({relevance_score}%)"
            # Only show signature moves for very high relevance
            if program_description.get("signature_moves"):
                moves = program_description.get("signature_moves", [])
                if len(moves) > 0:
                    result["matched_move_concerns"] = (
                        f"Semantic match: {', '.join(moves[:2])}" if moves else None
                    )
        elif relevance_score > 70:
            result["matched_objective_concerns"] = (
                f"Semantic match ({relevance_score}%)"
            )
            result["matched_move_concerns"] = "Semantic match"
        else:
            result["matched_objective_concerns"] = (
                f"Partial semantic match ({relevance_score}%)"
            )
    else:
        # Default score if no similarity provided
        relevance_score = 90
        result["matched_objective_concerns"] = "Semantic match"

    # Set the relevance score
    result["relevance_score"] = relevance_score

    # Set other match fields that weren't specifically handled above
    if "matched_acupressure_concerns" not in result:
        result["matched_acupressure_concerns"] = None

    if "matched_technique_concerns" not in result:
        result["matched_technique_concerns"] = (
            program.logic_technique if program.logic_technique else None
        )

    if "matched_title_concerns" not in result:
        result["matched_title_concerns"] = (
            program.name if relevance_score > 75 else None
        )

    return result


# --- Helper Function for Standardizing Program Sequence ---
def _standardize_program_sequence(programme_sequence: Dict[str, Any]) -> Dict[str, Any]:
    """
    Standardize the program sequence to a dictionary with keys "front", "main", and "cooling".
    """
    sequence_values = {}
    # Known labels and their standardized versions
    known_labels_lower = {
        "front sequence": "Front Sequence",
        "main sequence": "Main Sequence",
        "mid sequence": "Mid Sequence",
        "middle sequence": "Mid Sequence",
        "cooling sequence": "Cooling Sequence",
        "cooling sequnce": "Cooling Sequence",  # Handle typo
        "ending sequence": "Ending Sequence",
        "end sequence": "Ending Sequence",
        "sequence": "Sequence",  # General sequence
    }

    # First, try to get values for the standard labels
    for key, value in programme_sequence.items():
        key_lower = key.lower()
        if key_lower in known_labels_lower:
            standardized_key = known_labels_lower[key_lower]
            if standardized_key.startswith("Front"):
                sequence_values["front"] = value
            elif standardized_key.startswith("Main"):
                sequence_values["main"] = value
            elif standardized_key in ["Mid Sequence", "Middle Sequence"]:
                sequence_values["mid"] = value
            elif standardized_key.startswith("Cooling"):
                sequence_values["cooling"] = value
            elif standardized_key in ["Ending Sequence", "End Sequence"]:
                sequence_values["ending"] = value
            elif standardized_key == "Sequence":
                # General sequence, could be used as fallback
                sequence_values["general"] = value

    # Ensure we have values for the three main sequences (with fallbacks)
    target_sequence = {
        "front": sequence_values.get("front", ""),
        "main": sequence_values.get(
            "main", sequence_values.get("mid", "")
        ),  # Try mid as fallback
        "cooling": sequence_values.get(
            "cooling", sequence_values.get("ending", "")
        ),  # Try ending as fallback
    }
    return target_sequence


def _format_sequence_for_llm(standardized_sequence: Dict[str, Any]) -> str:
    """
    Format the standardized program sequence into readable text for LLM consumption.

    Args:
        standardized_sequence: Dict with 'front', 'main', 'cooling' keys

    Returns:
        Nicely formatted text description of the program phases
    """
    formatted_lines = []

    phase_labels = {
        'front': 'FRONT PHASE (Preparation/Warm-up)',
        'main': 'MAIN PHASE (Primary Treatment)',
        'cooling': 'COOLING PHASE (Recovery/Relaxation)'
    }

    for phase_key in ['front', 'main', 'cooling']:
        description = standardized_sequence.get(phase_key, '').strip()
        if description:
            formatted_lines.append(f"• {phase_labels[phase_key]}:")
            formatted_lines.append(f"  {description}")
        else:
            formatted_lines.append(f"• {phase_labels[phase_key]}: (No description provided)")

    return '\n'.join(formatted_lines)


# --- Segmentation Helper Functions ---

def format_steps_for_analysis(steps: List[Dict[str, Any]]) -> str:
    """Formats a list of step dictionaries into a rich, contextual string for the LLM."""

    formatted_steps = []

    for i, step in enumerate(steps):
        # Create a detailed description with massage therapy context
        parts = [f"Step {i+1}:"]

        # Position and targeting information with body part context
        if step.get('position_3d') is not None:
            pos_value = step['position_3d']

            # Get body parts for this position using official OSIM mapping
            body_parts = get_body_parts_from_position(pos_value)
            body_part_str = "/".join(body_parts) if body_parts != ["unknown"] else "unknown region"

            if isinstance(pos_value, list):
                # Handle list format like ['N', None] or [None, None]
                non_none_values = [v for v in pos_value if v is not None]
                if non_none_values:
                    parts.append(f"Position: {non_none_values} (targeting {body_part_str})")
                else:
                    parts.append(f"Position: {pos_value} (targeting {body_part_str})")
            else:
                try:
                    pos_val = int(float(pos_value))
                    parts.append(f"Position: {pos_val} (targeting {body_part_str})")
                except (ValueError, TypeError):
                    parts.append(f"Position: {pos_value} (targeting {body_part_str})")
        if step.get('width') is not None:
            parts.append(f"Width: {step['width']} (massage width setting)")

        # Massage techniques and intensities
        if step.get('kneading_speed') is not None:
            try:
                kneading_val = float(step['kneading_speed'])
                intensity = "gentle" if kneading_val < 50 else "moderate" if kneading_val < 80 else "intense"
                parts.append(f"Kneading: {step['kneading_speed']} ({intensity} kneading)")
            except (ValueError, TypeError):
                parts.append(f"Kneading: {step['kneading_speed']}")
        if step.get('tapping_speed') is not None:
            try:
                tapping_val = float(step['tapping_speed'])
                intensity = "light" if tapping_val < 50 else "moderate" if tapping_val < 80 else "vigorous"
                parts.append(f"Tapping: {step['tapping_speed']} ({intensity} percussion)")
            except (ValueError, TypeError):
                parts.append(f"Tapping: {step['tapping_speed']}")

        # Action descriptions with context
        if step.get('roller_action_description'):
            parts.append(f"Roller Action: {step['roller_action_description']}")
        if step.get('air_action_description'):
            parts.append(f"Air Compression: {step['air_action_description']}")

        # Duration and timing
        if step.get('duration') is not None:
            try:
                duration_val = float(step['duration'])
                parts.append(f"Duration: {duration_val}s")
            except (ValueError, TypeError):
                parts.append(f"Duration: {step['duration']}")

        # Add any other non-null values with context
        for key, value in step.items():
            if value is not None and key not in ["step_number", "type", "position_3d", "width",
                                                "kneading_speed", "tapping_speed", "roller_action_description",
                                                "air_action_description", "duration"]:
                parts.append(f"{key.replace('_', ' ').title()}: {value}")

        formatted_steps.append(" | ".join(parts))

    return "\n".join(formatted_steps)


def extract_step_state(step: Dict[str, Any]) -> Dict[str, Any]:
    """
    Extracts key state parameters from a single step for entry/exit state.

    Args:
        step: A single step dictionary

    Returns:
        A dictionary containing the key state parameters
    """
    state = {}

    # Key parameters to extract for state
    key_params = [
        'position_3d', 'width', 'kneading_speed', 'tapping_speed',
        'roller_action_description', 'air_action_description'
    ]

    for param in key_params:
        if param in step and step[param] is not None:
            value = step[param]
            # Handle special case for position_3d which might be a list
            if param == 'position_3d' and isinstance(value, list):
                # Only include if there are non-None values in the list
                non_none_values = [v for v in value if v is not None]
                if non_none_values:
                    state[param] = value
            else:
                state[param] = value

    # Ensure we have at least some state information
    if not state:
        # Fallback to any non-null values except step_number and type
        for key, value in step.items():
            if value is not None and key not in ["step_number", "type"]:
                state[key] = value

    return state


def extract_program_context(program_data) -> Dict[str, Any]:
    """Extract raw program context for creative agent analysis.

    Args:
        program_data: Either a dictionary or SQLAlchemy Program object
    """
    # Handle SQLAlchemy Program object
    if hasattr(program_data, '__dict__'):
        # Convert SQLAlchemy object to dict-like access
        program_name = getattr(program_data, 'name', '') or getattr(program_data, 'program_title', '')
        program_description = getattr(program_data, 'program_description', None)
    else:
        # Handle dictionary
        program_name = program_data.get('name', '') or program_data.get('program_title', '')
        program_description = program_data.get('program_description', None)

    context = {
        'program_name': program_name,
        'raw_program_description': None
    }

    # Pass the complete, unprocessed program description
    if program_description:
        # Handle both dict and JSON string
        if isinstance(program_description, str):
            try:
                desc = json.loads(program_description)
                # Convert the full description to readable text
                context['raw_program_description'] = json.dumps(desc, indent=2)
            except (json.JSONDecodeError, TypeError):
                context['raw_program_description'] = str(program_description)
        else:
            # Convert dict to readable text
            context['raw_program_description'] = json.dumps(program_description, indent=2)

    return context


def should_segment_program(program: Program) -> bool:
    """
    Determine if a program should be segmented based on its category.
    Returns False if the program's category is in the excluded list.
    """
    if not program.category:
        return True  # If no category, default to segmenting

    category_name = program.category.name.lower()
    return category_name not in settings.EXCLUDED_CATEGORIES
