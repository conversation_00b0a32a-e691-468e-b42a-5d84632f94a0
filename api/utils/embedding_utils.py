from typing import Dict, Any, List
import logging
from models.program_models import Program
from utils.pinecone_ops import get_pinecone_index
from config import settings
from openai import OpenAI, OpenAIError

logger = logging.getLogger(__name__)
openai_client = OpenAI(api_key=settings.OPENAI_API_KEY)


def format_program_for_embedding(program: Program) -> str:
    """
    Formats the relevant fields of a Program object into a single text string
    suitable for semantic embedding. Uses Objective, Sequence, and Moves.

    Args:
        program: The Program SQLAlchemy object.

    Returns:
        A formatted string representation of the program.
    """
    objective = "Not specified"
    moves_str = "None"
    sequence_str = "None"

    if program.program_description:
        objective = program.program_description.get(
            "target_group_objective", "Not specified"
        )
        # Extract signature_moves
        signature_moves = program.program_description.get("signature_moves", [])
        if isinstance(signature_moves, list) and signature_moves:
            moves_str = ", ".join(map(str, signature_moves))

        # Extract programme_sequence (handle key variations and typos)
        programme_sequence_data = program.program_description.get(
            "programme_sequence", {}
        )
        if isinstance(programme_sequence_data, dict) and programme_sequence_data:
            known_labels_lower = {
                "front sequence": "Front Sequence",
                "main sequence": "Main Sequence",
                "mid sequence": "Main Sequence",
                "middle sequence": "Main Sequence",
                "cooling sequence": "Cooling Sequence",
                "cooling sequnce": "Cooling Sequence",
                "ending sequence": "Cooling Sequence",
                "end sequence": "Cooling Sequence",
            }
            standardized_sequence = {}
            for key, value in programme_sequence_data.items():
                lower_key = key.lower().strip()
                standard_key = known_labels_lower.get(lower_key)
                if standard_key:
                    standardized_sequence[standard_key] = str(value)

            # Build the sequence string using standardized keys
            front = standardized_sequence.get("Front Sequence", "N/A")
            main = standardized_sequence.get("Main Sequence", "N/A")
            cooling = standardized_sequence.get("Cooling Sequence", "N/A")

            sequence_parts = [
                f"Front Sequence: {front}",
                f"Main Sequence: {main}",
                f"Cooling Sequence: {cooling}",
            ]
            sequence_str = "; ".join(sequence_parts)
            targeted_acupressure_points_str = "N/A"
            targeted_acupressure_points = program.program_description.get(
                "targeted_acupressure_points", []
            )
            if (
                isinstance(targeted_acupressure_points, list)
                and targeted_acupressure_points
            ):
                targeted_acupressure_points_str = ", ".join(
                    map(str, targeted_acupressure_points)
                )

    if not isinstance(objective, str):
        objective = str(objective)

    return f"Objective: {objective}\\nProgramme Sequence: {sequence_str}\\nSignature Moves: {moves_str}\\nTargeted Acupressure Points: {targeted_acupressure_points_str}"


def prepare_pinecone_metadata(program: Program) -> Dict[str, Any]:
    """
    Extracts relevant metadata from a Program object to be stored alongside
    its vector in Pinecone.

    Args:
        program: The Program SQLAlchemy object.

    Returns:
        A dictionary containing metadata fields.
    """
    metadata = {
        # Pinecone metadata values should be strings, numbers, booleans, or lists of strings
        "program_id": program.id,
        "product_id": program.product_id,
        "category_id": program.category_id,
        "name": program.name or program.program_title,
    }
    return metadata


def format_segment_for_embedding(segment) -> str:
    """
    Formats a ProgramSegment object into a text representation suitable for embedding.

    Args:
        segment: The ProgramSegment SQLAlchemy object.

    Returns:
        A formatted string containing the segment's key information.
    """
    # Build the text representation
    parts = []

    # LLM-generated description (most important for semantic search)
    if hasattr(segment, 'description') and segment.description:
        parts.append(f"Description: {segment.description}")

    # Phase and hierarchy info
    if segment.parent_segment_id:
        parts.append(f"Micro-chunk within {segment.parent_segment.phase} phase")
    else:
        parts.append(f"{segment.phase.title()} phase segment")

    # Ontology tags
    if segment.purpose_tags:
        parts.append(f"Purpose: {', '.join(segment.purpose_tags)}")

    if segment.technique_tags:
        parts.append(f"Techniques: {', '.join(segment.technique_tags)}")

    if segment.body_part_tags:
        parts.append(f"Body parts: {', '.join(segment.body_part_tags)}")

    # Intensity and duration
    if segment.intensity_score:
        parts.append(f"Intensity: {segment.intensity_score}/10")

    if segment.duration_seconds:
        parts.append(f"Duration: {segment.duration_seconds} seconds")

    # Interface contract (entry/exit states)
    if segment.entry_state:
        entry_summary = ", ".join([f"{k}: {v}" for k, v in segment.entry_state.items() if v is not None])
        if entry_summary:
            parts.append(f"Entry state: {entry_summary}")

    if segment.exit_state:
        exit_summary = ", ".join([f"{k}: {v}" for k, v in segment.exit_state.items() if v is not None])
        if exit_summary:
            parts.append(f"Exit state: {exit_summary}")

    # Step count
    if segment.steps:
        parts.append(f"Contains {len(segment.steps)} steps")

    return " | ".join(parts)


def prepare_segment_pinecone_metadata(segment) -> Dict[str, Any]:
    """
    Extracts relevant metadata from a ProgramSegment object to be stored alongside
    its vector in Pinecone.

    Args:
        segment: The ProgramSegment SQLAlchemy object.

    Returns:
        A dictionary containing metadata fields.
    """
    metadata = {
        # Core identifiers
        "segment_id": segment.id,
        "original_program_id": segment.original_program_id,
        "phase": segment.phase,
        "parent_segment_id": segment.parent_segment_id or 0,

        # LLM-generated description for semantic search
        "description": getattr(segment, 'description', '') or '',

        # Ontology tags (as lists of strings for Pinecone)
        "purpose_tags": segment.purpose_tags or [],
        "technique_tags": segment.technique_tags or [],
        "body_part_tags": segment.body_part_tags or [],

        # Metrics
        "intensity_score": segment.intensity_score,
        "duration_seconds": segment.duration_seconds,
        "step_count": len(segment.steps) if segment.steps else 0,

        # Hierarchy info for filtering
        "is_macro_phase": segment.parent_segment_id is None,
        "is_micro_chunk": segment.parent_segment_id is not None,
    }

    # Add position_in_parent only if it has a value (Pinecone doesn't accept null values)
    position_in_parent = getattr(segment, 'position_in_parent', None)
    if position_in_parent:
        metadata["position_in_parent"] = position_in_parent

    # Add parent phase information for micro chunks
    if segment.parent_segment_id and hasattr(segment, 'parent_segment') and segment.parent_segment:
        metadata["parent_phase"] = segment.parent_segment.phase
    else:
        metadata["parent_phase"] = segment.phase if segment.parent_segment_id is None else None

    # Add program metadata if available
    if hasattr(segment, 'original_program') and segment.original_program:
        metadata.update({
            "product_id": segment.original_program.product_id,
            "category_id": segment.original_program.category_id,
            "program_name": segment.original_program.name or segment.original_program.program_title,
        })

    return metadata


def get_embedding(text: str, model: str = settings.EMBEDDING_MODEL) -> list[float]:
    """Generates embedding for the given text using OpenAI API."""
    if not openai_client:
        logger.error("OpenAI client is not initialized. Cannot get embedding.")
        raise ValueError("OpenAI client not available")

    try:
        text = text.replace("\n", " ")  # Replace newlines as recommended by OpenAI
        response = openai_client.embeddings.create(input=[text], model=model)
        return response.data[0].embedding
    except OpenAIError as e:
        logger.error(f"OpenAI API error during embedding: {e}", exc_info=True)
        raise
    except Exception as e:
        logger.error(f"Unexpected error during embedding: {e}", exc_info=True)
        raise


def get_batch_embeddings(
    texts: List[str], model: str = settings.EMBEDDING_MODEL
) -> List[List[float]]:
    """Generates embeddings for a batch of texts using OpenAI API."""
    if not openai_client:
        logger.error("OpenAI client is not initialized. Cannot get embeddings.")
        raise ValueError("OpenAI client not available")

    try:
        # Replace newlines as recommended by OpenAI
        processed_texts = [text.replace("\n", " ") for text in texts]
        response = openai_client.embeddings.create(input=processed_texts, model=model)
        # Extract embeddings in the same order as the input texts
        return [data.embedding for data in response.data]
    except OpenAIError as e:
        logger.error(f"OpenAI API error during batch embedding: {e}", exc_info=True)
        raise
    except Exception as e:
        logger.error(f"Unexpected error during batch embedding: {e}", exc_info=True)
        raise


async def generate_embedding(text: str) -> List[float]:
    """
    Generate embedding for the given text using OpenAI API.

    Args:
        text: The text to generate embedding for

    Returns:
        List of floats representing the embedding vector

    Raises:
        Exception: If embedding generation fails
    """
    try:
        logger.debug(f"Generating embedding for text: {text[:100]}...")
        embedding_vector = get_embedding(text, model=settings.EMBEDDING_MODEL)
        logger.debug("Embedding generated successfully")
        return embedding_vector
    except Exception as e:
        logger.error(f"Failed to generate embedding: {e}", exc_info=True)
        raise


async def query_similar_segments(
    query_embedding: List[float],
    top_k: int = 8,
    filter_metadata: Dict[str, Any] = None
) -> List[Dict[str, Any]]:
    """
    Query similar segments from Pinecone using vector similarity.

    Args:
        query_embedding: The embedding vector to search with
        top_k: Number of top results to return
        filter_metadata: Optional metadata filters for Pinecone query

    Returns:
        List of dictionaries containing segment matches with 'id' and 'score' keys

    Raises:
        Exception: If Pinecone query fails
    """
    try:
        logger.debug(f"Querying Pinecone for similar segments with top_k={top_k}")

        # Get Pinecone index
        pinecone_index = get_pinecone_index()

        # Prepare query parameters
        query_params = {
            "vector": query_embedding,
            "top_k": top_k,
            "namespace": settings.PROGRAM_SEGMENTS_EMBEDDING_NAMESPACE,
            "include_metadata": True
        }

        # Add filter if provided
        if filter_metadata:
            query_params["filter"] = filter_metadata

        # Execute query
        query_response = pinecone_index.query(**query_params)

        # Format results
        results = []
        for match in query_response.matches:
            results.append({
                "id": match.id,
                "score": match.score,
                "metadata": match.metadata
            })

        logger.debug(f"Found {len(results)} similar segments")
        return results

    except Exception as e:
        logger.error(f"Failed to query similar segments: {e}", exc_info=True)
        raise
