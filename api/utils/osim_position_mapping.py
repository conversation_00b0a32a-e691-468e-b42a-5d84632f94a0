"""
OSIM uDream 3D Position Mapping Utilities

This module provides utilities for mapping 3D position values to body parts
and understanding therapeutic targeting for OSIM uDream massage chairs.

Based on official OSIM documentation v6.1 (16-Mar-20)
"""

from typing import List, Dict, Any, Optional
import logging

logger = logging.getLogger(__name__)

# Official OSIM 3D Position Mapping (MEDIUM/MID Intensity - Standard)
OSIM_3D_POSITION_MAPPING = {
    # Head and Neck Region
    "head": {
        "positions": [0, 1, 2, 3],  # 頭(頭上)
        "fit_pos": "FIT_POS_HEAD", 
        "region": "head",
        "japanese": "頭(頭上)",
        "description": "Head region targeting"
    },
    "neck_upper_head": {
        "positions": [0, 1, 2, 3],  # 首上(頭)
        "fit_pos": "FIT_POS_HEAD", 
        "region": "neck",
        "japanese": "首上(頭)",
        "description": "Upper neck (head) region"
    },
    "neck_upper": {
        "positions": [0],  # 首上 - all zeros in MID
        "fit_pos": "FIT_POS_NECK_UP", 
        "region": "neck",
        "japanese": "首上",
        "description": "Upper neck region"
    },
    "neck": {
        "positions": [0, 1, 2, 3, 4, 5],  # 首
        "fit_pos": "FIT_POS_NECK", 
        "region": "neck",
        "japanese": "首",
        "description": "Neck region"
    },
    
    # Shoulder Region
    "shoulder_upper": {
        "positions": [13, 14, 15, 16, 17, 18, 19],  # 肩上
        "fit_pos": "FIT_POS_SHD2", 
        "region": "shoulders",
        "japanese": "肩上",
        "description": "Upper shoulder region"
    },
    "shoulder": {
        "positions": [17, 18, 19, 20, 21, 22, 24],  # 肩
        "fit_pos": "FIT_POS_SHD", 
        "region": "shoulders",
        "japanese": "肩",
        "description": "Main shoulder region"
    },
    "shoulder_lower": {
        "positions": [14],  # 肩下
        "fit_pos": "FIT_POS_MID3", 
        "region": "shoulders",
        "japanese": "肩下",
        "description": "Lower shoulder region"
    },
    "shoulder_lower_2": {
        "positions": [10],  # 肩下2
        "fit_pos": "FIT_POS_MID2", 
        "region": "shoulders",
        "japanese": "肩下2",
        "description": "Lower shoulder region 2"
    },
    
    # Upper Back Region
    "shoulder_blade_upper": {
        "positions": [10],  # 肩甲骨上
        "fit_pos": "FIT_POS_MID2", 
        "region": "upper_back",
        "japanese": "肩甲骨上",
        "description": "Upper shoulder blade region"
    },
    "shoulder_blade": {
        "positions": [10],  # 肩甲骨
        "fit_pos": "FIT_POS_MID2", 
        "region": "upper_back",
        "japanese": "肩甲骨",
        "description": "Shoulder blade region"
    },
    "shoulder_blade_lower": {
        "positions": [9],  # 肩甲骨下
        "fit_pos": "FIT_POS_MID", 
        "region": "upper_back",
        "japanese": "肩甲骨下",
        "description": "Lower shoulder blade region"
    },
    "upper_back": {
        "positions": [9],  # 背上
        "fit_pos": "FIT_POS_MID", 
        "region": "upper_back",
        "japanese": "背上",
        "description": "Upper back region"
    },
    
    # Mid Back Region
    "mid_back": {
        "positions": [5],  # 背中
        "fit_pos": "FIT_POS_WAIST", 
        "region": "mid_back",
        "japanese": "背中",
        "description": "Mid back region"
    },
    
    # Lower Back and Lumbar Region
    "lower_back": {
        "positions": [2, 3, 4, 5, 8, 11, 14],  # 腰上1(背下)
        "fit_pos": "FIT_POS_WAIST", 
        "region": "lower_back",
        "japanese": "腰上1(背下)",
        "description": "Lower back region"
    },
    "waist": {
        "positions": [2, 3, 4, 5, 8, 11, 14],  # 腰(腰上)
        "fit_pos": "FIT_POS_WAIST", 
        "region": "lumbar",
        "japanese": "腰(腰上)",
        "description": "Waist/lumbar region"
    },
    
    # Hip Region
    "hips": {
        "positions": [5, 6, 7, 8, 13, 18, 24],  # 尻(腰上)
        "fit_pos": "FIT_POS_HIPS", 
        "region": "hips",
        "japanese": "尻(腰上)",
        "description": "Hip region"
    }
}

# STRONG Intensity Position Mapping (Enhanced targeting)
OSIM_3D_POSITION_MAPPING_STRONG = {
    # Head and Neck Region (Enhanced)
    "head": {
        "positions": [0, 1, 2, 3, 4],  # 頭(頭上)
        "fit_pos": "FIT_POS_HEAD",
        "region": "head",
        "japanese": "頭(頭上)",
        "shoulder_percent": "24%",
        "description": "Head region targeting enhanced"
    },
    "neck_upper_head": {
        "positions": [0, 1, 2, 3],  # 首上(頭)
        "fit_pos": "FIT_POS_HEAD",
        "region": "neck",
        "japanese": "首上(頭)",
        "shoulder_percent": "15%",
        "description": "Upper neck (head) enhanced targeting"
    },
    "neck_upper": {
        "positions": [0, 1, 2, 3, 4],  # 首上
        "fit_pos": "FIT_POS_NECK_UP",
        "region": "neck",
        "japanese": "首上",
        "shoulder_percent": "4%",
        "description": "Upper neck standard"
    },
    "neck": {
        "positions": [1, 2, 3, 4, 5, 6, 7],  # 首
        "fit_pos": "FIT_POS_NECK",
        "region": "neck",
        "japanese": "首",
        "shoulder_percent": "3%",
        "description": "Main neck region enhanced"
    },

    # Shoulder Region (Enhanced)
    "shoulder_upper": {
        "positions": [14, 15, 16, 17, 18, 19, 20],  # 肩上
        "fit_pos": "FIT_POS_SHD2",
        "region": "shoulders",
        "japanese": "肩上",
        "shoulder_percent": "2%",
        "description": "Upper shoulder enhanced range"
    },
    "shoulder": {
        "positions": [19, 20, 21, 22, 23, 24],  # 肩 - note: last value shows 24 twice
        "fit_pos": "FIT_POS_SHD",
        "region": "shoulders",
        "japanese": "肩",
        "shoulder_percent": None,
        "description": "Main shoulder region"
    },
    "shoulder_lower": {
        "positions": [15],  # 肩下
        "fit_pos": "FIT_POS_MID3",
        "region": "shoulders",
        "japanese": "肩下",
        "shoulder_percent": "8%",
        "description": "Lower shoulder enhanced targeting"
    },
    "shoulder_lower_2": {
        "positions": [13],  # 肩下2
        "fit_pos": "FIT_POS_MID2",
        "region": "shoulders",
        "japanese": "肩下2",
        "shoulder_percent": "16%",
        "description": "Lower shoulder 2 enhanced"
    },

    # Upper Back Region (Enhanced)
    "shoulder_blade_upper": {
        "positions": [13],  # 肩甲骨上
        "fit_pos": "FIT_POS_MID2",
        "region": "upper_back",
        "japanese": "肩甲骨上",
        "shoulder_percent": "36%",
        "description": "Upper shoulder blade enhanced"
    },
    "shoulder_blade": {
        "positions": [13],  # 肩甲骨
        "fit_pos": "FIT_POS_MID2",
        "region": "upper_back",
        "japanese": "肩甲骨",
        "shoulder_percent": "48%",
        "description": "Shoulder blade enhanced targeting"
    },
    "shoulder_blade_lower": {
        "positions": [11],  # 肩甲骨下
        "fit_pos": "FIT_POS_MID",
        "region": "upper_back",
        "japanese": "肩甲骨下",
        "shoulder_percent": "58%",
        "description": "Lower shoulder blade enhanced"
    },
    "upper_back": {
        "positions": [11],  # 背上
        "fit_pos": "FIT_POS_MID",
        "region": "upper_back",
        "japanese": "背上",
        "shoulder_percent": "68%",
        "description": "Upper back enhanced targeting"
    },

    # Mid Back Region (Enhanced)
    "mid_back": {
        "positions": [9],  # 背中
        "fit_pos": "FIT_POS_WAIST",
        "region": "mid_back",
        "japanese": "背中",
        "shoulder_percent": "80%",
        "description": "Mid back enhanced targeting"
    },

    # Lower Back and Lumbar Region (Enhanced)
    "lower_back": {
        "positions": [5, 6, 7, 8, 12, 16, 20],  # 腰上1(背下)
        "fit_pos": "FIT_POS_WAIST",
        "region": "lower_back",
        "japanese": "腰上1(背下)",
        "shoulder_percent": "92%",
        "description": "Lower back enhanced"
    },
    "waist": {
        "positions": [5, 6, 7, 8, 13, 18, 22],  # 腰(腰上)
        "fit_pos": "FIT_POS_WAIST",
        "region": "lumbar",
        "japanese": "腰(腰上)",
        "shoulder_percent": "100%",
        "description": "Waist/lumbar enhanced"
    },

    # Hip Region (Enhanced)
    "hips": {
        "positions": [7, 8, 9, 10, 15, 20, 24],  # 尻(腰上)
        "fit_pos": "FIT_POS_HIPS",
        "region": "hips",
        "japanese": "尻(腰上)",
        "shoulder_percent": "MAX",
        "description": "Hip region enhanced targeting"
    }
}

# WEAK Intensity Position Mapping (Gentle targeting)
OSIM_3D_POSITION_MAPPING_WEAK = {
    # Head and Neck Region (Gentle)
    "head": {
        "positions": [0, 1, 2],  # 頭(頭上)
        "fit_pos": "FIT_POS_HEAD",
        "region": "head",
        "japanese": "頭(頭上)",
        "shoulder_percent": "24%",
        "description": "Gentle head targeting"
    },
    "neck_upper_head": {
        "positions": [0, 1, 2, 3],  # 首上(頭)
        "fit_pos": "FIT_POS_HEAD",
        "region": "neck",
        "japanese": "首上(頭)",
        "shoulder_percent": "15%",
        "description": "Gentle upper neck (head)"
    },
    "neck_upper": {
        "positions": [0],  # 首上 - all zeros in WEAK
        "fit_pos": "FIT_POS_NECK_UP",
        "region": "neck",
        "japanese": "首上",
        "shoulder_percent": "4%",
        "description": "Minimal neck targeting"
    },
    "neck": {
        "positions": [0, 1, 2, 3, 4],  # 首
        "fit_pos": "FIT_POS_NECK",
        "region": "neck",
        "japanese": "首",
        "shoulder_percent": "3%",
        "description": "Gentle neck region"
    },

    # Shoulder Region (Gentle)
    "shoulder_upper": {
        "positions": [12, 13, 14, 15, 16, 17, 18],  # 肩上
        "fit_pos": "FIT_POS_SHD2",
        "region": "shoulders",
        "japanese": "肩上",
        "shoulder_percent": "2%",
        "description": "Gentle upper shoulder"
    },
    "shoulder": {
        "positions": [14, 15, 16, 17, 18, 19, 20],  # 肩
        "fit_pos": "FIT_POS_SHD",
        "region": "shoulders",
        "japanese": "肩",
        "shoulder_percent": None,
        "description": "Gentle shoulder region"
    },
    "shoulder_lower": {
        "positions": [11],  # 肩下
        "fit_pos": "FIT_POS_MID3",
        "region": "shoulders",
        "japanese": "肩下",
        "shoulder_percent": "8%",
        "description": "Gentle lower shoulder"
    },
    "shoulder_lower_2": {
        "positions": [8],  # 肩下2
        "fit_pos": "FIT_POS_MID2",
        "region": "shoulders",
        "japanese": "肩下2",
        "shoulder_percent": "16%",
        "description": "Gentle lower shoulder 2"
    },

    # Upper Back Region (Gentle)
    "shoulder_blade_upper": {
        "positions": [8],  # 肩甲骨上
        "fit_pos": "FIT_POS_MID2",
        "region": "upper_back",
        "japanese": "肩甲骨上",
        "shoulder_percent": "36%",
        "description": "Gentle shoulder blade upper"
    },
    "shoulder_blade": {
        "positions": [8],  # 肩甲骨
        "fit_pos": "FIT_POS_MID2",
        "region": "upper_back",
        "japanese": "肩甲骨",
        "shoulder_percent": "48%",
        "description": "Gentle blade targeting"
    },
    "shoulder_blade_lower": {
        "positions": [6],  # 肩甲骨下
        "fit_pos": "FIT_POS_MID",
        "region": "upper_back",
        "japanese": "肩甲骨下",
        "shoulder_percent": "58%",
        "description": "Gentle lower blade"
    },
    "upper_back": {
        "positions": [6],  # 背上
        "fit_pos": "FIT_POS_MID",
        "region": "upper_back",
        "japanese": "背上",
        "shoulder_percent": "68%",
        "description": "Gentle upper back"
    },

    # Mid Back Region (Gentle)
    "mid_back": {
        "positions": [4],  # 背中
        "fit_pos": "FIT_POS_WAIST",
        "region": "mid_back",
        "japanese": "背中",
        "shoulder_percent": "80%",
        "description": "Gentle mid back"
    },

    # Lower Back and Lumbar Region (Gentle)
    "lower_back": {
        "positions": [1, 2, 3, 4, 6, 8, 10],  # 腰上1(背下)
        "fit_pos": "FIT_POS_WAIST",
        "region": "lower_back",
        "japanese": "腰上1(背下)",
        "shoulder_percent": "92%",
        "description": "Gentle lower back"
    },
    "waist": {
        "positions": [1, 2, 3, 4, 6, 8, 10],  # 腰(腰上)
        "fit_pos": "FIT_POS_WAIST",
        "region": "lumbar",
        "japanese": "腰(腰上)",
        "shoulder_percent": "100%",
        "description": "Gentle lumbar/waist"
    },

    # Hip Region (Gentle)
    "hips": {
        "positions": [3, 4, 5, 6, 10, 14, 18],  # 尻(腰上)
        "fit_pos": "FIT_POS_HIPS",
        "region": "hips",
        "japanese": "尻(腰上)",
        "shoulder_percent": "MAX",
        "description": "Gentle hip targeting"
    }
}

# Simplified region mapping for quick lookup
REGION_MAPPING = {
    "head": ["head"],
    "neck": ["neck_upper", "neck"],
    "shoulders": ["shoulder_upper", "shoulder", "shoulder_lower"],
    "upper_back": ["shoulder_blade_upper", "shoulder_blade", "shoulder_blade_lower", "upper_back"],
    "mid_back": ["mid_back"],
    "lower_back": ["lower_back"],
    "lumbar": ["waist"],
    "hips": ["hips"]
}


def parse_position_value(position_value: Any) -> Optional[int]:
    """
    Parse a position value that could be a string, int, list, or other format.
    
    Args:
        position_value: The position value from step data
        
    Returns:
        Parsed integer position or None if cannot be parsed
    """
    if position_value is None:
        return None
        
    # Handle list format like ['N', None] or [15, None]
    if isinstance(position_value, list):
        for val in position_value:
            if val is not None:
                # Try to parse the first non-None value
                parsed = parse_position_value(val)
                if parsed is not None:
                    return parsed
        return None
    
    # Handle string values
    if isinstance(position_value, str):
        # Handle special position codes
        if position_value.upper() == 'N':
            return 0  # Neck position
        
        # Try to parse as number
        try:
            return int(float(position_value))
        except (ValueError, TypeError):
            return None
    
    # Handle numeric values
    if isinstance(position_value, (int, float)):
        return int(position_value)
    
    return None


def get_body_parts_from_position(position_value: Any, intensity: str = "medium") -> List[str]:
    """
    Get the body parts associated with a 3D position value.

    Args:
        position_value: The 3D position value from step data
        intensity: Intensity level ("medium", "strong", "weak") for enhanced targeting

    Returns:
        List of body part names that match the position
    """
    parsed_position = parse_position_value(position_value)

    if parsed_position is None:
        return ["unknown"]

    # Choose mapping based on intensity
    if intensity.lower() == "strong":
        mapping = OSIM_3D_POSITION_MAPPING_STRONG
    elif intensity.lower() == "weak":
        mapping = OSIM_3D_POSITION_MAPPING_WEAK
    else:
        mapping = OSIM_3D_POSITION_MAPPING  # Default to medium/standard

    matching_parts = []

    for part_name, part_info in mapping.items():
        positions = part_info["positions"]
        
        # Check if position is in the list of discrete positions
        if parsed_position in positions:
            matching_parts.append(part_info["region"])
    
    # Remove duplicates and return
    return list(set(matching_parts)) if matching_parts else ["unknown"]


def get_primary_body_region(position_value: Any) -> str:
    """
    Get the primary body region for a 3D position value.
    
    Args:
        position_value: The 3D position value from step data
        
    Returns:
        Primary body region name
    """
    body_parts = get_body_parts_from_position(position_value)
    
    if not body_parts or body_parts == ["unknown"]:
        return "unknown"
    
    # Priority order for primary region selection
    priority_order = ["neck", "shoulders", "upper_back", "mid_back", "lower_back", "lumbar", "hips", "head"]
    
    for region in priority_order:
        if region in body_parts:
            return region
    
    return body_parts[0]


def analyze_position_sequence(positions: List[Any]) -> Dict[str, Any]:
    """
    Analyze a sequence of positions to understand movement patterns and body targeting.
    
    Args:
        positions: List of position values from step sequence
        
    Returns:
        Analysis results including primary regions, movement direction, etc.
    """
    if not positions:
        return {"primary_regions": [], "movement_pattern": "unknown", "coverage": "none"}
    
    # Parse all positions
    parsed_positions = [parse_position_value(pos) for pos in positions]
    valid_positions = [pos for pos in parsed_positions if pos is not None]
    
    if not valid_positions:
        return {"primary_regions": ["unknown"], "movement_pattern": "unknown", "coverage": "none"}
    
    # Get all body regions covered
    all_regions = []
    for pos in positions:
        regions = get_body_parts_from_position(pos)
        all_regions.extend(regions)
    
    primary_regions = list(set(all_regions))
    
    # Analyze movement pattern
    movement_pattern = "static"
    if len(valid_positions) > 1:
        if valid_positions[-1] > valid_positions[0]:
            movement_pattern = "descending"  # Moving down the back
        elif valid_positions[-1] < valid_positions[0]:
            movement_pattern = "ascending"   # Moving up the back
        else:
            movement_pattern = "oscillating" # Mixed movement
    
    # Determine coverage
    coverage = "focused" if len(primary_regions) <= 2 else "broad"
    
    return {
        "primary_regions": primary_regions,
        "movement_pattern": movement_pattern,
        "coverage": coverage,
        "position_range": [min(valid_positions), max(valid_positions)] if valid_positions else None,
        "total_positions": len(valid_positions)
    }


def get_therapeutic_context(position_value: Any, movement_direction: str = "unknown") -> Dict[str, str]:
    """
    Get therapeutic context for a position and movement combination.
    
    Args:
        position_value: The 3D position value
        movement_direction: Direction of movement ("up", "down", "static")
        
    Returns:
        Therapeutic context information
    """
    primary_region = get_primary_body_region(position_value)
    
    # Therapeutic context based on body region
    therapeutic_contexts = {
        "neck": {
            "purpose": "tension_release",
            "technique": "gentle_kneading",
            "intensity": "low_to_medium"
        },
        "shoulders": {
            "purpose": "stress_relief",
            "technique": "deep_kneading",
            "intensity": "medium_to_high"
        },
        "upper_back": {
            "purpose": "posture_correction",
            "technique": "targeted_pressure",
            "intensity": "medium"
        },
        "mid_back": {
            "purpose": "core_support",
            "technique": "rolling_massage",
            "intensity": "medium"
        },
        "lower_back": {
            "purpose": "pain_relief",
            "technique": "deep_therapy",
            "intensity": "high"
        },
        "lumbar": {
            "purpose": "spinal_alignment",
            "technique": "sustained_pressure",
            "intensity": "medium_to_high"
        },
        "hips": {
            "purpose": "circulation_boost",
            "technique": "compression",
            "intensity": "medium"
        }
    }
    
    return therapeutic_contexts.get(primary_region, {
        "purpose": "general_massage",
        "technique": "mixed",
        "intensity": "medium"
    })


def get_enhanced_therapeutic_context(position_value: Any, intensity: str = "medium") -> Dict[str, Any]:
    """
    Get enhanced therapeutic context including intensity-specific information.

    Args:
        position_value: The 3D position value
        intensity: Intensity level ("medium", "strong", "weak")

    Returns:
        Enhanced therapeutic context with intensity-specific details
    """
    primary_region = get_primary_body_region(position_value)

    # Base therapeutic context
    base_context = get_therapeutic_context(position_value)

    # Intensity-specific enhancements
    intensity_enhancements = {
        "strong": {
            "targeting_precision": "enhanced",
            "coverage_area": "expanded",
            "therapeutic_depth": "deep",
            "shoulder_positioning": "precise_percentage_based"
        },
        "medium": {
            "targeting_precision": "standard",
            "coverage_area": "standard",
            "therapeutic_depth": "moderate",
            "shoulder_positioning": "standard_ranges"
        },
        "weak": {
            "targeting_precision": "gentle",
            "coverage_area": "focused",
            "therapeutic_depth": "light",
            "shoulder_positioning": "minimal_adjustment"
        }
    }

    enhancement = intensity_enhancements.get(intensity.lower(), intensity_enhancements["medium"])

    return {
        **base_context,
        "intensity_level": intensity,
        "targeting_precision": enhancement["targeting_precision"],
        "coverage_area": enhancement["coverage_area"],
        "therapeutic_depth": enhancement["therapeutic_depth"],
        "shoulder_positioning": enhancement["shoulder_positioning"],
        "primary_region": primary_region
    }
