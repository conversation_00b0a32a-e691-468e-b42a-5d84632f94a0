[{"id": 0, "action": "Seat Vibration", "params": 2, "speed": null, "position3D": null, "width": null}, {"id": 1, "action": "Start Leg program: Deep Relief", "params": null, "speed": null, "position3D": null, "width": null}, {"id": 2, "action": "Roll to Neck Upper 1", "params": null, "speed": "<PERSON>d", "position3D": null, "width": null}, {"id": 3, "action": "Cycle", "params": 4, "speed": "<PERSON>d", "position3D": null, "width": "<PERSON>rrow"}, {"id": 4, "action": "Roll to Neck Upper", "params": null, "speed": "<PERSON>d", "position3D": null, "width": null}, {"id": 5, "action": "Cycle", "params": 3, "speed": "<PERSON>d", "position3D": null, "width": "<PERSON>rrow"}, {"id": 6, "action": "Roll to Neck", "params": null, "speed": "<PERSON>d", "position3D": null, "width": null}, {"id": 7, "action": "Cycle", "params": 3, "speed": "<PERSON>d", "position3D": null, "width": "<PERSON>rrow"}, {"id": 8, "action": "Roll to Neck Upper 1", "params": null, "speed": "Rolling", "position3D": null, "width": null}, {"id": 9, "action": "Timer (Sec)", "params": 2, "speed": null, "position3D": null, "width": null}, {"id": 10, "action": "3D: Set 3D value", "params": null, "speed": null, "position3D": 0, "width": null}, {"id": 11, "action": "Timer (Sec)", "params": 1, "speed": null, "position3D": null, "width": null}, {"id": 12, "action": "Subroutine", "params": 1, "speed": null, "position3D": null, "width": null}, {"id": 13, "action": "Width change", "params": null, "speed": null, "position3D": null, "width": "<PERSON>rrow"}, {"id": 14, "action": "Roll to Shoulder Blade Lower", "params": null, "speed": "Slow Tap", "position3D": null, "width": null}, {"id": 15, "action": "Roll to Shoulder Lower 1", "params": null, "speed": "Slow K<PERSON>d and <PERSON>p", "position3D": null, "width": null}, {"id": 16, "action": "Subroutine", "params": 6, "speed": null, "position3D": null, "width": null}, {"id": 17, "action": "Roll to Shoulder Lower", "params": null, "speed": "Slow K<PERSON>d and <PERSON>p", "position3D": null, "width": null}, {"id": 18, "action": "3D: Set 3D value", "params": null, "speed": null, "position3D": 42, "width": null}, {"id": 19, "action": "Cycle", "params": 3, "speed": "<PERSON>d", "position3D": null, "width": "<PERSON>rrow"}, {"id": 20, "action": "Timer (Sec)", "params": 8, "speed": "Slow Tap", "position3D": null, "width": "<PERSON>rrow"}, {"id": 21, "action": "Roll to Neck Upper 1", "params": null, "speed": "<PERSON>d", "position3D": null, "width": null}, {"id": 22, "action": "3D: Set 3D value", "params": null, "speed": "<PERSON>d", "position3D": 0, "width": null}, {"id": 23, "action": "Cycle", "params": 3, "speed": "<PERSON>d", "position3D": null, "width": "<PERSON>rrow"}, {"id": 24, "action": "3D: Set 3D value", "params": null, "speed": null, "position3D": 48, "width": null}, {"id": 25, "action": "Cycle", "params": 3, "speed": "<PERSON>d", "position3D": null, "width": "<PERSON>rrow"}, {"id": 26, "action": "Roll to Neck", "params": null, "speed": "<PERSON>d", "position3D": null, "width": null}, {"id": 27, "action": "3D: Set 3D value", "params": null, "speed": null, "position3D": 45, "width": null}, {"id": 28, "action": "Cycle", "params": 3, "speed": "<PERSON>d", "position3D": null, "width": "<PERSON>rrow"}, {"id": 29, "action": "Roll to Shoulder Lower", "params": null, "speed": "<PERSON>d", "position3D": null, "width": null}, {"id": 30, "action": "3D: Set 3D value", "params": null, "speed": null, "position3D": 30, "width": null}, {"id": 31, "action": "Cycle", "params": 3, "speed": "<PERSON>d", "position3D": null, "width": "<PERSON>rrow"}, {"id": 32, "action": "Cycle", "params": 3, "speed": "Fast Knead & Slow Tap", "position3D": null, "width": "<PERSON>rrow"}, {"id": 33, "action": "Roll to Shoulder Blade Upper", "params": null, "speed": "Fast Knead & Slow Tap", "position3D": null, "width": null}, {"id": 34, "action": "3D: Set 3D value", "params": null, "speed": "Slow K<PERSON>d and <PERSON>p", "position3D": 30, "width": null}, {"id": 35, "action": "Cycle", "params": 3, "speed": "Slow K<PERSON>d and <PERSON>p", "position3D": null, "width": "<PERSON>rrow"}, {"id": 36, "action": "Width change", "params": null, "speed": null, "position3D": null, "width": "<PERSON>rrow"}, {"id": 37, "action": "Roll to Neck Upper 1", "params": null, "speed": "Rolling", "position3D": null, "width": null}, {"id": 38, "action": "Roll to Shoulder Lower", "params": null, "speed": "Rolling", "position3D": null, "width": null}, {"id": 39, "action": "Roll to Neck", "params": null, "speed": "Rolling", "position3D": null, "width": null}, {"id": 40, "action": "Subroutine", "params": 7, "speed": null, "position3D": null, "width": null}, {"id": 41, "action": "Roll to Waist", "params": null, "speed": "<PERSON> Knead", "position3D": null, "width": null}, {"id": 42, "action": "Width change", "params": null, "speed": null, "position3D": null, "width": "<PERSON>rrow"}, {"id": 43, "action": "3D: Set 3D value", "params": null, "speed": null, "position3D": 70, "width": null}, {"id": 44, "action": "Timer (Sec)", "params": 2, "speed": null, "position3D": null, "width": null}, {"id": 45, "action": "Cycle", "params": 4, "speed": "<PERSON>d", "position3D": null, "width": "<PERSON>rrow"}, {"id": 46, "action": "Cycle", "params": 3, "speed": "Fast Knead and Tap", "position3D": null, "width": "<PERSON>rrow"}, {"id": 47, "action": "Timer (Sec)", "params": 8, "speed": "Fast Tap", "position3D": null, "width": "<PERSON>rrow"}, {"id": 48, "action": "Roll to Waist", "params": null, "speed": "Fast Tap", "position3D": 56, "width": null}, {"id": 49, "action": "Roll to Waist Upper", "params": null, "speed": "Fast Knead & Slow Tap", "position3D": null, "width": null}, {"id": 50, "action": "3D: Set 3D value", "params": null, "speed": "Fast Knead & Slow Tap", "position3D": 30, "width": null}, {"id": 51, "action": "Roll to Shoulder Blade Lower 1", "params": null, "speed": "Fast Knead & Slow Tap", "position3D": null, "width": null}, {"id": 52, "action": "3D: Set 3D value", "params": null, "speed": "Fast Knead & Slow Tap", "position3D": 30, "width": null}, {"id": 53, "action": "Cycle", "params": 3, "speed": "Fast Knead & Slow Tap", "position3D": null, "width": "<PERSON>rrow"}, {"id": 54, "action": "Roll to Shoulder Blade Upper", "params": null, "speed": "Slow Tap", "position3D": null, "width": null}, {"id": 55, "action": "3D: Set 3D value", "params": null, "speed": "<PERSON> Knead", "position3D": 28, "width": null}, {"id": 56, "action": "Cycle", "params": 3, "speed": "<PERSON> Knead", "position3D": null, "width": "<PERSON>rrow"}, {"id": 57, "action": "Roll to Shoulder Lower", "params": null, "speed": "<PERSON> Knead", "position3D": null, "width": null}, {"id": 58, "action": "3D: Set 3D value", "params": null, "speed": "<PERSON>d", "position3D": 32, "width": null}, {"id": 59, "action": "Cycle", "params": 3, "speed": "<PERSON>d", "position3D": null, "width": "Wide"}, {"id": 60, "action": "Timer (Sec)", "params": 1, "speed": null, "position3D": null, "width": null}, {"id": 61, "action": "Width change", "params": null, "speed": null, "position3D": null, "width": "<PERSON>rrow"}, {"id": 62, "action": "Timer (Sec)", "params": 3, "speed": null, "position3D": null, "width": null}, {"id": 63, "action": "Timer (Sec)", "params": 8, "speed": "Slow Tap", "position3D": null, "width": "<PERSON>rrow"}, {"id": 64, "action": "Roll to Shoulder", "params": null, "speed": "<PERSON> Knead", "position3D": null, "width": null}, {"id": 65, "action": "3D: Set 3D value", "params": null, "speed": null, "position3D": 34, "width": null}, {"id": 66, "action": "Cycle", "params": 3, "speed": "<PERSON>d", "position3D": null, "width": "Wide"}, {"id": 67, "action": "Timer (Sec)", "params": 1, "speed": null, "position3D": null, "width": null}, {"id": 68, "action": "Width change", "params": null, "speed": null, "position3D": null, "width": "<PERSON>rrow"}, {"id": 69, "action": "Timer (Sec)", "params": 3, "speed": null, "position3D": null, "width": null}, {"id": 70, "action": "Roll to Neck Upper", "params": null, "speed": "Rolling", "position3D": null, "width": null}, {"id": 71, "action": "Timer (Sec)", "params": 2, "speed": null, "position3D": null, "width": null}, {"id": 72, "action": "3D: Set 3D value", "params": null, "speed": null, "position3D": 0, "width": null}, {"id": 73, "action": "Timer (Sec)", "params": 1, "speed": null, "position3D": null, "width": null}, {"id": 74, "action": "Cycle", "params": 3, "speed": "<PERSON>d", "position3D": null, "width": "<PERSON>rrow"}, {"id": 75, "action": "3D: Set 3D value", "params": null, "speed": null, "position3D": 50, "width": null}, {"id": 76, "action": "Cycle", "params": 3, "speed": "<PERSON>d", "position3D": null, "width": "<PERSON>rrow"}, {"id": 77, "action": "Roll to Shoulder Upper", "params": null, "speed": "<PERSON> Knead", "position3D": null, "width": null}, {"id": 78, "action": "Subroutine", "params": 1, "speed": null, "position3D": null, "width": null}, {"id": 79, "action": "Width change", "params": null, "speed": null, "position3D": null, "width": "<PERSON>rrow"}, {"id": 80, "action": "Roll to Shoulder Blade Lower", "params": null, "speed": "Slow Tap", "position3D": null, "width": null}, {"id": 81, "action": "Roll to Shoulder Lower 1", "params": null, "speed": "<PERSON> Knead", "position3D": null, "width": null}, {"id": 82, "action": "Subroutine", "params": 6, "speed": null, "position3D": null, "width": null}, {"id": 83, "action": "Timer (Sec)", "params": 1, "speed": null, "position3D": null, "width": null}, {"id": 84, "action": "3D: Set 3D value", "params": null, "speed": "Slow K<PERSON>d and <PERSON>p", "position3D": 30, "width": null}, {"id": 85, "action": "Roll to Back Lower", "params": null, "speed": "Slow K<PERSON>d and <PERSON>p", "position3D": null, "width": null}, {"id": 86, "action": "Roll to Shoulder", "params": null, "speed": "Slow K<PERSON>d and <PERSON>p", "position3D": null, "width": null}, {"id": 87, "action": "Width change", "params": null, "speed": null, "position3D": null, "width": "<PERSON>rrow"}, {"id": 88, "action": "3D: Set 3D value", "params": null, "speed": null, "position3D": 40, "width": null}, {"id": 89, "action": "Cycle", "params": 3, "speed": "<PERSON>d", "position3D": null, "width": "<PERSON>rrow"}, {"id": 90, "action": "Roll to Neck", "params": null, "speed": "<PERSON> Knead", "position3D": null, "width": null}, {"id": 91, "action": "Cycle", "params": 3, "speed": "<PERSON>d", "position3D": null, "width": "<PERSON>rrow"}, {"id": 92, "action": "Roll to Shoulder Lower 1", "params": null, "speed": "<PERSON> Knead", "position3D": null, "width": null}, {"id": 93, "action": "3D: Set 3D value", "params": null, "speed": "<PERSON> Knead", "position3D": 30, "width": null}, {"id": 94, "action": "Cycle", "params": 3, "speed": "<PERSON> Knead", "position3D": null, "width": "<PERSON>rrow"}, {"id": 95, "action": "Cycle", "params": 3, "speed": "Slow K<PERSON>d and <PERSON>p", "position3D": null, "width": "<PERSON>rrow"}, {"id": 96, "action": "Roll to Shoulder Blade Upper", "params": null, "speed": "Slow K<PERSON>d and <PERSON>p", "position3D": null, "width": null}, {"id": 97, "action": "3D: Set 3D value", "params": null, "speed": null, "position3D": 26, "width": null}, {"id": 98, "action": "Timer (Sec)", "params": 8, "speed": "Slow K<PERSON>d and <PERSON>p", "position3D": null, "width": null}, {"id": 99, "action": "Cycle", "params": 3, "speed": "<PERSON> Knead", "position3D": null, "width": "<PERSON>rrow"}, {"id": 100, "action": "Timer (Sec)", "params": 1, "speed": null, "position3D": null, "width": null}, {"id": 101, "action": "3D: Set 3D value", "params": null, "speed": "<PERSON>d", "position3D": 30, "width": null}, {"id": 102, "action": "Roll to Back Lower", "params": null, "speed": "<PERSON> Knead", "position3D": null, "width": null}, {"id": 103, "action": "Roll to Neck Upper 1", "params": null, "speed": "<PERSON>d", "position3D": null, "width": null}, {"id": 104, "action": "Roll to Back Upper", "params": null, "speed": "<PERSON>d", "position3D": null, "width": null}, {"id": 105, "action": "Roll to Shoulder", "params": null, "speed": "<PERSON> Knead", "position3D": null, "width": null}, {"id": 106, "action": "3D: Set 3D value", "params": null, "speed": null, "position3D": 40, "width": null}, {"id": 107, "action": "Cycle", "params": 3, "speed": "<PERSON>d", "position3D": null, "width": "<PERSON>rrow"}]