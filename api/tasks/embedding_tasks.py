import logging
from typing import Optional, List

from sqlalchemy.orm import Session
from core.database import SessionLocal

# --- Model Imports ---
from models.program_models import Program, ProgramSegment

# --- Utility Imports ---
from utils.embedding_utils import (
    format_program_for_embedding,
    prepare_pinecone_metadata,
    format_segment_for_embedding,
    prepare_segment_pinecone_metadata,
    get_embedding,
    get_batch_embeddings,
)
from utils.pinecone_ops import get_pinecone_index
from config import settings

# --- Logging ---
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)



def should_embed_program(program: Program) -> bool:
    """
    Determine if a program should be embedded based on its category.
    Returns False if the program's category is in the excluded list.
    """
    if not program.category:
        return True  # If no category, default to embedding

    category_name = program.category.name.lower()
    return category_name not in settings.EXCLUDED_CATEGORIES


def upsert_program_embedding(program_id: int):
    """
    Background task to fetch a program, generate its embedding, and upsert to Pinecone.
    """
    logger.info(f"Starting embedding upsert task for program_id: {program_id}")

    # --- Dependency Acquisition (Example using SessionLocal) ---
    # This needs to be adapted based on your actual background task setup
    db: Optional[Session] = None
    try:
        db = SessionLocal()
        if not db:
            raise Exception("Failed to acquire DB session for task.")

        # 1. Fetch Program from DB
        logger.info(f"Fetching program {program_id} from database...")
        program: Optional[Program] = (
            db.query(Program).filter(Program.id == program_id).first()
        )

        if not program:
            logger.warning(
                f"Program with ID {program_id} not found in database. Skipping embedding."
            )
            return

        # Check if program should be embedded
        if not should_embed_program(program):
            logger.info(
                f"Program {program_id} in category '{program.category.name}' is excluded from embedding. Skipping."
            )
            return

        # 2. Format Program Text
        logger.info(f"Formatting text for program {program_id}...")
        text_to_embed = format_program_for_embedding(program)
        logger.debug(
            f"Text to embed for program {program_id}: '{text_to_embed[:100]}...'"
        )  # Log snippet

        # 3. Generate Embedding via OpenAI
        logger.info(
            f"Generating embedding for program {program_id} using model {settings.EMBEDDING_MODEL}..."
        )
        try:
            embedding_vector = get_embedding(
                text_to_embed, model=settings.EMBEDDING_MODEL
            )
            logger.info(f"Successfully generated embedding for program {program_id}.")
        except Exception as e:
            logger.error(
                f"Failed to generate embedding for program {program_id}: {e}",
                exc_info=True,
            )
            # Depending on requirements, could re-raise to signal task failure or just log and exit
            return  # Exit task on embedding failure for now

        # 4. Prepare Pinecone Vector Data
        logger.info(f"Preparing vector data for program {program_id}...")
        vector_id = str(program.id)  # Pinecone IDs must be strings
        metadata = prepare_pinecone_metadata(program)
        vector_data = {
            "id": vector_id,
            "values": embedding_vector,
            "metadata": metadata,
        }
        logger.debug(
            f"Vector data prepared for program {program_id}: ID={vector_id}, Metadata={metadata}"
        )

        # 5. Upsert to Pinecone
        logger.info(
            f"Upserting vector for program {program_id} to Pinecone namespace '{settings.PROGRAM_EMBEDDING_NAMESPACE}'..."
        )
        try:
            pinecone_index = get_pinecone_index()  # Get initialized index
            upsert_response = pinecone_index.upsert(
                vectors=[vector_data], namespace=settings.PROGRAM_EMBEDDING_NAMESPACE
            )
            logger.info(
                f"Successfully upserted vector for program {program_id}. Response: {upsert_response}"
            )
        except Exception as e:
            logger.error(
                f"Failed to upsert vector for program {program_id} to Pinecone: {e}",
                exc_info=True,
            )
            # Signal task failure or log and exit
            return  # Exit task on upsert failure

        logger.info(
            f"Embedding upsert task completed successfully for program_id: {program_id}"
        )

    except Exception as e:
        logger.error(
            f"Error during embedding task for program_id {program_id}: {e}",
            exc_info=True,
        )

    finally:
        if db:
            db.close()  # Ensure session is closed
            logger.debug(f"Database session closed for task program_id: {program_id}")


def batch_upsert_program_embeddings(program_ids: List[int], batch_size: int = 10):
    """
    Process multiple program embeddings in batches to minimize API calls.

    Args:
        program_ids: List of program IDs to process
        batch_size: Number of embeddings to process in a single batch
    """
    logger.info(
        f"Starting batch embedding upsert for {len(program_ids)} programs with batch size {batch_size}"
    )

    db: Optional[Session] = None
    try:
        db = SessionLocal()
        if not db:
            raise Exception("Failed to acquire DB session for batch task.")

        # Process programs in batches
        for i in range(0, len(program_ids), batch_size):
            batch_ids = program_ids[i : i + batch_size]
            logger.info(f"Processing batch {i//batch_size + 1}: programs {batch_ids}")

            # 1. Fetch programs in batch
            programs = db.query(Program).filter(Program.id.in_(batch_ids)).all()
            if not programs:
                logger.warning(f"No programs found for batch {batch_ids}. Skipping.")
                continue

            programs_by_id = {prog.id: prog for prog in programs}
            found_ids = list(programs_by_id.keys())

            if len(found_ids) < len(batch_ids):
                missing_ids = set(batch_ids) - set(found_ids)
                logger.warning(f"Programs not found for IDs: {missing_ids}")

            # 2. Format program texts and filter excluded categories
            program_texts = []
            program_infos = []  # Store program info for later use
            eligible_program_ids = []

            for program_id in found_ids:
                program = programs_by_id[program_id]

                # Skip programs in excluded categories
                if not should_embed_program(program):
                    logger.info(
                        f"Program {program_id} in category '{program.category.name if program.category else 'None'}' is excluded from embedding. Skipping."
                    )
                    continue

                eligible_program_ids.append(program_id)
                text = format_program_for_embedding(program)
                program_texts.append(text)
                program_infos.append(
                    {"id": program.id, "metadata": prepare_pinecone_metadata(program)}
                )

            # 3. Generate embeddings in batch
            try:
                batch_embeddings = get_batch_embeddings(
                    program_texts, model=settings.EMBEDDING_MODEL
                )
                logger.info(
                    f"Successfully generated {len(batch_embeddings)} embeddings for batch"
                )
            except Exception as e:
                logger.error(f"Failed to generate batch embeddings: {e}", exc_info=True)
                continue  # Skip to next batch on embedding failure

            # 4. Prepare vectors for Pinecone
            vectors = []
            for idx, embedding in enumerate(batch_embeddings):
                program_info = program_infos[idx]
                vectors.append(
                    {
                        "id": str(program_info["id"]),  # Pinecone IDs must be strings
                        "values": embedding,
                        "metadata": program_info["metadata"],
                    }
                )

            # 5. Upsert batch to Pinecone
            try:
                pinecone_index = get_pinecone_index()
                upsert_response = pinecone_index.upsert(
                    vectors=vectors, namespace=settings.PROGRAM_EMBEDDING_NAMESPACE
                )
                logger.info(
                    f"Successfully upserted batch of {len(vectors)} vectors to Pinecone. Response: {upsert_response}"
                )
            except Exception as e:
                logger.error(f"Failed to upsert batch to Pinecone: {e}", exc_info=True)
                continue  # Skip to next batch on upsert failure

        logger.info(f"Batch embedding upsert completed for all programs")

    except Exception as e:
        logger.error(f"Error during batch embedding task: {e}", exc_info=True)
    finally:
        if db:
            db.close()
            logger.debug("Database session closed for batch task")


def upsert_segment_embedding(segment_id: int):
    """
    Background task to fetch a program segment, generate its embedding, and upsert to Pinecone.
    """
    logger.info(f"Starting embedding upsert task for segment_id: {segment_id}")

    db: Optional[Session] = None
    try:
        db = SessionLocal()
        if not db:
            raise Exception("Failed to acquire DB session for segment task.")

        # 1. Fetch Segment from DB with relationships
        logger.info(f"Fetching segment {segment_id} from database...")
        segment: Optional[ProgramSegment] = (
            db.query(ProgramSegment)
            .filter(ProgramSegment.id == segment_id)
            .first()
        )

        if not segment:
            logger.warning(
                f"Segment with ID {segment_id} not found in database. Skipping embedding."
            )
            return

        # 2. Format Segment Text
        logger.info(f"Formatting text for segment {segment_id}...")
        text_to_embed = format_segment_for_embedding(segment)
        logger.debug(
            f"Text to embed for segment {segment_id}: '{text_to_embed[:100]}...'"
        )

        # 3. Generate Embedding via OpenAI
        logger.info(
            f"Generating embedding for segment {segment_id} using model {settings.EMBEDDING_MODEL}..."
        )
        try:
            embedding_vector = get_embedding(
                text_to_embed, model=settings.EMBEDDING_MODEL
            )
            logger.info(f"Successfully generated embedding for segment {segment_id}.")
        except Exception as e:
            logger.error(
                f"Failed to generate embedding for segment {segment_id}: {e}",
                exc_info=True,
            )
            return

        # 4. Prepare Pinecone Vector Data
        logger.info(f"Preparing vector data for segment {segment_id}...")
        vector_id = str(segment.id)  # Pinecone IDs must be strings
        metadata = prepare_segment_pinecone_metadata(segment)
        vector_data = {
            "id": vector_id,
            "values": embedding_vector,
            "metadata": metadata,
        }
        logger.debug(
            f"Vector data prepared for segment {segment_id}: ID={vector_id}, Metadata={metadata}"
        )

        # 5. Upsert to Pinecone
        logger.info(
            f"Upserting vector for segment {segment_id} to Pinecone namespace '{settings.PROGRAM_SEGMENTS_EMBEDDING_NAMESPACE}'..."
        )
        try:
            pinecone_index = get_pinecone_index()
            upsert_response = pinecone_index.upsert(
                vectors=[vector_data], namespace=settings.PROGRAM_SEGMENTS_EMBEDDING_NAMESPACE
            )
            logger.info(
                f"Successfully upserted vector for segment {segment_id}. Response: {upsert_response}"
            )
        except Exception as e:
            logger.error(
                f"Failed to upsert vector for segment {segment_id} to Pinecone: {e}",
                exc_info=True,
            )
            return

        logger.info(
            f"Embedding upsert task completed successfully for segment_id: {segment_id}"
        )

    except Exception as e:
        logger.error(
            f"Error during embedding task for segment_id {segment_id}: {e}",
            exc_info=True,
        )

    finally:
        if db:
            db.close()
            logger.debug(f"Database session closed for task segment_id: {segment_id}")


def batch_upsert_segment_embeddings(segment_ids: List[int], batch_size: int = 10):
    """
    Process multiple segment embeddings in batches to minimize API calls.

    Args:
        segment_ids: List of segment IDs to process
        batch_size: Number of embeddings to process in a single batch
    """
    logger.info(
        f"Starting batch embedding upsert for {len(segment_ids)} segments with batch size {batch_size}"
    )

    db: Optional[Session] = None
    try:
        db = SessionLocal()
        if not db:
            raise Exception("Failed to acquire DB session for batch segment task.")

        # Process segments in batches
        for i in range(0, len(segment_ids), batch_size):
            batch_ids = segment_ids[i : i + batch_size]
            logger.info(f"Processing batch {i//batch_size + 1}: segments {batch_ids}")

            # 1. Fetch segments in batch
            segments = db.query(ProgramSegment).filter(ProgramSegment.id.in_(batch_ids)).all()
            if not segments:
                logger.warning(f"No segments found for batch {batch_ids}. Skipping.")
                continue

            segments_by_id = {seg.id: seg for seg in segments}
            found_ids = list(segments_by_id.keys())

            if len(found_ids) < len(batch_ids):
                missing_ids = set(batch_ids) - set(found_ids)
                logger.warning(f"Segments not found for IDs: {missing_ids}")

            # 2. Format segment texts
            segment_texts = []
            segment_infos = []

            for segment_id in found_ids:
                segment = segments_by_id[segment_id]
                text = format_segment_for_embedding(segment)
                segment_texts.append(text)
                segment_infos.append(
                    {"id": segment.id, "metadata": prepare_segment_pinecone_metadata(segment)}
                )

            # 3. Generate embeddings in batch
            try:
                batch_embeddings = get_batch_embeddings(
                    segment_texts, model=settings.EMBEDDING_MODEL
                )
                logger.info(
                    f"Successfully generated {len(batch_embeddings)} embeddings for batch"
                )
            except Exception as e:
                logger.error(f"Failed to generate batch embeddings: {e}", exc_info=True)
                continue

            # 4. Prepare vectors for Pinecone
            vectors = []
            for idx, embedding in enumerate(batch_embeddings):
                segment_info = segment_infos[idx]
                vectors.append(
                    {
                        "id": str(segment_info["id"]),
                        "values": embedding,
                        "metadata": segment_info["metadata"],
                    }
                )

            # 5. Upsert batch to Pinecone
            try:
                pinecone_index = get_pinecone_index()
                upsert_response = pinecone_index.upsert(
                    vectors=vectors, namespace=settings.PROGRAM_SEGMENTS_EMBEDDING_NAMESPACE
                )
                logger.info(
                    f"Successfully upserted batch of {len(vectors)} vectors to Pinecone. Response: {upsert_response}"
                )
            except Exception as e:
                logger.error(f"Failed to upsert batch to Pinecone: {e}", exc_info=True)
                continue

        logger.info(f"Batch segment embedding upsert completed for all segments")

    except Exception as e:
        logger.error(f"Error during batch segment embedding task: {e}", exc_info=True)
    finally:
        if db:
            db.close()
            logger.debug("Database session closed for batch segment task")
