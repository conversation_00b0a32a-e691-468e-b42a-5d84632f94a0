#!/usr/bin/env python3
"""
Test that LLM-generated descriptions are being captured and saved to the database.
"""

import asyncio
import sys

# Add the api directory to the path
sys.path.append('/home/<USER>/work/massage-ai/api')

from services.segmentation.micro_chunk_agent import discover_micro_chunks

# Sample massage steps for testing
test_steps = [
    # Gentle preparation
    {"step_number": "1", "kneading_speed": 40, "tapping_speed": None, "position_3d": ["N", 5], "roller_action_description": "gentle kneading", "air_action_description": "shoulders on"},
    {"step_number": "2", "kneading_speed": 50, "tapping_speed": None, "position_3d": ["N+2", 7], "roller_action_description": "gentle kneading", "air_action_description": "shoulders on"},
    {"step_number": "3", "kneading_speed": 60, "tapping_speed": None, "position_3d": ["N+4", 9], "roller_action_description": "gentle kneading", "air_action_description": "shoulders on"},
    
    # Intensive treatment
    {"step_number": "4", "kneading_speed": 120, "tapping_speed": None, "position_3d": ["N+6", 11], "roller_action_description": "deep kneading", "air_action_description": "shoulders on"},
    {"step_number": "5", "kneading_speed": 120, "tapping_speed": None, "position_3d": ["N+8", 13], "roller_action_description": "deep kneading", "air_action_description": "shoulders on"},
    {"step_number": "6", "kneading_speed": 120, "tapping_speed": None, "position_3d": ["N+10", 15], "roller_action_description": "deep kneading", "air_action_description": "shoulders on"},
    {"step_number": "7", "kneading_speed": 120, "tapping_speed": None, "position_3d": ["N+12", 17], "roller_action_description": "deep kneading", "air_action_description": "shoulders on"},
    {"step_number": "8", "kneading_speed": 120, "tapping_speed": None, "position_3d": ["N+14", 19], "roller_action_description": "deep kneading", "air_action_description": "shoulders on"},
    
    # Percussion phase
    {"step_number": "9", "kneading_speed": None, "tapping_speed": 150, "position_3d": ["N+12", 17], "roller_action_description": "percussion", "air_action_description": "arms on"},
    {"step_number": "10", "kneading_speed": None, "tapping_speed": 150, "position_3d": ["N+10", 15], "roller_action_description": "percussion", "air_action_description": "arms on"},
    {"step_number": "11", "kneading_speed": None, "tapping_speed": 150, "position_3d": ["N+8", 13], "roller_action_description": "percussion", "air_action_description": "arms on"},
    
    # Gentle finish
    {"step_number": "12", "kneading_speed": 40, "tapping_speed": None, "position_3d": ["N+6", 11], "roller_action_description": "gentle rolling", "air_action_description": "shoulders on"},
    {"step_number": "13", "kneading_speed": 30, "tapping_speed": None, "position_3d": ["N+4", 9], "roller_action_description": "gentle rolling", "air_action_description": "shoulders on"},
    {"step_number": "14", "kneading_speed": 20, "tapping_speed": None, "position_3d": ["N+2", 7], "roller_action_description": "gentle rolling", "air_action_description": "shoulders on"}
]

async def test_description_capture():
    """Test that micro-chunk descriptions are generated and can be captured."""
    print("Testing LLM Description Capture...")
    print("=" * 50)
    print(f"Analyzing {len(test_steps)} massage steps")
    print("\nExpected: 3-4 chunks with rich therapeutic descriptions")
    print("\n" + "=" * 50)
    
    try:
        # Generate micro-chunks with descriptions
        result = await discover_micro_chunks(test_steps)
        
        print(f"\nRESULTS:")
        print(f"Number of chunks: {len(result.chunks)}")
        
        for i, chunk in enumerate(result.chunks, 1):
            chunk_size = chunk.end_index - chunk.start_index + 1
            print(f"\nChunk {i}:")
            print(f"  Steps: {chunk.start_index} to {chunk.end_index} ({chunk_size} steps)")
            print(f"  Description: '{chunk.description}'")
            print(f"  Description length: {len(chunk.description)} characters")
        
        print("\n" + "=" * 50)
        print("DESCRIPTION QUALITY ANALYSIS:")
        
        # Check description quality
        descriptions = [chunk.description for chunk in result.chunks]
        
        # Check if descriptions are meaningful
        meaningful_descriptions = [d for d in descriptions if len(d) > 20 and any(word in d.lower() for word in ['therapeutic', 'massage', 'kneading', 'percussion', 'gentle', 'intensive', 'treatment'])]
        
        print(f"✅ Meaningful descriptions: {len(meaningful_descriptions)}/{len(descriptions)}")
        
        # Check for therapeutic language
        therapeutic_terms = ['therapeutic', 'treatment', 'therapy', 'massage', 'kneading', 'percussion', 'gentle', 'intensive', 'deep', 'circulation', 'muscle', 'tension']
        descriptions_with_therapeutic_terms = [d for d in descriptions if any(term in d.lower() for term in therapeutic_terms)]
        
        print(f"✅ Descriptions with therapeutic language: {len(descriptions_with_therapeutic_terms)}/{len(descriptions)}")
        
        # Check average description length
        avg_length = sum(len(d) for d in descriptions) / len(descriptions)
        print(f"✅ Average description length: {avg_length:.1f} characters")
        
        # Check for unique descriptions
        unique_descriptions = len(set(descriptions))
        print(f"✅ Unique descriptions: {unique_descriptions}/{len(descriptions)}")
        
        print("\n" + "=" * 50)
        print("EMBEDDING READINESS:")
        print("These descriptions will be:")
        print("1. ✅ Saved to database in 'description' field")
        print("2. ✅ Included in vector embeddings for semantic search")
        print("3. ✅ Available in Pinecone metadata for filtering")
        print("4. ✅ Used to enhance search and retrieval quality")
        
        return result
        
    except Exception as e:
        print(f"Error testing description capture: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    asyncio.run(test_description_capture())
