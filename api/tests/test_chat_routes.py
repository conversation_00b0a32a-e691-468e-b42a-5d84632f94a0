from models.account_models import User as UserModel
import pytest
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session


from main import app # Your FastAPI app
from models import ChatModels, Product, ChatConfig, Chat, Message, Program, ProgramCategory, User # Changed from api.models
from schemas.chat_schemas import ChatConfigCreate
from routes.chat_routes import ProgramActionRequest
from auth.cognito import get_current_user # For overriding

# Helper to override get_current_user for specific tests/users
def override_get_current_user_factory(user_obj: UserModel, roles: list[str]):
    def _override():
        user_obj.roles = roles
        return user_obj
    return _override

# ---- Test Data Setup ----

@pytest.fixture
def test_chat_model(db_session: Session) -> ChatModels:
    model = ChatModels(name="gpt-4-test", provider="openai")
    db_session.add(model)
    db_session.commit()
    db_session.refresh(model)
    return model

@pytest.fixture
def test_product(db_session: Session) -> Product:
    product = Product(name="Test Product", description="A product for testing")
    db_session.add(product)
    db_session.commit()
    db_session.refresh(product)
    return product


# ---- Tests for /chat/models ----

def test_get_chat_models_as_ai_engineer(client: TestClient, db_session: Session, test_chat_model: ChatModels, ai_engineer_user: UserModel):
    app.dependency_overrides[get_current_user] = override_get_current_user_factory(ai_engineer_user, roles=["AIEngineers"])

    response = client.get("/api2/chat/models")
    assert response.status_code == 200
    data = response.json()
    assert len(data["models"]) == 1
    assert data["models"][0]["name"] == test_chat_model.name
    assert data["models"][0]["id"] == test_chat_model.id

    app.dependency_overrides.pop(get_current_user, None)  # Clean up override


def test_get_chat_models_as_regular_user(client: TestClient, regular_user: UserModel):
    app.dependency_overrides[get_current_user] = override_get_current_user_factory(regular_user, roles=["Users"])

    response = client.get("/api2/chat/models")
    assert response.status_code == 401  # Unauthorized (access denied)

    app.dependency_overrides.pop(get_current_user, None)


def test_get_chat_models_empty(client: TestClient, db_session: Session, ai_engineer_user: UserModel):
    app.dependency_overrides[get_current_user] = override_get_current_user_factory(ai_engineer_user, roles=["AIEngineers"])

    # Ensure no models exist
    db_session.query(ChatModels).delete()
    db_session.commit()

    response = client.get("/api2/chat/models")
    assert response.status_code == 200
    data = response.json()
    assert len(data["models"]) == 0

    app.dependency_overrides.pop(get_current_user, None)


# ---- Tests for /chat/create_config ----

def test_create_chat_config_as_ai_engineer(
    client: TestClient, db_session: Session,
    test_chat_model: ChatModels, test_product: Product,
    ai_engineer_user: UserModel
):
    app.dependency_overrides[get_current_user] = override_get_current_user_factory(ai_engineer_user, roles=["AIEngineers"])

    chat_config_data = ChatConfigCreate(
        name="Test Config",
        model_id=test_chat_model.id,
        product_id=test_product.id,
        sft_model_name="test-sft-model"
    )
    response = client.post("/api2/chat/create_config", json=chat_config_data.model_dump())

    assert response.status_code == 201
    data = response.json()
    assert data["name"] == chat_config_data.name
    assert data["model_id"] == test_chat_model.id
    assert data["product_id"] == test_product.id
    assert data["user_id"] == ai_engineer_user.id

    # Verify it's in the DB
    db_config = db_session.query(ChatConfig).filter(ChatConfig.id == data["id"]).first()
    assert db_config is not None
    assert db_config.name == chat_config_data.name

    app.dependency_overrides.pop(get_current_user, None)


def test_create_chat_config_as_regular_user(
    client: TestClient, test_chat_model: ChatModels, test_product: Product, regular_user: UserModel
):
    app.dependency_overrides[get_current_user] = override_get_current_user_factory(regular_user, roles=["Users"])

    chat_config_data = {
        "name": "Forbidden Config",
        "model_id": test_chat_model.id,
        "product_id": test_product.id
    }
    response = client.post("/api2/chat/create_config", json=chat_config_data)
    assert response.status_code == 401

    app.dependency_overrides.pop(get_current_user, None)


def test_create_chat_config_invalid_model_id(
    client: TestClient, test_product: Product, ai_engineer_user: UserModel
):
    app.dependency_overrides[get_current_user] = override_get_current_user_factory(ai_engineer_user, roles=["AIEngineers"])

    chat_config_data = {
        "name": "Invalid Model Config",
        "model_id": 999,
        "product_id": test_product.id
    }
    response = client.post("/api2/chat/create_config", json=chat_config_data)
    assert response.status_code == 404 # Model not found

    app.dependency_overrides.pop(get_current_user, None)


# ---- Tests for /chat/update_config/{chat_config_id} ----

@pytest.fixture
def existing_chat_config(db_session: Session, test_chat_model: ChatModels, test_product: Product, ai_engineer_user: UserModel) -> ChatConfig:
    config = ChatConfig(
        name="Original Config",
        model_id=test_chat_model.id,
        product_id=test_product.id,
        user_id=ai_engineer_user.id,
        url="original-url"
    )
    db_session.add(config)
    db_session.commit()
    db_session.refresh(config)
    return config

def test_update_chat_config_as_ai_engineer(
    client: TestClient, db_session: Session,
    existing_chat_config: ChatConfig, test_chat_model: ChatModels, # test_chat_model for a new model if needed
    ai_engineer_user: UserModel
):
    app.dependency_overrides[get_current_user] = override_get_current_user_factory(ai_engineer_user, roles=["AIEngineers"])

    update_data = {"name": "Updated Config Name"}
    response = client.put(f"/api2/chat/update_config/{existing_chat_config.id}", json=update_data)

    assert response.status_code == 200
    data = response.json()
    assert data["name"] == "Updated Config Name"
    assert data["id"] == existing_chat_config.id

    db_session.refresh(existing_chat_config)
    assert existing_chat_config.name == "Updated Config Name"

    app.dependency_overrides.pop(get_current_user, None)

def test_update_chat_config_as_regular_user(
    client: TestClient, existing_chat_config: ChatConfig, regular_user: UserModel
):
    app.dependency_overrides[get_current_user] = override_get_current_user_factory(regular_user, roles=["Users"])
    
    update_data = {"name": "Attempted Update"}
    response = client.put(f"/api2/chat/update_config/{existing_chat_config.id}", json=update_data)
    assert response.status_code == 401

    app.dependency_overrides.pop(get_current_user, None)

def test_update_chat_config_not_found(client: TestClient, ai_engineer_user: UserModel):
    app.dependency_overrides[get_current_user] = override_get_current_user_factory(ai_engineer_user, roles=["AIEngineers"])
    
    update_data = {"name": "No Such Config"}
    response = client.put("/api2/chat/update_config/9999", json=update_data) # Non-existent ID
    assert response.status_code == 404 # Assuming update_chat_config_handler checks for existence

    app.dependency_overrides.pop(get_current_user, None)


# ---- Tests for /chat/list ----
def test_get_chat_configs_list(
    client: TestClient, db_session: Session, 
    existing_chat_config: ChatConfig, # Uses the one created by ai_engineer
    regular_user # Test with a regular user, as this endpoint is for everyone authenticated
):
    # This endpoint is accessible by any authenticated user.
    app.dependency_overrides[get_current_user] = override_get_current_user_factory(regular_user, roles=["Users"])

    response = client.get("/api2/chat/list")
    assert response.status_code == 200
    data = response.json()
    assert data["total"] >= 1
    assert len(data["chat_configs"]) >= 1
    
    found = False
    for config in data["chat_configs"]:
        if config["id"] == existing_chat_config.id:
            assert config["name"] == existing_chat_config.name
            assert config["model_name"] == existing_chat_config.model.name # Check joined model_name
            assert config["product_name"] == existing_chat_config.product.name # Check joined product_name
            found = True
            break
    assert found, "Existing chat config not found in list"

    app.dependency_overrides.pop(get_current_user, None)

def test_get_chat_configs_list_pagination(client: TestClient, db_session: Session, ai_engineer_user: UserModel, test_chat_model, test_product):
    app.dependency_overrides[get_current_user] = override_get_current_user_factory(ai_engineer_user, roles=["AIEngineers"])

    # Create a few configs
    for i in range(5):
        conf = ChatConfig(name=f"Conf {i}", model_id=test_chat_model.id, product_id=test_product.id, user_id=ai_engineer_user.id, url=f"url{i}")
        db_session.add(conf)
    db_session.commit()
    
    # Test limit
    response = client.get("/api2/chat/list?limit=2")
    assert response.status_code == 200
    data = response.json()
    assert len(data["chat_configs"]) == 2
    # Total reflects all chat configs in the system, not just for the user.
    
    # Test skip and limit
    response = client.get("/api2/chat/list?skip=1&limit=2")
    assert response.status_code == 200
    data = response.json()
    # This depends on the total number of configs and ordering.
    # For precise assertion, clear other configs or control ordering.

    app.dependency_overrides.pop(get_current_user, None)


# ---- Tests for /chat/list/{config_id} ----
def test_get_chat_config_by_id(
    client: TestClient, existing_chat_config: ChatConfig, regular_user: UserModel # Any authenticated user
):
    app.dependency_overrides[get_current_user] = override_get_current_user_factory(regular_user, roles=["Users"])
    
    response = client.get(f"/api2/chat/list/{existing_chat_config.id}")
    assert response.status_code == 200
    data = response.json()
    assert data["id"] == existing_chat_config.id
    assert data["name"] == existing_chat_config.name
    # The response model is ChatConfigResponse, which does not include model_name or product_name.
    # It includes model_id and product_id.

    app.dependency_overrides.pop(get_current_user, None)

def test_get_chat_config_by_id_not_found(client: TestClient, regular_user: UserModel):
    app.dependency_overrides[get_current_user] = override_get_current_user_factory(regular_user, roles=["Users"])
    
    response = client.get("/api2/chat/list/9999") # Non-existent ID
    assert response.status_code == 404

    app.dependency_overrides.pop(get_current_user, None)


# ---- Tests for /chat/{config_id} ---- (DELETE)
def test_delete_chat_config_as_ai_engineer(
    client: TestClient, db_session: Session, existing_chat_config: ChatConfig, ai_engineer_user: UserModel
):
    app.dependency_overrides[get_current_user] = override_get_current_user_factory(ai_engineer_user, roles=["AIEngineers"])
    
    config_id_to_delete = existing_chat_config.id
    
    # Optionally, create associated Chat and Message to test cascade deletion logic in endpoint
    test_chat = Chat(config_id=config_id_to_delete, user_id=ai_engineer_user.id, slug=f"test-chat-slug-for-delete-{config_id_to_delete}")
    db_session.add(test_chat)
    db_session.commit()
    test_message = Message(chat_id=test_chat.id, role="user", content="hello for delete test")
    db_session.add(test_message)
    db_session.commit()

    chat_id_to_check = test_chat.id
    message_id_to_check = test_message.id
    response = client.delete(f"/api2/chat/{config_id_to_delete}")
    assert response.status_code == 204
    
    deleted_config = db_session.query(ChatConfig).filter(ChatConfig.id == config_id_to_delete).first()
    assert deleted_config is None
    
    # Verify associated chats and messages are deleted by the endpoint logic
    deleted_chat = db_session.query(Chat).filter(Chat.id == chat_id_to_check).first()
    assert deleted_chat is None
    deleted_message = db_session.query(Message).filter(Message.id == message_id_to_check).first()
    assert deleted_message is None


    app.dependency_overrides.pop(get_current_user, None)

def test_delete_chat_config_as_regular_user(
    client: TestClient, existing_chat_config: ChatConfig, regular_user: UserModel
):
    app.dependency_overrides[get_current_user] = override_get_current_user_factory(regular_user, roles=["Users"])
    
    response = client.delete(f"/api2/chat/{existing_chat_config.id}")
    assert response.status_code == 204 # Regular user can currently delete, route is not restricted
    app.dependency_overrides.pop(get_current_user, None)

def test_delete_chat_config_not_found(client: TestClient, ai_engineer_user: UserModel):
    app.dependency_overrides[get_current_user] = override_get_current_user_factory(ai_engineer_user, roles=["AIEngineers"])
    
    response = client.delete("/api2/chat/9999") # Non-existent ID
    # The endpoint deletes and commits. If no row found, it doesn't error, just affects 0 rows.
    # So, it should still return 204.
    assert response.status_code == 204 

    app.dependency_overrides.pop(get_current_user, None)

# More tests for /chat/threads, /chat/thread/{slug}/messages, and program interaction routes
# would follow a similar pattern:
# 1. Set up necessary data (Chats, Messages, Programs, ProgramCategories).
# 2. Override current_user if endpoint requires specific roles or user context.
# 3. Make the request using `client`.
# 4. Assert status code and response data.
# 5. Verify database state changes if applicable.
# 6. Clean up dependency overrides.

# Example stubs for further tests:

# ---- Test Data for Threads and Messages ----
@pytest.fixture
def test_user_chat(db_session: Session, existing_chat_config: ChatConfig, regular_user: UserModel) -> Chat:
    # Ensure the user_id for the chat matches the regular_user["id"]
    # The existing_chat_config might have been created by ai_engineer_user.
    # A chat can be linked to a config, and a user.
    # Let's ensure the chat belongs to regular_user["id"]
    
    # If existing_chat_config.user_id must match chat.user_id, this needs care.
    # Chat.user_id is the owner of the chat thread.
    # ChatConfig.user_id is the creator of the config. They can be different.
    
    chat = Chat(
        config_id=existing_chat_config.id,
        user_id=regular_user.id,
        slug=f"chat-slug-{regular_user.id}-{existing_chat_config.id}"
    )
    db_session.add(chat)
    db_session.commit()
    db_session.refresh(chat)
    return chat

@pytest.fixture
def test_chat_message(db_session: Session, test_user_chat: Chat) -> Message:
    msg = Message(chat_id=test_user_chat.id, role="user", content="Hello from test user")
    db_session.add(msg)
    db_session.commit()
    db_session.refresh(msg)
    return msg
    
@pytest.fixture
def another_chat_message(db_session: Session, test_user_chat: Chat) -> Message:
    msg = Message(chat_id=test_user_chat.id, role="assistant", content="Hi there test user")
    db_session.add(msg)
    db_session.commit()
    db_session.refresh(msg)
    return msg


# ---- Tests for /chat/threads ----
def test_get_chat_threads_for_user(
    client: TestClient, db_session: Session,
    test_user_chat: Chat, test_chat_message: Message, # Ensure some chat and message exist for the user
    regular_user: UserModel
):
    app.dependency_overrides[get_current_user] = override_get_current_user_factory(regular_user, roles=["Users"])

    response = client.get("/api2/chat/threads")
    assert response.status_code == 200
    data = response.json()
    assert data["total"] >= 1
    assert len(data["threads"]) >= 1
    
    found_thread = False
    for thread_data in data["threads"]:
        if thread_data["id"] == test_user_chat.id:
            assert thread_data["slug"] == test_user_chat.slug
            # The first_message is the content of the first 'user' role message.
            assert thread_data["first_message"] == test_chat_message.content 
            assert thread_data["config_id"] == test_user_chat.config_id
            assert thread_data["config_name"] == test_user_chat.config.name # Relies on .config relationship
            found_thread = True
            break
    assert found_thread, "Test user's chat thread not found."

    app.dependency_overrides.pop(get_current_user, None)

# ---- Tests for /chat/thread/{slug}/messages ----
def test_get_chat_thread_messages(
    client: TestClient, db_session: Session,
    test_user_chat: Chat, test_chat_message: Message, another_chat_message: Message, # Ensure messages exist
    regular_user: UserModel
):
    app.dependency_overrides[get_current_user] = override_get_current_user_factory(regular_user, roles=["Users"])

    response = client.get(f"/api2/chat/thread/{test_user_chat.slug}/messages")
    assert response.status_code == 200
    data = response.json()

    assert data["thread"]["id"] == test_user_chat.id
    assert data["thread"]["slug"] == test_user_chat.slug
    
    assert len(data["messages"]) == 2 # test_chat_message (user), another_chat_message (assistant)
    assert data["messages"][0]["id"] == test_chat_message.id
    assert data["messages"][0]["role"] == "user"
    assert data["messages"][0]["content"] == test_chat_message.content
    assert data["messages"][1]["id"] == another_chat_message.id
    assert data["messages"][1]["role"] == "assistant"
    
    app.dependency_overrides.pop(get_current_user, None)

def test_get_chat_thread_messages_not_found(client: TestClient, regular_user: UserModel):
    app.dependency_overrides[get_current_user] = override_get_current_user_factory(regular_user, roles=["Users"])
    
    response = client.get("/api2/chat/thread/non-existent-slug/messages")
    assert response.status_code == 404

    app.dependency_overrides.pop(get_current_user, None)


# ---- Test Data for Program Interactions ----
@pytest.fixture
def test_program_category_generated(db_session: Session) -> ProgramCategory:
    category = db_session.query(ProgramCategory).filter_by(name="generated programs").first()
    if not category:
        category = ProgramCategory(name="generated programs", description="Auto-generated programs")
        db_session.add(category)
        db_session.commit()
        db_session.refresh(category)
    return category

@pytest.fixture
def test_program_category_ai(db_session: Session) -> ProgramCategory:
    category = db_session.query(ProgramCategory).filter_by(name="ai programs").first()
    if not category:
        category = ProgramCategory(name="ai programs", description="User saved AI programs")
        db_session.add(category)
        db_session.commit()
        db_session.refresh(category)
    return category


@pytest.fixture
def test_program(db_session: Session, test_product: Product, test_program_category_generated: ProgramCategory, regular_user: UserModel) -> Program:
    # Programs have user_id, product_id, category_id
    program = Program(
        name="Test Program for Interactions",
        program_title="Test Program Title",
        product_id=test_product.id,
        category_id=test_program_category_generated.id, # Starts in 'generated programs'
        user_id=regular_user.id, # Creator of the program
        liked=False,
        disliked=False,
        saved=False
    )
    db_session.add(program)
    db_session.commit()
    db_session.refresh(program)
    return program

# ---- Tests for /program/like ----
def test_like_program(client: TestClient, test_program: Program, regular_user: UserModel, db_session: Session):
    app.dependency_overrides[get_current_user] = override_get_current_user_factory(regular_user, roles=["Users"])
    
    # Like
    response = client.post("/api2/program/like", json={"program_id": test_program.id})
    assert response.status_code == 200
    data = response.json()
    assert data["success"] is True
    assert data["liked"] is True
    assert data["disliked"] is False
    db_session.refresh(test_program)
    assert test_program.liked is True

    # Unlike (like again)
    response = client.post("/api2/program/like", json={"program_id": test_program.id})
    assert response.status_code == 200
    data = response.json()
    assert data["liked"] is False
    db_session.refresh(test_program)
    assert test_program.liked is False
    
    app.dependency_overrides.pop(get_current_user, None)

# ---- Tests for /program/dislike ----
def test_dislike_program(client: TestClient, test_program: Program, regular_user: UserModel, db_session: Session):
    app.dependency_overrides[get_current_user] = override_get_current_user_factory(regular_user, roles=["Users"])
    
    # Dislike
    response = client.post("/api2/program/dislike", json={"program_id": test_program.id})
    assert response.status_code == 200
    data = response.json()
    assert data["success"] is True
    assert data["disliked"] is True
    assert data["liked"] is False # Ensure liking is removed if previously liked
    db_session.refresh(test_program)
    assert test_program.disliked is True

    # Undislike (dislike again)
    response = client.post("/api2/program/dislike", json={"program_id": test_program.id})
    assert response.status_code == 200
    data = response.json()
    assert data["disliked"] is False
    db_session.refresh(test_program)
    assert test_program.disliked is False
    
    app.dependency_overrides.pop(get_current_user, None)

# ---- Tests for /program/save ----
def test_save_program(client: TestClient, test_program: Program, regular_user: UserModel, db_session: Session, test_program_category_ai: ProgramCategory, test_program_category_generated: ProgramCategory):
    app.dependency_overrides[get_current_user] = override_get_current_user_factory(regular_user, roles=["Users"])
    
    initial_category_id = test_program.category_id
    assert initial_category_id == test_program_category_generated.id # Starts in generated

    # Save
    response = client.post("/api2/program/save", json={"program_id": test_program.id})
    assert response.status_code == 200
    data = response.json()
    assert data["success"] is True
    assert data["saved"] is True
    db_session.refresh(test_program)
    assert test_program.saved is True
    assert test_program.category_id == test_program_category_ai.id # Moved to 'ai programs'

    # Unsave
    response = client.post("/api2/program/save", json={"program_id": test_program.id})
    assert response.status_code == 200
    data = response.json()
    assert data["saved"] is False
    db_session.refresh(test_program)
    assert test_program.saved is False
    assert test_program.category_id == test_program_category_generated.id # Moved back
    
    app.dependency_overrides.pop(get_current_user, None)

def test_program_action_program_not_found(client: TestClient, regular_user: UserModel):
    app.dependency_overrides[get_current_user] = override_get_current_user_factory(regular_user, roles=["Users"])
    
    actions = ["like", "dislike", "save"]
    for action in actions:
        response = client.post(f"/api2/program/{action}", json={"program_id": 99999}) # Non-existent
        assert response.status_code == 404
        
    app.dependency_overrides.pop(get_current_user, None)

