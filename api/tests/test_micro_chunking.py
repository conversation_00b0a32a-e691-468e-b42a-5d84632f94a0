#!/usr/bin/env python3
"""
Test the updated micro-chunking agent to see if it creates fewer, more substantial chunks.
"""

import asyncio
import sys

# Add the api directory to the path
sys.path.append('/home/<USER>/work/massage-ai/api')

from services.segmentation.micro_chunk_agent import discover_micro_chunks

# Sample massage steps representing a macro-phase (20 steps)
test_steps = [
    # Preparation phase (gentle start)
    {"step_number": "1", "kneading_speed": 40, "tapping_speed": None, "position_3d": ["N", 5], "roller_action_description": "gentle kneading", "air_action_description": "shoulders on"},
    {"step_number": "2", "kneading_speed": 50, "tapping_speed": None, "position_3d": ["N+2", 7], "roller_action_description": "gentle kneading", "air_action_description": "shoulders on"},
    {"step_number": "3", "kneading_speed": 60, "tapping_speed": None, "position_3d": ["N+4", 9], "roller_action_description": "gentle kneading", "air_action_description": "shoulders on"},
    
    # Main treatment phase (intensive work)
    {"step_number": "4", "kneading_speed": 100, "tapping_speed": None, "position_3d": ["N+6", 11], "roller_action_description": "deep kneading", "air_action_description": "shoulders on"},
    {"step_number": "5", "kneading_speed": 110, "tapping_speed": None, "position_3d": ["N+8", 13], "roller_action_description": "deep kneading", "air_action_description": "shoulders on"},
    {"step_number": "6", "kneading_speed": 120, "tapping_speed": None, "position_3d": ["N+10", 15], "roller_action_description": "deep kneading", "air_action_description": "shoulders on"},
    {"step_number": "7", "kneading_speed": 120, "tapping_speed": None, "position_3d": ["N+12", 17], "roller_action_description": "deep kneading", "air_action_description": "shoulders on"},
    {"step_number": "8", "kneading_speed": 115, "tapping_speed": None, "position_3d": ["N+14", 19], "roller_action_description": "deep kneading", "air_action_description": "shoulders on"},
    {"step_number": "9", "kneading_speed": 110, "tapping_speed": None, "position_3d": ["N+16", 21], "roller_action_description": "deep kneading", "air_action_description": "shoulders on"},
    {"step_number": "10", "kneading_speed": 100, "tapping_speed": None, "position_3d": ["N+18", 23], "roller_action_description": "deep kneading", "air_action_description": "shoulders on"},
    
    # Percussion phase (technique change)
    {"step_number": "11", "kneading_speed": None, "tapping_speed": 140, "position_3d": ["N+16", 21], "roller_action_description": "percussion", "air_action_description": "arms on"},
    {"step_number": "12", "kneading_speed": None, "tapping_speed": 150, "position_3d": ["N+14", 19], "roller_action_description": "percussion", "air_action_description": "arms on"},
    {"step_number": "13", "kneading_speed": None, "tapping_speed": 160, "position_3d": ["N+12", 17], "roller_action_description": "percussion", "air_action_description": "arms on"},
    {"step_number": "14", "kneading_speed": None, "tapping_speed": 150, "position_3d": ["N+10", 15], "roller_action_description": "percussion", "air_action_description": "arms on"},
    {"step_number": "15", "kneading_speed": None, "tapping_speed": 140, "position_3d": ["N+8", 13], "roller_action_description": "percussion", "air_action_description": "arms on"},
    
    # Completion phase (gentle finish)
    {"step_number": "16", "kneading_speed": 60, "tapping_speed": None, "position_3d": ["N+6", 11], "roller_action_description": "gentle rolling", "air_action_description": "shoulders on"},
    {"step_number": "17", "kneading_speed": 50, "tapping_speed": None, "position_3d": ["N+4", 9], "roller_action_description": "gentle rolling", "air_action_description": "shoulders on"},
    {"step_number": "18", "kneading_speed": 40, "tapping_speed": None, "position_3d": ["N+2", 7], "roller_action_description": "gentle rolling", "air_action_description": "shoulders on"},
    {"step_number": "19", "kneading_speed": 30, "tapping_speed": None, "position_3d": ["N", 5], "roller_action_description": "gentle rolling", "air_action_description": "shoulders on"},
    {"step_number": "20", "kneading_speed": None, "tapping_speed": None, "position_3d": ["N", 0], "roller_action_description": "stop", "air_action_description": None}
]

async def test_micro_chunking():
    """Test the updated micro-chunking agent."""
    print("Testing Updated Micro-Chunking Agent...")
    print("=" * 50)
    print(f"Total steps to analyze: {len(test_steps)}")
    print("\nExpected: 3-4 major chunks:")
    print("1. Preparation phase (steps 1-3): Gentle warm-up")
    print("2. Main treatment (steps 4-10): Intensive kneading")
    print("3. Percussion phase (steps 11-15): Circulation boost")
    print("4. Completion phase (steps 16-20): Gentle finish")
    print("\n" + "=" * 50)
    
    try:
        result = await discover_micro_chunks(test_steps)
        
        print(f"\nACTUAL RESULTS:")
        print(f"Number of chunks created: {len(result.chunks)}")
        print("\nChunk Details:")
        
        for i, chunk in enumerate(result.chunks, 1):
            chunk_size = chunk.end_index - chunk.start_index + 1
            print(f"\nChunk {i}:")
            print(f"  Steps: {chunk.start_index} to {chunk.end_index} ({chunk_size} steps)")
            print(f"  Description: {chunk.description}")
        
        print("\n" + "=" * 50)
        print("ANALYSIS:")
        
        # Check if we achieved the goal
        num_chunks = len(result.chunks)
        if num_chunks <= 4:
            print(f"✅ SUCCESS: Created {num_chunks} chunks (target: 2-4)")
        else:
            print(f"❌ STILL OVER-SEGMENTED: Created {num_chunks} chunks (target: 2-4)")
        
        # Check chunk sizes
        chunk_sizes = [chunk.end_index - chunk.start_index + 1 for chunk in result.chunks]
        avg_size = sum(chunk_sizes) / len(chunk_sizes)
        print(f"✅ Average chunk size: {avg_size:.1f} steps (target: 5-15)")
        
        # Check coverage
        total_steps_covered = sum(chunk_sizes)
        if total_steps_covered == len(test_steps):
            print(f"✅ Complete coverage: {total_steps_covered}/{len(test_steps)} steps")
        else:
            print(f"❌ Incomplete coverage: {total_steps_covered}/{len(test_steps)} steps")
            
    except Exception as e:
        print(f"Error testing micro-chunking: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_micro_chunking())
