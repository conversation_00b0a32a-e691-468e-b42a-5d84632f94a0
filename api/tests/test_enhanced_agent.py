#!/usr/bin/env python3
"""
Quick test of the enhanced tagging agent to see if it generates better purpose tags.
"""

import asyncio
import sys

# Add the api directory to the path
sys.path.append('/home/<USER>/work/massage-ai/api')

from services.segmentation.tagging_agent import get_segment_ontology

# Sample massage steps with high intensity kneading and tapping
test_steps = [
    {
        "step_number": "60",
        "roller_action_description": "もみ上げ叩き 3cyc",
        "air_action_description": "肩　左右　on",
        "kneading_speed": 120,
        "tapping_speed": 140,
        "position_3d": ["N+12", 17],
        "width": None,
        "type": "action"
    },
    {
        "step_number": "61",
        "roller_action_description": "3D : N+12 to N with もみ上げ 1cyc",
        "air_action_description": "手腕　右　on",
        "kneading_speed": 120,
        "tapping_speed": None,
        "position_3d": ["N", 5],
        "width": None,
        "type": "action"
    },
    {
        "step_number": "62",
        "roller_action_description": "3D : N → N+13 with もみ上げ叩き　1cyc",
        "air_action_description": "肩　左右　on",
        "kneading_speed": 110,
        "tapping_speed": 150,
        "position_3d": ["N+13", 18],
        "width": None,
        "type": "action"
    },
    {
        "step_number": "63",
        "roller_action_description": "3D : N+13 to N with もみ上げ叩き 1cyc",
        "air_action_description": "手腕　右　on",
        "kneading_speed": 120,
        "tapping_speed": 190,
        "position_3d": ["N", 5],
        "width": None,
        "type": "action"
    }
]

# Sample program context with raw description
test_context = {
    'program_name': 'Beauty Enhancement Program',
    'raw_program_description': '''
{
  "program_name": "Beauty",
  "intensity": 4,
  "recommended_position": "Zero-Gravity",
  "target_group_objective": "For people who desire skin beautification and anti-aging effects through improved circulation and lymphatic drainage",
  "programme_sequence": {
    "Front Sequence": "Gentle massage starting from shoulder to buttocks area with focus on circulation enhancement",
    "Main Sequence": "Deep tissue work targeting specific acupressure points for beauty enhancement and skin rejuvenation",
    "Cooling Sequence": "Relaxing finish with lymphatic drainage techniques for toxin elimination"
  },
  "targeted_acupressure_points": [
    "1) Shoulder Well (肩井) - Promotes blood circulation for skin health",
    "2) Lung Transporter (肺俞) - Enhances respiratory function for better oxygenation",
    "3) Kidney Transporter (腎俞) - Supports detoxification and anti-aging"
  ],
  "signature_moves": [
    "1) Gentle rolling with circulation-boosting techniques",
    "2) Lymphatic drainage patterns for beauty enhancement",
    "3) Acupressure point stimulation for skin rejuvenation"
  ]
}
'''
}

async def test_enhanced_agent():
    """Test the enhanced agent with sample data."""
    print("Testing Enhanced Tagging Agent...")
    print("=" * 50)
    
    try:
        # Test without context first
        print("\n1. Testing WITHOUT program context:")
        result_no_context = await get_segment_ontology(test_steps)
        print(f"Purpose Tags: {result_no_context.purpose_tags}")
        print(f"Technique Tags: {result_no_context.technique_tags}")
        print(f"Body Part Tags: {result_no_context.body_part_tags}")
        print(f"Intensity Score: {result_no_context.intensity_score}")
        
        # Test with context
        print("\n2. Testing WITH program context:")
        result_with_context = await get_segment_ontology(test_steps, test_context)
        print(f"Purpose Tags: {result_with_context.purpose_tags}")
        print(f"Technique Tags: {result_with_context.technique_tags}")
        print(f"Body Part Tags: {result_with_context.body_part_tags}")
        print(f"Intensity Score: {result_with_context.intensity_score}")
        
        # Analysis
        print("\n3. ANALYSIS:")
        print("Expected to see:")
        print("- High intensity neck/shoulder work (speeds 110-190)")
        print("- Kneading + tapping combination techniques")
        print("- Position movement from N+12 to N to N+13")
        print("- Air compression on shoulders and arms")
        
        print("\nActual results:")
        print(f"- Purpose tags capture intensity: {any('intensive' in tag or 'high' in tag or 'deep' in tag for tag in result_with_context.purpose_tags)}")
        print(f"- Purpose tags are specific: {not any(tag in ['tension_release', 'relaxation', 'stress_relief'] for tag in result_with_context.purpose_tags)}")
        print(f"- Technique tags include combination: {'combination' in result_with_context.technique_tags or len(result_with_context.technique_tags) > 1}")
        
    except Exception as e:
        print(f"Error testing enhanced agent: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_enhanced_agent())
