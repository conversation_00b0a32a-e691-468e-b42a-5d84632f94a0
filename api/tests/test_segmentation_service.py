import pytest
from unittest.mock import AsyncMock, patch, MagicMock
from sqlalchemy.orm import Session

from services.segmentation.orchestrator import SegmentationOrchestrator
from services.segmentation.tagging_agent import get_segment_ontology
from models.program_models import Program, ProgramSegment
from schemas.segmentation_schemas import SegmentOntology


@pytest.fixture
def mock_program():
    """Create a mock program for testing."""
    program = Program(
        id=1,
        name="Test Program",
        steps=[
            {"step_number": 1, "position_3d": 10, "width": "W", "kneading_speed": 100},
            {"step_number": 2, "position_3d": 15, "width": "M", "kneading_speed": 120},
            {"step_number": 3, "position_3d": 20, "width": "N", "kneading_speed": 80},
        ],
        program_description={
            "Front Sequence": "Gentle warming massage",
            "Main Sequence": "Deep tissue focus",
            "Cooling Sequence": "Relaxing finish"
        }
    )
    return program


@pytest.fixture
def mock_segment_ontology():
    """Create a mock segment ontology for testing."""
    return SegmentOntology(
        purpose_tags=["tension_release", "relaxation"],
        technique_tags=["kneading", "rolling"],
        body_part_tags=["shoulders", "upper_back"],
        intensity_score=7,
        entry_state={"position_3d": 10, "width": "W", "kneading_speed": 100},
        exit_state={"position_3d": 20, "width": "N", "kneading_speed": 80}
    )


class TestSegmentationOrchestrator:
    """Test the SegmentationOrchestrator class."""

    def test_orchestrator_initialization(self, db_session: Session):
        """Test that the orchestrator initializes correctly."""
        orchestrator = SegmentationOrchestrator(db_session)
        assert orchestrator.db == db_session
        assert orchestrator.enable_embedding is True

        orchestrator_no_embedding = SegmentationOrchestrator(db_session, enable_embedding=False)
        assert orchestrator_no_embedding.enable_embedding is False

    @patch('services.segmentation.orchestrator.get_macro_phases')
    @patch('services.segmentation.orchestrator.discover_micro_chunks')
    @patch('services.segmentation.orchestrator.get_segment_ontology')
    @patch('services.segmentation.orchestrator.upsert_segment_embedding')
    async def test_process_program_success(
        self,
        mock_upsert_embedding,
        mock_get_ontology,
        mock_discover_chunks,
        mock_get_phases,
        db_session: Session,
        mock_program,
        mock_segment_ontology
    ):
        """Test successful program processing."""
        # Mock the AI agent responses
        from schemas.segmentation_schemas import MacroPhaseSegmentation, MacroPhase, MicroChunkDiscovery, MicroChunk
        
        mock_get_phases.return_value = MacroPhaseSegmentation(
            phases=[
                MacroPhase(phase_name="front", start_index=0, end_index=0),
                MacroPhase(phase_name="main", start_index=1, end_index=1),
                MacroPhase(phase_name="cooling", start_index=2, end_index=2),
            ]
        )
        
        mock_discover_chunks.return_value = MicroChunkDiscovery(
            chunks=[
                MicroChunk(description="Test micro chunk", start_index=0, end_index=0)
            ]
        )
        
        mock_get_ontology.return_value = mock_segment_ontology
        mock_upsert_embedding.return_value = None

        orchestrator = SegmentationOrchestrator(db_session, enable_embedding=False)
        
        # Process the program
        await orchestrator.process_program(mock_program)

        # Verify the agents were called
        mock_get_phases.assert_called_once()
        assert mock_discover_chunks.call_count == 3  # Once for each macro phase
        assert mock_get_ontology.call_count == 6  # 3 macro phases + 3 micro chunks

        # Verify segments were created in the database
        segments = db_session.query(ProgramSegment).all()
        assert len(segments) == 6  # 3 macro phases + 3 micro chunks

        # Check macro phases
        macro_phases = [s for s in segments if s.parent_segment_id is None]
        assert len(macro_phases) == 3
        assert {s.phase for s in macro_phases} == {"front", "main", "cooling"}

        # Check micro chunks
        micro_chunks = [s for s in segments if s.parent_segment_id is not None]
        assert len(micro_chunks) == 3
        assert all(s.phase == "discovered" for s in micro_chunks)

    def test_save_segment(self, db_session: Session, mock_segment_ontology):
        """Test the _save_segment method."""
        orchestrator = SegmentationOrchestrator(db_session)
        
        test_steps = [{"step_number": 1, "position_3d": 10}]
        
        segment = orchestrator._save_segment(
            program_id=1,
            phase_name="test_phase",
            ontology=mock_segment_ontology,
            steps=test_steps,
            parent_id=None
        )

        assert segment.id is not None
        assert segment.original_program_id == 1
        assert segment.phase == "test_phase"
        assert segment.parent_segment_id is None
        assert segment.steps == test_steps
        assert segment.purpose_tags == mock_segment_ontology.purpose_tags
        assert segment.technique_tags == mock_segment_ontology.technique_tags
        assert segment.body_part_tags == mock_segment_ontology.body_part_tags
        assert segment.intensity_score == mock_segment_ontology.intensity_score
        assert segment.entry_state == mock_segment_ontology.entry_state
        assert segment.exit_state == mock_segment_ontology.exit_state


class TestSegmentOntologyAgent:
    """Test the segment ontology tagging agent."""

    @patch('services.segmentation.tagging_agent.ontology_tagging_agent.run')
    async def test_get_segment_ontology(self, mock_agent_run):
        """Test the get_segment_ontology function."""
        # Mock the agent response
        mock_result = MagicMock()
        mock_result.output = SegmentOntology(
            purpose_tags=["tension_release"],
            technique_tags=["kneading"],
            body_part_tags=["shoulders"],
            intensity_score=5,
            entry_state={"position_3d": 10},
            exit_state={"position_3d": 20}
        )
        mock_agent_run.return_value = mock_result

        test_steps = [
            {"step_number": 1, "position_3d": 10, "kneading_speed": 100},
            {"step_number": 2, "position_3d": 20, "kneading_speed": 80}
        ]

        result = await get_segment_ontology(test_steps)

        assert isinstance(result, SegmentOntology)
        assert result.purpose_tags == ["tension_release"]
        assert result.technique_tags == ["kneading"]
        assert result.body_part_tags == ["shoulders"]
        assert result.intensity_score == 5
        mock_agent_run.assert_called_once()

    async def test_get_segment_ontology_empty_steps(self):
        """Test that get_segment_ontology raises error for empty steps."""
        with pytest.raises(ValueError, match="Input 'steps' list cannot be empty"):
            await get_segment_ontology([])


class TestEmbeddingIntegration:
    """Test the embedding integration."""

    @patch('services.segmentation.orchestrator.upsert_segment_embedding')
    @patch('services.segmentation.orchestrator.get_macro_phases')
    @patch('services.segmentation.orchestrator.discover_micro_chunks')
    @patch('services.segmentation.orchestrator.get_segment_ontology')
    async def test_embedding_enabled(
        self,
        mock_get_ontology,
        mock_discover_chunks,
        mock_get_phases,
        mock_upsert_embedding,
        db_session: Session,
        mock_program,
        mock_segment_ontology
    ):
        """Test that embeddings are generated when enabled."""
        # Setup mocks
        from schemas.segmentation_schemas import MacroPhaseSegmentation, MacroPhase, MicroChunkDiscovery
        
        mock_get_phases.return_value = MacroPhaseSegmentation(
            phases=[MacroPhase(phase_name="front", start_index=0, end_index=0)]
        )
        mock_discover_chunks.return_value = MicroChunkDiscovery(chunks=[])
        mock_get_ontology.return_value = mock_segment_ontology

        orchestrator = SegmentationOrchestrator(db_session, enable_embedding=True)
        await orchestrator.process_program(mock_program)

        # Verify embedding was called for the macro phase
        mock_upsert_embedding.assert_called()

    @patch('services.segmentation.orchestrator.upsert_segment_embedding')
    @patch('services.segmentation.orchestrator.get_macro_phases')
    @patch('services.segmentation.orchestrator.discover_micro_chunks')
    @patch('services.segmentation.orchestrator.get_segment_ontology')
    async def test_embedding_disabled(
        self,
        mock_get_ontology,
        mock_discover_chunks,
        mock_get_phases,
        mock_upsert_embedding,
        db_session: Session,
        mock_program,
        mock_segment_ontology
    ):
        """Test that embeddings are not generated when disabled."""
        # Setup mocks
        from schemas.segmentation_schemas import MacroPhaseSegmentation, MacroPhase, MicroChunkDiscovery
        
        mock_get_phases.return_value = MacroPhaseSegmentation(
            phases=[MacroPhase(phase_name="front", start_index=0, end_index=0)]
        )
        mock_discover_chunks.return_value = MicroChunkDiscovery(chunks=[])
        mock_get_ontology.return_value = mock_segment_ontology

        orchestrator = SegmentationOrchestrator(db_session, enable_embedding=False)
        await orchestrator.process_program(mock_program)

        # Verify embedding was not called
        mock_upsert_embedding.assert_not_called()
