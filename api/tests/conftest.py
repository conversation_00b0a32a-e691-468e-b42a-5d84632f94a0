import pytest
from fastapi.testclient import TestClient
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, Session
from typing import Generator, Any, Dict

from main import app  # Import your FastAPI app
from core.database import Base, get_db
from auth.cognito import get_current_user
# Import all models to populate Base.metadata
import models
from models.account_models import User as UserModel

# Use an in-memory SQLite database for testing
SQLALCHEMY_DATABASE_URL = "sqlite:///:memory:"

engine = create_engine(
    SQLALCHEMY_DATABASE_URL, connect_args={"check_same_thread": False}
)
TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# Fixture to create and drop tables for each test function
@pytest.fixture(scope="function")
def setup_database():
    Base.metadata.create_all(bind=engine)  # Create tables
    yield
    Base.metadata.drop_all(bind=engine)  # Drop tables after test

@pytest.fixture(scope="function")
def db_session(setup_database: None) -> Generator[Session, Any, None]:
    """
    Create a new database session for a test.
    Rolls back any changes after the test.
    """
    connection = engine.connect()
    transaction = connection.begin()
    session = TestingSessionLocal(bind=connection)
    yield session
    session.close()
    transaction.rollback()
    connection.close()

@pytest.fixture(scope="function")
def mock_current_user() -> Dict:
    # Create a mock user. Adjust attributes as needed for your tests.
    # This user will be used for endpoints requiring authentication.
    # Ensure it has an 'id' attribute at minimum if your get_current_user expects it.
    # If your get_current_user returns a dict, adjust this fixture accordingly.
    # For example, if get_current_user in your app returns something like:
    # {"id": 1, "username": "testuser", "email": "[email protected]", "cognito_id": "cognito_test_id", "roles": ["ai_engineer"]}
    # Then this fixture should return a similar dict or an object that mimics it.
    # Based on api/routes/chat_routes.py, get_current_user returns a dict.
    # The @require_ai_engineer decorator implies a "roles" check or similar.
    # For simplicity, let's assume it needs an 'id' and maybe a role.
    # The user object from get_current_user is used in `Chat.user_id == user.id`
    # and in `create_chat_config_handler(db, user, chat_config)` which might use user.id
    # Let's return a dict as expected by the application code.
    # The `require_ai_engineer` decorator likely checks a 'custom:roles' or similar field from Cognito.
    return {
        "id": 1,  # A dummy user ID
        "username": "testuser",
        "email": "[email protected]",
        "cognito_id": "test-cognito-id",
        "custom:roles": "ai_engineer" # Role for @require_ai_engineer
    }

# Override get_db dependency for testing
def override_get_db():
    try:
        db = TestingSessionLocal()
        yield db
    finally:
        db.close()

# Global dependency overrides are now handled per-test in the client fixture


@pytest.fixture(scope="function")
def client(db_session: Session) -> Generator[TestClient, Any, None]:
    """
    Create a new FastAPI TestClient that uses the `db_session` fixture to override
    the `get_db` dependency that is injected into routes.
    """
    # Override get_db to use the test database session
    def override_get_db_for_test():
        try:
            yield db_session
        finally:
            pass  # Don't close db_session here as it's managed by the fixture
    
    app.dependency_overrides[get_db] = override_get_db_for_test
    
    with TestClient(app) as c:
        yield c
    
    # Clean up the override
    app.dependency_overrides.pop(get_db, None)

# Fixture for a mock AI engineer user
@pytest.fixture
def ai_engineer_user(db_session: Session) -> UserModel:
    user = UserModel(
        username="ai_engineer_test",
        email="[email protected]",
        cognito_id="cognito_ai_engineer",
        name="AI Engineer Test"
    )
    db_session.add(user)
    db_session.commit()
    db_session.refresh(user)
    return user

# Fixture for a mock regular user (non-AI engineer)
@pytest.fixture
def regular_user(db_session: Session) -> UserModel:
    user = UserModel(
        username="regular_user_test",
        email="[email protected]",
        cognito_id="cognito_regular_user",
        name = "Regular User Test"
    )
    db_session.add(user)
    db_session.commit()
    db_session.refresh(user)
    return user

# Helper to override get_current_user with a specific user for a test
def override_get_current_user_with(user_obj: UserModel):
    def _override():
        return user_obj
    return _override
