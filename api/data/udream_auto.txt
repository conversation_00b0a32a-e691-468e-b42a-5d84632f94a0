//***************************//****************************************************************************
// File Name  :download_course.cpp
// Description:OSIM��????��?��??�C?���h??�h??��??�F�h?�O
// Create Date:2009.12.10
// Copy Right :����??��?(?)
//****************************************************************************
#include "stdafx.h"
#include "common.h"

//------------------------------------------------------------------------------
// PG1: uDream Signature 14 August 2020
//------------------------------------------------------------------------------
T_OPERATION	osim_pg1_course_tbl[] =
{
	TRACK_SELECT,		MEDIUM,					0,		0,		0,		0X0000,			0,
	ASI_START,			0,						0,		0,		0,		0X0000,			MODE_AUTO1,
//1
	POS,				POS_SHOULDER,			110,	0,		REW,	0X000C+NOHOLD,	M_KNEAD_ROLPART,
//2
	SEATM_START,		0,						0,		0,		0,		0X0000,			SM_AUTO45,
	WIDTH_SET,			0,						0,		0,		PN,		0X0000,			0,
	POS_3D_UP,			4,						0,		130,	PN,		0X0000,			M_TAP,
	TIMER,				4,						0,		130,	0,		0X00C0+NOHOLD,	M_TAP,
//5
	POS,				POS_SHOULDER_UPPER,		0,		0,		0,		0X000C+NOHOLD,	M_ROLPART,
//6
///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
////////////////////////////////////////// Subroutine Squeeze #4 ����`���� Ver.18 ////////////////////////////////////////
/////////////////////////////////////////////////// Start /////////////////////////////////////////////////////////////////
//1
	POS_3D_UP,			8,						0,		0,		0,		0X000C+NOHOLD,	0,
//2
	ROTATION,			3,						105,	0,		REW,	0X00C0+NOHOLD,	M_KNEAD,
//3
	POS,				POS_SHOULDER,			105,	0,		REW,	0X000C+NOHOLD,	M_KNEAD_ROLPART,
//4
	POS_3D_UP,			2,						0,		0,		0,		0X000C+NOHOLD,	0,
//5
	ROTATION,			3,						105,	0,		REW,	0X00C0+NOHOLD,	M_KNEAD,
//6
	POS,				POS_SHOULDER_LOWER,		105,	0,		REW,	0X00C0+NOHOLD,	M_KNEAD_ROLPART,
//7
	POS_3D_UP,			5,						0,		0,		0,		0X0000,			0,
	ROTATION,			3,						105,	0,		REW,	0X0010+NOHOLD,	M_KNEAD,
//8
	POS_3D_DOWN,		5,						105,	0,		REW,	0X0010+NOHOLD,	M_KNEAD,
////////////////////////////////////////////////////// End ////////////////////////////////////////////////////
//7
	POS,				POS_SHOULDER_UPPER,		100,	0,		PW,		0X00C0+NOHOLD,	M_KNEAD_ROLPART,
//8
	POS_3D_UP,			8,						0,		0,		0,		0X0010+NOHOLD,	0,
	TIMER,				3,						0,		0,		0,		0X0010+NOHOLD,	0,
//8A
	WIDTH_SET,			0,						0,		0,		PN,		0X0000,			0,
//9
	TIMER,				4,						0,		150,	0,		0X0020+NOHOLD,	M_TAP,
//10
	POS_3D_DOWN,		8,						0,		150,	0,		0X000C+NOHOLD,	M_TAP,
//11A
	SEATM_START,		0,						0,		0,		0,		0X0000,			SM_AUTO76,
///////////////////////////////////////////////////////////////////////////////////////////////////////////
//////////////////////////////// Subroutine Squeez #10 ���� �`�� Ver.13 //////////////////////////////////
////////////////////////////////////////////////// Start /////////////////////////////////////////////////
//1 
	POS,				POS_SHOULDER_UPPER,		110,	0,		0,		0X000C+NOHOLD,	M_KNEAD_ROLPART,
	ROTATION,			1,						105,	0,		REW,	0X000C+NOHOLD,	M_KNEAD,
	TIMER,				1,						0,		0,		0,		0X000C+NOHOLD,	0,
	WIDTH_SET,			0,						0,		0,		PW,		0X000C+NOHOLD,	0,
//2
	POS_3D_UP,			8,						0,		0,		0,		0X00C0+NOHOLD,	0,
	WIDTH_SET,			0,						105,	0,		PN,		0X00C0+NOHOLD,	M_KNEAD,
	TIMER,				2,						0,		0,		0,		0X00C0+NOHOLD,	0,
//3
	WIDTH_SET,			0,						105,	0,		REW+PW,	0X00C0+NOHOLD,	M_KNEAD,
	TIMER,				1,						0,		0,		0,		0X00C0+NOHOLD,	0,
//3A
	WIDTH_SET,			0,						105,	0,		PN,		0X00C0+NOHOLD,	M_KNEAD,
	TIMER,				2,						0,		0,		0,		0X00C0+NOHOLD, 	0,
//6
	POS,				POS_SHOULDER,			0,		0,		0,		0X00C0+NOHOLD,	M_ROLPART,
//7
	POS_3D_UP,			2,						0,		0,		0,		0X000C+NOHOLD,	0,
	TIMER,				1,						0,		0,		0,		0X000C+NOHOLD,	0,
//8
	ROTATION,			2,						105,	0,		REW+PW,	0X0020+NOHOLD,	M_KNEAD,
	TIMER,				1,						0,		0,		0,		0X0020+NOHOLD,	0,
	ROTATION,			1,						105,	0,		REW+PW,	0X0020,			M_KNEAD,	
//9
	WIDTH_SET,			0,						105,	0,		PN,		0X000C+NOHOLD,	M_KNEAD,
	TIMER,				2,						0,		0,		0,		0X000C+NOHOLD,	0,	
//10
	TIMER,				6,						0,		120,	0,		0X0010+NOHOLD,	M_TAP,
//11
 	POS_3D_DOWN,		2,						0,		0,		0,		0X000C+NOHOLD,	0,
////////////////////////////////////////////////////// END ////////////////////////////////////////////////////
//12
	POS,				POS_SHOULDER_LOWER,		110,	0,		0,		0X00C0+NOHOLD,	M_KNEAD_ROLPART,
//13
	ROTATION,			3,						110,	0,		PN,		0X00C0+NOHOLD,	M_KNEAD,
//14
	TIMER,				7,						0,		140,	0,		0X000C+NOHOLD,	M_TAP,
//15
	POS_3D_DOWN,		4,						0,		140,	0,		0X000C+NOHOLD,	M_TAP,
//15A
	POS,				POS_BACK,				0,		150,	0,		0x000C+NOHOLD,	M_TAP_ROLPART,
//16
	POS,				POS_HIPS,				0,		160,	0,		0X0010+NOHOLD,	M_TAP_ROLPART,
//16A
	POS_3D_UP,			10,						0,		180,	0,		0x000C+NOHOLD,	M_TAP,
	TIMER,				2,						0,		0,		0,		0x000C+NOHOLD,	0,
//17
	POS_3D_UP,			4,						0,		180,	0,		0X000C+NOHOLD,	M_TAP,
	TIMER,				4,						0,		0,		0,		0X000C+NOHOLD,	0,
//18
	ROTATION,			3,						120,	0,		REW,	0X0020+NOHOLD,	M_KNEAD,
//19
	POS_3D_DOWN,		14,						120,	0,		REW,	0X000C+NOHOLD,	M_KNEAD,
//20
	POS_3D,				FIT_POS0,				120,	0,		REW,	0X000C+NOHOLD,	M_KNEAD,
//21
	SEATM_START,		0,						0,		0,		0,		0X0000,			SM_AUTO73,
	WIDTH_SET,			0,						0,		0,		PN,		0X000C+NOHOLD,	0,
//22
	POS,				POS_WAIST,				0,		160,	0,		0X000C+NOHOLD,	M_TAP_ROLPART,
//22A
	POS_3D_UP,			12,						0,		160,	0,		0x0010+NOHOLD,	M_TAP,
	POS_3D_UP,			6,						0,		160,	0,		0x0010+NOHOLD,	M_TAP,	
	TIMER,				2,						0,		0,		0,		0x0010+NOHOLD,	0,
//23
	POS_3D_UP,			4,						0,		160,	0,		0X0010+NOHOLD,	M_TAP,
	TIMER,				3,						0,		0,		0,		0X0010+NOHOLD,	0,
//24
	ROTATION,			3,						120,	180,	REW,	0X000C+NOHOLD,	M_KNEAD_TAP,
//25
	POS_3D_UP,			2,						0,		0,		0,		0X0020+NOHOLD,	0,
	ROTATION,			3,						120,	0,		REW,	0X0020+NOHOLD,	M_KNEAD,
//26
	POS_3D_DOWN,		14,						120,	0,		REW,	0X000C+NOHOLD,	M_KNEAD,
	TIMER,				1,						0,		0,		0,		0X000C+NOHOLD,	0,
	POS_3D_DOWN,		10,						120,	0,		REW+PM,	0X000C+NOHOLD,	M_KNEAD,
//27
	POS,				POS_WAIST_UPPER1,		0,		160,	0,		0X000C+NOHOLD,	M_TAP_ROLPART,
//27A
	POS_3D_UP,			14,						0,		160,	0,		0X000C+NOHOLD,	M_TAP,
	POS_3D_UP,			5,						0,		160,	0,		0X000C+NOHOLD,	M_TAP,	
	TIMER,				2,						0,		160,	0,		0X000C+NOHOLD,	M_TAP,
//28
	POS_3D_UP,			3,						0,		170,	0,		0X000C+NOHOLD,	M_TAP,
	TIMER,				2,						0,		0,		0,		0X000C+NOHOLD,	0,
//29
	TIMER,				6,						0,		180,	0,		0X00C0+NOHOLD,	M_TAP,
//30
	ROTATION,			3,						120,	180,	REW,	0X0040+NOHOLD,	M_KNEAD_TAP,
//32
	ROTATION,			3,						100,	0,		REW,	0X0080+NOHOLD,	M_KNEAD,
//34
	POS_3D_DOWN,		14,						120,	0,		REW,	0X000C+NOHOLD,	M_KNEAD,
	TIMER,				1,						0,		0,		0,		0X000C+NOHOLD,	0,
	POS_3D_DOWN,		10,						120,	0,		REW,	0X000C+NOHOLD,	M_KNEAD,
//34A
	POS,				POS_SHDBLADE_LOWER,		120,	150,	REW+PN,	0X000C+NOHOLD,	M_KNEAD_TAP_ROLPART,
//34B
	POS,				POS_SHOULDER,			0,		180,	0,		0X00C0+NOHOLD,	M_OSI_ROLPART,	
//34C
	POS_3D_UP,			3,						105,	0,		REW+PN,	0X0010+NOHOLD,	M_KNEAD,
	ROTATION,			3,						105,	0,		REW+PN,	0X0010+NOHOLD,	M_KNEAD,
//34ca
	POS_3D_DOWN,		10,						105,	0,		REW,	0X000C+NOHOLD,	M_KNEAD,
	POS_3D_UP,			13,						105,	0,		REW,	0X000C+NOHOLD,	M_KNEAD,
//34C'
	POS,				POS_NECK_UPPER1,		0,		0,		0,		0X0000,			M_ROLPART,
//34D
	POS,				POS_UPLIMIT,			105,	0,		REW,	0X000C+NOHOLD,	M_KNEAD_ROLPART,
//34E
	POS_3D_UP,			2,						100,	0,		REW,	0X0020+NOHOLD,	M_KNEAD,
	ROTATION,			3,						100,	0,		REW,	0X0020+NOHOLD,	M_KNEAD,
//34F
	POS_3D,				FIT_POS0,				0,		0,		0,		0X000C+NOHOLD,	0,
	THREED_CTRL_DISABLE,0,						0,		0,		0,		0X000C+NOHOLD,	0,
//34F'
	POS_DOWN,			5,						100,	0,		REW,	0X000C+NOHOLD,	M_KNEAD_ROLPART,
//34G
	SEATM_START,		0,						0,		0,		0,		0X0000,			SM_AUTO74,
	ROTATION,			3,						100,	0,		PN,		0X00C0+NOHOLD,	M_KNEAD,
//34H
	POS,				POS_NECK_UPPER,			110,	0,		0,		0X00C0+NOHOLD,	M_KNEAD_ROLPART,
//34I
	THREED_CTRL_ENABLE,	0,						0,		0,		0,		0X000C+NOHOLD,	0,
//34J
	POS,				POS_SHOULDER_LOWER,		110,	0,		0,		0X000C+NOHOLD,	M_KNEAD_ROLPART, // 8min
//34K
	POS_3D_UP,			8,						100,	0,		REW+PM,	0X0010+NOHOLD,	M_KNEAD,
	TIMER,				6,						100,	0,		REW+PM,	0X0010+NOHOLD,	M_KNEAD,
	TIMER,				4,						100,	0,		REW+PM,	0X0010,			M_KNEAD,
//34KA
	POS_3D_DOWN,		6,						105,	0,		REW,	0X000C+NOHOLD,	M_KNEAD,
	POS_3D_UP,			4,						105,	0,		REW,	0X000C+NOHOLD,	M_KNEAD,
//34H'
	POS,				POS_NECK_UPPER1,		0,		0,		PN,		0X00C0+NOHOLD,	M_ROLPART,
//34L
	POS,				POS_UPLIMIT,			110,	0,		REW,	0X000C+NOHOLD,	M_KNEAD_ROLPART,
//34M
	ROTATION,			3,						100,	0,		REW,	0X0020+NOHOLD,	M_KNEAD,	
//34N
	POS,				POS_SHDBLADE_UPPER,		110,	0,		PN,		0X000C+NOHOLD,	M_KNEAD_ROLPART,	
//45	
	POS,				POS_BACK,				0,		0,		0,		0X000C+NOHOLD,	M_ROLPART,
//46
	ROTATION,			3,						100,	0,		REW+PN,	0X0040+NOHOLD,	M_KNEAD,
//47
	POS,				POS_SHDBLADE_UPPER,		0,		0,		0,		0X000C+NOHOLD,	M_ROLPART,
//48
	ROTATION,			3,						100,	0,		REW+PN,	0X0080+NOHOLD,	M_KNEAD,
	TIMER,				2,						0,		0,		0,		0X0080+NOHOLD,	0,
//50
	GO_UP_TIME,			8,						0,		140,	PN,		0X00CO+NOHOLD,	M_OSI_ROLPART,
//51
	GO_DOWN_TIME,		8,						0,		150,	PN,		0X000C+NOHOLD,	M_OSI_ROLPART,
//54
	POS,				POS_SHDBLADE_LOWER,		0,		150,	PN,		0X0040+NOHOLD,	M_OSI_ROLPART,
//55
	POS,				POS_BACK,				0,		160,	PN,		0X000C+NOHOLD,	M_OSI_ROLPART,
//56
	POS,				POS_BACK_UPPER,			110,	0,		0,		0X00C0+NOHOLD,	M_KNEAD_ROLPART,
//57
	SEATM_START,		0,						0,		0,		0,		0X0000,			SM_AUTO75,
	ROTATION,			3,						110,	0,		0,		0X0080+NOHOLD,	M_KNEAD,
//59
	GO_DOWN_TIME,		8,						0,		0,		PW,		0X0010+NOHOLD,	M_ROLPART,
//59a
	POS_3D,				FIT_POS0,				0,		0,		0,		0X0000,			0,
	TIMER,				1,						0,		0,		0,		0X0000,			0,
//60
	TIMER,				8,						0,		160,	0,		0X0020+NOHOLD,	M_OSI,
//63
	POS,				POS_SHDBLADE,			120,	0,		0,		0X00C0+NOHOLD,	M_KNEAD_ROLPART,
//64
	POS, 				POS_SHOULDER_LOWER,		125,	0,		REW+PN,	0X0000,			M_KNEAD_ROLPART,
//65

///////////////////////////////////////////////////////////////////////////////////////////////////////////////
///////////////////////////////////////////uLegacy Subroutine Neck and Shoulder Roll  #2 Ver10//////////////////
//////////////////////////////////////////////////////		START		////////////////////////////////////////////////
//1
	POS,				POS_SHOULDER_LOWER2,	0,		0,		PN,		0X000C+NOHOLD,		M_ROLPART,
//1A
	POS_3D_UP,			6,						0,		0,		0,		0X000C+NOHOLD,		0,
	THREED_CTRL_DISABLE,0,						0,		0,		0,		0X0000,				0,
//2
	TIMER,				1,						0,		0,		0,		0X000C+NOHOLD,		0,
//3
	POS,				POS_NECK_UPPER,			0,		0,		0,		0X0020+NOHOLD,		M_ROLPART,
	TIMER,				2,						0,		0,		0,		0X0020+NOHOLD,		0,
//3A
	POS_UP,				5,						0,		0,		0,		0X0020,				M_ROLPART,
//3B
	WIDTH_SET,			0,						0,		0,		PW,		0X000C+NOHOLD,		0,
	WIDTH_SET,			0,						0,		0,		PN,		0X000C+NOHOLD,		0,
	TIMER,				2,						0,		0,		0,		0X000C+NOHOLD,		0,
//4A
	POS_3D,				FIT_POS23,				0,		0,		0,		0X000C+NOHOLD,		0,
//5
	POS,				POS_SHOULDER_LOWER,		0,		0,		0,		0X0010+NOHOLD,		M_ROLPART,
	TIMER,				3,						0,		0,		0,		0X0010+NOHOLD,		0,	
//5A
	THREED_CTRL_ENABLE,	0,						0,		0,		0,		0X0000,				0,
//6
	POS,				POS_SHDBLADE_UPPER,		0,		0,		0,		0X000C+NOHOLD,		M_ROLPART,
//7 
	POS,				POS_SHDBLADE,			0,		0,		0,		0X000C+NOHOLD,		M_ROLPART,
	TIMER,				3,						0,		0,		0,		0X0000,				0,	
//8
	POS,				POS_SHOULDER,			0,		0,		0,		0X00C0+NOHOLD,		M_ROLPART,
//9
	POS_3D_UP,			4,						0,		150,	0,		0X000C+NOHOLD,		M_TAP,
//10
	TIMER,				6,						0,		150,	0,		0X00C0+NOHOLD,		M_TAP,
//11
	POS_3D_DOWN,		4,						0,		0,		0,		0X000C+NOHOLD,		0,
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////
////////////////////////////////////////uLegacy Subroutine Neck and Shoulder Roll////////////////////////////////
/////////////////////////////////////////////		END		/////////////////////////////////////////////////////
//66
	POS,				POS_SHOULDER,			0,		140,	0,		0X00C0+NOHOLD,		M_OSI_ROLPART,
//67
	POS,				POS_UPLIMIT,			0,		90,		0,		0X000C+NOHOLD,		M_OSI_ROLPART,
//68
	ROTATION,			3,						100,	0,		PN,		0X0010+NOHOLD,		M_KNEAD,
//69
	POS,				POS_NECK_UPPER,			0,		90,		0,		0X0010+NOHOLD,		M_OSI_ROLPART,
//70
	WIDTH_SET,			0,						0,		0,		PN,		0X0000,				0,
//71
	SEATM_START,		0,						0,		0,		0,		0X0000,				SM_AUTO72,
	POS,				POS_NECK_UPPER1,		0,		100,	0,		0X0020+NOHOLD,		M_OSI_ROLPART,
//71A
	POS_3D,				FIT_POS0,				0,		0,		0,		0X0000,				0,
	THREED_CTRL_DISABLE,0,						0,		0,		0,		0X0000,				0,
//72
	ROTATION,			3,						100,	0,		PN,		0X0020+NOHOLD,		M_KNEAD,
//82
	THREED_CTRL_ENABLE,	0,						0,		0,		0,		0X000C+NOHOLD,		0,	

//84
	POS,				POS_NECK,				100,	0,		0,		0X000C+NOHOLD,		M_KNEAD_ROLPART,
//85
	POS_3D_UP,			2,						100,	0,		PN,		0X000C+NOHOLD,		M_KNEAD,

	ROTATION,			3,						100,	0,		PN,		0X000C+NOHOLD,		M_KNEAD,
//86
	POS,				POS_SHOULDER_LOWER2,	0,		150,	0,		0X000C+NOHOLD,		M_OSI_ROLPART,	
//86A
	POS,				POS_SHDBLADE_UPPER,		115,	180,	0,		0X0020+NOHOLD,		M_KNEAD_OSI_ROLPART,//86B
//86C
	POS,				POS_HIPS,				120,	150,	0,		0X000C+NOHOLD,		M_KNEAD_TAP_ROLPART,
//86C'
	POS_3D_UP,			12,						120,	180,	0,		0x0000,				M_KNEAD_TAP,
	TIMER,				2,						0,		0,		0,		0x0000,				0,
//86D
	POS_3D_UP,			16,						120,	180,	0,		0X000C+NOHOLD,		M_KNEAD_TAP,
	ROTATION,			3,						120,	180,	0,		0X000C+NOHOLD,		M_KNEAD_TAP,
//86E
	POS_3D_DOWN,		8,						120,	180,	0,		0X000C+NOHOLD,		M_KNEAD_TAP,
	POS_3D_DOWN,		8,						120,	180,	0,		0X000C+NOHOLD,		M_KNEAD_TAP,
//86F
	SEATM_START,		0,						0,		0,		0,		0X0000,				SM_AUTO73,
	POS,				POS_SHOULDER_LOWER2,	120,	0,		REW+PN,	0X000C+NOHOLD,		M_KNEAD_ROLPART,
//86G
	ROTATION,			3,						100,	0,		0,		0X00C0+NOHOLD,		M_KNEAD,	
////////////////////////////////////////////////////////////////////////////////////////////////////
	POS_RET,	        0,						0,		0,		0,		0X0000,				0,
////////////////////////////////////////////////////////////////////////////////////////////////////////
//87
	POS,				POS_BACK,				110,	150,	0,		0X000C+NOHOLD,		M_KNEAD_TAP_ROLPART,
//88
	POS,				POS_WAIST,				110,	160,	0,		0X0010+NOHOLD,		M_KNEAD_TAP_ROLPART,
//88A
	POS_3D_UP,			12,						120,	180,	0,		0x0010,				M_KNEAD_TAP,
	TIMER,				2,						120,	180,	0,		0x0010,				M_KNEAD_TAP,
//89
	POS_3D_UP,			18,						120,	170,	0,		0X000C+NOHOLD,		M_KNEAD_TAP,
	ROTATION,			3,						120,	170,	0,		0X000C+NOHOLD,		M_KNEAD_TAP,
//90
	POS_3D_DOWN,		10,						120,	170,	0,		0X0020+NOHOLD,		M_KNEAD_TAP,
	POS_3D_DOWN,		8,						120,	170,	0,		0X0020+NOHOLD,		M_KNEAD_TAP,
//91
	POS,				POS_WAIST_UPPER1,		110,	160,	REW,	0X000C+NOHOLD,		M_KNEAD_TAP_ROLPART,
//91A
	POS_3D_UP,			19,						0,		180,	REW,	0x0020+NOHOLD,		M_TAP,
	ROTATION,			2,						0,		180,	REW,	0x0020+NOHOLD,		M_TAP,
//92
	POS_3D_UP,			23,						120,	160,	REW,		0X0020,			M_KNEAD_TAP,
	ROTATION,			2,						120,	160,	REW,	0X0020,				M_KNEAD_TAP,
//93
	POS_3D_DOWN,		13,						120,	160,	0,		0X000C+NOHOLD,		M_KNEAD_TAP,
	TIMER,				1,						0,		0,		0,		0X000C+NOHOLD,		0,	
	POS_3D_DOWN,		10,						120,	160,	REW,	0X000C+NOHOLD,		M_KNEAD_TAP,
//94
	POS,				POS_SHDBLADE_UPPER,		110,	140,	REW,	0X00C0+NOHOLD,		M_KNEAD_TAP_ROLPART,
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
	COOLDOWN,	        60,						0,		0,		0,		0X0000,			0,
////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//95
	WIDTH_SET,			0,						0,		0,		PN,		0X000C+NOHOLD,		0,
	POS_3D,				FIT_POS0,				0,		0,		PN,		0X000C+NOHOLD,		0,
//96
	POS,				POS_BACK_UPPER,			0,		130,	0,		0X000C+NOHOLD,		M_TAP_ROLPART,
//97
	POS,				POS_SHOULDER,			0,		120,	0,		0X00C0+NOHOLD,		M_TAP_ROLPART,
//98
	ROTATION,			2,						100,	0,		REW+PN,	0X000C+NOHOLD,		M_KNEAD,
//99
	POS,				POS_UPLIMIT,			0,		0,		0,		0X0010+NOHOLD,		M_ROLPART,
//100
	ROTATION,			2,						90,		0,		PN,		0X000C+NOHOLD,		M_KNEAD,
//101
	POS,				POS_SHOULDER,			0,		0,		0,		0X0020+NOHOLD,		M_ROLPART,
//102
	ROTATION,			2,						100,	0,		REW,	0X000C+NOHOLD,		M_KNEAD,

	0xff,				0xff,					0xff,	0xff,	0xff,	0xffff,			0xff
};
//------------------------------------------------------------------------------
// PG2: uDream Head & Neck 17 April 2020 
//------------------------------------------------------------------------------
T_OPERATION	osim_pg2_course_tbl[] =										
{
	
    TIMER,              1,                     0,      0,       0,      0X0000,         0,
    TRACK_SELECT,       WEAK,                  0,      0,       0,      0X0000,         0,//////////////
    ASI_START,          0,                     0,      0,       0,      0X0000,         MODE_AUTO3,
 //1
    SEATM_START,        0,                     0,      0,       0,      0X0000,         SM_AUTO53,
    POS,                POS_SHOULDER,          110,    0,       REW,    0X000C+NOHOLD,  M_KNEAD,
    ROTATION,           2,                     110,    0,       REW,    0X000C+NOHOLD,  M_KNEAD,
//1A
    POS,               POS_SHOULDER_UPPER,     0,      0,       PN,     0X000C+NOHOLD,  M_ROLPART,
//2
   POS_3D_UP,           8,                     0,      0,       0,      0X0020+NOHOLD,  0,
   ROTATION,            3,                     120,    0,       REW,    0X0020+NOHOLD,  M_KNEAD,
//3
   POS_3D_DOWN,         8,                     0,      0,       0,      0X000C+NOHOLD,  0,
//4
   POS,                 POS_SHOULDER,          120,    0,       REW,    0X000C+NOHOLD,  M_KNEAD_ROLPART,
//5
   POS_3D_UP,           5,                     0,      0,       0,      0X0010+NOHOLD,  0,
   ROTATION,            3,                     120,    0,       REW,    0X0010+NOHOLD,  M_KNEAD,
//6
   POS_3D_DOWN,         5,                     120,    0,       REW,    0X000C+NOHOLD,  M_KNEAD,
//7
   POS,                 POS_SHOULDER_LOWER,    120,    0,       0,      0X000C+NOHOLD,  M_KNEAD_ROLPART,
//8
   POS_3D_UP,           5,                     0,      0,        0,     0X00C0+NOHOLD,  0, 
   ROTATION,            3,                     120,    0,       REW,    0X00C0+NOHOLD,  M_KNEAD,
//9
   POS_3D_UP,           2,                     0,      0,       0,      0X00C0+NOHOLD,  0,
   ROTATION,            3,                     120,    140,     REW,    0X00C0+NOHOLD,  M_KNEAD_TAP,
//10
   POS,                 POS_SHOULDER,          120,    150,     REW,    0X0000+NOHOLD,  M_KNEAD_TAP_ROLPART,
//11
   POS_3D_UP,           6,                     0,       0,      0,      0X0020+NOHOLD,  0,
   ROTATION,            2,                     120,     0,      REW+PN, 0X0020+NOHOLD,  M_KNEAD, 
   ROTATION,            1,                     120,     0,      REW+PN, 0X0020+NOHOLD,  M_KNEAD,
//12
   TIMER,               4,                     0,       140,    0,      0X0010+NOHOLD,  M_TAP,
//14
   POS_3D_DOWN,         6,                     0,       0,      0,      0X0010+NOHOLD,  0,

////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
////////////////////////Subroutine uLegacy Neck Roll For Head Rec.6/////////////////////////////////////////////////
////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
// 0, 
   SEATM_START,         0,                     0,       0,      0,      0X0000+NOHOLD,  SM_AUTO54,
//1
   POS_3D_UP,           5,                     0,       0,      0,      0X000C+NOHOLD,  0,
   WIDTH_SET,           0,                     0,       0,      PM,     0X000C+NOHOLD,  0, 
//2
   WIDTH_SET,           0,                     0,       0,      PN,     0X00C0+NOHOLD,  0,
   TIMER,               1,                     0,       0,      0,      0X00C0,         0,
   
//3
   POS,                 POS_NECK_UPPER,        0,       0,      0,      0X00C0+NOHOLD,  M_ROLPART,
   TIMER,               4,                     0,       0,      0,      0X00C0,         0, 
//3A
   POS_3D_UP,           3,                     0,       0,      0,      0X000C+NOHOLD,  0,
   TIMER,               4,                     0,       0,      0,      0X000C,         0,
//3B
   POS_3D_DOWN,         3,                     0,       0,      0,      0X000C+NOHOLD,  0,
//4A
   POS_3D,              FIT_POS3,              0,       0,      0,      0X000C+NOHOLD,  0,
   THREED_CTRL_DISABLE, 0,                     0,       0,      0,      0X0000+NOHOLD,  0,
//5
   POS,                 POS_UPLIMIT,           0,       0,      0,      0X0010+NOHOLD,  M_ROLPART,
   TIMER,               1,                     0,       0,      0,      0X0010,         0,
//5A
   WIDTH_SET,           0,                     0,       0,      PM,     0X0010+NOHOLD,  0,
//6
   POS,                 POS_NECK_UPPER,        0,       0,      0,      0X0020+NOHOLD,  M_ROLPART,
   TIMER,               1,                     0,       0,      0,      0X0020,         0,
//6A
   THREED_CTRL_ENABLE,  0,                     0,       0,      0,      0X000C+NOHOLD,  0,
//7
   WIDTH_SET,           0,                     0,       0,      PN,     0X000C+NOHOLD,  0,
//8
   POS_3D,              FIT_POS22,             0,       0,      0,      0X000C+NOHOLD,  0,
//9
   THREED_CTRL_DISABLE, 0,                     0,       0,      0,      0X000C+NOHOLD,  0,
//10\
   POS,                 POS_SHOULDER_UPPER,    0,       0,      0,      0X00C0+NOHOLD,  M_ROLPART,
   TIMER,               2,                     0,       0,      0,      0X00C0,         0,
//11
   POS,                 POS_NECK_UPPER,        0,       0,      0,      0X00C0+NOHOLD,  M_ROLPART,
   TIMER,               4,                     0,       0,      0,      0X00C0,         0, 
//12
   THREED_CTRL_ENABLE,  0,                     0,       0,      0,      0X000C+NOHOLD,  0,
//14
   POS,                 POS_UPLIMIT,           90,      0,      REW,    0X0010+NOHOLD,  M_KNEAD_ROLPART,
   TIMER,               2,                     90,      0,      REW,    0X0010,         M_KNEAD,
//14A
   WIDTH_SET,           0,                     0,       0,      PM,     0X0000+NOHOLD,  0,
//14B
   POS_3D,              FIT_POS0,              0,       0,      0,      0X000C+NOHOLD,  0,
   THREED_CTRL_DISABLE, 0,                     0,       0,      0,      0X000C+NOHOLD,  0,
//15
   POS,                 POS_NECK_UPPER,        120,     0,      0,      0X0020+NOHOLD,  M_KNEAD_ROLPART,
   TIMER,               1,                     120,     0,      PN,     0X0020,         M_KNEAD,
//16
   THREED_CTRL_ENABLE,  0,                     0,       0,      0,      0X0000+NOHOLD,  0,  
//17
   POS_3D,              FIT_POS24,             0,       0,      0,      0X00C0+NOHOLD,  0,
//18
   THREED_CTRL_DISABLE, 0,                     0,       0,      0,      0X00C0+NOHOLD,  0,
//19
   POS,                 POS_SHOULDER,          0,       0,      0,      0X00CO+NOHOLD,  M_ROLPART,
   TIMER,               2,                     0,       0,      0,      0X00C0,         0,
//20
   THREED_CTRL_ENABLE,  0,                     0,       0,      0,      0X000C+NOHOLD,  0,
 
/////////////////////////////////////////////////////////////////////////////////////////////////////
/////////////////////////End Of Subroutine uLegacy Neck Roll for Head Rev.6/////////////////////////
///////////////////////////////////////////////////////////////////////////////////////////////////

//16
  POS,                  POS_NECK,              110,      0,      0,      0X0000+NOHOLD,    M_KNEAD_ROLPART,
//16A
  POS_3D_UP,            2,                     0,        0,      0,      0X0020+NOHOLD,    0,
  ROTATION,             2,                     110,      0,      PN,     0X0020+NOHOLD,    M_KNEAD,
  ROTATION,             1,                     110,      0,      PN,     0X0020,           M_KNEAD,
  TIMER,                3,                     110,      0,      0,      0X0020+NOHOLD,    M_KNEAD,
//18
  POS_3D_UP,            5,                     0,        0,      0,      0X0010+NOHOLD,    0,
  ROTATION,             2,                     90,       0,      PN,     0X0010+NOHOLD,    M_KNEAD,
  ROTATION,             1,                     90,       0,      PN,     0X0010,           M_KNEAD,
  TIMER,                3,                     90,       0,      0,      0X0010+NOHOLD,    M_KNEAD,
//18`
  POS_3D_DOWN,          6,                     0,        0,      0,      0X00C0+NOHOLD,    0,
  TIMER,                1,                     0,        0,      0,      0X00C0,           0,
//18A
  POS_3D,               FIT_POS1,              0,        0,      0,      0X00C0+NOHOLD,    0, 
  THREED_CTRL_DISABLE,  0,                     0,        0,      0,      0X00C0+NOHOLD,    0,
//19
  POS_UP,               7,                     0,        0,      0,      0X00C0+NOHOLD,    M_ROLPART,
  TIMER,                1,                     0,        0,      0,      0X00C0,           0,
//20
  ROTATION,             3,                     90,       0,      PN,     0X0020+NHOLD,     M_KNEAD,
//21
  POS_UP,               12,                    0,        0,      0,      0X0010+NOHOLD,    M_ROLPART,
//22
  ROTATION,             3,                     90,       0,      REW,    0X0010+NOHOLD,    M_KNEAD,
//23
  WIDTH_SET,            0,                     0,        0,      PN,     0X0000+NOHOLD,    0,
//24
  POS,                  POS_NECK_UPPER,        0,        0,      0,      0X0010+NOHOLD,    M_ROLPART,
//24A
  THREED_CTRL_ENABLE,   0,                     0,        0,      0,      0X0010+NOHOLD,    0,
//26
  SEATM_START,           0,                    0,        0,      0,      0X0000,           SM_AUTO55,    
  POS,                  POS_SHOULDER,          110,      0,      0,      0X00C0+NOHOLD,    M_KNEAD_ROLPART,
//27
  POS_3D_UP,            5,                     0,        0,      0,      0X0020+NOHOLD,    0,
  TIMER,                6,                     0,        150,    0,      0X0020+NOHOLD,    M_TAP,
//27A
  WIDTH_SET,            0,                     0,        150,    PM,     0X0000+NOHOLD,    M_TAP,
//28
  POS,                  POS_SHOULDER_LOWER,    0,        150,    0,      0X00C0+NOHOLD,    M_TAP_ROLPART,
//29
  POS_3D_UP,            9,                     0,        0,      0,      0X0010+NOHOLD,    0,
  TIMER,                6,                     0,        150,    0,      0X0010+NOHOLD,    M_TAP,
//29A
  WIDTH_SET,            0 ,                    0,        0,      PN,     0X000C+NOHOLD,    0,
//29aa
  POS_3D_DOWN,          9,                     0,        0,      0,      0X000C+NOHOLD,    0,
  TIMER,                1,                     0,        0,      0,      0X000C,           0,
//29B
  POS,                  POS_NECK_UPPER1,       0,        0,      0,      0X000C+NOHOLD,    M_ROLPART,
//29'A
  POS_UP,               POS_UPLIMIT,           110,      0,      REW,    0X00C0+NOHOLD,    M_KNEAD_ROLPART,
//29'B
  POS_3D_UP,            1,                     0,        0,      0,      0X0020+NOHOLD,    0,
  ROTATION,             3,                     100,      0,      0,      0X0020+NOHOLD,    M_KNEAD,
//29'C 
  POS,                  POS_SHOULDER,          110,      0,      PN,     0X000C+NOHOLD,    M_KNEAD_ROLPART,
//29'A
  POS,                  POS_NECK_UPPER1,       0,        0,      0,      0X000C+NOHOLD,    M_ROLPART,
//29'D
  POS_UP,               5,                     100,      0,      REW,    0X000C+NOHOLD,   M_KNEAD_ROLPART,
//29'E
  POS_3D_UP,            1,                     0,        0,      0,      0X0010+NOHOLD,    0,
  ROTATION,             3,                     100,      0,      0,      0X0010+NOHOLD,    M_KNEAD,
//29'B
  POS,                  POS_NECK,              110,      0,      0,      0X000C+NOHOLD,    M_KNEAD_ROLPART,
//29'C
  POS_3D_UP,            3,                     0,        0,      0,      0X000C+NOHOLD,    0,  
  ROTATION,             3,                     110,      0,      PN,     0X000C+NOHOLD,    M_KNEAD,
  TIMER,                3,                     110,      0,      0,      0X000C,           M_KNEAD,
//30
  POS,                  POS_NECK_UPPER,        0,        0,      0,      0X00C0+NOHOLD,    M_ROLPART,
//31
  POS_3D,               FIT_POS0,              0,        0,      0,      0X000C+NOHOLD,    0,
  THREED_CTRL_DISABLE,  0,                     0,        0,      0,      0X000C+NOHOLD,    0,
//33
  POS_UP,               17,                    0,        0,      0,      0X00C0+NOHOLD,    M_ROLPART,
//34
  ROTATION,             3,                     100,      0,      0,      0X00C0,           M_KNEAD,
//35
  POS_DOWN,             15,                    100,      0,      0,      0X0020+NOHOLD,    M_KNEAD_ROLPART,
//37 
  POS,                  POS_NECK,              0,        0,      0,      0X000C+NOHOLD,    M_ROLPART,
//37A
  THREED_CTRL_ENABLE,   0,                     0,        0,      0,      0X000C+NOHOLD,    0,
//38
  POS_3D_UP,            2,                     0,        0,      0,      0X0010+NOHOLD,    0,
  ROTATION,             2,                     100,      0,      0,      0X0010+NOHOLD,    M_KNEAD,
  ROTATION,             1,                     100,      0,      0,      0X0010,           M_KNEAD,
//39A
  POS_3D_UP,            6,                     0,        0,      0,      0X000C+NOHOLD,    0,
  ROTATION,             3,                     90,       0,      0,      0X000C+NOHOLD,    M_KNEAD,
//40
  SEATM_START,          0,                     0,        0,      0,      0X000C+NOHOLD,    SM_AUTO56,
  POS,                  POS_SHOULDER_UPPER,    110,      0,      0,      0X000C+NOHOLD,    M_KNEAD_ROLPART,
//41
  WIDTH_SET,            0,                     0,        0,      PN,     0X000C+NOHOLD,   0,
  POS_3D,               FIT_POS20,             0,        0,      0,      0X000C+NOHOLD,   0,
  THREED_CTRL_DISABLE,  0,                     0,        0,      0,      0X000C+NOHOLD,   0,
//42
  POS,                  POS_SHOULDER_LOWER,    0,        120,    0,      0X00C0+NOHOLD,   M_TAP_ROLPART,
//43
  TIMER,                6,                     0,        140,    0,      0X00C0+NOHOLD,   M_TAP,
//44
  POS,                  POS_SHOULDER_LOWER,    0,        0,      0,      0X00C0+NOHOLD,  0,
  ROTATION,             3,                     120,      140,    REW,    0X00C0+NOHOLD,  M_KNEAD_TAP,     
//45
  THREED_CTRL_ENABLE,   0,                     0,        0,      0,      0X000C+NOHOLD,  0,
//46
  SEATM_START,          0,                     0,        0,      0,      0X000C+NOHOLD,  SM_AUTO57,
  POS,                  POS_SHDBLADE_UPPER,    120,      0,      REW,    0X000C+NOHOLD,  M_KNEAD_ROLPART,
//46A
  POS,                  POS_BACK_UPPER,        120,      0,      REW,    0X0010+NOHOLD,  M_KNEAD_ROLPART,
//47
  POS,                  POS_WAIST_UPPER1,      120,      0,      REW,    0X0010+NOHOLD, M_KNEAD_ROLPART,
//47A
  POS,                  POS_HIPS,              120,      0,      REW,    0X000C+NOHOLD, M_KNEAD_ROLPART,
//47B
  POS_3D_UP,            14,                    0,        0,      0,      0X000C+NOHOLD, 0,
  ROTATION,             3,                     120,      0,      REW,    0X000C+NOHOLD, M_KNEAD,
//47'B
  POS_3D_DOWN,          14,                    120,      0,      REW,    0X000C+NOHOLD, M_KNEAD,
  TIMER,                1,                     0,        0,      0,      0X000C+NOHOLD, 0,
  POS_3D_DOWN,          6,                     120,      0,      REW,    0X000C+NOHOLD, M_KNEAD,
//47'C
  POS,                  POS_WAIST,             120,      0,      REW,    0X0010+NOHOLD, M_KNEAD_ROLPART,
//47C'
  POS_3D_UP,            12,                    120,      0,      REW,    0X000C+NOHOLD, M_KNEAD,
  TIMER,                2,                     0,        0,      0,      0X000C,        0,
  POS_3D_UP,            8,                     120,      0,      REW,    0X000C+NOHOLD, M_KNEAD,
  TIMER,                1,                     0,        0,      0,      0X000C,        0,
//47d
  POS_3D_UP,            4,                     0,        0,      0,      0X000C+NOHOLD, 0,
  ROTATION,             3,                     120,      0,      REW,    0X000C+NOHOLD, M_KNEAD,
//47E
  POS_3D_DOWN,          14,                    120,      0,      REW,    0X000C+NOHOLD, M_KNEAD,
  TIMER,                1,                     0,        0,      0,      0X000C,        0,
  POS_3D_DOWN,          10,                    120,      0,      REW,    0X000C+NOHOLD, M_KNEAD,
//49
  POS,                  POS_SHDBLADE_UPPER,    120,      0,      REW,    0X000C+NOHOLD, M_KNEAD_ROLPART,
//50
  POS,                  POS_NECK_UPPER,        120,      0,      REW,    0X00C0+NOHOLD, M_KNEAD_ROLPART,
//51
  WIDTH_SET,            0,                     0,        0,      PN,     0X00C0+NOHOLD, 0,
//52
  POS,                  POS_SHOULDER,          0,        0,      0,      0X0020+NOHOLD, M_ROLPART,
//53
  POS_3D_UP,            4,                     0,        0,      0,      0X0010+NOHOLD, 0,
  ROTATION,             3,                     110,      0,      REW,    0X0010+NOHOLD, M_KNEAD,
//54
  POS,                  POS_NECK,              110,      0,      0,      0X000C+NOHOLD, M_KNEAD_ROLPART, 
//54A
  POS_3D_UP,            3,                     0,        0,      0,      0X000C+NOHOLD, 0,
  ROTATION,              3,                    100,      0,      REW+PN, 0X000C+NOHOLD, M_KNEAD,
  TIMER,                3,                     0,        0,      0,      0X000C+NOHOLD, 0,
//54B
  POS_3D,               FIT_POS1,              0,        0,      0,      0X000C+NOHOLD, 0,
  THREED_CTRL_DISABLE,  0,                     0,        0,      0,      0X000C+NOHOLD, 0,
//55
  POS_UP,               12,                    0,        0,      0,      0X0020+NOHOLD, M_ROLPART,
//56
  ROTATION,             3,                     90,       0,      REW,    0X00C0+NOHOLD, M_KNEAD,
//57
  ROTATION,             3,                     90,       0,      PN,     0X0020+NOHOLD, M_KNEAD,
//58  
  POS_UP,               10,                    0,        0,      0,      0X000C+NOHOLD, M_ROLPART,
//59
  ROTATION,             3,                     90,       0,      REW,    0X00C0+NOHOLD, M_KNEAD,
//60
  WIDTH_SET,            0,                     0,        0,      PN,     0X0010+NOHOLD, 0,
  TIMER,                1,                     0,        0,      0,      0X0010,        0,
//61 
  POS,                  POS_UPLIMIT,           0,        0,      0,      0X0020+NOHOLD, M_ROLPART,
//62
  ROTATION,             3,                     90,       0,      PN,     0X000C+NOHOLD, M_KNEAD,
//63
  POS,                  POS_SHOULDER,          0,        0,      0,      0X00C0+NOHOLD, M_ROLPART,
//64
  THREED_CTRL_ENABLE,   0,                     0,        0,      0,      0X0000+NOHOLD, 0,
//65
  POS_3D_UP,            5,                     0,         0,      0,      0X00C0+NOHOLD, 0,
//66
  TIMER,                6,                     0,        140,    0,      0X00C0+NOHOLD, M_TAP,

/////////////////////////////////////////////////////////////////////////////////////////////////////
/////////////////////Subroutine uLegacy Neck Roll For Head Rev.6////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////////////////////////
          
//0
  SEATM_START,          0,                     0,        0,      0,      0X0000+NOHOLD, SM_AUTO54,
//1
  POS_3D_UP,            5,                     0,        0,      PM,     0X000C+NOHOLD, 0,
//2
  WIDTH_SET,            0,                     0,        0,      PN,     0X00C0+NOHOLD,  0,
  TIMER,                1,                     0,        0,      0,      0X00C0,        0,
//3
  POS,                  POS_NECK_UPPER,        0,        0,      0,      0X00C0+NOHOLD,  M_ROLPART,
  TIMER,                4,                     0,        0,      0,      0X00C0,         0,
//3A
  POS_3D_UP,            3,                     0,        0,      0,      0X000C+NOHOLD,  0,
  TIMER,                4,                     0,        0,      0,      0X000C,         0,
//3B
  POS_3D_DOWN,          3,                     0,        0,      0,      0X000C+NOHOLD,  0,
//4A
  POS_3D,               FIT_POS3,              0,        0,      0,      0X000C+NOHOLD,  0,
  THREED_CTRL_DISABLE,  0,                     0,        0,      0,      0X000C+NOHOLD,  0,
//5
  POS,                  POS_UPLIMIT,           0,        0,      0,      0X0010+NOHOLD,  M_ROLPART,
  TIMER,                1,                     0,        0,      0,      0X0010,         0,
//5A
  WIDTH_SET,            0,                     0,        0,      PM,     0X0010+NOHOLD,  0,
//6
  POS,                  POS_NECK_UPPER,        0,        0,      0,      0X0020+NOHOLD,  M_ROLPART,
  TIMER,                1,                     0,        0,      0,      0X0020,         0,
//6A
  THREED_CTRL_ENABLE,   0,                     0,        0,      0,      0X000C+NOHOLD,   0,
//7
  WIDTH_SET,            0,                     0,        0,      PN,     0X000C+NOHOLD,   0,
//8
  POS_3D,               FIT_POS22,             0,        0,      0,      0X000C+NOHOLD,   0,
//9
  THREED_CTRL_DISABLE,  0,                     0,        0,      0,      0X000C+NOHOLD,   0,
//10 
  POS,                  POS_SHOULDER_UPPER,    0,        0,      0,      0X00C0+NOHOLD,   M_ROLPART,
  TIMER,                2,                     0,        0,      0,      0X00CO,          0,
//11
  POS,                  POS_NECK_UPPER,        0,        0,      0,      0X00C0+NOHOLD,   M_ROLPART,
  TIMER,                4,                     0,        0,      0,      0X00C0,          0,
//12
  THREED_CTRL_ENABLE,   0,                     0,        0,      0,      0X000C+NOHOLD,   0,
//14
  POS,                  POS_UPLIMIT,           90,       0,      REW,    0X0010+NOHOLD,   M_KNEAD_ROLPART,
  TIMER,                2,                     0,        0,      0,      0X0010,          0,
//14A
  WIDTH_SET,            0,                     0,        0,      PM,     0X000C+NOHOLD,   0,
//14B
  POS_3D,               FIT_POS0,              0,        0,      0,      0X000C+NOHOLD,   0,
  THREED_CTRL_DISABLE,  0,                     0,        0,      0,      0X000C+NOHOLD,   0,
//15
  POS,                  POS_NECK_UPPER,        120,      0,      PN,     0X0020+NOHOLD,   M_KNEAD_ROLPART,
  TIMER,                1,                     0,        0,      0,      0X0020,          0,
//16
  THREED_CTRL_ENABLE,   0,                     0,        0,      0,      0X000C+NOHOLD,   0,
//17
  POS_3D,               FIT_POS24,             0,        0,      0,      0X00C0+NOHOLD,   0,
//18
  THREED_CTRL_DISABLE,  0,                     0,        0,      0,      0X00C0+NOHOLD,   0,
//19
  POS,                  POS_SHOULDER,          0,        0,      0,      0X00C0+NOHOLD,   M_ROLPART,
  TIMER,                2,                     0,        0,      0,      0X00C0,          0,
//20
  THREED_CTRL_ENABLE,   0,                     0,        0,      0,      0X000C+NOHOLD,   0,
////////////////////////////////////////////////////////////////////////////////////////////////////////
///////////////////////End Of Subroutine uLegacy Neck Roll for Head Rev.6//////////////////////////////
//////////////////////////////////////////////////////////////////////////////////////////////////////    
    
//68
  POS,                  POS_SHOULDER,          0,        0,       0,     0X00C0+NOHOLD,   0,
  ROTATION,             3,                     120,      130,     REW,   0X00C0+NOHOLD,   M_KNEAD_ROLPART,
//69
  POS_3D_UP,            5,                     0,        0,       0,     0X00C0+NOHOLD,   0,
  TIMER,                2,                     0,        0,       0,     0X00C0,          0,
//70
  TIMER,                8,                     0,        150,     0,     0X000C+NOHOLD,   0,
//71
  POS_3D_DOWN,          5,                     0,        150,     0,     0X000C+NOHOLD,   M_TAP,

///////////////////////////////////////////////////////////////////////////////////////////////////////

  POS_RET,              0,                     0,        0,        0,    0X0000+NOHOLD,   0,
  
//////////////////////////////////////////////////////////////////////////////////////////////////////
//100
  LED_COLOR,            0,                     0,        0,        0,      0,               COLOUR373,
  SEATM_START,          0,                     0,        0,        0,      0X0020+NOHOLD,   SM_AUTO54,
  POS,                  POS_SHOULDER_LOWER,    125,      0,        REW,    0X0020+NOHOLD,   M_KNEAD_ROLPART,
//101 
  POS_3D_UP,            4,                     0,        0,        0,      0XOO10+NOHOLD,   0,
  ROTATION,             3,                     125,      0,        REW+PN,  0X0010+NOHOLD,   M_KNEAD,
//103
  POS_3D,               FIT_POS24,             0,        0,        0,      0X00C0+NOHOLD,   0,
  THREED_CTRL_DISABLE,  0,                     0,        0,        0,      0X00C0+NOHOLD,   0,
//104
  POS,                  POS_NECK_UPPER,        0,        0,        0,      0X00C0+NOHOLD,   M_ROLPART,
//105
   POS_3D,              FIT_POS0,              0,        0,        0,      0X0000+NOHOLD,   0,
   THREED_CTRL_ENABLE,  0,                     0,        0,        0,      0X0000,          0,
//106
   WIDTH_SET,           0,                     0,        0,        PW,     0X0020+NOHOLD,   0,
   TIMER,               1,                     0,        0,        0,      0X0020+NOHOLD,   0,
//107
   POS_UP,              17,                    0,        0,        0,      0X0010+NOHOLD,   0,
   TIMER,               2,                     0,        0,        0,      0X0010,          0,
//108
   ROTATION,            3,                     90,       0,        0,      0X0010+NOHOLD,   M_KNEAD,
//109
   WIDTH_SET,           0,                     0,        0,        PN,     0X000C+NOHOLD,   0,
//110
   POS,                 POS_SHOULDER_UPPER,    0,        0,        0,      0X000C+NOHOLD,   M_ROLPART,
//110A
   THREED_CTRL_DISABLE, 0,                     0,        0,        0,      0X000C+NOHOLD,   0,
//111
   POS_3D_UP,           8,                     0,        0,        0,      0X00C0+NOHOLD,   0,
   ROTATION,            3,                     110,      0,        REW,    0X00C0+NOHOLD,   M_KNEAD,
//112
   POS_3D_DOWN,         8,                     0,        0,        0,      0X000C+NOHOLD,   0,        

//////////////////////////////////////////////////////////////////////////////////////////////////////////

  COOLDOWN,             60,                    0,        0,        0,      0X0000,           0,
  
//////////////////////////////////////////////////////////////////////////////////////////////////////////
//113
  SEATM_START,          0,                     0,        0,         0,     0X0000+NOHOLD,     SM_AUTO54,
  POS,                  POS_BACK_UPPER,        0,        0,         0,     0X000C+NOHOLD,     M_ROLPART,
//114
  POS,                  POS_WAIST,             100,      0,         0,     0X0010+NOHOLD,     M_KNEAD_ROLPART,
//115
  POS_3D_UP,           15,                     0,        0,         0,     0X0020+NOHOLD,     0,
  ROTATION,            2,                      110,      0,         REW ,  0X0020+NOHOLD,     M_KNEAD,
  ROTATION,            1,                      110,      0,         REW,   0X0020,            M_KNEAD, 
//116
  POS_3D_DOWN,         7,                      120,      0,         REW,   0X000C+NOHOLD,     M_KNEAD,
  POS_3D_DOWN,         8,                      120,       0,        REW,   0X000C+NOHOLD,     M_KNEAD,
  POS_3D,              FIT_POS4,               120,      0,         REW,   0X000C+NOHOLD,     M_KNEAD,
//118
  POS,                 POS_SHOULDER_LOWER,     120,      0,         0,     0X00C0+NOHOLD,     M_KNEAD_ROLPART,
//119
  POS_3D_UP,           4,                      0,        0,         0,     0X000C+NOHOLD,     0,
  ROTATION,            3,                      120,      0,         0,     0X000C+NOHOLD,     M_KNEAD, 
//120
  POS,                 POS_SHOULDER,           120,      0,         0,     0X000C+NOHOLD,     M_KNEAD_ROLPART,

	0xff,				0xff,					0xff,	0xff,	0xff,	0xffff,			0xff
};
//------------------------------------------------------------------------------
// PG3: uDream Neck & Shoulder 3 April 2020
//------------------------------------------------------------------------------
T_OPERATION	osim_pg3_course_tbl[] =	
{										
	 	TRACK_SELECT,		MEDIUM,					0,		0,		0,		0X0000,			0,	
  	POS,				POS_SHOULDER,			0,		0,		PM,		0X0000,			M_ROLPART,
  	ASI_START,	      	0,			            0,	 	0,		0,		0x0000,		    MODE_AUTO6,
//0
	SEATM_START,		0,						0,		0,		0,		0X0000,			SM_AUTO45,  //Neck & Shoulder seat sub1	
//1
	POS,				POS_SHOULDER_UPPER,		100,	0,		PN,		0X000C+NOHOLD,	M_KNEAD_ROLPART,
#if 0
//7a
////////////////////////////uLegacy Subroutine Squeeze #1  (�X�^�[�g�ʒu�@����j Ver.15 /////////////////////////
//1
	POS_3D_UP,			8,						0,		0,		0,		0X0000,			0,
	ROTATION,			3,						105,	0,		REW+PW,	0X000C+NOHOLD,	M_KNEAD,
	TIMER,				1,						0,		0,		0,		0X000C+NOHOLD,	0,
	
//2
	WIDTH_SET,			0,						105,	0,		PN,		0X0020+NOHOLD,	M_KNEAD,
	TIMER,				2,						0,		0,		0,		0X0020+NOHOLD,	0,
//3
	WIDTH_SET,			0,						105,	0,		REW+PW,	0X0020,			M_KNEAD,
	TIMER,				1,						0,		0,		0,		0X0020,			0,
//4 REPEAT 2-3
//2
	WIDTH_SET,			0,						105,	0,		PN,		0X0020+NOHOLD,	M_KNEAD,
	TIMER,				2,						0,		0,		0,		0X0020+NOHOLD,	0,
//3
	WIDTH_SET,			0,						105,	0,		REW+PW,	0X0020,			M_KNEAD,
	TIMER,				1,						0,		0,		0,		0X0020,			0,
////////////////////END REPEAT//////////////////////////////////////////////////////////////////////
//5
	POS,				POS_SHOULDER,			0,		0,		0,		0X000C+NOHOLD,	M_ROLPART,
//6
	POS_3D_UP,			1,						0,		0,		0,		0X0000,			0,
	ROTATION,			3,						105,	0,		REW+PW,	0X00C0+NOHOLD,	M_KNEAD,
	TIMER,				1,						0,		0,		0,		0X00C0+NOHOLD,	0,
//7
	WIDTH_SET,			0,						105,	0,		PN,		0X00C0+NOHOLD,	M_KNEAD,
	TIMER,				2,						0,		0,		0,		0X00C0+NOHOLD,	0,
//8
	WIDTH_SET,			0,						105,	0,		REW+PW,	0X0000,			M_KNEAD,
	TIMER,				1,						0,		0,		0,		0X0000,			0,
	
//9 REPEAT 7-8
//7
	WIDTH_SET,			0,						105,	0,		PN,		0X00C0+NOHOLD,	M_KNEAD,
	TIMER,				2,						0,		0,		0,		0X00C0+NOHOLD,	0,
//8
	WIDTH_SET,			0,						105,	0,		REW+PW,	0X0000,			M_KNEAD,
	
///////////////////////////////////////////////END REPEAT//////////////////////////////////////////
//9A
	POS_3D_DOWN,		4,						0,		0,		0,		0X000C+NOHOLD,	0,	
//10
	POS,				POS_SHOULDER_LOWER,		0,		0,		0,		0X000C+NOHOLD,	M_ROLPART,
//11
	POS_3D_UP,			5,						0,		0,		0,		0X0000,			0,
	ROTATION,			3,						105,	0,		REW+PW,	0X000C+NOHOLD,	M_KNEAD,	
	TIMER,				1,						0,		0,		0,		0X0000,			0,
//12
	WIDTH_SET,			0,						105,	0,		PN,		0X0010+NOHOLD,	M_KNEAD,
	TIMER,				2,						0,		0,		0,		0X0010+NOHOLD,	0,
//13
	WIDTH_SET,			0,						105,	0,		REW+PW,	0X000C+NOHOLD,	M_KNEAD,
	TIMER,				1,						0,		0,		0,		0X000C+NOHOLD,	0,
//14 REPEAT 12-13
//12
	WIDTH_SET,			0,						105,	0,		PN,		0X0010+NOHOLD,	M_KNEAD,
	TIMER,				2,						0,		0,		0,		0X0010+NOHOLD,	0,
//13
	WIDTH_SET,			0,						105,	0,		REW+PW,	0X0000,			M_KNEAD,

/////////////////////////////// END uLegacy Subroutine Squeeze #1  (�X�^�[�g�ʒu�@����j Ver.15  /////////////////////////
//8
	POS,				POS_SHOULDER_LOWER2,	110,	130,	0,		0x000C+NOHOLD,	M_KNEAD_OSI_ROLPART,
//8a
	ROTATION,			2,						110,	140,	0,		0X0010+NOHOLD,	M_KNEAD_OSI,
	ROTATION,			1,						110,	140,	0,		0X0010,			M_KNEAD_OSI,
//9
	POS,				POS_NECK,				100,	0,		0,		0x00C0+NOHOLD,	M_KNEAD_ROLPART,
//10
	POS_3D_UP,			3,						0,		0,		0,		0X0000,			0,				
	ROTATION,			2,						110,	0,		0,		0X00C0+NOHOLD,	M_KNEAD,	
//12
	POS_3D_UP,			4,						0,		0,		0,		0X0000,			0,
	ROTATION,			1,						110,	0,		0,		0X0020+NOHOLD,	M_KNEAD,
	ROTATION,			1,						110,	0,		0,		0X0020,			M_KNEAD,	
//12A
	POS_3D_DOWN,		7,						0,		0,		0,		0X000C+NOHOLD,	0,
	TIMER,				1,						0,		0,		0,		0X000C+NOHOLD,	0,			
//15
	POS,				POS_SHOULDER_UPPER,		100,	0,		0,		0X000C+NOHOLD,	M_KNEAD_ROLPART,
//16
	POS_3D_UP,			4,						0,		0,		0,		0X000C+NOHOLD,	0,
	ROTATION,			3,						120,	0,		PN,		0X000C+NOHOLD,	M_KNEAD,
//18
	POS_3D_UP,			4,						0,		0,		0,		0X0010+NOHOLD,	0,
	TIMER,				6,						0,		120,	0,		0X0010+NOHOLD,	M_TAP,
//18A
	SEATM_START,		0,						0,		0,		0,		0X0000,			SM_AUTO76, 
#endif 
////////////////////////////////// uLegacy Subroutine  Squeeze  #10 ����`�� Ver13 /////////////////////////////////
//1 
	POS,				POS_SHOULDER_UPPER,		0,		0,		0,		0X0000,			M_ROLPART,
	ROTATION,			1,						105,	0,		REW+PW,	0X000C+NOHOLD,	M_KNEAD,
	TIMER,				1,						0,		0,		0,		0X000C+NOHOLD,	0,
//2
	POS_3D_UP,			8,						105,	0,		PN,		0X00C0+NOHOLD,	M_KNEAD,
	TIMER,				2,						0,		0,		0,		0X00C0+NOHOLD,	0,
//3
	WIDTH_SET,			0,						105,	0,		REW+PW,	0X00C0+NOHOLD,	M_KNEAD,
	TIMER,				1,						0,		0,		0,		0X00C0+NOHOLD,	0,
//3A
	WIDTH_SET,			0,						105,	0,		PN,		0X00C0,			M_KNEAD,
	TIMER,				2,						0,		0,		0,		0X00C0, 		0,
//6
	POS,				POS_SHOULDER,			0,		0,		0,		0X00C0+NOHOLD,	M_ROLPART,
//7
	POS_3D_UP,			2,						0,		0,		0,		0X000C+NOHOLD,	0,
	TIMER,				1,						0,		0,		0,		0X000C+NOHOLD,	0,
//8
	ROTATION,			2,						105,	0,		REW+PW,	0X0020+NOHOLD,	M_KNEAD,
	ROTATION,			1,						105,	0,		REW+PW,	0X0020,			M_KNEAD,	
	TIMER,				1,						0,		0,		0,		0X0020,			0,
//9
	TIMER,				2,						105,	0,		PN,		0X0000,			M_KNEAD,	
//10
	TIMER,				6,						0,		120,	0,		0X0010+NOHOLD,	M_TAP,
//11
 	POS_3D_DOWN,		2,						0,		0,		0,		0X000C+NOHOLD,	0,
/////////////////////////////// END uLegacy Subroutine  Squeeze  #10 ����`�� Ver13 ///////////////////////////////
//20
	POS,				POS_SHOULDER,			0,		0,		0,		0X0010+NOHOLD,	M_ROLPART,
//21
	POS_3D_UP,			2,						0,		0,		0,		0x00C0+NOHOLD,	0,
	ROTATION,			3,						120,	0,		PM,		0X00C0+NOHOLD,	M_KNEAD,
//24
	POS,				POS_SHOULDER_LOWER,		0,		140,	0,		0x00C0+NOHOLD,	M_TAP_ROLPART,
//25
	POS_3D_UP,			2,						0,		140,	0,		0X000C+NOHOLD,	M_TAP,
	TIMER,				6,						0,		140,	0,		0x000C+NOHOLD,	M_TAP,
//26
	WIDTH_SET,			0,						0,		0,		PW,		0X000C+NOHOLD,	0,
	TIMER,				1,						0,		0,		0,		0x000C+NOHOLD,	0,
//27
	POS,				POS_SHOULDER_LOWER2,	0,		150,	0,		0x0020+NOHOLD,	M_TAP_ROLPART,
//28
	POS_3D_UP,			5,						0,		150,	0,		0x0020+NOHOLD,	M_TAP,
	TIMER,				6,						0,		150,	0,		0x0020+NOHOLD,	M_TAP,
//30
	ROTATION,			3,						105,	0,		REW,	0X0010+NOHOLD,	M_KNEAD,
//30'
	WIDTH_SET,			0,						0,		0,		PN,		0X0000,			0,
//30'A
	POS_3D,				FIT_POS20,				0,		0,		0,		0X0000,			0,
	THREED_CTRL_DISABLE,0,						0,		0,		0,		0X0000,			0,	
//30'B
	TIMER,				2,						0,		0,		0,		0X00C0+NOHOLD,	0,
//30'C
	POS,				POS_NECK_UPPER,			0,		0,		0,		0X00C0+NOHOLD,	M_ROLPART,
	POS_UP,				3,						0,		0,		0,		0X00C0+NOHOLD,	M_ROLPART,
	TIMER,				2,						0,		0,		0,		0X00C0+NOHOLD,	0,
//30'D
	POS,				POS_SHOULDER_LOWER,		0,		0,		0,		0X000C+NOHOLD,	M_ROLPART,
//30'E
	THREED_CTRL_ENABLE,0,						0,		0,		0,		0X000C+NOHOLD,	0,		
//30A
	POS,				POS_SHDBLADE_UPPER,		110,	0,		REW,	0X000C+NOHOLD,	M_KNEAD_ROLPART,
//30B
	ROTATION,			3,						115,	0,		REW,	0X000C+NOHOLD,	M_KNEAD,
//31
	POS,				POS_SHOULDER_UPPER,		115,	0,		0,		0x00C0+NOHOLD,	M_KNEAD_ROLPART,
//32
  	POS,				POS_NECK,				120,	0,		0,		0x00C0+NOHOLD,	M_KNEAD_ROLPART,
//33
	POS_3D_UP,			3,						0,		0,		0,		0X00C0+NOHOLD,	0,
	ROTATION,			3,						110,	0,		0,		0X00C0+NOHOLD,	M_KNEAD,
//35
	POS_3D_UP,			5,						115,	0,		0,		0X0010+NOHOLD,	M_KNEAD,
	ROTATION,			3,						115,	0,		0,		0X0010+NOHOLD,	M_KNEAD,
//36
	POS,				POS_SHOULDER_UPPER,		110,	0,		0,		0X0010+NOHOLD,	M_KNEAD_ROLPART,
//36B	
	SEATM_START,		0,						0,		0,		0,		0X0000,			SM_AUTO76, 
////////////////////////////uLegacy Subroutine Squeeze #1  (�X�^�[�g�ʒu�@����j Ver.15 /////////////////////////
//1
	POS_3D_UP,			8,						0,		0,		0,		0X0000,			0,
	ROTATION,			3,						105,	0,		REW+PW,	0X000C+NOHOLD,	M_KNEAD,
	TIMER,				1,						0,		0,		0,		0X000C+NOHOLD,	0,
	
//2
	WIDTH_SET,			0,						105,	0,		PN,		0X0020+NOHOLD,	M_KNEAD,
	TIMER,				2,						0,		0,		0,		0X0020+NOHOLD,	0,
//3
	WIDTH_SET,			0,						105,	0,		REW+PW,	0X0020,			M_KNEAD,
	TIMER,				1,						0,		0,		0,		0X0020,			0,
//4 REPEAT 2-3
//2
	WIDTH_SET,			0,						105,	0,		PN,		0X0020+NOHOLD,	M_KNEAD,
	TIMER,				2,						0,		0,		0,		0X0020+NOHOLD,	0,
//3
	WIDTH_SET,			0,						105,	0,		REW+PW,	0X0020,			M_KNEAD,
	TIMER,				1,						0,		0,		0,		0X0020,			0,
////////////////////END REPEAT/////////////////////////////////////////////////////////////////////////////////////////
//5
	POS,				POS_SHOULDER,			0,		0,		0,		0X000C+NOHOLD,	M_ROLPART,
//6
	POS_3D_UP,			1,						0,		0,		0,		0X0000,			0,
	ROTATION,			3,						105,	0,		REW+PW,	0X00C0+NOHOLD,	M_KNEAD,
	TIMER,				1,						0,		0,		0,		0X00C0+NOHOLD,	0,
//7
	WIDTH_SET,			0,						105,	0,		PN,		0X00C0+NOHOLD,	M_KNEAD,
	TIMER,				2,						0,		0,		0,		0X00C0+NOHOLD,	0,
//8
	WIDTH_SET,			0,						105,	0,		REW+PW,	0X0000,			M_KNEAD,
	TIMER,				1,						0,		0,		0,		0X0000,			0,
	
//9 REPEAT 7-8
//7
	WIDTH_SET,			0,						105,	0,		PN,		0X00C0+NOHOLD,	M_KNEAD,
	TIMER,				2,						0,		0,		0,		0X00C0+NOHOLD,	0,
//8
	WIDTH_SET,			0,						105,	0,		REW+PW,	0X0000,			M_KNEAD,
	
///////////////////////////////////////////////END REPEAT////////////////////////////////////////////////////////////////////
//9A
	POS_3D_DOWN,		4,						0,		0,		0,		0X000C+NOHOLD,	0,	
//10
	POS,				POS_SHOULDER_LOWER,		0,		0,		0,		0X000C+NOHOLD,	M_ROLPART,
//11
	POS_3D_UP,			5,						0,		0,		0,		0X0000,			0,
	ROTATION,			3,						105,	0,		REW+PW,	0X000C+NOHOLD,	M_KNEAD,	
	TIMER,				1,						0,		0,		0,		0X0000,			0,
//12
	WIDTH_SET,			0,						105,	0,		PN,		0X0010+NOHOLD,	M_KNEAD,
	TIMER,				2,						0,		0,		0,		0X0010+NOHOLD,	0,
//13
	WIDTH_SET,			0,						105,	0,		REW+PW,	0X000C+NOHOLD,	M_KNEAD,
	TIMER,				1,						0,		0,		0,		0X000C+NOHOLD,	0,
//14 REPEAT 12-13
//12
	WIDTH_SET,			0,						105,	0,		PN,		0X0010+NOHOLD,	M_KNEAD,
	TIMER,				2,						0,		0,		0,		0X0010+NOHOLD,	0,
//13
	WIDTH_SET,			0,						105,	0,		REW+PW,	0X0000,			M_KNEAD,
/////////////////////////////// END uLegacy Subroutine Squeeze #1  (�X�^�[�g�ʒu�@����j Ver.15  /////////////////////////
//37A
	WIDTH_SET,			0,						0,		0,		PN,		0X0000,			0,
//38
///////////////////////////////////////////uLegacy Subroutine Neck and Shoulder Roll  #2 Ver 9  //////////////
//1
	POS,			POS_SHOULDER_LOWER2,		0,		0,		0,		0X000C+NOHOLD,	M_ROLPART,
//1A
	POS_3D_UP,			6,						0,		0,		0,		0X000C+NOHOLD,	0,
	THREED_CTRL_DISABLE,0,						0,		0,		0,		0X0000,			0,
//2
	TIMER,				2,						0,		0,		0,		0X000C+NOHOLD,	0,	
//3 
	POS,				POS_SHOULDER_LOWER,		0,		0,		0,		0X000C+NOHOLD,	M_ROLPART,
	POS,				POS_NECK_UPPER,			0,		0,		0,		0X0020+NOHOLD,	M_ROLPART,	
//3A
	POS_UP,				3,						0,		0,		0,		0X0020,			M_ROLPART,
//3B
	WIDTH_SET,			0,						0,		0,		PW,		0X0020,			0,
	TIMER,				1,						0,		0,		0,		0X0020,			0,
	WIDTH_SET,			0,						0,		0,		PN,		0X0020,			0,
	TIMER,				2,						0,		0,		0,		0X0020,			0,
//4A
	POS_3D_UP,			3,						0,		0,		0,		0X000C+NOHOLD,	0,	
//5	
	POS,				POS_SHOULDER,			0,		0,		PN,		0X0010+NOHOLD,	M_ROLPART,
	TIMER,				3,						0,		0,		0,		0X0010+NOHOLD,	0,
//5A
	THREED_CTRL_ENABLE,	0,						0,		0,		0,		0X0000,			0,
//6
	POS,				POS_SHDBLADE_UPPER,		0,		0,		0,		0X000C+NOHOLD,	M_ROLPART,
//7	
	POS,				POS_SHDBLADE,			0,		0,		PN,		0X000C+NOHOLD,	M_ROLPART,
	TIMER,				2,						0,		0,		0,		0X000C+NOHOLD,	0,
//7A
	POS,				POS_SHDBLADE_UPPER,		0,		0,		0,		0X000C+NOHOLD,	M_ROLPART,
//8
	POS,				POS_SHOULDER,			0,		0,		0,		0X000C+NOHOLD,	M_ROLPART,
//9
	POS_3D_UP,			4,						0,		150,	0,		0X000C+NOHOLD,	M_TAP,
//10
	TIMER,				6,						0,		150,	PN,		0X00C0+NOHOLD,	M_TAP,
//11
	POS_3D_DOWN,		4,						0,		0,		0,		0X000C+NOHOLD,	0,
///////////////////////////////////////////uLegacy Subroutine Neck and Shoulder Roll  #2 Ver 9 END/////////////////////
//38
	POS,				POS_SHDBLADE_UPPER,		120,	0,		0,		0X000C+NOHOLD,	M_KNEAD_ROLPART,
//38A
	SEATM_START,		0,						0,		0,		0,		0X0000,			SM_AUTO46,
	POS,				POS_BACK_UPPER,			120,	0,		0,		0X0020+NOHOLD,	M_KNEAD_ROLPART,
//37
	POS,				POS_HIPS,				120,	0,		0,		0X000C+NOHOLD,	M_KNEAD_ROLPART,
//38
	POS_3D_UP,			12,						0,		0,		0,		0X0010+NOHOLD,	0,
	ROTATION,			3,						120,	0,		REW,	0X0010+NOHOLD,	M_KNEAD,
//38a
	POS_3D_DOWN,		12,						110,	0,		0,		0X000C+NOHOLD,	M_KNEAD,
	TIMER,				1,						0,		0,		0,		0X000C+NOHOLD,	0,
	POS_3D_DOWN,		8,						110,	0,		0,		0X000C+NOHOLD,	M_KNEAD,
	TIMER,				1,						0,		0,		0,		0X000C+NOHOLD,	0,	
//39
  	POS,				POS_SHOULDER,			0,		160, 	0,		0X000C+NOHOLD,	M_OSI_ROLPART,
//40
	POS,				POS_NECK,				95,		0,		PN,		0X00C0+NOHOLD,	M_KNEAD_ROLPART,
//41
	POS_3D_UP,			3,						100,	0,		0,		0X00C0+NOHOLD,	M_KNEAD,
	ROTATION,			2,						100,	0,		0,		0X00C0+NOHOLD,	M_KNEAD,
//43
	POS_3D_UP,			5,						0,		0,		0,		0X000C+NOHOLD,	0,
	ROTATION,			2,						110,	0,		PN,		0X000C+NOHOLD,	M_KNEAD,
//44
	POS,				POS_SHOULDER,			0,		0,		0,		0X00C0+NOHOLD,	M_ROLPART,
//45
	POS_3D_UP,			2,						0,		0,		0,		0X00C0+NOHOLD,	0,
	ROTATION,			2,						120,	0,		0,		0X00C0+NOHOLD,	M_KNEAD,
//45A
	POS_3D_DOWN,		2,						0,		0,		0,		0X000C+NOHOLD,	0,
	TIMER,				1,						0,		0,		0,		0X000C+NOHOLD,	0,
//47
	POS,				POS_SHOULDER_LOWER,		0,		140,	PN,		0X000C+NOHOLD,	M_TAP_ROLPART,
//48
	WIDTH_SET,			0,						0,		150,	PM,		0X0010+NOHOLD,	M_TAP,
	POS_3D_UP,			5,						0,		150,	0,		0X0010+NOHOLD,	M_TAP,
	TIMER,				6,						0,		150,	0,		0X0010+NOHOLD,	M_TAP,
//48a
	POS,				POS_SHOULDER,			0,		150,	0,		0X000C+NOHOLD,	M_TAP_ROLPART,
//49
	WIDTH_SET,			0,						0,		0,		PN,		0X000C+NOHOLD,	0,
	POS_3D_UP,			4,						0,		160,	0,		0X000C+NOHOLD,	M_TAP,	
	TIMER,				6,						0,		160,	0,		0X000C+NOHOLD,	M_TAP,
//49a
	WIDTH_SET,			0,						0,		0,		PW,		0X000C+NOHOLD,	0,
//49b
	POS_3D_UP,			4,						0,		170,	0,		0X0020+NOHOLD,	M_TAP,	
	TIMER,				5,						0,		170,	0,		0X0020+NOHOLD,	M_TAP,			
//50
	WIDTH_SET,			0,						0,		0,		PN,		0X000C+NOHOLD,	0,
//50A
	SEATM_START,		0,						0,		0,		0,		0X0000,			SM_AUTO76,
////////////////////////////////// uLegacy Subroutine  Squeeze  #10 ����`�� Ver13 /////////////////////////////////
//1 
	POS,				POS_SHOULDER_UPPER,		0,		0,		0,		0X0000,			M_ROLPART,
	ROTATION,			1,						105,	0,		REW+PW,	0X000C+NOHOLD,	M_KNEAD,
	TIMER,				1,						0,		0,		0,		0X000C+NOHOLD,	0,
//2
	POS_3D_UP,			8,						105,	0,		PN,		0X00C0+NOHOLD,	M_KNEAD,
	TIMER,				2,						0,		0,		0,		0X00C0+NOHOLD,	0,
//3
	WIDTH_SET,			0,						105,	0,		REW+PW,	0X00C0+NOHOLD,	M_KNEAD,
	TIMER,				1,						0,		0,		0,		0X00C0+NOHOLD,	0,
//3A
	WIDTH_SET,			0,						105,	0,		PN,		0X00C0,			M_KNEAD,
	TIMER,				2,						0,		0,		0,		0X00C0, 		0,
//6
	POS,				POS_SHOULDER,			0,		0,		0,		0X00C0+NOHOLD,	M_ROLPART,
//7
	POS_3D_UP,			2,						0,		0,		0,		0X000C+NOHOLD,	0,
	TIMER,				1,						0,		0,		0,		0X000C+NOHOLD,	0,
//8
	ROTATION,			2,						105,	0,		REW+PW,	0X0020+NOHOLD,	M_KNEAD,
	ROTATION,			1,						105,	0,		REW+PW,	0X0020,			M_KNEAD,	
	TIMER,				1,						0,		0,		0,		0X0020,			0,
//9
	TIMER,				2,						105,	0,		PN,		0X0000,			M_KNEAD,	
//10
	TIMER,				6,						0,		120,	0,		0X0010+NOHOLD,	M_TAP,
//11
 	POS_3D_DOWN,		2,						0,		0,		0,		0X000C+NOHOLD,	0,
/////////////////////////////// END uLegacy Subroutine  Squeeze  #10 ����`�� Ver13 ///////////////////////////////
//53
	POS,				POS_HIPS,				120,	0,		0,		0X000C+NOHOLD,	M_KNEAD_ROLPART,
//54
	POS_3D_UP,			10,						0,		0,		0,		0X000C+NOHOLD,	0,
	ROTATION,			3,						120,	0,		0,		0X000C+NOHOLD,	M_KNEAD,
//55
	POS_3D_DOWN,		10,						120,	0,		0,		0X000C+NOHOLD,	M_KNEAD,
	TIMER,				1,						0,		0,		0,		0X000C+NOHOLD,	0,	
//55a
	POS_3D_DOWN,		10,						120,	0,		0,		0X000C+NOHOLD,	M_KNEAD,
	TIMER,				1,						0,		0,		0,		0X000C+NOHOLD,	0,
////////////////////////////////////////////////////////////////////////////////////////////////////////
	POS_RET,	        0,					    0,		0,		0,		0X0000,			0,
////////////////////////////////////////////////////////////////////////////////////////////////////////
//57
	SEATM_START,		0,						0,		0,		0,		0X0000,			SM_AUTO46,
    POS,				POS_BACK,				120,	0,		REW,	0X000C+NOHOLD,	M_KNEAD_ROLPART,
//58
	POS,				POS_SHDBLADE_UPPER,		120,	0,		REW,	0X00D0+NOHOLD,	M_KNEAD_ROLPART,
//59
    POS,				POS_SHOULDER_UPPER,		120,	0,		REW,	0X00D0+NOHOLD,	M_KNEAD_ROLPART,  
//60
	SEATM_START,		0,						0,		0,		0,		0X0000,			SM_AUTO27,	
    POS,				POS_NECK,				110,	0,		REW,	0X00C0+NOHOLD,	M_KNEAD_ROLPART,
//61
	POS_3D_UP,			3,						0,		0,		0,		0X00E0+NOHOLD,	0,
 	ROTATION,			3,						100,	0,		REW+PN,	0X00E0+NOHOLD,	M_KNEAD,
//62
	POS,				POS_SHOULDER,			0,		0,		PN,		0X000C+NOHOLD,	M_ROLPART,
//63
	POS,				POS_BACK_UPPER,			120,	140,	0,		0X000C+NOHOLD,	M_KNEAD_TAP_ROLPART,
	POS_3D_UP,			6,						0,		0,		0,		0X000C+NOHOLD,	0,
	ROTATION,			3,						120,	140,	0,		0X000C+NOHOLD,	M_KNEAD_TAP,
//64
	POS,				POS_SHOULDER_LOWER,		100,	0,		REW,	0X00C0+NOHOLD,	M_KNEAD_ROLPART,
//64A
	WIDTH_SET,			0,						0,		0,		PN,		0X0000,			0,
//65
	POS,				POS_SHOULDER,			0,		0,		0,		0X0000,			M_ROLPART,
//66
	POS_3D_UP,			2,						0,		0,		0,		0X0010+NOHOLD,	0,
	TIMER,				6,						0,		130,	0,		0X0010+NOHOLD,	M_TAP,
//67
	WIDTH_SET,			0,						0,		0,		PM,		0X0010+NOHOLD,	0,
	TIMER,				1,						0,		0,		0,		0X0010+NOHOLD,	0,
//68
	POS,				POS_SHOULDER_LOWER,		0,		140,	0,		0X000C+NOHOLD,	M_TAP_ROLPART,
//69
	POS_3D_UP,			2,						0,		0,		0,		0X0020+NOHOLD,	0,
	TIMER,				6,						0,		140,	0,		0X0020+NOHOLD,	M_TAP,
//70
	WIDTH_SET,			0,						0,		0,		PW,		0X000C+NOHOLD,	0,
	TIMER,				1,						0,		0,		0,		0X000C+NOHOLD,	0,
//71
	POS,				POS_SHOULDER_LOWER2,	0,		150,	0,		0X0010+NOHOLD,	M_TAP_ROLPART,
//72
	POS_3D_UP,			2,						0,		0,		0,		0X0010+NOHOLD,	0,
	TIMER,				6,						0,		160,	0,		0X0010+NOHOLD,	M_TAP,
//73
	POS_3D_DOWN,		2,						0,		0,		0,		0X000C+NOHOLD,	0,
	
//****************************************************************************************************************************
	COOLDOWN,			60,						0,		0,		0,		0X0000,			0,
//****************************************************************************************************************************
//74
	POS,				POS_BACK,				100,	0,		REW,	0X000C+NOHOLD,	M_KNEAD_ROLPART,
//75
	POS,				POS_SHOULDER,			100,	0,		0,		0X000C+NOHOLD,	M_KNEAD_ROLPART,
//76
	POS,				POS_BACK,				100,	0,		0,		0X000C+NOHOLD,	M_KNEAD_ROLPART,
//76B
	POS,				POS_SHDBLADE_UPPER,		110,	0,		REW,	0X000C+NOHOLD,	M_KNEAD_ROLPART,
//77
	POS,				POS_SHOULDER,			100,	0,		REW,	0X0000,			M_KNEAD_ROLPART,
//78
	ROTATION,			2,						100,	0,		REW,	0X0000,			M_KNEAD,

	0xff,	 			0xff,					0xff,	0xff,	0xff,	0xffff,			0xff
};
//------------------------------------------------------------------------------
// PG4: uDream Lumbar 02 April 2020
//------------------------------------------------------------------------------
T_OPERATION	osim_pg4_course_tbl[] =										
{
	TRACK_SELECT,		MEDIUM,					0,		0,		0,		0X0000,			0,
	ASI_START,			0,						0,		0,		0,		0X0000,			MODE_AUTO1, 
//3
	SEATM_START,		0,						0,		0,		0,		0X0000,			SM_AUTO22,  //Lumbar Pamper seat sub1
	POS,				POS_WAIST_UPPER1,		110,	0,		0,		0X00C0+NOHOLD,	M_KNEAD_ROLPART,
//4
	POS,				POS_HIPS,				120,	0,		PN,		0X000C+NOHOLD,	M_KNEAD_ROLPART,
//4A
	POS_3D_UP,			8,						0,		180,	0,		0X000C+NOHOLD,	M_OSI,
	TIMER,				2,						0,		0,		0,		0X000C,			0,
	POS_3D_UP,			4,						0,		180,	0,		0X000C+NOHOLD,	M_OSI,
	TIMER,				2,						0,		0,		0,		0X000C,			0,	
	POS_3D_UP,			3,						0,		180,	0,		0X000C+NOHOLD,	M_OSI,
	TIMER,				3,						0,		0,		0,		0X000C,			0,	
//4B
	TIMER,				7,						0,		200,	PN,		0X00C0+NOHOLD,	M_OSI,
//4Ba
	ROTATION,			3,						125,	160,	0,		0X0010+NOHOLD,	M_KNEAD_TAP,
//4Bb
	ROTATION,			3,						125,	0,		REW+PN,	0X0020+NOHOLD,	M_KNEAD,
//4Bc
	POS_3D,				FIT_POS24,				0,		0,		0,		0X0000,			0,
	THREED_CTRL_DISABLE,0, 						0, 		0,		0,		0X0000,		 	0, 	
//4Bd
	POS,				POS_WAIST_UPPER1,		0,		0,		0,		0X0010+NOHOLD,	M_ROLPART,
//4Be
	POS,				POS_HIPS,				0,		0,		0,		0X0010+NOHOLD,	M_ROLPART,
//4Bf
	POS,				POS_WAIST_UPPER1,		0,		0,		0,		0X0010+NOHOLD,	M_ROLPART,
//4Bg
	POS,				POS_HIPS,				0,		0,		0,		0X0020+NOHOLD,	M_ROLPART,
//4Bh
	POS,				POS_WAIST_UPPER1,		0,		0,		0,		0X0020+NOHOLD,	M_ROLPART,
//4Bi
	POS,				POS_HIPS,				0,		0,		0,		0X0020+NOHOLD,	M_ROLPART,
//4Bh
	THREED_CTRL_ENABLE,0, 						0, 		0,		0,		0X000C+NOHOLD,	0, 			
//4C
	POS_3D_DOWN,		15,						125,	0,		0,		0X000C+NOHOLD,	M_KNEAD,
	TIMER,				1,						0,		0,		0,		0X000C+NOHOLD,	0,
	POS_3D,				FIT_POS0,				0,		0,		0,		0X000C+NOHOLD,	0,
//4D
	POS_3D_UP,			12,						0,		0,		0,		0X00C0+NOHOLD,	0,
	TIMER,				2,						0,		0,		0,		0X00C0,			0,
	POS_3D_UP,			6,						0,		0,		0,		0X00C0+NOHOLD,	0,
	TIMER,				4,						0,		0,		0,		0X00C0,			0,

//4E
	POS_3D_DOWN,		12,						0,		0,		0,		0X000C+NOHOLD,	0,
	TIMER,				2,						0,		0,		0,		0X000C+NOHOLD,	0,
	POS_3D_UP,			16,						0,		0,		0,		0X000C+NOHOLD,	0,
	TIMER,				4,						0,		0,		0,		0X000C+NOHOLD,	0,
//4F
	TIMER,				6,						0,		180,	0,		0X0000,			M_TAP,	
//5
	POS,				POS_WAIST_UPPER1,		100,	0,		REW+PN,	0X000C+NOHOLD,	M_KNEAD_ROLPART,
//5A
	POS_3D_UP,			12,						0,	    0,		0,		0X00C0+NOHOLD,	0,
	TIMER,				2,						0,		0,		0,		0X00C0+NOHOLD,	0,
	POS_3D_UP,			6,						0,	    0,		0,		0X00C0+NOHOLD,	0,
	TIMER,				2,						0,		0,		0,		0X00C0+NOHOLD,	0,
//5B
	POS_3D_DOWN,		12,						0,	    0,		0,		0X000C+NOHOLD,	0,
	TIMER,				2,						0,		0,		0,		0X000C+NOHOLD,	0,
	POS_3D_UP,			16,						0,	    0,		0,		0X000C+NOHOLD,	0,
	TIMER,				4,						0,		0,		0,		0X000C+NOHOLD,	0,	
//5C
	TIMER,				7,						0,		170,	0,		0X000C+NOHOLD,	M_TAP,
//11
	ROTATION,			3,						125,	180,	PN,		0X0010+NOHOLD,	M_KNEAD_TAP,
//12
	TIMER,				7,						0,		220,	0,		0X0020+NOHOLD,	M_TAP,
//12A
	POS_3D_DOWN,		14,						125,	0,		REW+PN,	0X000C+NOHOLD,	M_KNEAD,
	TIMER,				2,						0,		0,		0,		0X000C+NOHOLD,	0,
	POS_3D_DOWN,		6,						125,	0,		REW+PN,	0X000C+NOHOLD,	M_KNEAD,
	TIMER,				1,						0,		0,		0,		0X000C+NOHOLD,	0,
	POS_3D_DOWN,		4,						125,	0,		REW+PN,	0X000C+NOHOLD,	M_KNEAD,
//12B
	POS,				POS_WAIST_UPPER1,		120,	0,		REW+PN,	0X00C0+NOHOLD,	M_KNEAD_ROLPART,
//12C
	POS_3D_UP,			14,						0,	    0,		0,		0X00C0+NOHOLD,	0,
	TIMER,				4,						0,		0,		0,		0X00C0+NOHOLD,	0,
	POS_3D_UP,			4,						0,	    0,		0,		0X00C0+NOHOLD,	0,
	TIMER,				4,						0,		0,		0,		0X00C0+NOHOLD,	0,	
//12D
	POS_3D_DOWN,		12,						0,	    0,		0,		0X000C+NOHOLD,	0,
	TIMER,				2,						0,		0,		0,		0X000C+NOHOLD,	0,
	POS_3D_UP,			14,						0,	    0,		0,		0X000C+NOHOLD,	0,
	TIMER,				6,						0,		0,		0,		0X000C+NOHOLD,	0,
//12E
	TIMER,				6,						0,		160,	0,		0X000C+NOHOLD,	M_TAP,
//13
	POS_3D_DOWN,		10,						100,	0,		REW,	0X000C+NOHOLD,	M_KNEAD,
//15
	SEATM_START,		0,						0,		0,		0,		0X0000,			SM_AUTO22,  //Lumbar Pamper seat sub1
	POS,				POS_HIPS,				120,	0,		REW+PN,	0X000C+NOHOLD,	M_KNEAD_ROLPART,
//16
	POS_3D_UP,			10,						0,		0,		0,		0X00C0+NOHOLD,	0,
	TIMER,				2,						0,		0,		0,		0X00C0,			0,
	POS_3D_UP,			5,						0,		0,		0,		0X00C0+NOHOLD,	0,
	TIMER,				6,						0,		0,		0,		0X00C0,			0,

//16A
	POS_3D_DOWN,		5,						120,	180,	REW,	0X000C+NOHOLD,	M_KNEAD_TAP,
	ROTATION,			1,						120,	180,	REW,	0X000C+NOHOLD,	M_KNEAD_TAP,
//17
	POS_3D_UP,			6,						120,	200,	REW,	0X00C0+NOHOLD,	M_KNEAD_TAP,
	ROTATION,			1,						120,	200,	REW,	0X00C0+NOHOLD,	M_KNEAD_TAP,
//18
	ROTATION,			2,						120,	0,		REW,	0X0080+NOHOLD,	M_KNEAD,
	ROTATION,			1,						120,	0,		REW,	0X0020+NOHOLD,	M_KNEAD,
//18B
	ROTATION,			2,						120,	0,		REW+PN,	0X0040+NOHOLD,	M_KNEAD,
	ROTATION,			1,						120,	0,		REW+PN,	0X0010+NOHOLD,	M_KNEAD,
//18C
	POS_3D,				FIT_POS24,				0,		0,		0,		0X00C0+NOHOLD,	0,
	THREED_CTRL_DISABLE,0,						0,		0,		0,		0X0000,			0,
//18D
	POS,				POS_WAIST_UPPER1,		0,		180,	0,		0X0010+NOHOLD,	M_TAP_ROLPART,
	POS_UP,				5,						0,		180,	0,		0X0010+NOHOLD,	M_TAP_ROLPART,
//18E
	POS,				POS_HIPS,				0,		200,	0,		0X0010,			M_TAP_ROLPART,
//18F
	POS,				POS_WAIST_UPPER1,		0,		180,	0,		0X0020+NOHOLD,	M_TAP_ROLPART,
	POS_UP,				5,						0,		180,	0,		0X0020+NOHOLD,	M_TAP_ROLPART,
//18G	
	POS,				POS_HIPS,				0,		200,	0,		0X0020,			M_TAP_ROLPART,
	THREED_CTRL_ENABLE,0,						0,		0,		0,		0X0000,			0,
//19
	POS_3D_DOWN,		17,						120,	0,		REW,	0X00C0+NOHOLD,	M_KNEAD,
//23A
	POS,				POS_WAIST,				120,	0,		PN,		0X00C0+NOHOLD,	M_KNEAD_ROLPART,
//23A'
	POS_3D_UP,			10,						0,		0,		0,		0X000C+NOHOLD,	0,
	TIMER,				2,						0,		0,		0,		0X000C+NOHOLD,	0,	
	POS_3D_UP,			6,						0,		0,		0,		0X000C+NOHOLD,	0,
	TIMER,				4,						0,		0,		0,		0X000C+NOHOLD,	0,		
	POS_3D_UP,			7,						0,		0,		0,		0X000C+NOHOLD,	0,
	TIMER,				6,						0,		0,		0,		0X000C+NOHOLD,	0,	
//23A''
	TIMER,				6,						0,		190,	0,		0X000C+NOHOLD,	M_TAP,
//23C
	POS_3D_UP,			1,						100,	0,		0,		0X0000,			M_KNEAD,
	ROTATION,			1,						100,	0,		0,		0X0010+NOHOLD,	M_KNEAD,
	ROTATION,			2,						100,	0,		0,		0X0080+NOHOLD,	M_KNEAD,
//23M
	ROTATION,			1,						125,	0,		REW,	0X00C0+NOHOLD,	M_KNEAD,
	ROTATION,			1,						125,	0,		REW,	0X0000,			M_KNEAD,
//23N
	ROTATION,			1,						100,	0,		0,		0X0020+NOHOLD,	M_KNEAD,
	ROTATION,			2,						100,	0,		0,		0X0040+NOHOLD,	M_KNEAD,
//23L
	POS_3D_DOWN,		14,						120,	0,		REW,	0X00C0+NOHOLD,	M_KNEAD,
	TIMER,				2,						120,	0,		REW,	0X00C0+NOHOLD,	M_KNEAD,
	POS_3D_DOWN,		5,						120,	0,		REW,	0X00C0+NOHOLD,	M_KNEAD,
	TIMER,				1,						120,	0,		REW,	0X00C0+NOHOLD,	M_KNEAD,
	POS_3D_DOWN,		5,						120,	0,		REW,	0X00C0+NOHOLD,	M_KNEAD,	
//23G
	POS,				POS_HIPS,				110,	0,		0,		0X000C+NOHOLD,	M_KNEAD_ROLPART,
	WIDTH_SET,			0,						110,	0,		PN,		0X000C+NOHOLD,	M_KNEAD,
//23D
	SEATM_START,		0,						0,		0,		0,		0X0000,			SM_AUTO16,
	POS_3D_UP,			8,						0,		0,		0,		0X0000,			0,	
	TIMER,				1,						0,		0,		0,		0X0000,			0,
	POS_3D_UP,			4,						0,		0,		0,		0X0000,			0,	
	TIMER,				2,						0,		0,		0,		0X0000,			0,
	POS_3D_UP,			4,						0,		0,		0,		0X0000,			0,
	
	TIMER,				2,						0,		0,		0,		0X0080+NOHOLD,	0,
	TIMER,				2,						0,		0,		0,		0X0000,			0,
	TIMER,				3,						0,		0,		0,		0X0080+NOHOLD,	0,
	TIMER,				1,						0,		0,		0,		0X0000,			0,
//23I
	POS_3D_DOWN,		4,						120,	180,	0,		0X0000,			M_KNEAD_TAP,
	ROTATION,			1,						120,	180,	0,		0X00C0+NOHOLD,	M_KNEAD_TAP,
	ROTATION,			1,						120,	180,	0,		0X0000,			M_KNEAD_TAP,
	ROTATION,			1,						120,	180,	0,		0X00C0+NOHOLD,	M_KNEAD_TAP,
	WIDTH_SET,			0,						120,	0,		PN,		0X0000,			M_KNEAD,
//231a
	POS_3D_UP,			4,						0,		180,	0,		0X0000,			M_TAP,
//23J
	TIMER,				4,						0,		0,		0,		0X0040+NOHOLD,	0,
	TIMER,				2,						0,		0,		0,		0X0000,			0,
	TIMER,				4,						0,		0,		0,		0X0040+NOHOLD,	0,
//23Ja
	ROTATION,			1,						120,	0,		0,		0X0020+NOHOLD,	M_KNEAD,
	ROTATION,			1,						120,	0,		0,		0X0010+NOHOLD,	M_KNEAD,
	ROTATION,			1,						120,	0,		0,		0X00C0+NOHOLD,	M_KNEAD,
//23K
	POS_3D_DOWN,		17,						120,	0,		0,		0X0000,			M_KNEAD,
//23L
	POS_3D,				FIT_POS0,				0,		0,		0,		0X0000,			0,
//41
	POS,				POS_WAIST_UPPER1,		120,	0,		0,		0X00C0+NOHOLD,	M_KNEAD_ROLPART,
	WIDTH_SET,			0,						120,	0,		PN,		0X00C0+NOHOLD,	M_KNEAD,
//41A
	SEATM_START,		0,						0,		0,		0,		0X000C+NOHOLD,	SM_AUTO17,
	POS,				POS_SHDBLADE_LOWER,		0,		160,	0,		0X000C+NOHOLD,	M_TAP_ROLPART,
//42
	POS,				POS_SHOULDER,			0,		140,	0,		0X000C+NOHOLD,	M_TAP_ROLPART,
//43
	ROTATION,			3,						120,	110,	0,		0X0020+NOHOLD,	M_KNEAD_TAP,
//43A
	ROTATION,			3,						120,	0,		REW,	0X0010+NOHOLD,	M_KNEAD,
	WIDTH_SET,			0,						120,	0,		PN,		0X0010+NOHOLD,	M_KNEAD,
//43B
	POS,				POS_BACK_UPPER,			0,		100,	0,		0X000C+NOHOLD,	M_TAP_ROLPART,
//49a
	POS,				POS_WAIST_UPPER1,		120,	0,		0,		0X00C0+NOHOLD,	M_KNEAD_ROLPART,
	WIDTH_SET,			0,						120,	0,		PN,		0X00C0+NOHOLD,	M_KNEAD,
//50
	POS_3D_UP,			14,						0,		180,	0,		0X00C0+NOHOLD,	M_TAP,
	TIMER,				2,						0,		0,		0,		0X00C0+NOHOLD,	0,
	POS_3D_UP,			5,						0,		180,	0,		0X00C0+NOHOLD,	M_TAP,
	TIMER,				2,						0,		0,		0,		0X00C0+NOHOLD,	0,	
//51
	ROTATION,			3,						120,	0,		REW,	0X0010+NOHOLD,	M_KNEAD,
//52
	ROTATION,			3,						120,	160,	REW,	0X0020+NOHOLD,	M_KNEAD_TAP,
//53
	POS_3D_DOWN,		11,						100,	0,		REW,	0X00C0+NOHOLD,	M_KNEAD,
	TIMER,				1,						0,		0,		0,		0X00C0+NOHOLD,	0,	
	POS_3D_DOWN,		8,						100,	0,		REW,	0X00C0+NOHOLD,	M_KNEAD,
//55
	WIDTH_SET,			0,						0,		0,		PN,		0X000C+NOHOLD,	0,
	TIMER,				1,						0,		0,		0,		0X000C+NOHOLD,	0,
//56 
	POS,				POS_WAIST,				120,	0,		REW+PN,	0X000C+NOHOLD,	M_KNEAD_ROLPART,
//56A
	POS_3D_UP,			8,						0,		0,		0,		0X0080+NOHOLD,	0,
	TIMER,				2,						0,		0,		0,		0X0080,			0,	
	POS_3D_UP,			4,						0,		0,		0,		0X0080+NOHOLD,	0,
	TIMER,				4,						0,		0,		0,		0X0080,	0,		
//56B
	POS_3D_UP,			5,						0,		0,		0,		0X000C+NOHOLD,	0,
	TIMER,				6,						0,		0,		0,		0X000C+NOHOLD,	0,	
//57A
	POS_3D_UP,			2,						0,		0,		0,		0X0000,			0,
	ROTATION,			1,						120,	0,		0,		0X0020+NOHOLD,	M_KNEAD,
	ROTATION,			2,						120,	0,		0,		0X0080+NOHOLD,	M_KNEAD,
//58
	POS_3D_DOWN,		4,						120,	0,		0,		0X00C0+NOHOLD,	M_KNEAD,
	ROTATION,			1,						120,	0,		0,		0X00C0+NOHOLD,	M_KNEAD,
//58A
	POS_3D_UP,			4,						110,	0,		0,		0X00C0+NOHOLD,	M_KNEAD,
	ROTATION,			1,						110,	0,		0,		0X00C0+NOHOLD,	M_KNEAD,
//58C
	ROTATION,			2,						120,	180,	0,		0X0040+NOHOLD,	M_KNEAD_TAP,
//58D
	ROTATION,			1,						120,	180,	0,		0X000C+NOHOLD,	M_KNEAD_TAP,
//63C
	POS_3D,				FIT_POS24,				0,		0,		0,		0X00C0+NOHOLD,	0,
	THREED_CTRL_DISABLE,	0,					0,		0,		0,		0X0000,			0,
//63D
	POS,				POS_WAIST_UPPER1,		0,		220,	0,		0X0010+NOHOLD,	M_OSI_ROLPART,
	POS_UP,				5,						0,		220,	0,		0X0010+NOHOLD,	M_OSI_ROLPART,
//63E
	POS,				POS_HIPS,				0,		220,	0,		0X0080+NOHOLD,	M_OSI_ROLPART,
//63F
	POS,				POS_WAIST_UPPER1,		0,		220,	0,		0X00C0+NOHOLD,	M_OSI_ROLPART,
	POS_UP,				5,						0,		220,	0,		0X00C0+NOHOLD,	M_OSI_ROLPART,
//63G	
	POS,				POS_HIPS,				0,		220,	0,		0X0020+NOHOLD,	M_OSI_ROLPART,
	THREED_CTRL_ENABLE,0,						0,		0,		0,		0X0000,			0,
//64
	POS_3D_DOWN,		8,						120,	0,		REW,	0X0010+NOHOLD,	M_KNEAD,	// drop
	ROTATION,			1,						120,	0,		REW,	0X0010+NOHOLD,	M_KNEAD,
	POS_3D_DOWN,		6,						120,	0,		REW,	0X0010+NOHOLD,	M_KNEAD,	// drop
	ROTATION,			1,						120,	0,		REW,	0X0010+NOHOLD,	M_KNEAD,
	POS_3D_DOWN,		6,						120,	0,		REW,	0X0010,			M_KNEAD,	// drop
	ROTATION,			1,						120,	0,		REW,	0X0010,			M_KNEAD,
	
//65
	POS,				POS_HIPS,				120,	150,	REW+PN,	0X00C0+NOHOLD,	M_KNEAD_TAP_ROLPART,
//65A
	POS_3D_UP,			12,						0,		0,		PN,		0X000C+NOHOLD,	0,
	TIMER,				2,						0,		0,		0,		0X000C+NOHOLD,	0,
	POS_3D_UP,			4,						0,		0,		PN,		0X000C+NOHOLD,	0,
	TIMER,				7,						0,		0,		0,		0X000C+NOHOLD,	0,	
//65B
	TIMER,				9,						0,		170,	0,		0X00C0+NOHOLD,	M_OSI,
//66
	SEATM_START,		0,						0,		0,		0,		0X000C+NOHOLD,	SM_AUTO18,
	ROTATION,			3,						120,	200,	REW,	0X000C+NOHOLD,	M_KNEAD_TAP,
//67
	POS_3D_DOWN,		4,						120,	200,	REW,	0X000C+NOHOLD,	M_KNEAD_TAP,
	TIMER,				1,						0,		0,		0,		0X000C+NOHOLD,	0,
	POS_3D_DOWN,		12,						120,	200,	REW,	0X000C+NOHOLD,	M_KNEAD_TAP,
//67A
	POS_3D,				FIT_POS0,				0,		0,		0,		0X000C+NOHOLD,	0,
//70
	POS,				POS_WAIST,				100,	0,		REW,	0X000C+NOHOLD,	M_KNEAD_ROLPART,
//71
	POS_3D_UP,			10,						120,	0,		REW,	0X00C0+NOHOLD,	M_KNEAD,
	ROTATION,			1,						120,	0,		REW,	0X00C0+NOHOLD,	M_KNEAD,
	POS_3D_UP,			6,						120,	0,		REW,	0X00C0+NOHOLD,	M_KNEAD,
	ROTATION,			1,						120,	0,		REW,	0X00C0+NOHOLD,	M_KNEAD,
	POS_3D_UP,			7,						120,	0,		REW,	0X00C0+NOHOLD,	M_KNEAD,
	ROTATION,			1,						120,	0,		REW,	0X00C0+NOHOLD,	M_KNEAD,
	POS_3D_UP,			3,						120,	0,		REW,	0X00C0+NOHOLD,	M_KNEAD,
	ROTATION,			1,						120,	0,		REW,	0X00C0+NOHOLD,	M_KNEAD,
//72
	ROTATION,			3,						120,	0,		REW,	0X0020+NOHOLD,	M_KNEAD,
//73
	POS_3D_DOWN,		4,						100,	0,		REW,	0X00C0+NOHOLD,	M_KNEAD,
	ROTATION,			1,						100,	0,		REW,	0X00C0+NOHOLD,	M_KNEAD,
	POS_3D_DOWN,		4,						100,	0,		REW,	0X00C0+NOHOLD,	M_KNEAD,
	ROTATION,			1,						100,	0,		REW,	0X00C0+NOHOLD,	M_KNEAD,
	POS_3D_DOWN,		9,						100,	0,		REW,	0X00C0+NOHOLD,	M_KNEAD,
//74
	POS,				POS_HIPS,				100,	0,		REW+PN,	0X000C+NOHOLD,	M_KNEAD_ROLPART,
//75
	POS_3D_UP,			8,						0,		0,		0,		0X00C0+NOHOLD,	0,
	TIMER,				1,						0,		0,		0,		0X00C0,			0,
	POS_3D_UP,			4,						0,		0,		0,		0X00C0+NOHOLD,	0,
	TIMER,				2,						0,		0,		0,		0X00C0,			0,
	POS_3D_UP,			4,						0,		0,		0,		0X00C0+NOHOLD,	0,
	TIMER,				6,						0,		0,		0,		0X00C0,			0,
	
//75A
	ROTATION,			3,						120,	200,	REW+PN,	0X000C+NOHOLD,	M_KNEAD_TAP,
//76
	ROTATION,			3,						120,	0,		REW,	0X0010+NOHOLD,	M_KNEAD,
//77
	POS_3D_DOWN,		9,						100,	0,		REW,	0X000C+NOHOLD,	M_KNEAD,
	POS_3D_DOWN,		8,						100,	0,		REW,	0X000C+NOHOLD,	M_KNEAD,

//77A
	POS_3D,				FIT_POS0,				0,		0,		0,		0X000C+NOHOLD,	0,
//78
	POS,				POS_WAIST_UPPER1,		120,	0,		REW,	0X00C0+NOHOLD,	M_KNEAD_ROLPART,
//79
	POS_3D_UP,			16,						120,	0,		REW,	0X00C0+NOHOLD,	M_KNEAD,
	TIMER,				1,						120,	0,		REW,	0X00C0+NOHOLD,	M_KNEAD,
	POS_3D_UP,			8,						120,	0,		REW,	0X00C0+NOHOLD,	M_KNEAD,	
//79A
	ROTATION,			3,						120,	0,		REW,	0X0020+NOHOLD,	M_KNEAD,
//79Aa
	SEATM_START,		0,						0,		0,		0,		0X0000,			SM_AUTO19,
	POS_3D_DOWN,		14,						120,	0,		REW,	0X000C+NOHOLD,	M_KNEAD,
	TIMER,				2,						0,		0,		0,		0X000C+NOHOLD,	0,
	POS_3D_DOWN,		10,						120,	0,		REW,	0X000C+NOHOLD,	M_KNEAD,
	TIMER,				2,						0,		0,		0,		0X000C+NOHOLD,	0,	
//91
	POS,				POS_BACK,				120,	0,		REW,	0X000C+NOHOLD,	M_KNEAD_ROLPART,
//91B
	POS,				POS_SHDBLADE_LOWER,		120,	0,		REW,	0X00C0+NOHOLD,	M_KNEAD_ROLPART,
//95	
	POS,				POS_NECK,				100,	0,		0,		0X000C+NOHOLD,	M_KNEAD_ROLPART,
//96
	POS_3D_UP,			1,						0,		0,		0,		0X00C0+NOHOLD,	0,
	ROTATION,			2,						100,	0,		0,		0X00C0+NOHOLD,	M_KNEAD,
////////////////////////////////////////////////////////////////////////////////////////////////////////
	POS_RET,	        0,						0,		0,		0,		0X0000,			0,
////////////////////////////////////////////////////////////////////////////////////////////////////////
//97
	SEATM_START,		0,						0,		0,		0,		0X0000,			SM_AUTO20,
	POS,				POS_SHOULDER,			110,	0,		0,		0X0000,			M_KNEAD_ROLPART,
//98
	POS_3D_UP,			4,						0,		0,		0,		0X0020+NOHOLD,	0,
	ROTATION,			3,						120,	0,		0,		0X0020+NOHOLD,	M_KNEAD,
//99
	ROTATION,			3,						125,	140,	0,		0X0010+NOHOLD,	M_KNEAD_TAP,
//100
	POS_3D_DOWN,		5,						125,	0,		0,		0X000C+NOHOLD,	M_KNEAD,
//101
	POS,				POS_BACK,				125,	0,		0,		0X000C+NOHOLD,	M_KNEAD_ROLPART,
//102
	POS,				POS_WAIST_UPPER1,		125,	0,		0,		0X00C0+NOHOLD,	M_KNEAD_ROLPART,
//102A
	POS_3D_UP,			12,						125,	110,	0,		0X0000,			M_KNEAD_TAP,
	TIMER,				1,						125,	110,	REW,	0X000C+NOHOLD,	M_KNEAD_TAP,
	POS_3D_UP,			5,						125,	110,	0,		0X000C+NOHOLD,	M_KNEAD_TAP,
//103
	ROTATION,			3,						125,	110,	0,		0X0020+NOHOLD,	M_KNEAD_TAP,
//104
	POS_3D_DOWN,		8,						125,	100,	0,		0X00C0+NOHOLD,	M_KNEAD_TAP,
	TIMER,				1,						0,		0,		0,		0X00C0+NOHOLD,	0,
	POS_3D_DOWN,		9,						125,	100,	0,		0X00C0+NOHOLD,	M_KNEAD_TAP,
//105
	POS,				POS_WAIST,				125,	0,		0,		0X00C0+NOHOLD,	M_KNEAD_ROLPART,
//105A
	POS_3D_UP,			14,						125,	0,		0,		0X0010+NOHOLD,	M_KNEAD,
	TIMER,				1,						125,	0,		0,		0X0010+NOHOLD,	M_KNEAD,
	POS_3D_UP,			5,						125,	0,		0,		0X0010+NOHOLD,	M_KNEAD,
//106
	ROTATION,			3,						125,	0,		0,		0X0010+NOHOLD,	M_KNEAD,
//107
	POS_3D_DOWN,		10,						125,	0,		0,		0X00C0+NOHOLD,	M_KNEAD,
	TIMER,				1,						125,	0,		0,		0X00C0+NOHOLD,	M_KNEAD,
	POS_3D_DOWN,		10,						125,	0,		0,		0X00C0+NOHOLD,	M_KNEAD,
//107A
	POS_3D,				FIT_POS0,				0,		0,		0,		0X0000,			0,
////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
	COOLDOWN,	        60,						0,		0,		0,		0X0000,			0,
////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//112
	SEATM_START,		0,						0,		0,		0,		0X0000,			SM_AUTO21,
	POS,				POS_HIPS,				125,	0,		REW,	0X000C+NOHOLD,	M_KNEAD_ROLPART,
//113
	POS_3D_UP,			12,						0,		0,		0,		0X0020+NOHOLD,	0,
	ROTATION,			3,						125,	0,		REW,	0X0020+NOHOLD,	M_KNEAD,
//114
	ROTATION,			3,						125,	0,		REW,	0X0010+NOHOLD,	M_KNEAD,
//115
	POS_3D_DOWN,		12,						125,	0,		REW,	0X000C+NOHOLD,	M_KNEAD,
//115A
	POS_3D,				FIT_POS0,				0,		0,		0,		0X000C+NOHOLD,	0,
//120
	POS,				POS_SHOULDER,			100,	0,		REW,	0X000C+NOHOLD,	M_KNEAD_ROLPART,

	0xff,			 	0xff,   	 			0xff,	0xff,	0xff,	0xffff,			0xff
};
 #endif
//------------------------------------------------------------------------------
// PG5: uDream Butt & Thighs 14 August 2020
//------------------------------------------------------------------------------
T_OPERATION	osim_pg5_course_tbl[] =										
{
	//0
	TRACK_SELECT,		WEAK,					0,		0,		0,		0X0000,			0,
	ASI_START,			0,						0,		0,		0,		0X0000,			MODE_AUTO3,
	POS,				POS_SHOULDER,			0,		0,		0,		0X0000,			M_ROLPART,
	SEATM_START,		0,						0,		0,		0,		0X0000,			SM_AUTO7,	
//0A
	POS,				POS_SHDBLADE_UPPER,		100,	0,		0,		0X000C+NOHOLD,	M_KNEAD_ROLPART,
//1
	POS,				POS_SHDBLADE_LOWER,		100,	0,		0,		0X00C0+NOHOLD,	M_KNEAD_ROLPART,
//2
	POS_3D_DOWN,		3,						110,	0,		0,		0X000C+NOHOLD,	M_KNEAD,
	ROTATION,			3,						110,	0,		0,		0X000C+NOHOLD,	M_KNEAD,
//3
	POS,				POS_BACK_UPPER,			110,	0,		0,		0X00C0+NOHOLD,	M_KNEAD_ROLPART,
//4
	ROTATION,			3,						110,	0,		0,		0X0020+NOHOLD,	M_KNEAD,
//5
	POS,				POS_WAIST_UPPER1,		110,	0,		0,		0X00C0+NOHOLD,	M_KNEAD_ROLPART,
//6
	POS_3D_UP,			8,						0,		0,		0,		0X0010+NOHOLD,	0,
	ROTATION,			3,						120,	0,		0,		0X0010+NOHOLD,	M_KNEAD,
//7
	POS_3D_DOWN,		8,						100,	0,		0,		0X00C0+NOHOLD,	M_KNEAD,
	POS_3D_UP,			10,						100,	0,		0,		0X00C0+NOHOLD,	M_KNEAD,
	ROTATION,			1,						100,	0,		0,		0X00C0+NOHOLD,	M_KNEAD,
//7A
	POS_3D_DOWN,		10,						100,	0,		0,		0X000C+NOHOLD,	M_KNEAD,
	ROTATION,			1,						100,	0,		0,		0X000C+NOHOLD,	M_KNEAD,
//8
	POS,				POS_WAIST,				110,	0,		0,		0X000C+NOHOLD,	M_KNEAD_ROLPART,
//9
	POS_3D_UP,			11,						0,		0,		0,		0X00C0+NOHOLD,	M_KNEAD,
	ROTATION,			3,						120,	0,		0,		0X00C0+NOHOLD,	M_KNEAD,
//10
	POS_3D_DOWN,		11,						100,	0,		0,		0X000C+NOHOLD,	M_KNEAD,
	POS_3D_UP,			16,						100,	0,		0,		0X000C+NOHOLD,	M_KNEAD,
	ROTATION,			1,						100,	0,		0,		0X000C+NOHOLD,	M_KNEAD,
	POS_3D_DOWN,		16,						100,	0,		0,		0X000C+NOHOLD,	M_KNEAD,
//11
	WIDTH_SET,			0,						0,		0,		PN,		0X0000,	0,
//12
	POS,				POS_HIPS,				0,		0,		0,		0X000C+NOHOLD,	M_ROLPART,
//12A
	POS_3D_UP,			12,						0,		0,		0,		0X0020+NOHOLD,	0,
	TIMER,				1,						0,		0,		0,		0X0020+NOHOLD,	0,
	POS_3D_UP,			7,						0,		0,		0,		0X0020+NOHOLD,	0,
	TIMER,				1,						0,		0,		0,		0X0020+NOHOLD,	0,
	POS_3D_DOWN,		19,						0,		0,		0,		0X0020+NOHOLD,	0,
//12B
	POS,				POS_WAIST_UPPER1,		120,	0,		REW,	0X0010+NOHOLD,	M_KNEAD_ROLPART,
//12C
	SEATM_START,		0,						0,		0,		0,		0X000C+NOHOLD,	SM_AUTO13,
	POS_3D_UP,			15,						0,		0,		0,		0X000C+NOHOLD,	0,
	ROTATION,			1,						120,	0,		0,		0X000C+NOHOLD,	M_KNEAD,
//13
	POS,				POS_BACK,				0,		110,	0,		0X000C+NOHOLD,	M_TAP_ROLPART,
//13A
	POS,				POS_SHDBLADE_LOWER,		0,		160,	0,		0X00C0+NOHOLD,	M_OSI_ROLPART,

//14
	POS,				POS_SHOULDER,			0,		0,		0,		0X000C+NOHOLD,	M_ROLPART,
//15
	ROTATION,			3,						110,	0,		0,		0X00C0+NOHOLD,	M_KNEAD,
//16
	POS_3D_UP,			4,						0,		0,		0,		0X000C+NOHOLD,	0,
	TIMER,				3,						0,		0,		0,		0X000C+NOHOLD,	0,
//17
	POS,				POS_SHDBLADE,			0,		0,		0,		0X00C0+NOHOLD,	M_ROLPART,
//17a
	POS,				POS_BACK_UPPER,			0,		0,		0,		0X000C+NOHOLD,	M_ROLPART,
//18
	POS,				POS_HIPS,				0,		0,		0,		0X00CC+NOHOLD,	M_ROLPART,
//19
	WIDTH_SET,			0,						0,		0,		PN,		0X000C+NOHOLD,	0,
	TIMER,				2,						0,		0,		0,		0X000C+NOHOLD,	0,
//19A
	POS_3D_UP,			13,						120,	0,		PN,		0X000C+NOHOLD,	M_KNEAD,
	TIMER,				1,						0,		0,		0,		0X000C+NOHOLD,	0,
	POS_3D_UP,			6,						120,	0,		PN,		0X000C+NOHOLD,	M_KNEAD,
	TIMER,				1,						0,		0,		0,		0X000C+NOHOLD,	0,
//19B
	POS,				POS_WAIST_UPPER1,		0,		150,	0,		0X000C+NOHOLD,	M_TAP_ROLPART,
//20
	POS,				POS_BACK_UPPER,			0,		110,	0,		0X00C0+NOHOLD,	M_TAP_ROLPART,
//20A
	POS,				POS_SHDBLADE,			0,		0,		0,		0X000C+NOHOLD,	M_ROLPART,
//20B
	SEATM_START,		0,						0,		0,		0,		0X0000,			SM_AUTO14,
	POS,				POS_SHOULDER_LOWER2,	0,		0,		0,		0X00C0+NOHOLD,	M_ROLPART,
//27
	POS,				POS_SHOULDER_UPPER,		105,	0,		0,		0X00C0+NOHOLD,	M_KNEAD_ROLPART,
//29
	POS_3D_UP,			6,						0,		0,		0,		0X0010+NOHOLD,	0,
	ROTATION,			3,						105,	0,		0,		0X0010+NOHOLD,	M_KNEAD,
//30
	POS,				POS_SHOULDER,			105,	0,		REW,	0X0010+NOHOLD,	M_KNEAD_ROLPART,
//31
	ROTATION,			4,						105,	0,		REW,	0X0020+NOHOLD,	M_KNEAD,
//32
	POS,				POS_SHOULDER_LOWER,		105,	0,		REW,	0X000C+NOHOLD,	M_KNEAD_ROLPART,
//33
	ROTATION,			2,						105,	0,		REW+PN,	0X00C0+NOHOLD,	M_KNEAD,
//34
	POS_3D,				FIT_POS16,				0,		0,		0,		0X00C0+NOHOLD,	0,
	THREED_CTRL_ENABLE,	0,						0,		0,		0,		0X0000,			0,
//35
	POS,				POS_NECK,				0,		0,		0,		0X000C+NOHOLD,	M_ROLPART,
//35A
	POS_3D,				FIT_POS20,				0,		0,		0,		0X000C+NOHOLD,	0,
//36
	POS,				POS_SHOULDER,			0,		0,		0,		0X0040+NOHOLD,	M_ROLPART,
//37
	POS,				POS_NECK,				0,		0,		0,		0X00C0+NOHOLD,	M_ROLPART,	
//38
	POS,				POS_SHOULDER,			0,		0,		0,		0X0080+NOHOLD,	M_ROLPART,
//38A
	POS_3D,				FIT_POS16,				0,		0,		0,		0X00C0+NOHOLD,	0,
//39
	POS,				POS_SHDBLADE_UPPER,		0,		180,	0,		0X00C0+NOHOLD,	M_OSI_ROLPART,
//40
	POS,				POS_HIPS,				0,		220,	0,		0X000C+NOHOLD,	M_OSI_ROLPART,
//40A
	POS_3D_UP,			13,						120,	0,		0,		0X00C0+NOHOLD,	M_KNEAD,
	TIMER,				1,						0,		0,		0,		0X00C0+NOHOLD,	0,
	POS_3D_UP,			6,						120,	0,		0,		0X00C0+NOHOLD,	M_KNEAD,
	TIMER,				1,						0,		0,		0,		0X00C0+NOHOLD,	0,
	WIDTH_SET,			0,						0,		0,		PN,		0X00C0+NOHOLD,	0,
//41
	POS,				POS_SHDBLADE_LOWER,		0,		160,	0,		0X000C+NOHOLD,	M_OSI_ROLPART,
//42
	POS,				POS_SHOULDER,			0,		140,	0,		0X00C0+NOHOLD,	M_OSI_ROLPART,
//42A
	TIMER,				3,						0,		160,	0,		0x000C+NOHOLD,	M_OSI,
//42B
	POS,				POS_SHDBLADE,			0,		160,	0,		0X000C+NOHOLD,	M_OSI_ROLPART,
//44
	POS,				POS_BACK,				0,		180,	0,		0X00C0+NOHOLD,	M_OSI_ROLPART,
//45
	POS,				POS_WAIST,				0,		200,	0,		0X0020+NOHOLD,	M_OSI_ROLPART,
//45A
	POS_3D_DOWN,		2,						0,		200,	0,		0X0020+NOHOLD,	M_OSI,
	TIMER,				2,						0,		0,		0,		0X0020+NOHOLD,	0,
//46
	POS,				POS_BACK,				0,		200,	0,		0X0010+NOHOLD,	M_OSI_ROLPART,
//46A
	POS,				POS_BACK_UPPER,			0,		180,	0,		0X0010+NOHOLD,	M_OSI_ROLPART,
//47
	POS,				POS_SHOULDER,			0,		180,	0,		0X000C+NOHOLD,	M_OSI_ROLPART,
//49
	POS,				POS_NECK,				0,		0,		0,		0X000C+NOHOLD,	M_ROLPART,	
//50
	ROTATION,			3,						120,	0,		PN,		0X00C0+NOHOLD,	M_KNEAD,
//51
	POS_3D_UP,			2,						0,		0,		0,		0X000C+NOHOLD,	0,
	POS,				POS_SHOULDER,			0,		0,		0,		0X000C+NOHOLD,	M_ROLPART,
//51a
	POS_3D_UP,			2,						0,		0,		0,		0X0020+NOHOLD,	0,
	TIMER,				6,						0,		110,	0,		0X0020+NOHOLD,	M_TAP,
//52
	POS,				POS_SHOULDER_LOWER,		0,		110,	0,		0X00C0+NOHOLD,	M_TAP_ROLPART,
//52A
	WIDTH_SET,			0,						0,		0,		PM,		0X00C0+NOHOLD,	0,
//53
	SEATM_START,		0,						0,		0,		0,		0X00C0+NOHOLD,	SM_AUTO8,
	POS_3D_UP,			6,						0,		0,		0,		0X0010+NOHOLD,	0,
	TIMER,				6,						0,		140,	0,		0X0010+NOHOLD,	M_TAP,
//58
	POS,				POS_SHOULDER,			100,	0,		REW,	0X00C0+NOHOLD,	M_KNEAD_ROLPART,
//59
	ROTATION,			3,						100,	0,		REW+PN,	0X00C0+NOHOLD,	M_KNEAD,
//60
	POS,				POS_NECK_UPPER,			0,		0,		0,		0X000C+NOHOLD,	M_ROLPART,
//61
	POS,				POS_SHOULDER_LOWER,		0,		0,		0,		0X000C+NOHOLD,	M_ROLPART,
//62
	POS_3D_UP,			4,						0,		0,		0,		0X0020+NOHOLD,	0,
	TIMER,				5,						0,		0,		0,		0X0020+NOHOLD,	0,
//64
	POS,				POS_SHDBLADE_UPPER,		0,		0,		0,		0X000C+NOHOLD,	M_ROLPART,
//65
	POS_3D_UP,			5,						0,		0,		0,		0X0010+NOHOLD,	0,
	TIMER,				5,						0,		0,		0,		0X0010+NOHOLD,	0,
//66
	POS,				POS_BACK,				0,		180,	0,		0X00C0+NOHOLD,	M_OSI_ROLPART,
//73
	POS,				POS_WAIST,				0,		190,	0,		0X000C+NOHOLD,	M_TAP_ROLPART,
//73
	POS_3D_UP,			16,						110,	190,	0,		0X00C0+NOHOLD,	M_KNEAD_TAP,
	TIMER,				1,						0,		0,		0,		0X00C0+NOHOLD,	0,
//74
	POS_3D_UP,			4,						0,		0,		0,		0X00C0+NOHOLD,	0,
	ROTATION,			2,						110,	190,	0,		0X00C0+NOHOLD,	M_KNEAD_TAP,
	WIDTH_SET,			0,						0,		0,		PM,		0X0000,			0,
//74A
	POS_3D_DOWN,		14,						110,	190,	0,		0X00C0+NOHOLD,	M_KNEAD_TAP,//drop
//84
	POS,				POS_WAIST_UPPER1,		110,	0,		REW,	0X000C+NOHOLD,	M_KNEAD_ROLPART,
//84A
	POS_3D_UP,			17,						120,	0,		0,		0X0000,			M_KNEAD,
	TIMER,				1,						0,		0,		0,		0X0000,			0,
//85
	POS_3D_UP,			23,						0,		0,		0,		0X000C+NOHOLD,	0,
	ROTATION,			3,						120,	0,		REW,	0X000C+NOHOLD,	M_KNEAD,
//85A
	POS_3D_DOWN,		13,						120,	0,		REW,	0X000C+NOHOLD,	M_KNEAD,//drop
	TIMER,				2,						0,		0,		0,		0X000C+NOHOLD,	0,
	POS_3D_DOWN,		10,						120,	0,		REW,	0X000C+NOHOLD,	M_KNEAD,//drop
//90
	POS,				POS_WAIST,				120,	0,		REW,	0X000C+NOHOLD,	M_KNEAD_ROLPART,
//90A
	POS_3D_UP,			16,						120,	0,		REW,	0X000C+NOHOLD,	M_KNEAD,
	TIMER,				1,						120,	0,		REW,	0X000C+NOHOLD,	M_KNEAD,
//91
	POS_3D_UP,			12,						0,		0,		0,		0X000C+NOHOLD,	0,
	ROTATION,			3,						120,	0,		REW,	0X000C+NOHOLD,	M_KNEAD,
//92
	POS_3D_UP,			8,						120,	0,		REW,	0X0000,			M_KNEAD,
	TIMER,				1,						120,	0,		REW,	0X0000,			M_KNEAD,
	POS_3D_DOWN,		8,						120,	0,		REW,	0X00C0+NOHOLD,	M_KNEAD,	// drop
	TIMER,				1,						120,	0,		REW,	0X00C0+NOHOLD,	M_KNEAD,
	POS_3D_DOWN,		12,						120,	0,		REW,	0X00C0+NOHOLD,	M_KNEAD,	// drop
//92A
	TIMER,				1,						120,	0,		REW,	0x000C+NOHOLD,	M_KNEAD,
	POS_3D_DOWN,		2,						120,	0,		REW,	0X000C+NOHOLD,	M_KNEAD,	// drop
	ROTATION,			2,						120,	0,		REW,	0X000C+NOHOLD,	M_KNEAD,
//93
	WIDTH_SET,			0,						0,		0,		PN,		0X000C+NOHOLD,	0,
	POS,				POS_SHDBLADE_LOWER,		0,		0,		0,		0X000C+NOHOLD,	M_ROLPART,
//94
	POS,				POS_SHOULDER,			0,		0,		0,		0X00C0+NOHOLD,	M_ROLPART,
//////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
	POS_RET,			0,						0,		0,		0,		0x0000,			0,
//////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//95
	SEATM_START,		0,						0,		0,		0,		0X0000,			SM_AUTO9,
	POS,				POS_SHDBLADE_LOWER,		0,		0,		0,		0X000C+NOHOLD,	M_ROLPART,
//96
	POS,				POS_BACK,				0,		0,		0,		0X00C0+NOHOLD,	M_ROLPART,
//97
	ROTATION,			2,						100,	0,		REW,	0X00C0+NOHOLD,	M_KNEAD,
//97a
	POS_3D_DOWN,		7,						100,	0,		REW,	0X0000,			M_KNEAD,
//98
	POS,				POS_BACK_UPPER,			100,	0,		REW,	0X0020+NOHOLD,	M_KNEAD_ROLPART,
//98A
	POS,				POS_SHDBLADE_LOWER,		100,	0,		REW,	0X000C+NOHOLD,	M_KNEAD_ROLPART,
//99
	POS,				POS_BACK,				100,	0,		REW,	0X0010+NOHOLD,	M_KNEAD_ROLPART,
//100
	ROTATION,			3,						100,	0,		REW,	0X0020+NOHOLD,	M_KNEAD,
//100A
	POS,				POS_WAIST_UPPER1,		110,	0,		REW,	0X000C+NOHOLD,	M_KNEAD_ROLPART,
//100B
	POS_3D_UP,			16,						110,	0,		0,		0X0000,			M_KNEAD,
	ROTATION,			3,						110,	0,		REW,	0X0010+NOHOLD,	M_KNEAD,
//100C
	POS_3D_DOWN,		16,						120,	0,		0,		0X00C0+NOHOLD,	M_KNEAD,
	TIMER,				1,						120,	0,		0,		0X00C0+NOHOLD,	M_KNEAD,
//100D
	POS,				POS_BACK,				120,	0,		REW,	0X00C0+NOHOLD,	M_KNEAD_ROLPART,
//100E
	POS,				POS_SHDBLADE_LOWER,		120,	0,		0,		0X000C+NOHOLD,	M_KNEAD_ROLPART,
//100F
	POS,				POS_SHDBLADE,			120,	0,		0,		0X00C0+NOHOLD,	M_KNEAD_ROLPART,
//100G
	POS,				POS_SHDBLADE_UPPER,		120,	0,		0,		0X00C0+NOHOLD,	M_KNEAD_ROLPART,
//101
	POS,				POS_SHOULDER_UPPER,		100,	0,		REW,	0X000C+NOHOLD,	M_KNEAD_ROLPART,
//102
	ROTATION,			4,						110,	0,		REW,	0X00C0+NOHOLD,	M_KNEAD,
//////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
	COOLDOWN,			60,						0,		0,		0,		0X0000,			0,
//////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//103
	POS,				POS_WAIST,				100,	0,		REW,	0X000C+NOHOLD,	M_KNEAD_ROLPART,	
//103A
	ROTATION,			3,						100,	0,		0,		0X00C0+NOHOLD,	M_KNEAD,
	WIDTH_SET,			0,						0,		0,		PN,		0X00C0+NOHOLD,	0,
//103D
	POS,				POS_SHDBLADE_LOWER,		0,		200,	0,		0X000C+NOHOLD,	M_OSI_ROLPART,
//105
	POS,				POS_BACK_UPPER,			0,		180,	0,		0X000C+NOHOLD,	M_OSI_ROLPART,
//105
	POS,				POS_SHOULDER,			0,		160,	0,		0X0000+NOHOLD,	M_OSI_ROLPART,

  	0xff,				0xff,					0xff,	0xff,	0xff,	0xffff,			0xff
};
//------------------------------------------------------------------------------
// Pg6 uDream Stretch 10 January 2020
//------------------------------------------------------------------------------

T_OPERATION	osim_pg6_course_tbl[] =	

{
//1
//1A
//1B
//1C
//1D
//1E
//1F
//1G
//1H
//1I
//4
//5
//6
//6A
//8
//8A
//9
//10
//10A
//200
//201
//11B
//20
//21
//22
//23
//24
//25
//26
//27
//28
//30
//30A
//30B
//30C
//30D
//32
//33
//34
//35
//36
//37
//38
//38A
//38B
//38C
//38D
//39
//40
//40A
//40A'
//40C
//40D
//40AA
//40"
//40B
//42
//43
//44
//45
//46
//47
//47A
//48
//49
//50
//202
//50A
//203
//204
//204A
//204B
//204C
//204D
//204E
//204F
//51A
//51B
//51C
//205
//51D
//56
//58
//59
//59A
//59B
//60
//61
//206
//207
//208
//210
//62
//63
//64
//66B
//67
//67B
//67C
//67D
//67E
//67F
//67G
//
	0xff,		0xff,				0xff,0xff,	0xff,	0xffff,			0xff
};

 
//------------------------------------------------------------------------------
// PG7: uDream Sleep 30 March 2020
//------------------------------------------------------------------------------
T_OPERATION	osim_pg7_course_tbl[] =		
								
{
	TIMER,				1,						0,		0,		0,		0X0000,			0,
	TRACK_SELECT,		WEAK,					0,		0,		0,		0X0000,		  	0,
	ASI_START,	    	0,			            0,		0,		0,		0X0000,	      	MODE_AUTO9,
//1
	POS,				POS_SHOULDER_UPPER,		100,	0,		0,		0X000C+NOHOLD,	M_KNEAD_ROLPART,		
//2
	POS_3D_UP,			6,						0,		0,		0,		0X000C+NOHOLD,	0,
	ROTATION, 	 	    3,		    			100, 	0, 		0,		0X000C+NOHOLD,	M_KNEAD,
//2'
	POS_3D_DOWN,		6,						0,		0,		0,		0X000C+NOHOLD,	0,
	WIDTH_SET,			0,						0,		0,		PN,		0X000C+NOHOLD,	0,
//2'A
	POS_DOWN,			2,						0,		0,		0,		0X000C+NOHOLD,	M_ROLPART,
//2'B
	POS_3D_UP,			7,						0,		0,		0,		0X000C+NOHOLD,	0,
	TIMER,				4,						0,		0,		0,		0X000C+NOHOLD,	0,
//2a'
	POS_3D_DOWN,		7,						0,		0,		0,		0X000C+NOHOLD,	0,
	WIDTH_SET,			0,						0,		0,		PW,		0X000C+NOHOLD,	0,	
//2a'A
	POS_UP,				3,						0,		0,		0,		0X000C+NOHOLD,	M_ROLPART,	
//4
	TIMER,				9,						0,		0,		0,		0X00C0+NOHOLD,	0,
	TIMER,				5,						0,		0,		0,		0X0080+NOHOLD,	0,
	TIMER,				5,						0,		0,		0,		0X00C0+NOHOLD,	0,
	TIMER,				5,						0,		0,		0,		0X0040+NOHOLD,	0,
	TIMER,				2,						0,		0,		0,		0X0000,			0,
//5
	TIMER,				8,						0,		0,		0,		0X0020+NOHOLD,	0,
	TIMER,				4,						0,		0,		0,		0X0080+NOHOLD,	0,
	TIMER,				3,						0,		0,		0,		0X00C0+NOHOLD,	0,
	TIMER,				4,						0,		0,		0,		0X0040+NOHOLD,	0,
	TIMER,				8,						0,		0,		0,		0X0010+NOHOLD,	0,
	TIMER,				2,						0,		0,		0,		0X0000,			0,
//6
	TIMER,				8,						0,		0,		0,		0X0010+NOHOLD,	0,
	TIMER,				8,						0,		0,		0,		0X00C0+NOHOLD,	0,
	TIMER,				8,						0,		0,		0,		0X0020+NOHOLD,	0,
	TIMER,				8,						0,		0,		0,		0X00C0+NOHOLD,	0,
	TIMER,				6,						0,		0,		0,		0X0080+NOHOLD,	0,
	TIMER,				4,						0,		0,		0,		0X00C0+NOHOLD,	0,
	TIMER,				6,						0,		0,		0,		0X0040+NOHOLD,	0,
	TIMER,				4,						0,		0,		0,		0X00C0+NOHOLD,	0,
	TIMER,				2,						0,		0,		0,		0X0000,			0,

//6A
	POS_3D_UP,			6,						0,		0,		0,		0X000C+NOHOLD,	0,
	ROTATION, 	 	    3,		    			100, 	0, 		0,		0X000C+NOHOLD,	M_KNEAD,
	
//7
	POS_3D_DOWN,		6,						0,		0,		0,		0X000C+NOHOLD,	0,
	WIDTH_SET,			0,						0,		0,		PN,		0X000C+NOHOLD,	0,
	TIMER, 				1,						0,		0,		0,		0X000C+NOHOLD,	0,
//7A
	POS,				POS_SHOULDER,			0,		0,		0,		0X000C+NOHOLD,	M_ROLPART,
//8
	ROTATION, 	 	    2,		    			100, 	0, 		0,		0X0020+NOHOLD, 	M_KNEAD,
	ROTATION, 	 	    1,		    			100, 	0, 		0,		0X0000, 	 	M_KNEAD,	                                                                                          
//9
	POS,				POS_SHOULDER_LOWER2,	0,		0,		PN,		0X0000,			M_ROLPART,
//10
	POS,				POS_NECK_UPPER,			0,		0,		0,		0X00C0+NOHOLD,	M_ROLPART,
//10B
	POS,				POS_NECK,				0,		0,		0,		0X000C+NOHOLD,	M_ROLPART,
//11
	POS_3D_UP,			3,						0,		0,		0,		0X0000,			0,
	ROTATION,			2,						95,		0,		REW,	0X0010+NOHOLD,	M_KNEAD,
	ROTATION,			1,						95,		0,		REW,	0X0000,			M_KNEAD,
//12
	POS_3D,				FIT_POS20,				0,		0,		0,		0X0000,			0,
	THREED_CTRL_DISABLE,0,						0,		0,		0,		0X0000,			0,
//13
	POS,				POS_SHOULDER,			0,		0,		0,		0X0050+NOHOLD,	M_ROLPART,
//14
	POS,				POS_NECK_UPPER,			0,		0,		0,		0X00A0+NOHOLD,	M_ROLPART,
//14A
	POS,				POS_SHOULDER,			0,		0,		0,		0X0050+NOHOLD,	M_ROLPART,
//14B
	POS,				POS_NECK_UPPER,			0,		0,		0,		0X00A0+NOHOLD,	M_ROLPART,
//15
	POS,				POS_SHOULDER_LOWER,		0,		0,		0,		0X00C0+NOHOLD,	M_ROLPART,
//16
	THREED_CTRL_ENABLE,	0,						0,		0,		0,		0X0000,			0,
	POS_3D_DOWN,		6,						0,		0,		0,		0X0000,			0,
//17
	ROTATION,			2,						100,	0,		0,		0X0020+NOHOLD,	M_KNEAD,
	ROTATION,			1,						100,	0,		0,		0X0000,			M_KNEAD,
//17A
	POS_3D_DOWN,		5,						0,		0,		0,		0X000C+NOHOLD,	0,
//18
	POS,				POS_SHOULDER_LOWER2,	100,	0,		0,		0X000C+NOHOLD,	M_KNEAD_ROLPART,
//19
	POS_3D_UP,			1,						0,		0,		0,		0X0000,			0,
	ROTATION,			2,						100,	0,		0,		0X0010+NOHOLD,	M_KNEAD,
	ROTATION,			1,						100,	0,		PN,		0X0000,			M_KNEAD,
//21
	POS,		        POS_SHDBLADE_UPPER,		0,		0,		0,	 	0X000C+NOHOLD,	M_ROLPART,
//22
	POS,		        POS_BACK,	   			0,		0,		0,	 	0X000C+NOHOLD,	M_ROLPART,
//22Aa
	POS,				POS_WAIST,				0,		180,	0,		0X000C+NOHOLD,	M_OSI_ROLPART,
//22Ab
	WIDTH_SET,			0,						0,		0,		PM,		0X000C+NOHOLD,	0,	
//22A
	TIMER,				4,						0,		0,		0,		0X00E0+NOHOLD,	0,
	TIMER,				4,						0,		0,		0,		0X00A0+NOHOLD,	0,
//22B
	TIMER,				4,						0,		0,		0,		0X00D0+NOHOLD,	0,
	TIMER,				4,						0,		0,		0,		0X0050+NOHOLD,	0,		
//23
	POS,				POS_HIPS,				0,		0,		0,		0X00C0+NOHOLD,	M_ROLPART,
//23A
	POS_3D_UP,			10,						110,	0,		REW,	0X00C0+NOHOLD,	M_KNEAD,
	TIMER,				1,						0,		0,		0,		0X00C0+NOHOLD,	0,
//24
	POS_3D_UP,			4,						0,		0,		0,		0X0000,			0,
	ROTATION,			1,						105,	0,		REW,	0X0000,			M_KNEAD,
	ROTATION,			1,						105,	0,		REW,	0X00C0+NOHOLD,	M_KNEAD,
	ROTATION,			1,						105,	0,		REW,	0X0000,			M_KNEAD,
//25
	ROTATION,			1,						105,	100,	REW+PN,	0X00C0+NOHOLD,	M_KNEAD_TAP,
	ROTATION,			1,						105,	100,	REW+PN,	0X0000,			M_KNEAD_TAP,
	ROTATION,			1,						105,	100,	REW+PN,	0X00C0+NOHOLD,	M_KNEAD_TAP,
//26
	POS_3D_DOWN,		14,						0,		140,	0,		0X0050+NOHOLD,	M_OSI,
	TIMER,				7,						0,		140,	0,		0X0050+NOHOLD,	M_OSI,
//26A
	POS_3D,				FIT_POS0,				0,		0,		0,		0X0000,			0,	
//27
	GO_UP_TIME,			7,						0,		140,	0,		0X00C0+NOHOLD,	M_OSI_ROLPART,
//28
	WIDTH_SET,			0,						0,		130,	PM,		0X0000,			M_OSI,
//29
	GO_DOWN_TIME,		7,						0,		160,	0,		0X00A0+NOHOLD,	M_OSI_ROLPART,
//30
	GO_UP_TIME,			3,						105,	0,		REW,	0X00C0+NOHOLD,	M_KNEAD_ROLPART,
//31	
	POS,				POS_HIPS,				105,	0,		REW+PW,	0X000C+NOHOLD,	M_KNEAD_ROLPART,
	POS_3D,				FIT_POS0,				0,		0,		0,		0X000C+NOHOLD,	0,
	THREED_CTRL_DISABLE,0,						0,		0,		0,		0X0000,			0,
//32
	TIMER,				7,						0,		0,		0,		0X0080+NOHOLD,	0,
//33
	TIMER,				3,						0,		0,		0,		0X00C0+NOHOLD,	0,
	TIMER,				2,						0,		0,		0,		0X0040+NOHOLD,	0,
//33A
	THREED_CTRL_ENABLE,0,						0,		0,		0,		0X0000,			0,	
//34
	GO_UP_TIME,			5,						105,	0,		REW,	0X0040+NOHOLD,	M_KNEAD_ROLPART,
//35
	POS,				POS_SHDBLADE_UPPER,		110,	0,		REW,	0X000C+NOHOLD,	M_KNEAD_ROLPART,
//36
	POS,				POS_SHOULDER_UPPER,		110,	0,		REW,	0X00C0+NOHOLD,	M_KNEAD_ROLPART,
//36A
	WIDTH_SET,			0,						0,		0,		PN,		0X000C+NOHOLD,	0,
//37
	POS,		        POS_SHDBLADE_UPPER,		0,		0,		0,	 	0X000C+NOHOLD,	M_ROLPART,
//37A
	POS,		        POS_BACK,				0,		0,		0,	 	0X000C+NOHOLD,	M_ROLPART,
//37B
	POS,		        POS_HIPS,				0,		0,		0,	 	0X0010+NOHOLD,	M_ROLPART,
//37C
	WIDTH_SET,			0,						0,		0,		PM,		0X0000,			0,
//37D
	POS,		        POS_BACK,				0,		0,		0,	 	0X0020+NOHOLD,	M_ROLPART,
//37E
	WIDTH_SET,			0,						0,		0,		PN,		0X0000,			0,
//37F
	POS,		        POS_SHDBLADE_UPPER,		0,		0,		0,	 	0X00C0+NOHOLD,	M_ROLPART,
//37G
	POS,				POS_SHOULDER_LOWER2,	110,	0,		0,		0X00C0+NOHOLD,	M_KNEAD_ROLPART,
//41
	POS,				POS_SHOULDER_UPPER,		110,	0,		0,		0X0000,			M_KNEAD_ROLPART,
//42
	POS_3D_UP,			6,						0,		0,		0,		0X000C+NOHOLD,	0,
	ROTATION,			3,						100,	0,		PW,		0X000C+NOHOLD,	M_KNEAD,
//43
	POS_3D_DOWN,		6,						0,		0,		0,		0X0000,			0,
//44
   	TIMER,				10,						0,		0,		0,		0X0090+NOHOLD,	0,
//45
   	TIMER,				8,						0,		0,		0,		0X00C0+NOHOLD,	0,
//46
	TIMER,				10,						0,		0,		0,		0X0060+NOHOLD,	0,
//47
	TIMER,				8,						0,		0,		0,		0X00C0+NOHOLD,	0,
//48
	WIDTH_SET,			0,						0,		0,		PN,		0X0000,			0,
//48A
	POS_3D,				FIT_POS21,				0,		0,		0,		0X0000,			0,
	THREED_CTRL_DISABLE,0,						0,		0,		0,		0X0000,			0,
//49
	POS,				POS_NECK_UPPER,			0,		0,		PN,		0X00C0+NOHOLD,	M_ROLPART,
//50
	POS,				POS_SHOULDER_LOWER,		0,		0,		PN,		0X000C+NOHOLD,	M_ROLPART,
//50A
	POS,				POS_NECK_UPPER,			0,		0,		0,		0X0020+NOHOLD,	M_ROLPART,
//50B
	POS,				POS_SHOULDER,			0,		0,		0,		0X0010+NOHOLD,	M_ROLPART,
//50C
	POS_3D,				FIT_POS15,				0,		0,		0,		0X0000,			0,
	THREED_CTRL_ENABLE,0,						0,		0,		0,		0X0000,			0,
//50D
	WIDTH_SET,			0,						0,		0,		PW,		0X0000,			0,
//55
	TIMER,				6,						0,		0,		0,		0X0080+NOHOLD,	0,
	TIMER,				4,						0,		0,		0,		0X00C0+NOHOLD,	0,
	
//56
	TIMER,				6,						0,		0,		0,		0X00A0+NOHOLD,	0,
	TIMER,				4,						0,		0,		0,		0X00C0+NOHOLD,	0,
//57
	TIMER,				6,						0,		0,		0,		0X0040+NOHOLD,	0,
	TIMER,				4,						0,		0,		0,		0X00C0+NOHOLD,	0,
	
//58
	TIMER,				6,						0,		0,		0,		0X0050+NOHOLD,	0,
	TIMER,				4,						0,		0,		0,		0X00C0+NOHOLD,	0,
	TIMER,				2,						0,		0,		0,		0X0000,			0,
//58A
	WIDTH_SET,			0,						0,		0,		PN,		0X000C+NOHOLD,	0,
//60
	POS,				POS_BACK,				0,		0,		PN,	 	0X000C+NOHOLD,	M_ROLPART,
//62
	POS,				POS_SHDBLADE_UPPER,		0,		0,		PN,		0X000C+NOHOLD,	M_ROLPART,
//64
	POS,				POS_SHDBLADE,			0,		0,		PN,		0X000C+NOHOLD,	M_ROLPART,
//66
	POS,              	POS_SHOULDER,			0, 		0, 		PN,		0X000C+NOHOLD,	M_ROLPART,
//67
	POS,              	POS_NECK,		       	0, 		0, 		0,		0X000C+NOHOLD,	M_ROLPART,
//68
	POS_3D,				FIT_POS22,				0,		0,		0,		0X000C+NOHOLD,	0,
	THREED_CTRL_DISABLE,0,						0,		0,		0,		0X000C+NOHOLD,	0,
//69
	POS,              	POS_SHOULDER,			0, 		0, 		0,		0X0010+NOHOLD,	M_ROLPART,
//70
	POS,            	POS_NECK_UPPER,		    0, 		0, 		0,		0X0010+NOHOLD,	M_ROLPART,
//70a
	POS,            	POS_SHOULDER,		    0, 		0, 		0,		0X0020+NOHOLD,	M_ROLPART,
//70B
	POS,            	POS_NECK_UPPER,		    0, 		0, 		0,		0X0020+NOHOLD,	M_ROLPART,
//71
	POS,				POS_SHOULDER_UPPER,		0,		0,		0,		0X000C+NOHOLD,	M_ROLPART,
//72
	THREED_CTRL_ENABLE,	0,						0,		0,		0,		0X000C+NOHOLD,	0,
//73
	WIDTH_SET,			0,						0,		0,		PW,		0X000C+NOHOLD,	0,
//74
	TIMER,				5,						0,		0,		0,		0X0020+NOHOLD,	0,
	TIMER,				6,						0,		0,		0,		0X0040+NOHOLD,	0,
	TIMER,				3,						0,		0,		0,		0X00C0+NOHOLD,	0,
	TIMER,				4,						0,		0,		0,		0X0020+NOHOLD,	0,
	

//75
	TIMER,				5,						0,		0,		0,		0X0010+NOHOLD,	0,
	TIMER,				6,						0,		0,		0,		0X0080+NOHOLD,	0,
	TIMER,				3,						0,		0,		0,		0X00C0+NOHOLD,	0,
	TIMER,				4,						0,		0,		0,		0X0010+NOHOLD,	0,
	
//76
	TIMER,				3,						0,		0,		0,		0X00C0+NOHOLD,	0,
////////////////////////////////repeat loop///////////////////////////////////////////////////////////////////
	POS_RET,	        0,						0,		0,		0,		0X0000,			0,
//////////////////////////////repeat loop/////////////////////////////////////////////////////////////////////
//78
	WIDTH_SET,			0,						0,		0,		PW,		0X0010+NOHOLD,	0,
	TIMER,	 		   	6,						0,		0,		PW,		0X0010+NOHOLD,	0,
//79
	TIMER,       		5,						0,		0,		0,		0X0090+NOHOLD,	0,
	TIMER,      		6,						0,		0,		0,		0X0080+NOHOLD,	0,
	TIMER,      		2,						0,		0,		0,		0X0080,			0,
//80
	TIMER,		  	  	4,						0,		0,		0,		0x00C0+NOHOLD,	0,
	TIMER,		  	  	2,						0,		0,		0,		0x00C0,			0,
	TIMER,				6,						0,		0,		0,		0X0020+NOHOLD,	0,	
//81
	TIMER,	        	5,						0,		0,		0,		0x0060+NOHOLD,	0,
	TIMER,	       		6,						0,		0,		0,		0x0040+NOHOLD,	0,
	TIMER,	       		2,						0,		0,		0,		0x0040,			0,
//84
	TIMER,		  	  	6,						0,		0,		0,		0x00C0+NOHOLD,	0,
//88A
	TIMER,				6,						0,		0,		0,		0x0060+NOHOLD,	0,
//88B
	TIMER,				6,						0,		0,		0,		0x00E0+NOHOLD,	0,
//88C
	TIMER,				6,						0,		0,		0,		0x0050+NOHOLD,	0,
//88D
	TIMER,				6,						0,		0,		0,		0x00D0+NOHOLD,	0,
//88E
	TIMER,				4,						0,		0,		0,		0x00C0+NOHOLD,	0,

//////////////////////////////////////////////////////////////////////////////////////////////////////////////
	COOLDOWN,			60,						0,		0,		0,		0X0000,			0,
//////////////////////////////////////////////////////////////////////////////////////////////////////////////
//89 
	TIMER,				7,						0,		0,		0,		0X00C0+NOHOLD,	0,
	TIMER,				6,						0,		0,		0,		0X0010+NOHOLD,	0,
	TIMER,				5,						0,		0,		0,		0X00C0+NOHOLD,	0,
	TIMER,				2,						0,		0,		0,		0X00C0,			0,	
	TIMER,				6,						0,		0,		0,		0X0020+NOHOLD,	0,
//92
	TIMER,				7,						0,		0,		0,		0X00C0+NOHOLD,	0,
	TIMER,				3,						0,		0,		0,		0X0040+NOHOLD,	0,
	TIMER,				3,						0,		0,		0,		0X0040,			0,	
	TIMER,				7,						0,		0,		0,		0X00C0+NOHOLD,	0,
	TIMER,				3,						0,		0,		0,		0X0080+NOHOLD,	0,
	TIMER,				3,						0,		0,		0,		0X0080,			0,
	
//93
	TIMER,				8,						0,		0,		0,		0X00C0+NOHOLD,	0,
	TIMER,				3,						0,		0,		0,		0X0000,			0,
	
	
	0xff,				0xff,					0xff,	0xff,	0xff,	0xffff,				0xff
};
//------------------------------------------------------------------------------
// PG8: uDream Energize 20 August 2020
//------------------------------------------------------------------------------
T_OPERATION	osim_pg8_course_tbl[] =
{
	TRACK_SELECT,		STRONG,					0,		0,		0,		0X0000,				0,
	POS,				POS_SHOULDER,			0,		0,		0,		0X0000,				M_ROLPART,
	ASI_START,	    	0,			            0,		0,		0,	  	0X0000,		      	MODE_AUTO2,
//1
	LED_COLOR,  		0, 						0, 		0, 		0, 		0,  				COLOUR339,
	WIDTH_SET,			0,						0,		0,		PN,		0X000C+NOHOLD,		0,
//2
	SEATM_START,		0,						0,		0,		0,		0X0000,				SM_AUTO23, //Energy Boost seat sub 1
	TIMER,				3,						0,		120,	0,		0X000C+NOHOLD,		M_TAP,
//3
	POS_3D_UP,			3,						0,		0,		0,		0X00C0+NOHOLD,		0,
	ROTATION,			3,						125,	140,	PM,		0X00C0+NOHOLD,		M_KNEAD_TAP,
//4
	POS_3D_UP,			2,						0,		0,		0,		0X00C0+NOHOLD,		0,
	TIMER,				6,						0,		130,	0,		0X00C0+NOHOLD,		M_TAP,
//7
	POS,				POS_SHDBLADE_UPPER,		120,	0,		REW,	0X0000,				M_KNEAD_ROLPART,
//8
	POS,				POS_BACK_UPPER,			120,	0,		REW,	0X00C0+NOHOLD,		M_KNEAD_ROLPART,
//9
//	SEATM_START,		0,						0,		0,		0,		0X0000,				SM_AUTO34, //Energy Boost seat sub 2
	POS,				POS_WAIST_UPPER1,		125,	0,		REW,	0X00C0+NOHOLD,		M_KNEAD_ROLPART,
//10
	POS,				POS_WAIST,				125,	180,	REW,	0X00C0+NOHOLD,		M_KNEAD_TAP_ROLPART,
//11
	LED_COLOR,  		0, 						0, 		0, 		0, 		0,  				COLOUR340,
	SEATM_START,		0,						0,		0,		0,		0X0000,				SM_AUTO24, //Energy Boost seat sub 3
	POS,				POS_HIPS,				125,	180,	REW,	0X00C0+NOHOLD,		M_KNEAD_TAP_ROLPART,
//11a
	POS_3D,				FIT_POS5,				0,		0,		0,		0X000C+NOHOLD,		0,
	TIMER,				1,						0,		0,		0,		0x000C,				0,
	POS_3D,				FIT_POS0,				0,		0,		0,		0x000C+NOHOLD,		0,
//12
	POS,				POS_WAIST_UPPER1,		125,	0,	REW,		0X000C+NOHOLD,		M_KNEAD_ROLPART,
	POS_UP,				5,						125,	0,	REW,		0X000C+NOHOLD,		M_KNEAD_ROLPART,
//13
	POS_3D_UP,			8,						125,	0,		REW,	0X000C+NOHOLD,		M_KNEAD,
	ROTATION,			1,						125,	0,		REW,	0X000C+NOHOLD,		M_KNEAD,
	POS_3D_UP,			9,						125,	0,		REW,	0X000C+NOHOLD,		M_KNEAD,
	TIMER,				2,						0,		0,		0,		0X000C+NOHOLD,		0,
//13A
	POS_3D_UP,			4,						125,	0,		REW,	0X0020+NOHOLD,		M_KNEAD,	
	ROTATION,			2,						125,	0,		REW,	0X0020+NOHOLD,		M_KNEAD,
	ROTATION,			1,						125,	0,		REW,	0X0020,				M_KNEAD,
//14
	POS_DOWN,			5,						125,	170,	0,		0X0000+NOHOLD,		M_KNEAD_TAP_ROLPART,
//15
	POS_3D_UP,			13,						0,		0,		0,		0X0010+NOHOLD,		0,
	ROTATION,			3,						125,	170,	PN,		0X0010+NOHOLD,		M_KNEAD_TAP,
//16
	POS_3D_DOWN,		4,						0,		200,	0,		0X0000+NOHOLD,		M_TAP,
	POS_3D_DOWN,		4,						0,		200,	0,		0X0000+NOHOLD,		M_TAP,
	POS_3D_DOWN,		5,						0,		200,	0,		0X0000+NOHOLD,		M_TAP,
//16A
	POS_3D,				FIT_POS0,				0,		0,		0,		0X0000,				0,
//17
	POS,				POS_BACK,				110,	0,		0,		0X000C+NOHOLD,		M_KNEAD_ROLPART,
//18
	POS_3D_UP,			14,						0,		0,		0,		0X0020+NOHOLD,		0,
	ROTATION,			2,						125,	0,		PN,		0X0020+NOHOLD,		M_KNEAD,
//19
	POS_3D_DOWN,		12,						125,	0,		0,		0X0000,				M_KNEAD,
	POS_3D_UP,			8,						125,	0,		0,		0X0000,				M_KNEAD,
	POS_3D_DOWN,		8,						125,	0,		0,		0X0000,				M_KNEAD,
//20
	POS,				POS_BACK_UPPER,			125,	0,		0,		0X00C0+NOHOLD,		M_KNEAD_ROLPART,
//21
	POS_3D_UP,			12,						0,		0,		0,		0X0010+NOHOLD,		0,
	ROTATION,			2,						125,	0,		PN,		0X0010+NOHOLD,		M_KNEAD,
//22	
	POS_3D_DOWN,		12,						125,	0,		0,		0X00C0+NOHOLD,		M_KNEAD,
	POS_3D_UP,			8,						125,	0,		0,		0X00C0+NOHOLD,		M_KNEAD,
	POS_3D_DOWN,		18,						125,	0,		0,		0X00C0+NOHOLD,		M_KNEAD,
//27
	LED_COLOR,  		0, 						0, 		0, 		0, 		0,  				COLOUR341,

	POS,				POS_SHOULDER,			125,	0,		0,		0X0000,				M_KNEAD_ROLPART,	
//28
//////////////////////////////////// Subroutine Squeez #2 Ver4/////////////////////////////////
//1
	POS_3D_UP,			2,						0,		0,		0,		0X0020+NOHOLD,		0,
	ROTATION,			3,						125,	150,	PW,		0X0020+NOHOLD,		M_KNEAD_TAP,
	TIMER,				1,						0,		0,		0,		0X0020+NOHOLD,		0,
//2
	WIDTH_SET,			0,						125,	150,	PN,		0X0010+NOHOLD,		M_KNEAD_TAP,
	TIMER,				2,						0,		0,		0,		0X0010+NOHOLD,		0,
//3
	WIDTH_SET,			0,						125,	0,		PW,		0X0020+NOHOLD,		M_KNEAD,
	TIMER,				1,						0,		0,		0,		0X0020+NOHOLD,		0,
//4 (REPEAT STEP 2 - 3 X1)
	WIDTH_SET,			0,						125,	220,	PN,		0X0010+NOHOLD,		M_KNEAD_TAP,
	TIMER,				2,						0,		0,		0,		0X0010+NOHOLD,		0,
	WIDTH_SET,			0,						125,	150,	PW,		0X0020+NOHOLD,		M_KNEAD_TAP,
	TIMER,				1,						0,		0,		0,		0X0020+NOHOLD,		0,
//REPEAT END
//5
	ROTATION,			3,						125,	160,	REW+PN,	0X0010+NOHOLD,		M_KNEAD_TAP,
//5A
	TIMER,				1,						0,		0,		0,		0X000C+NOHOLD,		0,
//6
	POS_3D_DOWN,		2,						0,		0,		0,		0X000C+NOHOLD,		0,
	POS,				POS_SHOULDER_LOWER,		0,		0,		0,		0X000C+NOHOLD,		M_ROLPART,
//7
	POS_3D_UP,			2,						0,		0,		0,		0X0010+NOHOLD,		0,
	ROTATION,			3,						125,	160,	PW,		0X0010+NOHOLD,		M_KNEAD_TAP,
//7A
	TIMER,				1,						0,		0,		0,		0X000C+NOHOLD,		0,
//8
	WIDTH_SET,			0,						0,		0,		PN,		0X0020+NOHOLD,		0,
	TIMER,				2,						125,	160,	0,		0X0020+NOHOLD,		M_KNEAD_TAP,
//9
	WIDTH_SET,			0,						125,	0,		PW,		0X0010+NOHOLD,		M_KNEAD,
	TIMER,				1,						0,		0,		0,		0X0010+NOHOLD,		0,
//10(REPEAT 8-9)
	WIDTH_SET,			0,						0,		0,		PN,		0X0020+NOHOLD,		0,
	TIMER,				2,						125,	160,	0,		0X0020+NOHOLD,		M_KNEAD_TAP,
	WIDTH_SET,			0,						125,	0,		PW,		0X0010+NOHOLD,		M_KNEAD,
	TIMER,				1,						0,		0,		0,		0X0010+NOHOLD,		0,
//REPEAT END
//11	
	ROTATION,			2,						125,	170,	REW+PN,	0X0020+NOHOLD,		M_KNEAD_TAP,
	ROTATION,			1,						125,	170,	REW+PN,	0X0020,				M_KNEAD_TAP,
	TIMER,				1,						0,		0,		0,		0X0020,				0,
///////////////////////////////END Subroutine Squeez #2 VER4	///////////////////////////////////////////////////		
//29
	POS,				POS_SHDBLADE_LOWER,		0,		160,	0,		0X00C0+NOHOLD,		M_TAP_ROLPART,
//30
	LED_COLOR,  		0, 						0, 		0, 		0, 		0,  				COLOUR342,
	POS,				POS_BACK_UPPER,			0,		170,	0,		0X00C0+NOHOLD,		M_TAP_ROLPART,
//32
	POS,				POS_HIPS,				0,		200,	0,		0X000C+NOHOLD,		M_TAP_ROLPART,
//33
	POS_3D_UP,			8,						0,		220,	0,		0X0020+NOHOLD,		M_TAP,
	TIMER,				4,						0,		220,	0,		0X0020+NOHOLD,		M_TAP,
//33'
	POS_3D_DOWN,		8,						0,		220,	0,		0X0020,				M_TAP,
//33'A
	POS_3D_UP,			8,						0,		220,	0,		0X0010+NOHOLD,		M_TAP,
	TIMER,				4,						0,		220,	0,		0X0010+NOHOLD,		M_TAP,
//33'B
	POS_3D_DOWN,		8,						0,		220,	0,		0X0010,				M_TAP,
//33A
	POS_3D,				FIT_POS0,				0,		0,		0,		0X000C+NOHOLD,		0,
//33B
	POS,				POS_WAIST,				125,	200,	REW,	0X000C+NOHOLD,		M_KNEAD_TAP_ROLPART,
//33C
	POS_3D_UP,			8,						0,		200,	0,		0X000C+NOHOLD,		M_TAP,
	TIMER,				2,						0,		200,	0,		0X000C+NOHOLD,		M_TAP,
	POS_3D_UP,			8,						0,		200,	0,		0X000C+NOHOLD,		M_TAP,
	TIMER,				2,						0,		200,	0,		0X000C+NOHOLD,		M_TAP,
	POS_3D_UP,			6,						0,		200,	0,		0X000C+NOHOLD,		M_TAP,
	TIMER,				4,						0,		200,	0,		0X000C+NOHOLD,		M_TAP,
//35
	LED_COLOR,  		0, 						0, 		0, 		0, 		0,  			COLOUR343,
	POS_3D_UP,			2,						0,		0,		0,		0X000C+NOHOLD,		0,
	ROTATION,			3,						125,	0,		REW+PN,	0X000C+NOHOLD,		M_KNEAD,
//36'
	POS_3D_DOWN,		14,						0,		200,	0,		0X000C+NOHOLD,		M_TAP,
	TIMER,				1,						0,		200,	0,		0X000C+NOHOLD,		M_TAP,
//36'A
	POS_3D_UP,			10,						0,		200,	0,		0X0010+NOHOLD,		M_TAP,
	TIMER,				3,						0,		200,	0,		0X0010+NOHOLD,		M_TAP,
	POS_3D_UP,			4,						0,		200,	0,		0X0010+NOHOLD,		M_TAP,
	TIMER,				4,						0,		200,	0,		0X0010+NOHOLD,		M_TAP,
//36A
	POS_3D,				FIT_POS24,				0,		0,		0,		0X000C+NOHOLD,		0,
	THREED_CTRL_DISABLE,0, 						0, 		0,		0,		0X000C+NOHOLD, 		0,
//37
	POS,				POS_WAIST_UPPER1,		0,		180,	0,		0X000C+NOHOLD,		M_TAP_ROLPART,
//38
	ROTATION,			2,						125,	180,	REW+PN,	0X0020+NOHOLD,		M_KNEAD_TAP,
	ROTATION,			1,						125,	180,	REW+PN,	0X0020,				M_KNEAD_TAP,
//38'
	POS_3D,				FIT_POS16,				0,		0,		0,		0x000C+NOHOLD,		0,
	TIMER,				1,						0,		0,		0,		0x000C,				0,
//38'a
	POS_3D_DOWN,		6,						0,		0,		0,		0x000C+NOHOLD,		0,
	TIMER,				1,						0,		0,		0,		0x000C,				0,
//38'b
	POS_3D_DOWN,		5,						0,		0,		0,		0x000C+NOHOLD,		0,
	TIMER,				1,						0,		0,		0,		0x000C,				0,
//38A
	THREED_CTRL_ENABLE,0, 						0, 		0,		0,		0X000C+NOHOLD, 		0,
//39
	POS_3D_UP,			5,						0,		180,	0,		0X000C+NOHOLD,		M_TAP,
	TIMER,				1,						0,		180,	0,		0X000C+NOHOLD,		M_TAP,	
	
	POS_3D_UP,			7,						0,		180,	0,		0X000C+NOHOLD,		M_TAP,
	TIMER,				1,						0,		180,	0,		0X000C+NOHOLD,		M_TAP,		
	
	POS_3D_UP,			8,						0,		180,	0,		0X000C+NOHOLD,		M_TAP,
	TIMER,				1,						0,		180,	0,		0X000C+NOHOLD,		M_TAP,
	
	POS_3D_UP,			4,						0,		180,	0,		0X000C+NOHOLD,		M_TAP,
	TIMER,				4,						0,		180,	0,		0X000C+NOHOLD,		M_TAP,
	
	POS_3D_DOWN,		10,						0,		180,	0,		0X000C+NOHOLD,		M_TAP,			
	TIMER,				1,						0,		180,	0,		0X000C+NOHOLD,		M_TAP,
	
	POS_3D_DOWN,		6,						0,		180,	0,		0X000C+NOHOLD,		M_TAP,
	POS_3D_UP,			8,						0,		180,	0,		0X000C+NOHOLD,		M_TAP,
	TIMER,				1,						0,		180,	0,		0X000C+NOHOLD,		M_TAP,
	
	POS_3D_UP,			4,						0,		180,	0,		0X000C+NOHOLD,		M_TAP,
	TIMER,				1,						0,		180,	0,		0X000C+NOHOLD,		M_TAP,
			
	POS_3D_UP,			4,						0,		180,	0,		0x000C+NOHOLD,		M_TAP,
	TIMER,				4,						0,		180,	0,		0x000C+NOHOLD,		M_TAP,
//39A
	POS_3D,				FIT_POS24,				0,		0,		0,		0X000C+NOHOLD,		0,
	THREED_CTRL_DISABLE,0, 						0, 		0,		0,		0X000C+NOHOLD, 		0,
//40
	POS_UP,				5,						125,	0,		REW,	0X000C+NOHOLD,		M_KNEAD_ROLPART,
//41	
	ROTATION,			3,						125,	0,		REW+PN,	0X0010+NOHOLD,		M_KNEAD,
//41'
	POS_3D,				FIT_POS16,				0,		0,		0,		0x000C+NOHOLD,		0,
	TIMER,				1,						0,		0,		0,		0x000C,				0,
//41'a
	POS_3D,				FIT_POS10,				0,		0,		0,		0x000C+NOHOLD,		0,
	TIMER,				1,						0,		0,		0,		0x000C,				0,
//41'b
	POS_3D,			FIT_POS5,				0,		0,		0,		0x000C+NOHOLD,		0,
	TIMER,				1,						0,		0,		0,		0x000C,				0,
//41A
	LED_COLOR,  		0, 						0, 		0, 		0, 		0,  				COLOUR344,
	SEATM_START,		0,						0,		0,		0,		0X0000,				SM_AUTO25, //Energy Boost seat sub 3
	THREED_CTRL_ENABLE,0, 						0, 		0,		0,		0X000C+NOHOLD, 		0,
//42
	POS_3D_UP,			6,						125,	0,		REW,		0X000C+NOHOLD,	M_KNEAD,
	TIMER,				1,						125,	0,		REW,		0X000C,			M_KNEAD,
		
	POS_3D_UP,			6,						125,	0,		REW,		0X000C+NOHOLD,	M_KNEAD,
	TIMER,				1,						125,	0,		REW,		0X000C,			M_KNEAD,
	
	POS_3D_UP,			7,						125,	0,		REW,		0X000C+NOHOLD,	M_KNEAD,
	TIMER,				2,						125,	0,		REW,		0X000C,			M_KNEAD,
		
	POS_3D_UP,			4,						125,	0,		REW,		0X000C+NOHOLD,	M_KNEAD,
	TIMER,				2,						125,	0,		REW,		0X000C,			M_KNEAD,

	POS_3D_DOWN,		10,						125,	0,		REW,		0X000C+NOHOLD,	M_KNEAD,
	TIMER,				2,						125,	0,		REW,		0X000C,			M_KNEAD,
		
	POS_3D_DOWN,		5,						125,	0,		REW,		0X000C+NOHOLD,	M_KNEAD,
	TIMER,				1,						125,	0,		REW,		0X000C,			M_KNEAD,
	
	POS_3D_UP,			11,						125,	0,		REW,		0X000C+NOHOLD,	M_KNEAD,
	TIMER,				2,						125,	0,		REW,		0X000C,			M_KNEAD,
	
	POS_3D_UP,			4,						125,	0,		REW,		0X000C+NOHOLD,	M_KNEAD,
	TIMER,				1,						125,	0,		REW,		0X000C,			M_KNEAD,
	
	POS_3D_DOWN,		10,						125,	0,		REW,		0X000C+NOHOLD,	M_KNEAD,
	TIMER,				2,						125,	0,		REW,		0X000C,			M_KNEAD,
	POS_3D_DOWN,		5,						125,	0,		REW,		0X000C+NOHOLD,	M_KNEAD,
	TIMER,				2,						125,	0,		REW,		0x000C,			M_KNEAD,
	WIDTH_SET,			0,						125,	0,		REW+PN,		0x000C+NOHOLD,	M_KNEAD,
//43
	POS,				POS_BACK,				115,	0,		REW+PN,		0X0000+NOHOLD,	M_KNEAD_ROLPART,
//43A
	THREED_CTRL_DISABLE,0, 						0, 		0,		0,		0X0000+NOHOLD, 		0,
//43B
	POS,				POS_SHDBLADE_UPPER,		115,	0,	REW,		0X00C0+NOHOLD,		M_KNEAD_ROLPART,
//43C
	THREED_CTRL_ENABLE,0, 						0, 		0,		0,		0X000C+NOHOLD, 		0,
//55
	SEATM_START,		0,						0,		0,		0,		0X0000,				SM_AUTO4, 
	POS,				POS_SHOULDER_LOWER,		110,	0,	REW+PN,		0X00C0+NOHOLD,		M_KNEAD_ROLPART,
//55A
	POS,				POS_SHOULDER,			110,	0,	REW+PN,		0X000C+NOHOLD,		M_KNEAD_ROLPART,	
//57
	ROTATION,			3,						100,	0,	REW+PN,		0X002C+NOHOLD,		M_KNEAD,
//57a
	SEATM_START,		0,						0,		0,		0,		0X0000,				SM_AUTO26, //Energy Boost seat sub 3
//////////////////////////////////////////////////Subroutine #3 ���݉����@�� with ��r �� rev 5//////////////////////////////////////////////////////
//1
	ROTATION,			2,						125,	160,	PM,		0X0020+NOHOLD,		M_KNEAD_TAP,
	ROTATION,			1,						125,	160,	PM,		0X0020,				M_KNEAD_TAP,
	
//2
	TIMER,				2,						0,		170,	PN,		0X0000,				M_TAP,
	TIMER,				4,						0,		170,	PN,		0X0020+NOHOLD,		M_TAP,
//3
	ROTATION,			3,						100,	0,		PN,		0X0010+NOHOLD,		M_KNEAD,
//4
	TIMER,				2,						0,		130,	PN,		0X0000,				M_TAP,
	TIMER,				4,						0,		130,	PN,		0X0010+NOHOLD,		M_TAP,
//4A
	POS,				POS_SHOULDER_LOWER,		110,	130,	PN,		0X00C0+NOHOLD,		M_KNEAD_TAP_ROLPART,
//5
	ROTATION,			2,						125,	160,	PN,		0X0080+NOHOLD,		M_KNEAD_TAP,
//6
	TIMER,				2,						0,		130,	PN,		0X0000,				M_TAP,
	TIMER,				4,						0,		130,	PN,		0X00C0+NOHOLD,		M_TAP,
//7
	ROTATION,			2,						125,	0,		PN,		0X0040+NOHOLD,		M_KNEAD,
//8
	TIMER,				4,						0,		170,	PN,		0X00C0+NOHOLD,		M_TAP,
	TIMER,				2,						0,		170,	0,		0X0000+NOHOLD,		M_TAP,

//////////////////////////////////////////////////Subroutine #3 ���݉����@�� with ��r END//////////////////////////////////////////////////////	
//60
	LED_COLOR,  		0, 						0, 		0, 		0, 		0,  			COLOUR345,
	SEATM_START,		0,						0,		0,		0,		0X0000,				SM_AUTO27, //Energy Boost seat sub 3
	ROTATION,			2,						125,	0,		REW+PN,		0X0010+NOHOLD,	M_KNEAD,
//60A
	POS_3D_DOWN,		2,						120,	0,		REW+PM,	0X0000+NOHOLD,		M_KNEAD,
//69
	POS,				POS_SHDBLADE_LOWER,		100,	0,		0,		0X00C0+NOHOLD,		M_KNEAD_ROLPART,
//69A
	POS,				POS_HIPS,				110,	0,		REW,	0X000C+NOHOLD,		M_KNEAD_ROLPART,
//69a'
	POS_3D_UP,			5,						120,	0,		REW,	0X000C+NOHOLD,		M_KNEAD,
//69B
	POS_3D_UP,			5,						120,	0,		REW+PM,	0X0020+NOHOLD,		M_KNEAD,
	ROTATION,			3,						120,	0,		REW+PM,	0X0020+NOHOLD,		M_KNEAD,
//69b'
	POS_3D_DOWN,		5,						120,	0,		REW,	0x000C+NOHOLD,		M_KNEAD,
	POS_3D_DOWN,		5,						120,	0,		REW,	0x000C+NOHOLD,		M_KNEAD,
//69D

///////////////////////////////////////////Subroutine  #2 �@�� with ���� REV7////////////////////////////////////////////

//1
	POS_3D_UP,			6,						120,	200,	REW,	0X0080+NOHOLD,		M_KNEAD_TAP,
	POS_3D_UP,			6,						120,	200,	REW,	0X0080+NOHOLD,		M_KNEAD_TAP,
	ROTATION,			3,						120,	200,	REW+PN,	0X0080+NOHOLD,		M_KNEAD_TAP,
//2
	TIMER,				4,						0,		210,	0,		0X00C0+NOHOLD,		M_TAP,
	TIMER,				2,						0,		210,	0,		0X0000,				M_TAP,
//3
	POS_3D_UP,			2,						120,	210,	REW,	0X0040+NOHOLD,		M_KNEAD_TAP,
	ROTATION,			3,						120,	210,	REW,	0X0040+NOHOLD,		M_KNEAD_TAP,
//4
	ROTATION,			1,						105,	0,		REW,	0X00C0+NOHOLD,		M_KNEAD,
	ROTATION,			1,						105,	0,		REW,	0X0000,				M_KNEAD,
	ROTATION,			1,						105,	0,		REW,	0X00C0+NOHOLD,		M_KNEAD,
//4A
	POS_3D_DOWN,		7,						120,	0,		REW,	0X0000,				M_KNEAD,
	POS_3D_DOWN,		7,						120,	0,		REW,	0X0000,				M_KNEAD,
//4B
	POS_3D,				FIT_POS0,				0,		0,		0,		0X0000,				0,
	
//////////////////////////////////////////Subroutine  #2  �@�� with ���� END////////////////////////////////////////////	
//73
	LED_COLOR,  		0, 						0, 		0, 		0, 		0,  			COLOUR346,

	SEATM_START,		0,						0,		0,		0,		0X0000,				SM_AUTO28, //Energy Boost seat sub 3
	POS,				POS_WAIST,				100,	0,		REW,	0X000C+NOHOLD,		M_KNEAD_ROLPART,
//73A
	POS_3D_UP,			8,						120,	0,		REW,	0X0010+NOHOLD,		M_KNEAD,
	ROTATION,			1,						120,	0,		REW,	0X0010+NOHOLD,		M_KNEAD,
//74
	POS_3D_UP,			15,						120,	0,		REW,	0X0010+NOHOLD,		M_KNEAD,
	TIMER,				6,						120,	0,		REW,	0X0010+NOHOLD,		M_KNEAD,
	TIMER,				4,						120,	0,		REW,	0X0010,				M_KNEAD,
	
//80
	POS_3D_UP,			20,						100,	190,	REW,	0X000C+NOHOLD,		M_KNEAD_TAP,
	ROTATION,			3,						100,	190,	REW,	0X000C+NOHOLD,		M_KNEAD_TAP,
//80'
	POS_3D_DOWN,		8,						100,	190,	REW,	0X000C+NOHOLD,		M_KNEAD_TAP,
	TIMER,				2,						100,	190,	REW,	0X000C,				M_KNEAD_TAP,
	POS_3D_DOWN,		6,						100,	190,	REW,	0x000C+NOHOLD,		M_KNEAD_TAP,
	TIMER,				1,						100,	190,	REW,	0x000C,				M_KNEAD_TAP,
	POS_3D_DOWN,		6,						100,	190,	REW,	0X000C+NOHOLD,		M_KNEAD_TAP,
//82
	POS,				POS_BACK,				120,	0,		REW,	0X00C0+NOHOLD,		M_KNEAD_ROLPART,
//90
	POS,				POS_SHOULDER,			125,	0,		REW,	0X0000,				M_KNEAD_ROLPART,
//93
	ROTATION,			3,						125,	0,		REW,	0X00C0+NOHOLD,		M_KNEAD,
//94
	POS_3D_DOWN,		5,						120,	0,	REW,		0X00C0+NOHOLD,		M_KNEAD,
	POS_3D_UP,			3,						120,	0,	REW,		0X00C0+NOHOLD,		M_KNEAD,
	POS_3D_DOWN,		3,						120,	0,	REW,		0X00C0+NOHOLD,		M_KNEAD,

////////////////////////////////////////////////////////////////////////////////////////////////////////
	POS_RET,	        0,					    0,		0,		0,		0X0000,			0,
////////////////////////////////////////////////////////////////////////////////////////////////////////
//97
	LED_COLOR,  		0, 						0, 		0, 		0, 		0,  				COLOUR347,
	SEATM_START,		0,						0,		0,		0,		0X0000,				SM_AUTO47, //Energy Boost seat sub 6
	POS,				POS_NECK,				125,	0,		REW,	0X000C+NOHOLD,		M_KNEAD_ROLPART,
//99
	POS_3D_DOWN,		18,						0,		0,		0,		0X00C0+NOHOLD,		0,
	POS,				POS_SHOULDER,			125,	0,		REW,	0X00C0+NOHOLD,		M_KNEAD_ROLPART,
//100
	POS_3D_UP,			2,						0,		0,		0,		0X00C0+NOHOLD,		0,
	TIMER,				6,						0,		160,	0,		0X00C0+NOHOLD,		M_TAP,
//100A
	POS,				POS_SHDBLADE_LOWER,		115,	130,	0,		0X000C+NOHOLD,		M_KNEAD_TAP_ROLPART,
//100'
	POS,				POS_WAIST_UPPER1,		120,	140,	0,		0X000C+NOHOLD,		M_KNEAD_TAP_ROLPART,
//100B
	POS,				POS_HIPS,				120,	150,	0,		0X0010+NOHOLD,		M_KNEAD_TAP_ROLPART,
//100c
	POS_3D,				FIT_POS0,				0,		0,		0,		0x000C+NOHOLD,		0,
//110B'
	POS,				POS_BACK_UPPER,			120,	150,	0,		0X0010+NOHOLD,		M_KNEAD_TAP_ROLPART,
//100C
	POS,				POS_SHDBLADE,			110,	130,	0,		0X0020+NOHOLD,		M_KNEAD_TAP,
//101	
	POS,				POS_SHOULDER,			110,	130,	0,		0X000C+NOHOLD,		M_KNEAD_TAP,
	ROTATION,			3,						125,	150,	0,		0X000C+NOHOLD,		M_KNEAD_TAP,
//102
	POS_3D_DOWN,		5,						125,	160,	0,		0X000C+NOHOLD,		M_KNEAD_TAP,
//103
	POS,				POS_SHDBLADE,			0,		170,	0,		0X00C0+NOHOLD,		M_TAP_ROLPART,
//104
	POS,				POS_BACK_UPPER,			0,		170,	0,		0X00C0+NOHOLD,		M_TAP_ROLPART,
//105C
	POS,				POS_WAIST,				125,	180,	REW,	0X00C0+NOHOLD,		M_KNEAD_TAP_ROLPART,
//105D
	POS_3D_UP,			7,						125,	200,	REW,	0X0010+NOHOLD,		M_KNEAD_TAP,
	ROTATION,			1,						125,	200,	REW,	0X0010+NOHOLD,		M_KNEAD_TAP,
//105D'
	POS_3D_UP,			5,						125,	200,	REW,	0X0010+NOHOLD,		M_KNEAD_TAP,
	ROTATION,			1,						125,	200,	REW,	0X0010+NOHOLD,		M_KNEAD_TAP,
	ROTATION,			2,						125,	200,	REW,	0X0020+NOHOLD,		M_KNEAD_TAP,
//105E
	POS_3D_UP,			4,						120,	0,		REW,	0X0000,				M_KNEAD,
	ROTATION,			2,						120,	0,		REW,	0X0010+NOHOLD,		M_KNEAD,
	ROTATION,			2,						120,	0,		REW,	0X0020+NOHOLD,		M_KNEAD,
//105F
	POS_3D_DOWN,		16,						120,	0,		REW,	0X0000,				M_KNEAD,
	POS_3D,				FIT_POS0,				0,		0,		0,		0X0000,				0,
////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
	COOLDOWN,	        60,				        0,		0,		0,		 0X0000,		    0,
////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//105G
	LED_COLOR,  		0, 						0, 		0, 		0, 		0,  				COLOUR348,

	SEATM_START,		0,						0,		0,		0,		0X0000,				SM_AUTO26, //Energy Boost seat sub 3
	WIDTH_SET,			0,						0,		0,		PN,		0X0000+NOHOLD,		0,	
//106
	POS,				POS_WAIST,				0,		220,	0,		0X000C+NOHOLD,		M_TAP_ROLPART,
//107
	ROTATION,			3,						125,	220,	REW,	0X000C+NOHOLD,		M_KNEAD_TAP,
//108
	POS,				POS_BACK,				0,		200,	0,		0X00C0+NOHOLD,		M_TAP_ROLPART,
//109	
	POS,				POS_SHOULDER,			0,		170,	0,		0X000C+NOHOLD,		M_TAP_ROLPART,
//110
	ROTATION,			3,						110,	140,	0,		0X0000,				M_KNEAD_TAP,

	0xff,				0xff,					0xff,	0xff,	0xff,	0xffff,				0xff
};

//------------------------------------------------------------------------------
// PG9: uDream Deep Tissue 23 January 2020
//------------------------------------------------------------------------------
T_OPERATION	osim_pg9_course_tbl[] =										
{
	TRACK_SELECT,		STRONG,					0,		0,		0,		0X0000,			0,	
	POS,				POS_SHOULDER,			0,		0,		PM,		0x0000,			M_ROLPART,
	ASI_START,			0,						0,		0,		0,		0X0000,			MODE_AUTO1,
//1
	SEATM_START,		0,						0,		0,		0,		0X0000,			SM_AUTO29,
	POS,				POS_SHOULDER_LOWER,		100,	0,		0,		0X000C+NOHOLD,	M_KNEAD_ROLPART,
//2
	ROTATION,			2,						120,	0,		0,		0x00C0+NOHOLD,	M_KNEAD,
//3
	POS,				POS_SHDBLADE_UPPER,		110,	0,		0,		0X00C0+NOHOLD,	M_KNEAD_ROLPART,
//4
	ROTATION,			1,						120,	0,		0,		0x00C0+NOHOLD,	M_KNEAD,
	ROTATION,			1,						120,	0,		0,		0x00C0,			M_KNEAD,
//5
//////////////////////////Subroutine  for Deep Comfort # 1�F���b����`�w��-Rev. 2	//////////////////////////////////
//////////////////////////////////////////// Start ///////////////////////////////////////////////////////////////////
//1
	WIDTH_SET,			0,						0,		0,		PN,		0x00C0+NOHOLD,	0,
	POS_3D_DOWN,		5,						0,		0,		0,		0x00C0+NOHOLD,	0,
	POS_3D_UP,			8,						0,		0,		0,		0x00C0+NOHOLD,	0,
//2
	TIMER,				5,						0,		220,	0,		0x00C0,			M_OSI,
//3
	POS,				POS_SHDBLADE,			0,		0,		0,		0X000C+NOHOLD,	M_ROLPART,
//4
	WIDTH_SET,			0,						0,		0,		PN,		0x00C0+NOHOLD,	0,
	POS_3D_DOWN,		5,						0,		0,		PN,		0x00C0+NOHOLD,	0,
	POS_3D_UP,			8,						0,		0,		PN,		0x00C0+NOHOLD,	0,
//5
	TIMER,				3,						0,		220,	0,		0x00C0+NOHOLD,	M_OSI,
	TIMER,				2,						0,		220,	0,		0x00C0,			M_OSI,
//6
	POS,				POS_SHDBLADE_LOWER,		0,		0,		0,		0X000C+NOHOLD,	M_ROLPART,
//7
	WIDTH_SET,			0,						0,		0,		PN,		0x00C0+NOHOLD,	0,
	POS_3D_DOWN,		5,						0,		0,		PN,		0x00C0+NOHOLD,	0,
	POS_3D_UP,			6,						0,		0,		PN,		0x00C0+NOHOLD,	0,
//8
	TIMER,				5,						0,		220,	0,		0x00C0+NOHOLD,	M_OSI,
//9
	POS,				POS_BACK_UPPER,			0,		0,		0,		0X000C+NOHOLD,	M_ROLPART,
//10
	WIDTH_SET,			0,						0,		0,		PN,		0x00C0+NOHOLD,	0,
	POS_3D_DOWN,		5,						0,		0,		PN,		0x00C0+NOHOLD,	0,
	POS_3D_UP,			6,						0,		0,		PN,		0x00C0+NOHOLD,	0,
//11
	TIMER,				5,						0,		220,	0,		0x00C0+NOHOLD,	M_OSI,
//12
	POS,				POS_BACK,				0,		0,		0,		0X000C+NOHOLD,	M_ROLPART,
//13
	WIDTH_SET,			0,						0,		0,		PN,		0x00C0+NOHOLD,	0,
	POS_3D_DOWN,		5,						0,		0,		PN,		0x00C0+NOHOLD,	0,
	POS_3D_UP,			8,						0,		0,		PN,		0x00C0+NOHOLD,	0,
//14
	TIMER,				3,						0,		220,	0,		0x00C0+NOHOLD,	M_OSI,
	TIMER,				2,						0,		220,	0,		0x00C0,			M_OSI,	
		
//////////////////////////// End of Subroutine ////////////////////////////////////////////
//6
	POS,				POS_BACK_UPPER,			110,	0,		0,		0x00C0+NOHOLD,	M_KNEAD_ROLPART,
//7
	POS_3D_UP,			7,						120,	0,		0,		0X000C+NOHOLD,	M_KNEAD,
	ROTATION,			3,						120,	0,		0,		0X000C+NOHOLD,	M_KNEAD,
//8
	POS_3D_DOWN,		7,						100,	0,		0,		0x00C0+NOHOLD,	M_KNEAD,
	POS_3D_UP,			7,						100,	0,		0,		0x00C0+NOHOLD,	M_KNEAD,
	POS_3D_DOWN,		7,						100,	0,		0,		0x00C0+NOHOLD,	M_KNEAD,
//9
	POS,				POS_BACK,				110,	0,		0,		0x00C0+NOHOLD,	M_KNEAD_ROLPART,
//10
	SEATM_START,		0,						0,		0,		0,		0X0000,			SM_AUTO30,
	POS_3D_UP,			8,						120,	0,		0,		0X000C+NOHOLD,	M_KNEAD,
	ROTATION,			3,						120,	0,		0,		0X000C+NOHOLD,	M_KNEAD,
//11
	POS_3D_DOWN,		8,						100,	0,		0,		0x0020+NOHOLD,	M_KNEAD,
	POS_3D_UP,			8,						100,	0,		0,		0x0020+NOHOLD,	M_KNEAD,
	POS_3D_DOWN,		8,						100,	0,		0,		0x0020+NOHOLD,	M_KNEAD,
//12
	POS,				POS_WAIST_UPPER1,		110,	0,		0,		0x00C0+NOHOLD,	M_KNEAD_ROLPART,
//12A
	POS_3D_UP,			12,						120,	0,		0,		0X00C0,			M_KNEAD,
	TIMER,				1,						120,	0,		0,		0x00C0,			M_KNEAD,
//13
	POS_3D_UP,			6,						0,		0,		0,		0X0000,			0,
	ROTATION,			2,						120,	0,		0,		0x00C0+NOHOLD,	M_KNEAD,
	ROTATION,			1,						120,	0,		0,		0x00C0,	M_KNEAD,	
//14
	POS_3D_DOWN,		8,						100,	0,		0,		0x0010+NOHOLD,	M_KNEAD,
	POS_3D_UP,			8,						100,	0,		0,		0x0010+NOHOLD,	M_KNEAD,
	POS_3D_DOWN,		8,						100,	0,		0,		0x0010+NOHOLD,	M_KNEAD,
//15
	POS,				POS_WAIST,				120,	0,		REW,	0X000C+NOHOLD,	M_KNEAD_ROLPART,
//15A
	POS_3D_UP,			12,						120,	0,		REW,	0X00C0+NOHOLD,	M_KNEAD,
	TIMER,				1,						120,	0,		REW,	0x00C0+NOHOLD,	M_KNEAD,
//16
	ROTATION,			3,						120,	0,		REW,	0x00C0+NOHOLD,	M_KNEAD,
//17
	POS_3D_DOWN,		8,						120,	120,	REW,	0x0020+NOHOLD,	M_KNEAD_TAP,
	POS_3D_UP,			8,						120,	120,	REW,	0x0020+NOHOLD,	M_KNEAD_TAP,
	POS_3D_DOWN,		8,						120,	120,	REW,	0x0020+NOHOLD,	M_KNEAD_TAP,
//18
	POS,				POS_HIPS,				100,	150,	PN,		0x00C0+NOHOLD,	M_KNEAD_TAP_ROLPART,
//19
	TIMER,				5,						0,		170,	0,		0x00C0+NOHOLD,	M_TAP,
//20
	POS_3D_DOWN,		14,						120,	150,	REW,	0x0010+NOHOLD,	M_KNEAD_TAP,
	POS_3D_UP,			14,						120,	150,	REW,	0x0010+NOHOLD,	M_KNEAD_TAP,
//21
	SEATM_START,		0,						0,		0,		0,		0X0000,			SM_AUTO31,
	POS,				POS_SHDBLADE_LOWER,		120,	0,		REW,	0x00C0+NOHOLD,	M_KNEAD_ROLPART,
//22
	POS,				POS_SHOULDER_LOWER2,	120,	0,		REW,	0X000C+NOHOLD,	M_KNEAD_ROLPART,
//23
	ROTATION,			2,						110,	0,		REW,	0x00C0+NOHOLD,	M_KNEAD,
//24
///////////////////// Subroutine for Deep Comfort #2�F����2�`�w��-Rev.  4 ////////////////////
////////////////////////////////////////// Start ////////////////////////////////////////////
//1
	WIDTH_SET,			0,						0,		0,		PN,		0x00C0+NOHOLD,	0,
	POS_3D_UP,			7,						0,		0,		PN,		0x00C0+NOHOLD,	0,
	TIMER,				2,						0,		0,		PN,		0x00C0+NOHOLD,	0,
//2
	WIDTH_SET,			0,						0,		0,		PW,		0X000C+NOHOLD,	0,
	TIMER,				2,						0,		0,		0,		0X000C+NOHOLD,	0,
//3
	POS_3D_DOWN,		4,						0,		0,		0,		0x00C0+NOHOLD,	0,
	WIDTH_SET,			0,						0,		0,		PN,		0x00C0+NOHOLD,	0,
//3a
	TIMER,				4,						0,		180,	0,		0X00C0+NOHOLD,	M_OSI,
//4
	POS_3D_UP,			5,						0,		0,		0,		0x00C0+NOHOLD,	0,
	TIMER,				2,						0,		0,		0,		0x00C0+NOHOLD,	0,
//5
	WIDTH_SET,			0,						0,		0,		PW,		0X000C+NOHOLD,	0,
	TIMER,				2,						0,		0,		0,		0X000C+NOHOLD,	0,
//6
	POS_3D_DOWN,		10,						0,		0,		0,		0x000C+NOHOLD,	0,
	WIDTH_SET,			0,						0,		0,		PN,		0x000C+NOHOLD,	0,
//7
	POS,				POS_BACK_UPPER,			0,		220,	0,		0x00C0+NOHOLD,	M_OSI_ROLPART,
//8
	WIDTH_SET,			0,						0,		0,		PN,		0x00C0,			0,
	POS_3D_UP,			7,						0,		0,		PN,		0x00C0,			0,
	TIMER,				2,						0,		0,		PN,		0x00C0,			0,
//9
	WIDTH_SET,			0,						0,		0,		PW,		0X000C+NOHOLD,	0,
	TIMER,				2,						0,		0,		PW,		0X000C+NOHOLD,	0,
//10
	POS_3D_DOWN,		4,						0,		0,		0,		0x00C0+NOHOLD,	0,
	WIDTH_SET,			0,						0,		0,		PN,		0x00C0+NOHOLD,	0,
//10a
	TIMER,				4,						0,		180,	0,		0X00C0+NOHOLD,	M_OSI,
//11
	POS_3D_UP,			7,						0,		0,		0,		0x00C0+NOHOLD,	0,
	TIMER,				2,						0,		0,		0,		0x00C0+NOHOLD,	0,
//12
	WIDTH_SET,			0,						0,		0,		PW,		0X000C+NOHOLD,	0,
	TIMER,				2,						0,		0,		PW,		0X000C+NOHOLD,	0,
//13
	POS_3D_DOWN,		10,						0,		0,		0,		0X000C+NOHOLD,	0,
	WIDTH_SET,			0,						0,		0,		PN,		0X000C+NOHOLD,	0,
/////////////////////////////////// End oF Subroutine ///////////////////////////////
//24A
   POS,         		POS_SHDBLADE_UPPER,     0,      170,      0,    0X000C+NOHOLD,  M_TAP_ROLPART,	
//25
	POS,				POS_SHOULDER_UPPER,		0,		150,	0,		0x00C0+NOHOLD,	M_TAP_ROLPART,
//26
	POS_3D_UP,			7,						0,		170,	0,		0x00D0+NOHOLD,	M_OSI,
	TIMER,				6,						0,		170,	0,		0x00D0+NOHOLD,	M_OSI,
	TIMER,				2,						0,		170,	0,		0x00D0,			M_OSI,	
//28
	ROTATION,			3,						120,	0,		PN,		0x00D0+NOHOLD,	M_KNEAD,
	TIMER,				2,						0,		0,		0,		0x00D0,			0,
//29
	POS,				POS_SHOULDER,			0,		150,	0,		0X000C+NOHOLD,	M_OSI_ROLPART,
//30
	POS_3D_UP,			2,						0,		180,	0,		0x00E0+NOHOLD,	M_OSI,
	TIMER,				6,						0,		180,	0,		0x00E0+NOHOLD,	M_OSI,
	TIMER,				2,						0,		180,	0,		0x00E0,			M_OSI,	
//32
	ROTATION,			3,						120,	0,		PN,		0x00E0+NOHOLD,	M_KNEAD,
	TIMER,				2,						0,		0,		0,		0x00E0,			0,
//32A
   POS,         		POS_SHDBLADE_UPPER,     0,       170,      0,    0X000C+NOHOLD, M_TAP_ROLPART,	
	POS,				POS_SHOULDER_UPPER,		0,		150,	0,		0x00C0+NOHOLD,	M_TAP_ROLPART,

	POS_3D_UP,			7,						0,		170,	0,		0x00D0+NOHOLD,	M_OSI,
	TIMER,				8,						0,		170,	0,		0x00D0+NOHOLD,	M_OSI,

	ROTATION,			1,						120,	0,		0,		0x00D0+NOHOLD,	M_KNEAD,
	ROTATION,			2,						120,	0,		0,		0x00D0,			M_KNEAD,	
	WIDTH_SET,			0,						120,	0,		PN,		0x00D0+NOHOLD,	M_KNEAD,
	TIMER,				2,						0,		0,		0,		0x00D0+NOHOLD,	0,

	POS_3D_UP,			2,						0,		180,	0,		0x00E0+NOHOLD,	M_OSI,
	TIMER,				8,						0,		180,	0,		0x00E0+NOHOLD,	M_OSI,

	ROTATION,			1,						120,	0,		0,		0x00E0+NOHOLD,	M_KNEAD,
	ROTATION,			2,						120,	0,		0,		0x00E0,			M_KNEAD,	
	WIDTH_SET,			0,						120,	0,		PN,		0x00E0+NOHOLD,	M_KNEAD,
	TIMER,				2,						0,		0,		0,		0x00E0+NOHOLD,	0,
//33
	POS,				POS_SHOULDER_LOWER,		0,		150,	0,		0X000C+NOHOLD,	M_OSI_ROLPART,
//34
	POS_3D_UP,			9,						0,		190,	0,		0x00D0+NOHOLD,	M_OSI,
	TIMER,				8,						0,		190,	0,		0x00D0+NOHOLD,	M_OSI,
//36
	ROTATION,			3,						120,	0,		0,		0x00D0,			M_KNEAD,
	WIDTH_SET,			0,						120,	0,		PN,		0x00D0,			M_KNEAD,
//37
	POS,				POS_SHOULDER_LOWER2,	0,		150,	0,		0X000C+NOHOLD,	M_OSI_ROLPART,
//38
	POS_3D_UP,			10,						0,		200,	0,		0x00E0+NOHOLD,	M_OSI,
	TIMER,				8,						0,		200,	0,		0x00E0+NOHOLD,	M_OSI,
//40
	ROTATION,			3,						120,	0,		0,		0x00E0,			M_KNEAD,
	WIDTH_SET,			0,						120,	0,		PN,		0x00E0,			M_KNEAD,	
//41
	SEATM_START,		0,						0,		0,		0,		0X0000,			SM_AUTO32,
	POS,				POS_SHDBLADE_LOWER,		0,		150,	0,		0X000C+NOHOLD,	M_OSI_ROLPART,
//42
	POS_3D_UP,			7,						0,		210,	0,		0x00C0+NOHOLD,	M_OSI,
	TIMER,				8,						0,		210,	0,		0x00C0+NOHOLD,	M_OSI,
//44
	ROTATION,			1,						120,	0,		REW,	0x00C0+NOHOLD,	M_KNEAD,
	ROTATION,			2,						120,	0,		REW,	0x00C0,			M_KNEAD,
	WIDTH_SET,			0,						0,		0,		PN,		0x00C0+NOHOLD,	0,
//45
	POS,				POS_SHOULDER_LOWER,		120,	0,		REW,	0X000C+NOHOLD,	M_KNEAD_ROLPART,
//47
	POS_3D_UP,			7,						0,		0,		0,		0x000C+NOHOLD,	0,
	TIMER,				2,						0,		0,		0,		0X000C+NOHOLD,	0,
	THREED_CTRL_DISABLE,0,						0,		0,		0,		0x0000,			0,
//48
	POS,				POS_NECK_UPPER,			0,		0,		0,		0x000C+NOHOLD,	M_ROLPART,
//48A
	POS,				POS_NECK_UPPER1,		0,		0,		0,		0x0090+NOHOLD,	M_ROLPART,
//49
	POS,				POS_SHOULDER_LOWER,		0,		0,		0,		0x0090,			M_ROLPART,
//50
	POS,				POS_NECK_UPPER,			0,		0,		0,		0x000C+NOHOLD,	M_ROLPART,
//50A
	POS,				POS_NECK_UPPER1,		0,		0,		0,		0x0090+NOHOLD,	M_ROLPART,
//51
	POS,				POS_SHOULDER_LOWER,		0,		0,		0,		0x0090,			M_ROLPART,
//52
	ROTATION,			1,						120,	0,		REW,	0x00C0+NOHOLD,	M_KNEAD,
	ROTATION,			1,						120,	0,		REW,	0x0000,			M_KNEAD,
	WIDTH_SET,			0,						0,		0,		PN,		0x0000,			0,
//53
	POS,				POS_NECK_UPPER,			0,		0,		0,		0x0060+NOHOLD,	M_ROLPART,
//53A
	POS,				POS_NECK_UPPER1,		0,		0,		0,		0x0060,			M_ROLPART,
//54
	SEATM_START,		0,						0,		0,		0,		0X0000,			SM_AUTO33,
	POS,				POS_SHOULDER_LOWER,		0,		0,		0,		0x0060,			M_ROLPART,
//55
	POS,				POS_NECK_UPPER,			0,		0,		0,		0x0060+NOHOLD,	M_ROLPART,
//55A
	POS,				POS_NECK_UPPER1,		0,		0,		0,		0x0060,			M_ROLPART,	
//56
	POS,				POS_SHOULDER_LOWER,		0,		0,		0,		0x0060,	M_ROLPART,
	THREED_CTRL_ENABLE,	0,						0,		0,		0,		0x0000,			0,
//57
	POS_3D_UP,			5,						0,		0,		0,		0X00C0+NOHOLD,	0,
	ROTATION,			2,						120,	0,		REW,	0x00C0+NOHOLD,	M_KNEAD,
//58
	POS,				POS_SHOULDER,			120,	0,		REW,	0X000C+NOHOLD,	M_KNEAD_ROLPART,
//////////////////////////////////////////////////////////////////////////////////////////////////////////
	POS_RET,			0,						0,		0,		0,		0x0000,			0,
//////////////////////////////////////////////////////////////////////////////////////////////////////////
//58A
	POS,				POS_SHDBLADE,			110,	0,		REW,	0X000C+NOHOLD,	M_KNEAD_ROLPART,
//59
	SEATM_START,		0,						0,		0,		0,		0X0000,			SM_AUTO34,
	POS,				POS_BACK,				110,	0,		0,		0x00C0+NOHOLD,	M_KNEAD_ROLPART,
//60
	POS_3D_UP,			12,						120,	0,		0,		0x0090+NOHOLD,	M_KNEAD,
	ROTATION,			2,						120,	0,		0,		0x0090+NOHOLD,	M_KNEAD,
	ROTATION,			1,						120,	0,		0,		0x0090,			M_KNEAD,
//61
	POS,				POS_WAIST,				120,	0,		REW,	0x00C0+NOHOLD,	M_KNEAD_ROLPART,
//61A
	POS_3D_DOWN,		12,						120,	0,		REW,	0x0060+NOHOLD,	M_KNEAD,
	TIMER,				1,						120,	0,		REW,	0x0060+NOHOLD,	M_KNEAD,
//62
	POS_3D_UP,			4,						120,	0,		0,		0X0060+NOHOLD,	M_KNEAD,
	ROTATION,			3,						120,	0,		REW,	0x0060+NOHOLD,	M_KNEAD,
	WIDTH_SET,			0,						120,	0,		PN,		0x0060+NOHOLD,	M_KNEAD,
//62A
	POS_3D_DOWN,		8,						120,	0,		REW,	0x0000,			M_KNEAD,
	TIMER,				1,						120,	0,		REW,	0x0000,			M_KNEAD,
	POS_3D_DOWN,		8,						120,	0,		REW,	0x0000,			M_KNEAD,
//63
//////////////////////////////Subroutine for Deep Comfort #3�F���i����P�j-Rev. 2 /////////////////////////
////////////////////////////////////////////////Start //////////////////////////////////////
//1
	POS_3D_UP,			17,						100,	0,		REW,	0X000C+NOHOLD,	M_KNEAD,
	POS_3D_DOWN,		17,						100,	0,		REW,	0X000C+NOHOLD,	M_KNEAD,
	WIDTH_SET,			0,						0,		0,		PN,		0X000C+NOHOLD,	0,
//2
	POS_3D_UP,			17,						0,		170,	0,		0x00C0+NOHOLD,	M_OSI,
	POS_3D_DOWN,		17,						0,		170,	0,		0x00C0+NOHOLD,	M_OSI,
//2a
	TIMER,				1,						0,		170,	0,		0X0000,			M_OSI,
//3
	POS_3D_UP,			17,						0,		0,		0,		0X00C0+NOHOLD,	0,
	POS_3D_DOWN,		17,						0,		0,		0,		0x00C0+NOHOLD,	0,
//3a
	TIMER,				1,						0,		0,		0,		0X0000,			0,
//4
	POS_3D_UP,			17,						0,		220,	0,		0x00C0+NOHOLD,	M_OSI,
	POS_3D_DOWN,		17,						0,		220,	0,		0x00C0,			M_OSI,
//5
	POS_3D_UP,			17,						120,	0,		REW,	0X000C+NOHOLD,	M_KNEAD,
	POS_3D_DOWN,		17,						120,	0,		REW,	0X000C+NOHOLD,	M_KNEAD,
	WIDTH_SET,			0,						0,		0,		PN,		0x0000,			0,
/////////////////////////////////////// End Of Subroutine /////////////////////////////////////////////
//64
	POS,				POS_WAIST_UPPER1,		100,	0,		0,		0x00C0+NOHOLD,	M_KNEAD_ROLPART,
//64A
	POS_3D_DOWN,		12,						120,	0,		0,		0x0000,			M_KNEAD,
	TIMER,				1,						120,	0,		0,		0x0000,			M_KNEAD,
	POS_3D_UP,			8,						120,	0,		0,		0x0000,			M_KNEAD,
	TIMER,				1,						120,	0,		0,		0x0000,			M_KNEAD,

//65
	POS_3D_UP,			24,						120,	0,		0,		0X0090+NOHOLD,	M_KNEAD,
	ROTATION,			3,						120,	0,		0,		0x0090+NOHOLD,	M_KNEAD,
//66
	POS,				POS_WAIST,				100,	0,		REW,	0x00C0+NOHOLD,	M_KNEAD_ROLPART,
//66A
	POS_3D_UP,			12,						120,	0,		REW,	0x0060+NOHOLD,	M_KNEAD,
	TIMER,				1,						120,	0,		REW,	0x0060+NOHOLD,	M_KNEAD,
//67
	POS_3D_UP,			16,						0,		0,		0,		0X0000,			0,
	ROTATION,			3,						120,	0,		REW,	0x0060+NOHOLD,	M_KNEAD,
//67A
	POS_3D_UP,			8,						120,	0,		REW,	0x0000,			M_KNEAD,
	TIMER,				1,						120,	0,		REW,		0x0000,		M_KNEAD,
//68
	POS,				POS_SHOULDER_LOWER,		120,	0,		REW,	0X000C+NOHOLD,	M_KNEAD_ROLPART,
	WIDTH_SET,			0,						0,		0,		PN,		0X000C+NOHOLD,	0,


/////////////////////////////////////////////////////////////////////////////////////////////
	COOLDOWN,			60,						0,		0,		0,		0x0000,			0,
////////////////////////////////////////////////////////////////////////////////////////////

//70
	POS,				POS_NECK_UPPER,			100,	0,		REW,	0X000C+NOHOLD,	M_KNEAD_ROLPART,
//71
	POS,				POS_SHDBLADE_UPPER,		100,	0,		REW,	0x00C0+NOHOLD,	M_KNEAD_ROLPART,
//72
	POS,				POS_WAIST,				100,	0,		REW,	0X000C+NOHOLD,	M_KNEAD_ROLPART,

	0xff,		0xff,				0xff,0xff,0xff,		0xffff,			0xff,
};
