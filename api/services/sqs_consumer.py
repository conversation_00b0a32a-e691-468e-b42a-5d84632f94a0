import asyncio
import json
import logging
import signal
import threading
from typing import Dict, Any, Optional, List
from contextlib import asynccontextmanager
from datetime import datetime, timezone

from sqlalchemy.orm import Session
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

from config.settings import settings
from services.sqs_service import sqs_service
from services.segmentation.orchestrator import SegmentationOrchestrator
from services.segmentation.product_orchestrator import ProductSegmentationOrchestrator
from models.program_models import Program, SegmentationJob, SegmentationJobStatus, ProductSegmentationJob

logger = logging.getLogger(__name__)


class SQSConsumer:
    """Consumer for processing segmentation jobs from SQS."""
    
    def __init__(self):
        self.running = False
        self.consumer_thread = None
        self.shutdown_event = threading.Event()

        # Create separate database engine for the consumer
        database_url = f"postgresql://{settings.DATABASE_USERNAME}:{settings.DATABASE_PASSWORD}@{settings.DATABASE_ENDPOINT}/{settings.DATABASE_NAME}"
        self.engine = create_engine(database_url)
        self.SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=self.engine)

        # Concurrency settings for multi-program processing
        self.max_concurrent_programs = getattr(settings, 'MAX_CONCURRENT_PROGRAMS', 3)
        self.max_messages_per_batch = getattr(settings, 'MAX_MESSAGES_PER_BATCH', 5)

        logger.info(f"SQS Consumer initialized with MAX_CONCURRENT_PROGRAMS={self.max_concurrent_programs}, MAX_MESSAGES_PER_BATCH={self.max_messages_per_batch}")
    
    def start(self):
        """Start the SQS consumer in a separate thread."""
        if self.running:
            logger.warning("SQS consumer is already running")
            return
            
        logger.info("Starting SQS consumer...")
        self.running = True
        self.shutdown_event.clear()
        self.consumer_thread = threading.Thread(target=self._run_consumer, daemon=True)
        self.consumer_thread.start()
        logger.info("SQS consumer started")
    
    def stop(self):
        """Stop the SQS consumer gracefully."""
        if not self.running:
            logger.warning("SQS consumer is not running")
            return
            
        logger.info("Stopping SQS consumer...")
        self.running = False
        self.shutdown_event.set()
        
        if self.consumer_thread and self.consumer_thread.is_alive():
            self.consumer_thread.join(timeout=30)
            if self.consumer_thread.is_alive():
                logger.warning("SQS consumer thread did not stop gracefully")
        
        logger.info("SQS consumer stopped")
    
    def _run_consumer(self):
        """Main consumer loop running in separate thread with persistent event loop."""
        logger.info("SQS consumer loop started")
        logger.info(f"Queue URL: {sqs_service.queue_url}")

        # Create a new event loop for this thread
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

        try:
            while self.running and not self.shutdown_event.is_set():
                try:
                    # Receive multiple messages from SQS for concurrent processing
                    messages = sqs_service.receive_messages(max_messages=self.max_messages_per_batch, wait_time=20)

                    if not messages:
                        continue

                    logger.info(f"Received {len(messages)} message(s) from SQS")

                    # Process messages concurrently with semaphore limiting
                    if messages:
                        loop.run_until_complete(self._process_messages_concurrent(messages, loop))

                except Exception as e:
                    logger.error(f"Error in SQS consumer loop: {e}", exc_info=True)
                    if self.running:
                        # Brief pause before retrying
                        self.shutdown_event.wait(5)
        except Exception as e:
            logger.error(f"Unexpected error in SQS consumer loop: {e}", exc_info=True)
        finally:
            # Clean up the event loop
            try:
                # Cancel all pending tasks
                pending = asyncio.all_tasks(loop)
                for task in pending:
                    task.cancel()

                # Wait for tasks to complete cancellation
                if pending:
                    loop.run_until_complete(asyncio.gather(*pending, return_exceptions=True))

                # Close the loop
                loop.close()
                logger.info("Event loop closed cleanly")
            except Exception as e:
                logger.error(f"Error closing event loop: {e}")

        logger.info("SQS consumer loop ended")

    async def _process_messages_concurrent(self, messages: List[Dict[str, Any]], loop: asyncio.AbstractEventLoop):
        """Process multiple SQS messages concurrently with semaphore limiting."""
        semaphore = asyncio.Semaphore(self.max_concurrent_programs)

        async def process_single_message(message):
            async with semaphore:
                if not self.running or self.shutdown_event.is_set():
                    return
                await self._process_message_async(message)

        # Create tasks for all messages
        tasks = [process_single_message(message) for message in messages]

        # Wait for all messages to complete
        await asyncio.gather(*tasks, return_exceptions=True)

    async def _process_message_async(self, message: Dict[str, Any]):
        """Process a single SQS message asynchronously."""
        receipt_handle = message.get('ReceiptHandle')
        message_id = message.get('MessageId')

        try:
            # Parse message body
            body = json.loads(message.get('Body', '{}'))
            job_type = body.get('job_type')
            job_id = body.get('job_id')

            logger.info(f"Processing SQS message: {message_id}, job_type={job_type}, job_id={job_id}")

            if job_type == 'segmentation':
                # Handle individual program segmentation
                program_id = body.get('program_id')
                product_job_id = body.get('product_job_id')  # Optional parent job

                if not program_id or not job_id:
                    logger.error(f"Missing required fields in segmentation message: program_id={program_id}, job_id={job_id}")
                    sqs_service.delete_message(receipt_handle)
                    return

                # Check if job was cancelled before processing
                with self.SessionLocal() as db:
                    job = db.query(SegmentationJob).filter(SegmentationJob.id == job_id).first()
                    if not job:
                        logger.warning(f"Segmentation job {job_id} not found in database")
                        sqs_service.delete_message(receipt_handle)
                        return

                    if job.status == SegmentationJobStatus.CANCELLED:
                        logger.info(f"Segmentation job {job_id} was cancelled, skipping processing")
                        sqs_service.delete_message(receipt_handle)
                        return

                # Process the segmentation job
                await self._process_segmentation_job(program_id, job_id, receipt_handle, product_job_id)

            elif job_type == 'product_segmentation':
                # Handle product segmentation
                product_id = body.get('product_id')

                if not product_id or not job_id:
                    logger.error(f"Missing required fields in product segmentation message: product_id={product_id}, job_id={job_id}")
                    sqs_service.delete_message(receipt_handle)
                    return

                # Check if job was cancelled before processing
                with self.SessionLocal() as db:
                    job = db.query(ProductSegmentationJob).filter(ProductSegmentationJob.id == job_id).first()
                    if not job:
                        logger.warning(f"Product segmentation job {job_id} not found in database")
                        sqs_service.delete_message(receipt_handle)
                        return

                    if job.status == SegmentationJobStatus.CANCELLED:
                        logger.info(f"Product segmentation job {job_id} was cancelled, skipping processing")
                        sqs_service.delete_message(receipt_handle)
                        return

                # Process the product segmentation job
                await self._process_product_segmentation_job(product_id, job_id, receipt_handle)

            else:
                logger.error(f"Unknown job type: {job_type}")
                sqs_service.delete_message(receipt_handle)

        except Exception as e:
            logger.error(f"Error processing message {message_id}: {e}", exc_info=True)
            # Don't delete message on error - let it retry

    def _process_message_sync(self, message: Dict[str, Any], loop: asyncio.AbstractEventLoop):
        """Process a single SQS message using the provided event loop."""
        receipt_handle = message.get('ReceiptHandle')
        message_id = message.get('MessageId')

        try:
            # Parse message body
            body = json.loads(message.get('Body', '{}'))
            job_type = body.get('job_type')
            job_id = body.get('job_id')

            logger.info(f"Processing SQS message: {message_id}, job_type={job_type}, job_id={job_id}")

            if job_type == 'segmentation':
                # Handle individual program segmentation
                program_id = body.get('program_id')
                product_job_id = body.get('product_job_id')  # Optional parent job

                if not program_id or not job_id:
                    logger.error(f"Missing required fields in segmentation message: program_id={program_id}, job_id={job_id}")
                    sqs_service.delete_message(receipt_handle)
                    return

                # Check if job was cancelled before processing
                with self.SessionLocal() as db:
                    job = db.query(SegmentationJob).filter(SegmentationJob.id == job_id).first()
                    if not job:
                        logger.warning(f"Segmentation job {job_id} not found in database")
                        sqs_service.delete_message(receipt_handle)
                        return

                    if job.status == SegmentationJobStatus.CANCELLED:
                        logger.info(f"Segmentation job {job_id} was cancelled, skipping processing")
                        sqs_service.delete_message(receipt_handle)
                        return

                # Process the segmentation job using the persistent event loop
                loop.run_until_complete(self._process_segmentation_job(program_id, job_id, receipt_handle, product_job_id))

            elif job_type == 'product_segmentation':
                # Handle product-level segmentation
                product_id = body.get('product_id')

                if not product_id or not job_id:
                    logger.error(f"Missing required fields in product segmentation message: product_id={product_id}, job_id={job_id}")
                    sqs_service.delete_message(receipt_handle)
                    return

                # Check if job was cancelled before processing
                with self.SessionLocal() as db:
                    job = db.query(ProductSegmentationJob).filter(ProductSegmentationJob.id == job_id).first()
                    if not job:
                        logger.warning(f"Product segmentation job {job_id} not found in database")
                        sqs_service.delete_message(receipt_handle)
                        return

                    if job.status == SegmentationJobStatus.CANCELLED:
                        logger.info(f"Product segmentation job {job_id} was cancelled, skipping processing")
                        sqs_service.delete_message(receipt_handle)
                        return

                # Process the product segmentation job using the persistent event loop
                loop.run_until_complete(self._process_product_segmentation_job(product_id, job_id, receipt_handle))

            else:
                logger.warning(f"Unknown job type: {job_type}")
                sqs_service.delete_message(receipt_handle)
                return

        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse message body: {e}")
            sqs_service.delete_message(receipt_handle)
        except Exception as e:
            logger.error(f"Error processing SQS message {message_id}: {e}", exc_info=True)
            # Don't delete message on error - let it retry


    
    async def _process_segmentation_job(self, program_id: int, job_id: int, receipt_handle: str, product_job_id: Optional[int] = None):
        """Process a segmentation job asynchronously."""
        db = None
        try:
            # Create database session
            db = self.SessionLocal()

            # Get the program
            program = db.query(Program).filter(Program.id == program_id).first()
            if not program:
                logger.error(f"Program {program_id} not found")
                # Update job as failed
                job = db.query(SegmentationJob).filter(SegmentationJob.id == job_id).first()
                if job:
                    job.status = SegmentationJobStatus.FAILED
                    job.error_message = f"Program {program_id} not found"
                    db.commit()
                sqs_service.delete_message(receipt_handle)
                return

            # Check job status again (in case it was cancelled during processing)
            job = db.query(SegmentationJob).filter(SegmentationJob.id == job_id).first()
            if not job or job.status == SegmentationJobStatus.CANCELLED:
                logger.info(f"Job {job_id} was cancelled during processing")
                sqs_service.delete_message(receipt_handle)
                return

            # Use the segmentation orchestrator
            orchestrator = SegmentationOrchestrator(db, enable_embedding=True, job_id=job_id)

            await orchestrator.process_program(program)

            # Update parent product job progress if this is part of a product segmentation
            if product_job_id:
                self._update_product_job_progress(db, product_job_id)

            # Delete message after successful processing
            sqs_service.delete_message(receipt_handle)
            logger.info(f"Successfully processed segmentation job: program_id={program_id}, job_id={job_id}")
            
        except Exception as e:
            logger.error(f"Error in segmentation job processing: {e}", exc_info=True)
            
            # Update job status to failed
            if db:
                try:
                    job = db.query(SegmentationJob).filter(SegmentationJob.id == job_id).first()
                    if job and job.status != SegmentationJobStatus.CANCELLED:
                        job.status = SegmentationJobStatus.FAILED
                        job.error_message = str(e)
                        db.commit()

                        # Update parent product job progress if this is part of a product segmentation
                        if product_job_id:
                            self._update_product_job_progress(db, product_job_id)

                except Exception as db_error:
                    logger.error(f"Failed to update job status: {db_error}")

            # Don't delete message on error - let it retry
            
        finally:
            if db:
                db.close()

    async def _process_product_segmentation_job(self, product_id: int, job_id: int, receipt_handle: str):
        """Process a product segmentation job asynchronously."""
        db = None
        try:
            # Create database session
            db = self.SessionLocal()

            # Check job status again (in case it was cancelled during processing)
            job = db.query(ProductSegmentationJob).filter(ProductSegmentationJob.id == job_id).first()
            if not job:
                logger.warning(f"Product segmentation job {job_id} not found in database")
                sqs_service.delete_message(receipt_handle)
                return

            if job.status == SegmentationJobStatus.CANCELLED:
                logger.info(f"Product segmentation job {job_id} was cancelled during processing")
                sqs_service.delete_message(receipt_handle)
                return

            # Create product orchestrator and process
            orchestrator = ProductSegmentationOrchestrator(db, job_id=job_id)
            await orchestrator.process_product(product_id)

            # Delete message after successful processing
            sqs_service.delete_message(receipt_handle)
            logger.info(f"Successfully processed product segmentation job: product_id={product_id}, job_id={job_id}")

        except Exception as e:
            logger.error(f"Error in product segmentation job processing: {e}", exc_info=True)

            # Update job status to failed
            if db:
                try:
                    job = db.query(ProductSegmentationJob).filter(ProductSegmentationJob.id == job_id).first()
                    if job and job.status != SegmentationJobStatus.CANCELLED:
                        job.status = SegmentationJobStatus.FAILED
                        job.error_message = str(e)
                        db.commit()
                except Exception as db_error:
                    logger.error(f"Failed to update product job status: {db_error}")

            # Don't delete message on error - let it retry

        finally:
            if db:
                db.close()

    def _update_product_job_progress(self, db: Session, product_job_id: int):
        """Update the progress of a product segmentation job based on completed program jobs."""
        try:
            # Get the product job
            product_job = db.query(ProductSegmentationJob).filter(
                ProductSegmentationJob.id == product_job_id
            ).first()

            if not product_job:
                logger.warning(f"Product job {product_job_id} not found")
                return

            # Count completed and failed program jobs
            completed_jobs = db.query(SegmentationJob).filter(
                SegmentationJob.product_job_id == product_job_id,
                SegmentationJob.status == SegmentationJobStatus.COMPLETED
            ).count()

            failed_jobs = db.query(SegmentationJob).filter(
                SegmentationJob.product_job_id == product_job_id,
                SegmentationJob.status == SegmentationJobStatus.FAILED
            ).count()

            total_jobs = product_job.total_programs

            # Update product job progress
            product_job.completed_programs = completed_jobs
            product_job.failed_programs = failed_jobs

            if total_jobs > 0:
                # Calculate progress percentage (completed + failed = processed)
                processed_jobs = completed_jobs + failed_jobs
                progress_percentage = min(100, int((processed_jobs / total_jobs) * 100))
                product_job.progress_percentage = progress_percentage

                # Update current step
                if processed_jobs < total_jobs:
                    product_job.current_step = f"Processing programs: {processed_jobs}/{total_jobs} completed"
                else:
                    # All jobs processed
                    if failed_jobs == 0:
                        product_job.status = SegmentationJobStatus.COMPLETED
                        product_job.current_step = f"All {completed_jobs} programs segmented successfully"
                        product_job.completed_at = datetime.now(timezone.utc)
                    else:
                        product_job.status = SegmentationJobStatus.COMPLETED  # Still completed, but with some failures
                        product_job.current_step = f"Completed: {completed_jobs} successful, {failed_jobs} failed"
                        product_job.completed_at = datetime.now(timezone.utc)

            db.commit()
            logger.info(f"Updated product job {product_job_id}: {completed_jobs}/{total_jobs} completed, {failed_jobs} failed")

        except Exception as e:
            logger.error(f"Error updating product job progress: {e}", exc_info=True)
            db.rollback()


# Global consumer instance
sqs_consumer = SQSConsumer()


def start_sqs_consumer():
    """Start the SQS consumer - called from main application."""
    sqs_consumer.start()


def stop_sqs_consumer():
    """Stop the SQS consumer - called during shutdown."""
    sqs_consumer.stop()


# Signal handlers for graceful shutdown
def signal_handler(signum, frame):
    """Handle shutdown signals."""
    logger.info(f"Received signal {signum}, shutting down SQS consumer...")
    stop_sqs_consumer()


# Register signal handlers
signal.signal(signal.SIGTERM, signal_handler)
signal.signal(signal.SIGINT, signal_handler)
