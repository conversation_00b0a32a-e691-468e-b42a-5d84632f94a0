"""
Segment-Aware Synthesis Service.

This service implements the segment-aware synthesis strategy for generating massage programs
by analyzing user requirements and creating plans for semantic segment retrieval.
"""

import logging
from typing import AsyncGenerator, Dict, List, Any
from pydantic_ai import Agent
from pydantic_ai.models.openai import OpenAIModel
from pydantic_ai.providers.openai import OpenAIProvider


from config import settings
from schemas.chat_schemas import MassageProgramDetails, HighEndProductSteps, HighEndProductStep
from schemas.synthesis_schemas import ProgramPlan
from utils.pinecone_ops import get_pinecone_index
from utils.embedding_utils import get_embedding
from models.program_models import ProgramSegment
from sqlalchemy.orm import Session

logger = logging.getLogger(__name__)

# --- Constants ---
PLANNING_MODEL_NAME = "gpt-4o"


class SegmentAwareSynthesisError(Exception):
    """
    Custom exception for segment-aware synthesis errors.

    This exception is raised when any step of the segment-aware synthesis process fails,
    including:
    - Program plan generation
    - Semantic search for segments
    - Segment selection
    - Program assembly
    - Database operations
    """

    def __init__(self, message: str, error_code: str = None, original_error: Exception = None):
        """
        Initialize the exception.

        Args:
            message: Human-readable error message
            error_code: Optional error code for categorization
            original_error: Original exception that caused this error
        """
        super().__init__(message)
        self.message = message
        self.error_code = error_code
        self.original_error = original_error

    def __str__(self):
        if self.error_code:
            return f"[{self.error_code}] {self.message}"
        return self.message


class SegmentAwareSynthesisService:
    """
    Service for segment-aware synthesis strategy.

    This service creates program plans based on user requirements and will eventually
    use semantic search to find and assemble relevant program segments.
    """

    def __init__(self, db: Session = None):
        self.db = db
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")

    async def generate_program_steps(
        self,
        massage_details: MassageProgramDetails,
        **kwargs
    ) -> AsyncGenerator[str, None]:
        """
        Generate massage program steps using position-based segment-aware synthesis.

        This method implements a deterministic approach that uses position_in_parent metadata
        to ensure structured therapeutic progression without AI-based selection.

        Args:
            massage_details: User requirements for the massage program
            **kwargs: Additional parameters (for compatibility)

        Yields:
            Dict: Status updates including program plan, search results, and final program

        Raises:
            SegmentAwareSynthesisError: If synthesis fails
        """
        try:
            self.logger.info("Starting segment-aware synthesis")

            # Step 1: Create program plan
            program_plan = await create_program_plan(massage_details)

            yield {
                "type": "program_plan",
                "content": program_plan.model_dump()
            }
            self.logger.info("Segment-aware synthesis completed successfully")

            # Step 2: Position-based semantic search for segments
            segments = await self.semantic_search_segments(program_plan)

            yield {
                "type": "segment_search_results",
                "content": segments
            }

            # Step 3: Assemble program directly from position-organized segments
            # No selection needed - each position gets the most relevant segment
            final_program = await self.assemble_program_from_position_data(segments)
            yield {
                "type": "final_program",
                "content": final_program.model_dump(),
                "program_steps": final_program
            }


        except SegmentAwareSynthesisError:
            # Re-raise our custom errors without wrapping
            raise
        except Exception as e:
            error_msg = f"Segment-aware synthesis failed: {str(e)}"
            self.logger.error(error_msg, exc_info=True)
            yield {
                "type": "error",
                "content": f"❌ Error: {error_msg}"
            }
            raise SegmentAwareSynthesisError(
                error_msg,
                error_code="SYNTHESIS_ERROR",
                original_error=e
            ) from e

    async def semantic_search_segments(self, program_plan: ProgramPlan) -> Dict[str, Dict[str, Any]]:
        """
        Perform position-based semantic search for micro segments in each phase.
        Uses position_in_parent metadata to ensure structured therapeutic progression.

        Args:
            program_plan: The ProgramPlan containing search criteria for each phase

        Returns:
            Dict with phase names as keys and position-organized segment data
            Format: {"front": {"front": segment, "mid": segment, "last": segment}, ...}

        Raises:
            SegmentAwareSynthesisError: If search fails
        """
        try:
            self.logger.info("Starting position-based semantic search for segments")

            # Initialize Pinecone index
            pinecone_index = get_pinecone_index()

            results = {
                "front": {},
                "main": {},
                "cooling": {}
            }

            # Define position requirements for each phase
            phase_positions = {
                "front": ["front", "mid", "last"],  # 3 chunks: front, mid, last
                "main": ["front", "mid", "mid", "last"],  # 4 chunks: front, 2 mid, last
                "cooling": ["front", "mid", "last"]  # 3 chunks: front, mid, last
            }

            # Search for each phase and position combination
            for phase_name, phase_instruction in [
                ("front", program_plan.front_phase),
                ("main", program_plan.main_phase),
                ("cooling", program_plan.cooling_phase)
            ]:
                self.logger.info(f"Processing {phase_name} phase with position-based search")

                # Format search query for this phase (same query used for all positions)
                search_query = self._format_phase_search_query(phase_name, phase_instruction)
                query_embedding = get_embedding(search_query)

                positions = phase_positions[phase_name]
                position_results = {}

                # Handle each position for this phase
                for i, position in enumerate(positions):
                    position_key = f"{position}_{i+1}" if positions.count(position) > 1 else position

                    # Prepare metadata filters for this specific position
                    metadata_filter = {
                        "parent_phase": phase_name,
                        "position_in_parent": position
                    }

                    self.logger.info(f"Searching for {phase_name} phase, {position} position (key: {position_key})")

                    # Determine top_k based on position needs
                    # For main phase mid position, we need 2 chunks, so get top 2 on first mid search
                    if phase_name == "main" and position == "mid" and position_key == "mid_1":
                        top_k = 2  # Get 2 mid chunks for main phase
                    else:
                        top_k = 1  # Get 1 chunk for all other positions

                    # Perform vector search with position filter
                    search_results = pinecone_index.query(
                        vector=query_embedding,
                        filter=metadata_filter,
                        top_k=top_k,
                        include_metadata=True,
                        namespace=settings.PROGRAM_SEGMENTS_EMBEDDING_NAMESPACE
                    )

                    # Process search results for this position
                    if search_results.matches:
                        if phase_name == "main" and position == "mid" and position_key == "mid_1":
                            # Handle main phase mid position - get 2 chunks
                            for j, match in enumerate(search_results.matches[:2]):
                                try:
                                    segment_id = int(match.id) if str(match.id).isdigit() else match.id
                                    db_segment = self._fetch_segments_from_db([segment_id])
                                    if db_segment:
                                        segment_data = self._format_segment_data(db_segment[0], match.score)
                                        mid_key = f"mid_{j+1}"
                                        position_results[mid_key] = segment_data
                                        self.logger.info(f"Found segment {segment_id} for {phase_name} {mid_key}")
                                except (ValueError, TypeError) as e:
                                    self.logger.warning(f"Invalid segment ID from vector search: {match.id}, error: {e}")
                                    continue
                        else:
                            # Handle single chunk positions
                            match = search_results.matches[0]
                            try:
                                segment_id = int(match.id) if str(match.id).isdigit() else match.id
                                db_segment = self._fetch_segments_from_db([segment_id])
                                if db_segment:
                                    segment_data = self._format_segment_data(db_segment[0], match.score)
                                    position_results[position_key] = segment_data
                                    self.logger.info(f"Found segment {segment_id} for {phase_name} {position_key}")
                            except (ValueError, TypeError) as e:
                                self.logger.warning(f"Invalid segment ID from vector search: {match.id}, error: {e}")
                                continue
                    else:
                        self.logger.warning(f"No segments found for {phase_name} phase, {position} position")

                results[phase_name] = position_results
                self.logger.info(f"Completed {phase_name} phase search with {len(position_results)} position-based segments")

            self.logger.info("Position-based semantic search completed successfully")
            return results

        except Exception as e:
            error_msg = f"Position-based semantic search for segments failed: {str(e)}"
            self.logger.error(error_msg, exc_info=True)
            raise SegmentAwareSynthesisError(
                error_msg,
                error_code="SEARCH_ERROR",
                original_error=e
            ) from e

    def _format_phase_search_query(self, phase_name: str, phase_instruction) -> str:
        """
        Format a phase instruction into a natural language search query for embedding.

        Args:
            phase_name: Name of the phase (front, main, cooling)
            phase_instruction: PhaseSearchInstruction object

        Returns:
            str: Formatted search query for semantic search
        """
        query_parts = []

        # Add phase context
        if phase_name == "front":
            query_parts.append("Preparation and warm-up massage")
        elif phase_name == "main":
            query_parts.append("Therapeutic massage treatment")
        elif phase_name == "cooling":
            query_parts.append("Relaxation and wind-down massage")

        # Add body parts
        if phase_instruction.body_parts:
            body_parts_str = ", ".join(phase_instruction.body_parts)
            query_parts.append(f"targeting {body_parts_str}")

        # Add techniques
        if phase_instruction.techniques:
            techniques_str = ", ".join(phase_instruction.techniques)
            query_parts.append(f"using {techniques_str}")

        # Add purposes
        if phase_instruction.purposes:
            purposes_str = ", ".join(phase_instruction.purposes)
            query_parts.append(f"for {purposes_str}")

        # Add intensity
        if phase_instruction.intensity_range:
            query_parts.append(f"with {phase_instruction.intensity_range} intensity")

        # Add description keywords
        if phase_instruction.description_keywords:
            keywords_str = ", ".join(phase_instruction.description_keywords)
            query_parts.append(f"focusing on {keywords_str}")

        # Join all parts into a coherent search query
        search_query = " ".join(query_parts)

        self.logger.debug(f"Generated search query for {phase_name}: {search_query}")
        return search_query

    def _fetch_segments_from_db(self, segment_ids: List[int]) -> List[ProgramSegment]:
        """
        Fetch complete segment data from database.

        Args:
            segment_ids: List of segment IDs to fetch

        Returns:
            List of ProgramSegment objects from database

        Raises:
            SegmentAwareSynthesisError: If database query fails
        """
        try:
            if not segment_ids:
                return []

            self.logger.info(f"Fetching {len(segment_ids)} segments from database")

            segments = self.db.query(ProgramSegment).filter(
                ProgramSegment.id.in_(segment_ids)
            ).all()

            self.logger.info(f"Successfully fetched {len(segments)} segments from database")
            return segments

        except Exception as e:
            error_msg = f"Failed to fetch segments from database: {str(e)}"
            self.logger.error(error_msg, exc_info=True)
            raise SegmentAwareSynthesisError(
                error_msg,
                error_code="DB_FETCH_ERROR",
                original_error=e
            ) from e

    def _format_segment_data(self, db_segment: ProgramSegment, search_score: float) -> Dict[str, Any]:
        """
        Format database segment data for UI consumption.

        Args:
            db_segment: ProgramSegment object from database
            search_score: Relevance score from vector search

        Returns:
            Dict with formatted segment data for UI
        """
        try:
            # Calculate step count from actual steps
            step_count = len(db_segment.steps) if db_segment.steps else 0

            segment_data = {
                "id": db_segment.id,
                "original_program_id": db_segment.original_program_id,
                "phase": db_segment.phase,
                "parent_segment_id": db_segment.parent_segment_id,
                "position_in_parent": db_segment.position_in_parent,
                "entry_state": db_segment.entry_state or {},
                "exit_state": db_segment.exit_state or {},
                "purpose_tags": db_segment.purpose_tags or [],
                "technique_tags": db_segment.technique_tags or [],
                "body_part_tags": db_segment.body_part_tags or [],
                "intensity_score": db_segment.intensity_score,
                "duration_seconds": db_segment.duration_seconds,
                "steps": db_segment.steps or [],
                "score": search_score,  # Vector search relevance score
                "description": db_segment.description or "",
                "step_count": step_count
            }

            return segment_data

        except Exception as e:
            self.logger.error(f"Failed to format segment data for segment {db_segment.id}: {str(e)}")
            # Return minimal data to prevent complete failure
            return {
                "id": db_segment.id,
                "original_program_id": db_segment.original_program_id,
                "phase": db_segment.phase,
                "parent_segment_id": db_segment.parent_segment_id,
                "position_in_parent": db_segment.position_in_parent,
                "entry_state": {},
                "exit_state": {},
                "purpose_tags": [],
                "technique_tags": [],
                "body_part_tags": [],
                "intensity_score": None,
                "duration_seconds": None,
                "steps": [],
                "score": search_score,
                "description": "",
                "step_count": 0
            }



    async def assemble_program(self, selected_segments: Dict[str, Any]) -> HighEndProductSteps:
        """
        Assemble a complete program from selected segment IDs by querying the database
        for segment steps and combining them into a coherent program.

        Args:
            selected_segments: Dict containing selected segment IDs for each phase
                              Format: {"selected_segments": {"front": [...], "main": [...], "cooling": [...]}}

        Returns:
            HighEndProductSteps: Complete program with all steps from selected segments

        Raises:
            SegmentAwareSynthesisError: If assembly fails
        """
        try:
            self.logger.info("Starting program assembly from selected segments")

            all_steps = []
            step_counter = 1

            # Process each phase in order: front, main, cooling
            for phase_name in ["front", "main", "cooling"]:
                segment_ids = selected_segments.get("selected_segments", {}).get(phase_name, [])

                if not segment_ids:
                    self.logger.warning(f"No segments selected for {phase_name} phase")
                    continue

                self.logger.info(f"Processing {len(segment_ids)} segments for {phase_name} phase")

                # Query database for each segment's steps
                for segment_id in segment_ids:
                    try:
                        segment = self.db.query(ProgramSegment).filter(
                            ProgramSegment.id == int(segment_id)
                        ).first()

                        if not segment:
                            self.logger.warning(f"Segment {segment_id} not found in database")
                            continue

                        if not segment.steps:
                            self.logger.warning(f"Segment {segment_id} has no steps")
                            continue

                        self.logger.info(f"Adding {len(segment.steps)} steps from segment {segment_id} ({phase_name} phase)")

                        # Convert segment steps to HighEndProductStep objects
                        for step_data in segment.steps:
                            try:
                                # Ensure step has a step_number
                                if not step_data.get("step_number"):
                                    step_data["step_number"] = str(step_counter)

                                # Create HighEndProductStep object
                                step = HighEndProductStep(**step_data)
                                all_steps.append(step)
                                step_counter += 1

                            except Exception as step_error:
                                self.logger.warning(f"Failed to process step from segment {segment_id}: {step_error}")
                                continue

                    except Exception as segment_error:
                        self.logger.error(f"Failed to process segment {segment_id}: {segment_error}")
                        continue

            if not all_steps:
                self.logger.warning("No steps were assembled from selected segments")
                return HighEndProductSteps(steps=[])

            self.logger.info(f"Program assembly completed successfully with {len(all_steps)} total steps")
            return HighEndProductSteps(steps=all_steps)

        except Exception as e:
            error_msg = f"Program assembly failed: {str(e)}"
            self.logger.error(error_msg, exc_info=True)
            raise SegmentAwareSynthesisError(
                error_msg,
                error_code="ASSEMBLY_ERROR",
                original_error=e
            ) from e

    async def assemble_program_from_position_data(self, position_segments: Dict[str, Dict[str, Any]]) -> HighEndProductSteps:
        """
        Assemble a complete program from position-organized segment data.

        Args:
            position_segments: Dict containing position-organized segment data
                              Format: {"front": {"front": segment, "mid": segment, "last": segment}, ...}

        Returns:
            HighEndProductSteps: Complete program with all steps from position-organized segments

        Raises:
            SegmentAwareSynthesisError: If assembly fails
        """
        try:
            self.logger.info("Starting position-based program assembly")

            all_steps = []
            step_counter = 1

            # Define the order of positions for each phase
            phase_position_order = {
                "front": ["front", "mid", "last"],
                "main": ["front", "mid_1", "mid_2", "last"],
                "cooling": ["front", "mid", "last"]
            }

            # Process each phase in order: front, main, cooling
            for phase_name in ["front", "main", "cooling"]:
                phase_segments = position_segments.get(phase_name, {})
                position_order = phase_position_order[phase_name]

                if not phase_segments:
                    self.logger.warning(f"No segments found for {phase_name} phase")
                    continue

                self.logger.info(f"Processing {phase_name} phase with {len(phase_segments)} position-based segments")

                # Process positions in the defined order
                for position_key in position_order:
                    segment_data = phase_segments.get(position_key)

                    if not segment_data:
                        self.logger.warning(f"No segment found for {phase_name} phase, {position_key} position")
                        continue

                    steps = segment_data.get("steps", [])

                    if not steps:
                        self.logger.warning(f"Segment {segment_data.get('id', 'unknown')} has no steps")
                        continue

                    segment_id = segment_data.get("id", "unknown")
                    self.logger.info(f"Adding {len(steps)} steps from segment {segment_id} ({phase_name} {position_key})")

                    # Convert segment steps to HighEndProductStep objects
                    for step_data in steps:
                        try:
                            # Ensure step has a step_number
                            if not step_data.get("step_number"):
                                step_data["step_number"] = str(step_counter)

                            # Create HighEndProductStep object
                            step = HighEndProductStep(**step_data)
                            all_steps.append(step)
                            step_counter += 1

                        except Exception as step_error:
                            self.logger.warning(f"Failed to process step from segment {segment_id}: {step_error}")
                            continue

            if not all_steps:
                self.logger.warning("No steps were assembled from position-organized segments")
                return HighEndProductSteps(steps=[])

            self.logger.info(f"Position-based program assembly completed successfully with {len(all_steps)} total steps")
            return HighEndProductSteps(steps=all_steps)

        except Exception as e:
            error_msg = f"Position-based program assembly failed: {str(e)}"
            self.logger.error(error_msg, exc_info=True)
            raise SegmentAwareSynthesisError(
                error_msg,
                error_code="ASSEMBLY_ERROR",
                original_error=e
            ) from e

    async def assemble_program_from_data(self, selected_segments: Dict[str, Any], all_segments: Dict[str, List[Any]]) -> HighEndProductSteps:
        """
        Assemble a complete program from selected segment IDs using already-fetched segment data.
        This method avoids duplicate database queries by reusing data from semantic_search_segments.

        Args:
            selected_segments: Dict containing selected segment IDs for each phase
                              Format: {"selected_segments": {"front": [...], "main": [...], "cooling": [...]}}
            all_segments: Dict containing all fetched segment data from semantic_search_segments
                         Format: {"front": [...], "main": [...], "cooling": [...]}

        Returns:
            HighEndProductSteps: Complete program with all steps from selected segments

        Raises:
            SegmentAwareSynthesisError: If assembly fails
        """
        try:
            self.logger.info("Starting optimized program assembly using cached segment data")

            all_steps = []
            step_counter = 1

            # Create a lookup map for quick segment access
            segment_lookup = {}
            for phase_name, phase_segments in all_segments.items():
                for segment in phase_segments:
                    segment_lookup[segment["id"]] = segment

            # Process each phase in order: front, main, cooling
            for phase_name in ["front", "main", "cooling"]:
                segment_ids = selected_segments.get("selected_segments", {}).get(phase_name, [])

                if not segment_ids:
                    self.logger.warning(f"No segments selected for {phase_name} phase")
                    continue

                self.logger.info(f"Processing {len(segment_ids)} segments for {phase_name} phase")

                # Process each selected segment
                for segment_id in segment_ids:
                    try:
                        # Look up segment in cached data
                        segment_data = segment_lookup.get(segment_id)

                        if not segment_data:
                            self.logger.warning(f"Segment {segment_id} not found in cached data, falling back to DB query")
                            # Fallback to database query if not in cache
                            segment = self.db.query(ProgramSegment).filter(
                                ProgramSegment.id == int(segment_id)
                            ).first()

                            if not segment:
                                self.logger.warning(f"Segment {segment_id} not found in database")
                                continue

                            steps = segment.steps or []
                        else:
                            steps = segment_data.get("steps", [])

                        if not steps:
                            self.logger.warning(f"Segment {segment_id} has no steps")
                            continue

                        self.logger.info(f"Adding {len(steps)} steps from segment {segment_id} ({phase_name} phase)")

                        # Convert segment steps to HighEndProductStep objects
                        for step_data in steps:
                            try:
                                # Ensure step has a step_number
                                if not step_data.get("step_number"):
                                    step_data["step_number"] = str(step_counter)

                                # Create HighEndProductStep object
                                step = HighEndProductStep(**step_data)
                                all_steps.append(step)
                                step_counter += 1

                            except Exception as step_error:
                                self.logger.warning(f"Failed to process step from segment {segment_id}: {step_error}")
                                continue

                    except Exception as segment_error:
                        self.logger.error(f"Failed to process segment {segment_id}: {segment_error}")
                        continue

            if not all_steps:
                self.logger.warning("No steps were assembled from selected segments")
                return HighEndProductSteps(steps=[])

            self.logger.info(f"Optimized program assembly completed successfully with {len(all_steps)} total steps")
            return HighEndProductSteps(steps=all_steps)

        except Exception as e:
            error_msg = f"Optimized program assembly failed: {str(e)}"
            self.logger.error(error_msg, exc_info=True)
            raise SegmentAwareSynthesisError(
                error_msg,
                error_code="ASSEMBLY_ERROR",
                original_error=e
            ) from e



# --- Program Plan Agent ---

program_plan_agent = Agent(
    model=OpenAIModel(
        PLANNING_MODEL_NAME, provider=OpenAIProvider(api_key=settings.OPENAI_API_KEY)
    ),
    output_type=ProgramPlan,
    system_prompt="""
You are an expert massage program strategist specializing in segment-aware synthesis. Your task is to analyze user requirements and create a detailed plan for finding and selecting massage program segments from a database through semantic search.

## Your Role
You translate user massage requirements into specific search instructions for each phase (front, main, cooling). Your output will be used to generate embeddings and search for matching segments in the database.

## Database Context
The segment database contains massage program segments with rich metadata:
- **body_part_tags**: Specific anatomical regions (e.g., "neck", "shoulders", "upper_back", "lumbar")
- **technique_tags**: Massage techniques (e.g., "deep_kneading", "percussion", "air_compression", "rolling")
- **purpose_tags**: Therapeutic purposes (e.g., "muscle_knot_release", "spinal_mobilization", "circulation_boost")
- **intensity_score**: Numerical intensity (1-10 scale)
- **description**: Natural language description of the segment's therapeutic action

## Phase Strategy Framework

### Front Phase (Preparation)
**Purpose**: Prepare the body for therapeutic work, positioning, gentle warm-up
**Strategy**: Target areas that need preparation before main therapeutic intervention
**Approach**: Gentle introduction, muscle warming, positioning

### Main Phase (Therapeutic Work)
**Purpose**: Primary therapeutic intervention, problem-solving, deep tissue work
**Strategy**: Focus on user's primary concerns and problem areas
**Approach**: Intensive therapeutic techniques, targeted pressure, comprehensive coverage

### Cooling Phase (Wind-down)
**Purpose**: Gentle conclusion, relaxation, circulation improvement
**Strategy**: Soothing finish, gentle relaxation, circulation enhancement
**Approach**: Light pressure, calming techniques, gentle conclusion

## Search Plan Creation Instructions

For each phase, create search criteria that will find relevant segments:

1. **body_parts**: List specific anatomical regions to target
2. **techniques**: List massage techniques that would be appropriate
3. **purposes**: List therapeutic goals for this phase
4. **intensity_range**: Specify appropriate intensity level (e.g., "low", "medium", "high")
5. **description_keywords**: Key terms that would appear in segment descriptions

## Key Principles
- **User-focused**: Prioritize the user's focus_area and desired_outcome
- **Phase-appropriate**: Match search criteria to each phase's therapeutic purpose
- **Complementary**: Include supporting body parts that work with the focus areas
- **Progressive**: Ensure logical flow from preparation → intervention → relaxation
- **Searchable**: Use terms that will effectively match database content

## Example Search Logic
- If user wants "lower back pain relief":
  - Front: Prepare lower back, lumbar, supporting muscles
  - Main: Target lower back, lumbar, plus related areas (hips, glutes, mid_back)
  - Cooling: Gentle lower back, circulation improvement

Create a comprehensive plan that will guide effective semantic search to find segments that address the user's specific needs while following sound therapeutic progression.
""",
    retries=1,
)




async def create_program_plan(massage_details: MassageProgramDetails) -> ProgramPlan:
    """
    Create a comprehensive program plan based on user massage requirements.
    
    Args:
        massage_details: The structured user requirements from MassageProgramDetails
        
    Returns:
        ProgramPlan: A detailed plan for finding and selecting program segments
        
    Raises:
        Exception: If plan generation fails
    """
    try:
        logger.info("Creating program plan from massage details")
        logger.debug(f"Input details: {massage_details}")
        
        # Convert MassageProgramDetails to a formatted string for the agent
        user_requirements = _format_massage_details_for_agent(massage_details)
        
        # Generate the program plan
        result = await program_plan_agent.run(user_requirements)
        
        logger.info("Program plan created successfully")
        logger.debug(f"Generated plan: {result.output}")

        return result.output
        
    except Exception as e:
        logger.error(f"Failed to create program plan: {e}", exc_info=True)
        raise


def _format_massage_details_for_agent(details: MassageProgramDetails) -> str:
    """
    Format MassageProgramDetails into a readable string for the agent.
    
    Args:
        details: The MassageProgramDetails object
        
    Returns:
        str: Formatted string representation of user requirements
    """
    parts = []
    
    if details.target_market:
        parts.append(f"Target Market: {details.target_market}")
    
    if details.focus_area:
        parts.append(f"Focus Areas: {', '.join(details.focus_area)}")
    
    if details.problem_desired_outcome:
        parts.append(f"Desired Outcome: {details.problem_desired_outcome}")
    
    if details.program_flow:
        flow_parts = []
        if details.program_flow.highlights:
            flow_parts.append(f"Highlights: {details.program_flow.highlights}")
        if details.program_flow.signature_moves:
            flow_parts.append(f"Signature Moves: {details.program_flow.signature_moves}")
        if details.program_flow.logic_techniques:
            flow_parts.append(f"Logic/Techniques: {details.program_flow.logic_techniques}")
        if details.program_flow.intensity:
            flow_parts.append(f"Intensity: {details.program_flow.intensity}")
        
        if flow_parts:
            parts.append(f"Program Flow: {'; '.join(flow_parts)}")
    
    if details.massage_settings:
        settings_parts = []
        if details.massage_settings.warm_air is not None:
            settings_parts.append(f"Warm Air: {'On' if details.massage_settings.warm_air else 'Off'}")
        if details.massage_settings.vibration is not None:
            settings_parts.append(f"Vibration: {'On' if details.massage_settings.vibration else 'Off'}")
        if details.massage_settings.air_bags is not None:
            settings_parts.append(f"Air Bags: {'On' if details.massage_settings.air_bags else 'Off'}")
        if details.massage_settings.scent is not None:
            settings_parts.append(f"Scent: {'On' if details.massage_settings.scent else 'Off'}")
        if details.massage_settings.mood_light is not None:
            settings_parts.append(f"Mood Light: {'On' if details.massage_settings.mood_light else 'Off'}")
        
        if settings_parts:
            parts.append(f"Massage Settings: {'; '.join(settings_parts)}")
    
    if not parts:
        return "No specific requirements provided. Create a general wellness massage program plan."
    
    return "\n".join(parts)
