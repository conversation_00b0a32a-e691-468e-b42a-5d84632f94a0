"""
SFT (Supervised Fine-Tuning) program generation service implementation.

This module implements the SFT approach for generating massage program steps
using fine-tuned models that can directly generate complete step sequences.
"""

import json
import logging
from typing import Optional

from schemas.chat_schemas import MassageProgramDetails, HighEndProductSteps, HighEndProductStep
from services.chat_agents import sft_program_generator_agent_factory

logger = logging.getLogger(__name__)


class SFTProgramGenerationError(Exception):
    """Custom exception for SFT program generation errors."""
    pass


class SFTProgramGenerationService:
    """
    Service for SFT (Supervised Fine-Tuning) program generation using fine-tuned models.

    This service uses specialized models that have been fine-tuned on massage program data
    to directly generate complete step sequences based on user preferences.
    """
    
    def _construct_sft_prompt(self, details: MassageProgramDetails) -> str:
        """Construct a prompt for the SFT model based on user preferences."""
        prompt_lines = [
            "You are an expert massage program designer. Based on the following user preferences, generate a complete, step-by-step massage program sequence in the required JSON format (HighEndProductSteps).",
            "User Preferences:",
        ]
        if details.target_market:
            prompt_lines.append(f"- Target User/Market: {details.target_market}")
        if details.duration:
            prompt_lines.append(f"- Requested Duration: {details.duration} minutes. Design the program steps to fit this duration.")
        if details.focus_area:
            prompt_lines.append(f"- Key Focus Areas: {', '.join(details.focus_area)}")
        if details.problem_desired_outcome:
            prompt_lines.append(f"- Main Goal/Desired Outcome: {details.problem_desired_outcome}")

        if details.program_flow:
            flow_parts = []
            if details.program_flow.highlights:
                flow_parts.append(f"Highlights: {details.program_flow.highlights}")
            if details.program_flow.signature_moves:
                flow_parts.append(f"Signature Moves to Incorporate: {details.program_flow.signature_moves}")
            if details.program_flow.logic_techniques:
                flow_parts.append(f"Preferred Logic/Techniques: {details.program_flow.logic_techniques}")
            if details.program_flow.intensity:
                flow_parts.append(f"Overall Intensity: {details.program_flow.intensity}")
            if flow_parts:
                prompt_lines.append(f"- Program Flow Preferences: {'; '.join(flow_parts)}")

        if details.massage_settings:
            settings_parts = []
            if details.massage_settings.warm_air is not None:
                settings_parts.append(f"Warm Air: {'On' if details.massage_settings.warm_air else 'Off'}")
            if details.massage_settings.vibration is not None:
                settings_parts.append(f"Vibration: {'On' if details.massage_settings.vibration else 'Off'}")
            if details.massage_settings.air_bags is not None:
                settings_parts.append(f"Air Bags: {'On' if details.massage_settings.air_bags else 'Off'}")
            # Add other settings as needed
            if settings_parts:
                prompt_lines.append(f"- Specific Massage Settings: {'; '.join(settings_parts)}")
        
        prompt_lines.append("\nGenerate the full program steps now.")
        return "\n".join(prompt_lines)

    async def generate_steps(
        self,
        program_details: MassageProgramDetails,
        sft_model_name: str
    ) -> HighEndProductSteps:
        """
        Generate massage program steps using a specified SFT model.
        
        Args:
            program_details: Extracted user preferences and massage details
            sft_model_name: The name/identifier of the SFT model to use
            
        Returns:
            HighEndProductSteps: Generated program steps
            
        Raises:
            SFTProgramGenerationError: If there's an issue during generation
        """
        logger.info(f"SFT Service: Attempting direct generation with model: {sft_model_name}")
        try:
            sft_agent = sft_program_generator_agent_factory(sft_model_name)
            logger.info(f"SFT Service: Agent for {sft_model_name} instantiated.")

            sft_prompt = self._construct_sft_prompt(program_details)
            logger.debug(f"SFT Service: Constructed prompt: {sft_prompt}")

            sft_agent_run_result = await sft_agent.run(sft_prompt)
            raw_output = sft_agent_run_result.output
            
            sft_generated_steps: Optional[HighEndProductSteps] = None
            
            if isinstance(raw_output, HighEndProductSteps):
                sft_generated_steps = raw_output
                logger.info(f"SFT Service: Model returned HighEndProductSteps object directly.")
            elif isinstance(raw_output, str):
                try:
                    logger.info(f"SFT Service: Model returned JSON string, parsing...")
                    parsed_json = json.loads(raw_output)
                    
                    if isinstance(parsed_json, dict) and "steps" in parsed_json:
                        sft_generated_steps = HighEndProductSteps(**parsed_json)
                        if sft_generated_steps.steps: # Ensure steps list is not empty
                             logger.info(f"SFT Service: Successfully parsed JSON string into HighEndProductSteps with {len(sft_generated_steps.steps)} steps")
                        else:
                            logger.warning(f"SFT Service: Parsed JSON but 'steps' list is empty.")
                            raise SFTProgramGenerationError(f"SFT model {sft_model_name} returned empty 'steps' list after parsing.")
                    elif isinstance(parsed_json, list):
                        if not parsed_json: # Handle empty list case
                            logger.warning(f"SFT Service: Model returned an empty list of steps.")
                            raise SFTProgramGenerationError(f"SFT model {sft_model_name} returned an empty list of steps.")
                        logger.info(f"SFT Service: Model returned array of {len(parsed_json)} steps, wrapping.")
                        sft_generated_steps = HighEndProductSteps(steps=[HighEndProductStep(**step) for step in parsed_json])
                        logger.info(f"SFT Service: Successfully wrapped {len(sft_generated_steps.steps)} steps")
                    else:
                        logger.error(f"SFT Service: Unexpected JSON structure. Type: {type(parsed_json)}")
                        raise SFTProgramGenerationError(f"SFT model returned unexpected JSON structure: expected {{'steps': [...]}} or non-empty [...], got {type(parsed_json)}")
                        
                except (json.JSONDecodeError, TypeError, ValueError) as parse_err:
                    logger.error(f"SFT Service: Failed to parse SFT JSON output: {parse_err}. Raw output: {raw_output[:500]}", exc_info=True)
                    raise SFTProgramGenerationError(f"SFT model returned invalid JSON: {parse_err}")
            else:
                logger.error(f"SFT Service: Model returned unexpected type: {type(raw_output)}. Raw output: {str(raw_output)[:500]}")
                raise SFTProgramGenerationError(f"SFT model returned unexpected data type: {type(raw_output)}")

            if not sft_generated_steps or not sft_generated_steps.steps: # Double check after parsing/wrapping
                logger.warning(f"SFT Service: Model {sft_model_name} did not produce valid steps after processing.")
                raise SFTProgramGenerationError(f"SFT model {sft_model_name} failed to generate program steps or returned empty steps after processing.")
            
            logger.info(f"SFT Service: Model {sft_model_name} generated {len(sft_generated_steps.steps)} steps.")
            return sft_generated_steps

        except SFTProgramGenerationError: # Re-raise specific errors
            raise
        except Exception as e:
            logger.error(f"SFT Service: Unexpected error during step generation with {sft_model_name}: {e}", exc_info=True)
            raise SFTProgramGenerationError(f"Unexpected error in SFT generation service: {e}") from e
