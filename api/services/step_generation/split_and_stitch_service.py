"""
Split and stitch service implementation.

This module implements the split-and-stitch approach for generating massage program steps
by segmenting existing programs and stitching the best segments together.
"""

import logging
from typing import List, Dict, Any, Tuple, AsyncGenerator
# Pydantic AI Imports
from pydantic_ai import Agent
from pydantic_ai.models.openai import OpenAIModel
from pydantic_ai.providers.openai import OpenAIProvider
from pydantic_ai.messages import ModelMessage, UserPromptPart, ModelRequest
from pydantic_ai.agent import AgentRunResult

# SQLAlchemy imports
from sqlalchemy.orm import Session

# Project Imports
from config import settings
from schemas.chat_schemas import (
    HighEndProductStep,
    HighEndProductSteps,
    TargetSequence,
    SegmentedSteps,
    StitchingInput,
    MassageProgramDetails,
)
from pydantic import BaseModel, Field

from models.program_models import Program
from utils.program_helpers import _standardize_program_sequence, _format_program_for_response
from utils.embedding_utils import get_embedding
from utils.pinecone_ops import get_pinecone_index
from services.chat_agents import sequence_generator_agent


class SplitAndStitchError(Exception):
    """Custom exception for split and stitch errors."""
    pass

# --- Logging ---
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# --- Constants ---
# Consider using a more powerful model for synthesis tasks if needed
SYNTHESIS_MODEL_NAME = "gpt-4o"  # Or "gpt-4o-mini" if sufficient


# --- Models for Step Analysis ---
class SegmentationPoints(BaseModel):
    """Model for LLM to provide split points for segments."""

    front_end_index: int = Field(
        ..., description="Index where front sequence ends (exclusive)"
    )
    main_end_index: int = Field(
        ..., description="Index where front sequence ends (exclusive)"
    )


class SelectedSegmentIndices(BaseModel):
    """Model for LLM to provide the indices of the best segments."""

    selected_front_index: int = Field(
        ...,
        ge=0,
        le=2,
        description="0-based index of the program providing the best front sequence.",
    )
    selected_main_index: int = Field(
        ...,
        ge=0,
        le=2,
        description="0-based index of the program providing the best main sequence.",
    )
    selected_cooling_index: int = Field(
        ...,
        ge=0,
        le=2,
        description="0-based index of the program providing the best cooling sequence.",
    )


class TransitionStep(BaseModel):
    """Model for LLM to provide transition steps between segments."""

    steps: List[HighEndProductStep] = Field(
        ..., description="List of transition steps to smooth between segments"
    )


def format_step_description(step: HighEndProductStep) -> str:
    """Format a step into a human-readable description."""
    parts = []
    if step.roller_action_description:
        parts.append(f"Roller: {step.roller_action_description}")
    if step.air_action_description:
        parts.append(f"Air: {step.air_action_description}")
    if step.kneading_speed is not None:
        parts.append(f"Kneading Speed: {step.kneading_speed}")
    if step.tapping_speed is not None:
        parts.append(f"Tapping Speed: {step.tapping_speed}")
    if step.position_3d is not None:
        parts.append(f"Position 3D: {step.position_3d}")
    if step.width is not None:
        parts.append(f"Width: {step.width}")
    parts.append(f"Type: {step.type}")
    if step.subroutine_id:
        parts.append(f"Subroutine: {step.subroutine_id}")
    return " | ".join(parts)


# --- LLM for step analysis ---
segmentation_analyzer = Agent(
    model=OpenAIModel(
        SYNTHESIS_MODEL_NAME, provider=OpenAIProvider(api_key=settings.OPENAI_API_KEY)
    ),
    output_type=SegmentationPoints,
    system_prompt="""
You are a massage program analyzer. You must analyze massage program steps and return ONLY the JSON indices for logical breakpoints.

CRITICAL: You must return valid JSON matching this exact schema:
{
  "front_end_index": <integer>,
  "main_end_index": <integer>
}

Rules:
- front_end_index: Index where front sequence ends (exclusive, 1-based)
- main_end_index: Index where main sequence ends (exclusive, 1-based)  
- front_end_index must be >= 1 and < total_steps
- main_end_index must be > front_end_index and <= total_steps
- Return ONLY valid JSON, no explanations or additional text

For a typical massage sequence:
1. Front sequence (warm-up/initial phase)
2. Main sequence (main work/focus phase)
3. Cooling sequence (concluding/wind-down phase)

Given `Program Sequence Structure` and `Steps to Analyze`, analyze the steps
and output the indices where the Front sequence should end and where the Main sequence should end.

""",
    retries=3,
)

# --- Transition agent for smoothing segment transitions ---
transition_agent = Agent(
    model=OpenAIModel(
        SYNTHESIS_MODEL_NAME, provider=OpenAIProvider(api_key=settings.OPENAI_API_KEY)
    ),
    output_type=TransitionStep,
    system_prompt="""
You are a massage program transition designer. Your task is to create smooth transitions between different
segments of a massage program. Given the last step from one segment and the first step from the next segment,
you need to create 1-2 transition steps that make the change in intensity, speed, or technique more gradual.

Focus on the following aspects:
1. Gradually change numeric values (kneading_speed, tapping_speed, etc.) between segments
2. Create logical position transitions for rollers if positions change drastically
3. Ensure that air pressure changes are logical and not too abrupt
4. Maintain the flow of the massage experience

The transition steps should feel natural and prepare the user for the change in technique or focus area.
""",
    retries=3,
)


# --- Hybrid segmentation function ---
async def analyze_and_segment_steps(
    program_steps: List[HighEndProductStep], program: Program
) -> SegmentedSteps:
    """
    Uses LLM to analyze steps and determine split points, then splits programmatically.
    """
    if not program_steps:
        return SegmentedSteps()

    # If very few steps, use a simple split
    if len(program_steps) <= 6:
        front_size = max(1, len(program_steps) // 3)
        main_size = max(1, len(program_steps) // 3)

        front_steps = program_steps[:front_size]
        main_steps = program_steps[front_size : front_size + main_size]
        cooling_steps = program_steps[front_size + main_size :]

        logger.info(
            f"Basic split for small program ({len(program_steps)} steps): {len(front_steps)} front, {len(main_steps)} main, {len(cooling_steps)} cooling"
        )

        return SegmentedSteps(
            front_steps=front_steps, main_steps=main_steps, cooling_steps=cooling_steps
        )

    # Prepare analysis data
    # Create a simplified list of step descriptions for the LLM to analyze
    step_descriptions = [
        f"Step {i+1}: {format_step_description(step)}"
        for i, step in enumerate(program_steps)
    ]

    # Build a dictionary mapping standard sequence names to their values
    programme_sequence = program.program_description.get("programme_sequence", {})
    target_sequence = _standardize_program_sequence(programme_sequence)

    try:
        # Get split points from LLM analysis
        logger.info(f"Analyzing {program} steps to determine segmentation points...")
        agent_history: List[ModelMessage] = []
        agent_history.append(
            ModelRequest(
                parts=[
                    UserPromptPart(
                        content=f"Program Sequence Structure: \n Front: {target_sequence['front']} \n Main: {target_sequence['main']} \n Cooling: {target_sequence['cooling']}"
                    ),
                    UserPromptPart(content=f"Total Steps: {len(program_steps)}"),
                ]
            )
        )

        result = await segmentation_analyzer.run(
            f"Analyze the following steps and determine the logical breakpoints between the three main parts of a massage: {step_descriptions}",
            message_history=agent_history,
        )
        logger.info(f"Raw LLM response: {result}")
        logger.info(f"Segmentation result: {result.output}")

        # Extract split points, ensuring they're valid indices
        front_end = min(max(1, result.output.front_end_index), len(program_steps) - 1)
        main_end = min(
            max(front_end + 1, result.output.main_end_index), len(program_steps)
        )

        # Split the steps using the identified points
        front_steps = program_steps[:front_end]
        main_steps = program_steps[front_end:main_end]
        cooling_steps = program_steps[main_end:]

    except Exception as e:
        logger.error(f"Error during step analysis: {e}", exc_info=True)
        # Instead of silent fallback, raise an error so the UI can show feedback
        # The fallback logic can still be used, but users should know the AI analysis failed
        raise SplitAndStitchError(
            f"AI step analysis failed for program {program.id}. Using basic fallback segmentation. Error: {e}"
        ) from e

    return SegmentedSteps(
        front_steps=front_steps, main_steps=main_steps, cooling_steps=cooling_steps
    )


# --- Agent Definitions ---

# Agent to select the best segment from each category
segment_selector_agent: Agent[StitchingInput, SelectedSegmentIndices] = Agent(
    model=OpenAIModel(
        SYNTHESIS_MODEL_NAME, provider=OpenAIProvider(api_key=settings.OPENAI_API_KEY)
    ),
    output_type=SelectedSegmentIndices,
    system_prompt="""
You are a massage program analyst. You are given three segmented massage programs
(`segmented_program_1`, `segmented_program_2`, `segmented_program_3`), each potentially containing
'front_steps', 'main_steps', and 'cooling_steps'. You are also given a `target_sequence`
which describes the desired characteristics for the 'front', 'main', and 'cooling' parts of the final program.

Your task is to SELECT the best segment for each part (front, main, cooling) from the available options
in the three input programs based on how well they align with the `target_sequence` description for that part.

- Evaluate the 'front_steps' from program 1, program 2, and program 3 against the `target_sequence.front_sequence` description.
- Evaluate the 'main_steps' from program 1, program 2, and program 3 against the `target_sequence.main_sequence` description.
- Evaluate the 'cooling_steps' from program 1, program 2, and program 3 against the `target_sequence.cooling_sequence` description.

Output the 0-based index (0, 1, or 2) of the program that provided the *single best* segment for each category
using the `SelectedSegmentIndices` schema.
""",
    retries=3,
)


async def create_transition_steps(
    last_step: HighEndProductStep,
    first_step: HighEndProductStep,
    example_programs: List[Program],
) -> List[HighEndProductStep]:
    """
    Creates smooth transition steps between two segments using the transition agent, with all example steps as context.

    Args:
        last_step: The last step of the previous segment
        first_step: The first step of the next segment
        example_programs: List of example programs to use for context

    Returns:
        List[HighEndProductStep]: 1-2 transition steps to make the change smoother
    """
    try:
        # Prepare the steps data for the agent
        last_step_desc = "LAST STEP:\\n" + format_step_description(last_step)
        first_step_desc = "FIRST STEP:\\n" + format_step_description(first_step)

        # --- FULL EXAMPLE STEPS ---
        example_steps_str = ""
        for prog_idx, prog in enumerate(example_programs):
            if not isinstance(prog.steps, list) or not prog.steps:
                continue
            try:
                parsed_steps = [
                    HighEndProductStep(**s) if isinstance(s, dict) else s
                    for s in prog.steps
                ]
                example_steps_str += f"\nEXAMPLE PROGRAM {prog_idx+1}:\n"
                for step_idx, step in enumerate(parsed_steps):
                    example_steps_str += (
                        f"  Step {step_idx+1}: {format_step_description(step)}\n"
                    )
            except Exception:
                continue

        # Call the transition agent
        agent_history: List[ModelMessage] = []
        if example_steps_str:
            agent_history.append(
                ModelRequest(parts=[UserPromptPart(content=example_steps_str)])
            )
        agent_history.append(
            ModelRequest(
                parts=[
                    UserPromptPart(content=last_step_desc),
                    UserPromptPart(content=first_step_desc),
                ]
            )
        )

        result = await transition_agent.run(
            "Create 1-2 transition steps to smoothly bridge between the LAST STEP and FIRST STEP, "
            "focusing on gradual changes in intensity, position, and technique. Use the EXAMPLE PROGRAMS as a reference for style and smoothness if provided.",
            message_history=agent_history,
        )

        logger.info(f"Transition agent created {len(result.output.steps)} steps")
        return result.output.steps

    except Exception as e:
        logger.error(f"Error creating transition steps: {e}", exc_info=True)
        # Return empty list on error - no transitions will be added
        raise SplitAndStitchError(f"Failed to create transition steps: {e}") from e


# --- Main Split and Stitch Service ---
class SplitAndStitchService:
    """
    Service for split-and-stitch step generation using AI agents.

    This service implements the split-and-stitch approach:
    1. Generates target sequence from user details
    2. Performs semantic search to find relevant programs
    3. Splits existing programs into segments (front, main, cooling)
    4. Uses AI to select the best segments based on target requirements
    5. Stitches selected segments together with smooth transitions
    """

    def __init__(self, db: Session = None):
        self.db = db
        self.pinecone_index = get_pinecone_index() if db else None

    async def _generate_target_sequence(self, details: MassageProgramDetails) -> AgentRunResult[TargetSequence]:
        """Runs the sequence generator agent."""
        logger.info(
            f"Running sequence generator agent for details: {details.model_dump_json(indent=2, exclude_none=True)}"
        )

        prompt_lines = [
            "Please generate an ideal massage program sequence based on the following user preferences and extracted details:"
        ]

        if details.target_market:
            prompt_lines.append(f"- Target User/Market: {details.target_market}")
        if details.duration:
            prompt_lines.append(f"- Requested Duration: {details.duration} minutes")
        if details.focus_area:
            prompt_lines.append(f"- Key Focus Areas: {', '.join(details.focus_area)}")
        if details.problem_desired_outcome:
            prompt_lines.append(
                f"- Main Goal/Desired Outcome: {details.problem_desired_outcome}"
            )

        if details.program_flow:
            flow_parts = []
            if details.program_flow.highlights:
                flow_parts.append(f"Highlights: {details.program_flow.highlights}")
            if details.program_flow.signature_moves:
                flow_parts.append(
                    f"Signature Moves to Incorporate: {details.program_flow.signature_moves}"
                )
            if details.program_flow.logic_techniques:
                flow_parts.append(
                    f"Preferred Logic/Techniques: {details.program_flow.logic_techniques}"
                )
            if details.program_flow.intensity:
                flow_parts.append(f"Overall Intensity: {details.program_flow.intensity}")
            if flow_parts:
                prompt_lines.append(f"- Program Flow Preferences: {'; '.join(flow_parts)}")

        if details.massage_settings:
            relevant_settings_parts = []
            if details.massage_settings.air_bags and details.focus_area and \
               any(area in ["legs", "feet", "calves", "thighs"] for area in details.focus_area):
                relevant_settings_parts.append(f"Air Bags for leg/foot focus: {details.massage_settings.air_bags}")
            if relevant_settings_parts:
                prompt_lines.append(f"- Consider Specific Massage Settings: {'; '.join(relevant_settings_parts)}")

        user_concerns_prompt = "\n".join(prompt_lines)
        logger.info(
            f"Constructed prompt for sequence_generator_agent: {user_concerns_prompt}"
        )
        try:
            generated_target_sequence_result = await sequence_generator_agent.run(
                user_concerns_prompt
            )

            # Validate the output type
            if not isinstance(generated_target_sequence_result.output, TargetSequence):
                logger.error(
                    f"Sequence generator agent returned unexpected type: {type(generated_target_sequence_result.output)}. "
                    f"Full output: {generated_target_sequence_result.output}"
                )
                raise ValueError("Sequence Agent returned an unexpected data type.")

            if not generated_target_sequence_result.output.to_formatted_string():
                logger.error(
                    "Sequence generator agent returned an empty or invalid TargetSequence."
                )
                raise ValueError(
                    "Sequence Agent returned empty or invalid target sequence."
                )

            logger.info(
                f"Generated target sequence (snippet): {generated_target_sequence_result.output.to_formatted_string()[:100]}..."
            )
            return generated_target_sequence_result # Return the full AgentRunResult
        except Exception as e:
            logger.error(f"Sequence generator agent failed: {e}", exc_info=True)
            raise SplitAndStitchError(
                "Failed to generate target sequence from provided details."
            ) from e

    async def _perform_semantic_search(
        self, query_text: str, product_id: int
    ) -> Tuple[List[Program], List[Dict[str, Any]]]:
        """Generates embedding, queries Pinecone, fetches programs from DB, and formats."""
        # 1. Get Query Embedding
        logger.info("Generating query embedding...")
        try:
            query_vector = get_embedding(
                query_text
            )  # Uses EMBEDDING_MODEL from settings via tasks module
            logger.info("Query embedding generated.")
        except Exception as e:
            logger.error(f"Failed to get query embedding: {e}", exc_info=True)
            raise SplitAndStitchError("Failed to generate embedding for search.") from e

        # 2. Query Pinecone
        logger.info(
            f"Querying Pinecone namespace '{settings.PROGRAM_EMBEDDING_NAMESPACE}'..."
        )
        try:
            query_response = self.pinecone_index.query(
                vector=query_vector,
                top_k=9,
                namespace=settings.PROGRAM_EMBEDDING_NAMESPACE,
                filter={"product_id": product_id},
            )
            logger.info(
                f"Pinecone query completed. Found {len(query_response.matches)} matches."
            )
        except Exception as e:
            logger.error(f"Pinecone query failed: {e}", exc_info=True)
            raise SplitAndStitchError("Failed to query semantic search index.") from e

        matched_ids = [
            int(match.id) for match in query_response.matches if match.id.isdigit()
        ]

        # 3. Fetch Programs from DB and Format
        fetched_programs = []
        programs_data = []
        if matched_ids:
            logger.info(f"Fetching program details from DB for IDs: {matched_ids}")
            try:
                # Fetch programs preserving Pinecone order
                programs_query = self.db.query(Program).filter(
                    Program.id.in_(matched_ids)
                )
                fetched_programs = programs_query.all()
                programs_dict = {p.id: p for p in fetched_programs}

                similarity_scores = {
                    int(match.id): match.score
                    for match in query_response.matches
                    if match.id.isdigit()
                }

                ordered_programs = [
                    programs_dict[p_id] for p_id in matched_ids if p_id in programs_dict
                ]
                logger.info(f"Fetched {len(ordered_programs)} programs from DB.")

                # Format for response
                programs_data = [
                    _format_program_for_response(
                        p, similarity_score=similarity_scores.get(p.id)
                    )
                    for p in ordered_programs
                ]

                # Store the fetched programs to return them
                fetched_programs = ordered_programs

            except Exception as e:
                logger.error(
                    f"Failed to fetch or format programs from DB: {e}", exc_info=True
                )
                # Non-fatal? Or raise SplitAndStitchError? For now, return empty list.
                programs_data = []  # Indicate fetch/format failure
                fetched_programs = []

        else:
            logger.info("No matching program IDs found from Pinecone query.")
            programs_data = []

        return fetched_programs, programs_data

    async def synthesize_steps_with_streaming(
        self,
        details: MassageProgramDetails,
        product_id: int
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """
        Enhanced synthesize_steps that handles target sequence generation and semantic search internally,
        yielding intermediate results for streaming to the client.

        Args:
            details: MassageProgramDetails from the chat agent
            product_id: Product ID for filtering programs

        Yields:
            Dict containing intermediate results (target_sequence, programs, synthesizing status, final steps)
        """
        if not self.db:
            raise SplitAndStitchError("Database session is required for enhanced synthesis")

        try:
            # 1. Generate Target Sequence
            logger.info("Generating target sequence...")
            generated_target_sequence = await self._generate_target_sequence(details)

            yield {
                "type": "target_sequence",
                "content": generated_target_sequence.output.model_dump(),
                "agent_result": generated_target_sequence
            }

            # 2. Perform Semantic Search
            logger.info("Performing semantic search...")
            fetched_programs, programs_data = await self._perform_semantic_search(
                generated_target_sequence.output.to_formatted_string(),
                product_id,
            )

            yield {
                "type": "programs",
                "content": programs_data,
                "fetched_programs": fetched_programs
            }

            # 3. Synthesize steps from top programs
            if fetched_programs and len(fetched_programs) > 0:
                yield {
                    "type": "synthesizing",
                    "content": "Creating a customized program by splitting and stitching the best segments...",
                }

                synthesized_steps = await self.synthesize_steps(
                    fetched_programs, generated_target_sequence.output
                )

                if not synthesized_steps or not synthesized_steps.steps:
                    raise SplitAndStitchError(
                        "Split-and-stitch completed but did not produce any steps."
                    )

                yield {
                    "type": "synthesized_steps",
                    "content": synthesized_steps,
                    "target_sequence": generated_target_sequence.output
                }
            else:
                raise SplitAndStitchError(
                    "Cannot synthesize steps: No relevant programs found to base on."
                )

        except SplitAndStitchError:
            raise
        except Exception as e:
            logger.error(f"Error during enhanced synthesis: {e}", exc_info=True)
            raise SplitAndStitchError(
                f"Enhanced synthesis failed unexpectedly. Details: {str(e)}"
            ) from e

    async def synthesize_steps(
        self, top_programs: List[Program], target_sequence: TargetSequence
    ) -> HighEndProductSteps:
        """
        Orchestrates the segmentation and stitching of steps from top programs.
        Uses hybrid LLM analysis + programmatic segmentation approach.
        """
        logger.info(f"Starting split-and-stitch for {len(top_programs)} programs.")

        if len(top_programs) < 1:
            logger.error("No programs provided for split-and-stitch.")
            return HighEndProductSteps(steps=[])
        if len(top_programs) < 3:
            logger.warning(
                f"Fewer than 3 programs provided ({len(top_programs)}). Split-and-stitch quality may vary."
            )

        programs_to_process = top_programs[:3]
        example_programs = top_programs[-3:]
        segmented_programs = []
        segmentation_errors = []  # Track segmentation errors

        # 1. Extract steps from programs and segment them
        for program in programs_to_process:
            try:
                if not isinstance(program.steps, list):
                    logger.warning(
                        f"Program ID {program.id} has invalid steps format. Skipping."
                    )
                    # Add empty segmented steps for skipped program
                    segmented_programs.append(SegmentedSteps())
                    continue

                # Parse steps into HighEndProductStep objects
                parsed_steps = []
                for step in program.steps:
                    if not isinstance(step, dict):
                        continue
                    try:
                        parsed_step = HighEndProductStep(**step)
                        parsed_steps.append(parsed_step)
                    except Exception as e:
                        logger.warning(
                            f"Failed to parse individual step for program {program.id}: {e}"
                        )
                        continue

                # Segment the steps using LLM analysis + programmatic splitting
                segmented_program = await analyze_and_segment_steps(
                    parsed_steps, program
                )
                segmented_programs.append(segmented_program)

            except Exception as e:
                logger.error(
                    f"Failed to process steps for program {program.id}: {e}",
                    exc_info=True,
                )
                # Track segmentation errors for later reporting
                segmentation_errors.append(f"Program {program.id}: {str(e)}")
                segmented_programs.append(SegmentedSteps())  # Use empty on error

        # Check if we had critical segmentation failures
        if segmentation_errors:
            error_summary = "; ".join(segmentation_errors)
            logger.warning(f"Segmentation errors occurred: {error_summary}")
            # If all programs failed segmentation, raise an error
            if len(segmentation_errors) >= len(programs_to_process):
                raise SplitAndStitchError(
                    f"All program segmentations failed. Errors: {error_summary}"
                )
            # If some programs failed, continue but raise a warning error
            elif len(segmentation_errors) > 0:
                raise SplitAndStitchError(
                    f"Some AI analysis failed, using fallback segmentation. Errors: {error_summary}"
                )

        # Ensure we have at least 3 segmented programs (even if some are empty)
        while len(segmented_programs) < 3:
            segmented_programs.append(SegmentedSteps())

        # 2. Create stitching input
        stitching_input = StitchingInput(
            segmented_program_1=segmented_programs[0],
            segmented_program_2=(
                segmented_programs[1]
                if len(segmented_programs) > 1
                else SegmentedSteps()
            ),
            segmented_program_3=(
                segmented_programs[2]
                if len(segmented_programs) > 2
                else SegmentedSteps()
            ),
            target_sequence=target_sequence,
        )

        # 3. Call the segment selector agent
        try:
            logger.info("Calling segment selector agent...")
            # Convert to a formatted JSON string for better readability by the agent
            formatted_input = stitching_input.model_dump_json(indent=2)
            agent_history: List[ModelMessage] = []
            agent_history.append(
                ModelRequest(parts=[UserPromptPart(content=formatted_input)])
            )
            selection_result = await segment_selector_agent.run(
                "Select the best segments from the three programs based on the target sequence.",
                message_history=agent_history,
            )
            selected_indices = selection_result.output
            logger.info(
                f"Agent selected indices: Front={selected_indices.selected_front_index}, Main={selected_indices.selected_main_index}, Cooling={selected_indices.selected_cooling_index}"
            )

            # 4. Retrieve the selected segments
            selected_segments = []

            # Selected front steps
            if 0 <= selected_indices.selected_front_index < len(segmented_programs):
                front_steps = segmented_programs[
                    selected_indices.selected_front_index
                ].front_steps
                if front_steps:
                    selected_segments.append(("front", front_steps))

            # Selected main steps
            if 0 <= selected_indices.selected_main_index < len(segmented_programs):
                main_steps = segmented_programs[
                    selected_indices.selected_main_index
                ].main_steps
                if main_steps:
                    selected_segments.append(("main", main_steps))

            # Selected cooling steps
            if 0 <= selected_indices.selected_cooling_index < len(segmented_programs):
                cooling_steps = segmented_programs[
                    selected_indices.selected_cooling_index
                ].cooling_steps
                if cooling_steps:
                    selected_segments.append(("cooling", cooling_steps))

            # 5. Create transitions and build final sequence
            final_steps_unrenumbered = []

            for i, (segment_type, segment_steps) in enumerate(selected_segments):
                # Add the segment steps
                if not final_steps_unrenumbered:
                    # First segment - add all steps
                    final_steps_unrenumbered.extend(segment_steps)
                else:
                    # Not the first segment - add transition steps if there are steps in both segments
                    if final_steps_unrenumbered and segment_steps:
                        last_step = final_steps_unrenumbered[-1]
                        first_step = segment_steps[0]

                        # Create transition steps between segments
                        logger.info(f"Creating transition from {segment_type} segment")
                        transition_steps = await create_transition_steps(
                            last_step, first_step, example_programs
                        )
                        final_steps_unrenumbered.extend(transition_steps)

                    # Add the segment steps
                    final_steps_unrenumbered.extend(segment_steps)

            # 6. Renumber the final sequence
            final_steps = []
            for i, step in enumerate(final_steps_unrenumbered):
                # Create a new step object to avoid modifying original segment steps
                updated_step = step.model_copy(deep=True)
                updated_step.step_number = str(i + 1)
                final_steps.append(updated_step)

            logger.info(
                f"Successfully split-and-stitched {len(final_steps)} steps with smooth transitions."
            )
            return HighEndProductSteps(steps=final_steps)

        except SplitAndStitchError:  # Re-raise if it's already our custom error
            raise
        except Exception as e:
            logger.error(
                f"Error during segment selection or final assembly: {e}", exc_info=True
            )
            # Fallback to empty result on error during selection/assembly
            raise SplitAndStitchError(
                f"Failed during segment selection or final assembly: {e}"
            ) from e
