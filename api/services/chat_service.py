import random
import json
import logging
import secrets
from typing import As<PERSON>Generator, Optional, List, Dict, Any, <PERSON><PERSON>
from datetime import datetime

from sqlalchemy.orm import Session
from fastapi import HTTPException
from models import (
    Chat,
    Message,
    ChatConfig,
    Program,
    User,
    ProgramCategory,
    ProgramVersion,
)
from models.program_models import ProgramStatus
from schemas.chat_schemas import (
    MassageProgramDetails,
    TargetSequence,
    HighEndProductSteps,
    HighEndProductStep,
    ProgrammeSequenceDetail
)

from services.chat_agents import (
    massage_agent,
    sft_program_generator_agent_factory
)
from services.step_generation import (
    SplitAndStitchService,
    SFTProgramGenerationService,
    SFTProgramGenerationError,
    SegmentAwareSynthesisService,
    SegmentAwareSynthesisError,
)

from utils.pinecone_ops import get_pinecone_index

# PydanticAI message types
from pydantic_ai.messages import (
    ModelMessage,
    UserPromptPart,
    TextPart,
    ModelResponse,
    ModelRequest,
    ToolCallPart,
    ToolReturnPart,
)


logger = logging.getLogger(__name__)


class ChatServiceError(Exception):
    """Custom exception for chat service errors."""

    pass


class ChatService:
    def __init__(
        self,
        db: Session,
        user: User,
        split_and_stitch_service: SplitAndStitchService,
        sft_program_generation_service: SFTProgramGenerationService,
        segment_aware_synthesis_service: SegmentAwareSynthesisService
    ):
        self.db = db
        self.user = user
        self.pinecone_index = get_pinecone_index()
        self.split_and_stitch_service = split_and_stitch_service
        self.sft_program_generation_service = sft_program_generation_service
        self.segment_aware_synthesis_service = segment_aware_synthesis_service

    @classmethod
    def create_with_default_services(cls, db: Session, user: User) -> "ChatService":
        """
        Factory method to create ChatService with default service implementations.

        Args:
            db: Database session

        Returns:
            ChatService: Instance with default service implementations
        """
        split_and_stitch_service = SplitAndStitchService(db)
        sft_program_service = SFTProgramGenerationService()
        segment_aware_service = SegmentAwareSynthesisService(db)
        return cls(db, user, split_and_stitch_service, sft_program_service, segment_aware_service)

    async def process_chat_message(
        self,
        message_content: str,
        user: User,  # Use the actual user type
        slug: Optional[str],
        chat_config_id: int,
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """
        Processes a user's chat message, interacts with agents, performs searches,
        and yields results as dictionaries for event-stream formatting in the route.
        """
        try:
            # --- 1. Get or Create Chat and Config ---
            chat, chat_config = self._get_or_create_chat_and_config(
                user, slug, chat_config_id
            )

            # --- 2. Load History ---
            agent_history = self._load_chat_history(chat.id)

            # --- 3. Save User Message ---
            self._save_db_messages("user", message_content, chat.id)

            # --- 4. Run Massage Agent (Streaming) ---
            full_assistant_response_text = ""
            structured_output: Optional[MassageProgramDetails] = None

            logger.info(f"Running massage_agent for chat ID: {chat.id}")
            try:
                message_id = random.randint(1, 1000000)
                async with massage_agent.run_stream(
                    message_content, message_history=agent_history
                ) as stream_result:
                    async for delta in stream_result.stream_text(delta=True):
                        yield {
                            "message_id": message_id,
                            "type": "token",
                            "slug": chat.slug,
                            "content": delta,
                        }
                        full_assistant_response_text += delta
                    # Save assistant message
                    if full_assistant_response_text:
                        self._save_db_messages(
                            "assistant", full_assistant_response_text, chat.id
                        )
                    try:
                        structured_output = await stream_result.get_output()
                        tool_messages = stream_result.new_messages()
                        tool_call_part = tool_messages[1]
                        tool_return_part = tool_messages[2]

                        # Save tool_call_part (ModelResponse with ToolCallPart)
                        if (
                            isinstance(tool_call_part, ModelResponse)
                            and tool_call_part.parts
                            and isinstance(tool_call_part.parts[0], ToolCallPart)
                        ):
                            actual_tool_call = tool_call_part.parts[0]
                            tool_call_content_dict = {
                                "tool_name": actual_tool_call.tool_name,
                                "args": actual_tool_call.args,  # args should be a string, often JSON itself
                                "tool_call_id": actual_tool_call.tool_call_id,
                            }
                            self._save_db_messages(
                                role="tool_call",
                                content=json.dumps(tool_call_content_dict),
                                chat_id=chat.id,
                                model_name=tool_call_part.model_name,
                            )
                            logger.info(
                                f"Saved tool_call message for chat ID: {chat.id}, tool_call_id: {actual_tool_call.tool_call_id}"
                            )
                        else:
                            pass

                        # Save tool_return_part (ModelRequest with ToolReturnPart)
                        if (
                            isinstance(tool_return_part, ModelRequest)
                            and tool_return_part.parts
                            and isinstance(tool_return_part.parts[0], ToolReturnPart)
                        ):
                            actual_tool_return = tool_return_part.parts[0]
                            tool_return_content_dict = {
                                "tool_name": actual_tool_return.tool_name,
                                "content": actual_tool_return.content,
                                "tool_call_id": actual_tool_return.tool_call_id,
                            }
                            self._save_db_messages(
                                role="tool_return",
                                content=json.dumps(tool_return_content_dict),
                                chat_id=chat.id,
                            )
                            logger.info(
                                f"Saved tool_return message for chat ID: {chat.id}, tool_call_id: {actual_tool_return.tool_call_id}"
                            )
                        else:
                            pass

                    except Exception:
                        # Text-only response or internal agent error without specific output
                        pass
            except Exception as e:
                logger.error(
                    f"Massage agent execution error for chat {chat.id}: {e}",
                    exc_info=True,
                )
                error_message = "Sorry, I encountered an issue processing your request. Please try again."
                yield {
                    "message_id": random.randint(1, 1000000),
                    "type": "token",
                    "slug": chat.slug,
                    "content": error_message,
                }
                self._save_db_messages("error", error_message, chat.id)

            # --- 5. Process Agent Output ---
            if structured_output and isinstance(structured_output, MassageProgramDetails):
                logger.info(
                    f"Massage agent returned structured output for chat {chat.id}: {structured_output.focus_area}"
                )
                try:
                    massage_program_details_message = self._save_db_messages(
                        "massage_program_details",
                        json.dumps(structured_output.model_dump()),
                        chat.id,
                    )
                    yield {
                        "message_id": massage_program_details_message.id,
                        "type": "massage_program_details",
                        "slug": chat.slug,
                        "content": json.dumps(structured_output.model_dump()),
                    }

                    # NEW CONDITIONAL LOGIC STARTS HERE
                    if chat_config.use_sft_direct_generation and chat_config.sft_model_name:
                        logger.info(f"Attempting SFT direct generation with model: {chat_config.sft_model_name} for chat {chat.id}")
                        try:
                            sft_generated_steps = await self.sft_program_generation_service.generate_steps(
                                program_details=structured_output,
                                sft_model_name=chat_config.sft_model_name
                            )

                            if sft_generated_steps and sft_generated_steps.steps: # Service raises error if no steps
                                logger.info(f"SFT service returned {len(sft_generated_steps.steps)} steps for chat {chat.id}")
                                
                                sft_program_name = f"SFT: {structured_output.problem_desired_outcome or 'Custom Program'}"
                                sft_program_objective = f"Directly generated by SFT model {chat_config.sft_model_name} based on user request: {structured_output.model_dump_json(indent=2, exclude_none=True)[:200]}..."
                                
                                programme_sequence_data = {
                                    "Front Sequence": "Generated by SFT",
                                    "Main Sequence": "Generated by SFT",
                                    "Cooling Sequence": "Generated by SFT"
                                }
                                pseudo_target_sequence_for_sft = TargetSequence(
                                    name=sft_program_name,
                                    objective=sft_program_objective,
                                    programme_sequence=ProgrammeSequenceDetail(**programme_sequence_data),
                                    targeted_acupressure_points=[],
                                    signature_moves=[]
                                )

                                sft_steps_message = self._save_synthesized_program_and_message(
                                    product_id=chat_config.product_id,
                                    target_sequence=pseudo_target_sequence_for_sft,
                                    steps=sft_generated_steps,
                                    chat_id=chat.id,
                                    source_note_prefix="SFT Generated: "
                                )

                                yield {
                                    "message_id": sft_steps_message.id,
                                    "type": "synthesized_steps",
                                    "slug": chat.slug,
                                    "content": sft_steps_message.content,
                                    "program_id": sft_steps_message.program_id,
                                }
                            # No 'else' needed here as service raises SFTProgramGenerationError if steps are not generated
                        
                        except SFTProgramGenerationError as sft_err: # Catch specific error from the service
                            logger.error(f"SFT direct generation failed for chat {chat.id}: {sft_err}", exc_info=True)
                            yield {
                                "message_id": random.randint(1, 1000000),
                                "type": "error",
                                "slug": chat.slug,
                                "content": f"Failed to generate program with specialized model: {str(sft_err)}",
                            }
                            self._save_db_messages("error", f"SFT Generation Error: {str(sft_err)}", chat.id)
                    elif chat_config.synthesis_strategy == "segment-aware-synthesis":
                        logger.info(f"Using segment-aware synthesis for chat {chat.id}")
                        try:
                            async for result in self.segment_aware_synthesis_service.generate_program_steps(
                                structured_output
                            ):
                                if result["type"] == "program_plan":
                                    # Save the program plan as a message
                                    program_plan_message = self._save_db_messages(
                                        "program_plan",
                                        json.dumps(result["content"]),
                                        chat.id,
                                    )
                                    yield {
                                        "message_id": program_plan_message.id,
                                        "type": "program_plan",
                                        "slug": chat.slug,
                                        "content": result["content"],
                                    }
                                # elif result["type"] == "segment_search_results":
                                #     # Save the segment search results as a message
                                #     segment_search_message = self._save_db_messages(
                                #         "segment_search_results",
                                #         json.dumps(result["content"]),
                                #         chat.id,
                                #     )
                                #     yield {
                                #         "message_id": segment_search_message.id,
                                #         "type": "segment_search_results",
                                #         "slug": chat.slug,
                                #         "content": result["content"],
                                #     }
                                # elif result["type"] == "segment_selection":
                                #     # Save the segment selection as a message
                                #     segment_selection_message = self._save_db_messages(
                                #         "segment_selection",
                                #         json.dumps(result["content"]),
                                #         chat.id,
                                #     )
                                #     yield {
                                #         "message_id": segment_selection_message.id,
                                #         "type": "segment_selection",
                                #         "slug": chat.slug,
                                #         "content": result["content"],
                                #     }
                                elif result["type"] == "final_program":
                                    # Handle final program assembly
                                    final_program_message = self._save_synthesized_program_and_message(
                                        product_id=chat_config.product_id,
                                        target_sequence=None,  # We don't have a target sequence in segment-aware synthesis
                                        steps=result["program_steps"],
                                        chat_id=chat.id,
                                        source_note_prefix="Segment-Aware Synthesis: "
                                    )
                                    yield {
                                        "message_id": final_program_message.id,
                                        "type": "synthesized_steps",
                                        "slug": chat.slug,
                                        "content": final_program_message.content,
                                        "program_id": final_program_message.program_id,
                                    }
                                elif result["type"] == "error":
                                    # Handle errors from the service
                                    yield {
                                        "message_id": random.randint(1, 1000000),
                                        "type": "error",
                                        "slug": chat.slug,
                                        "content": result["content"],
                                    }
                        except SegmentAwareSynthesisError as segment_err:
                            logger.error(f"Segment-aware synthesis failed for chat {chat.id}: {segment_err}", exc_info=True)
                            yield {
                                "message_id": random.randint(1, 1000000),
                                "type": "error",
                                "slug": chat.slug,
                                "content": f"Failed to generate program plan: {str(segment_err)}",
                            }
                            self._save_db_messages("error", f"Segment-Aware Synthesis Error: {str(segment_err)}", chat.id)
                    else:
                        logger.info(f"Proceeding with split-and-stitch synthesis for chat {chat.id}")
                        # Use the split-and-stitch service with streaming
                        async for result in self.split_and_stitch_service.synthesize_steps_with_streaming(
                            structured_output, chat_config.product_id
                        ):
                            if result["type"] == "target_sequence":
                                # Save target sequence message
                                target_sequence_message = self._save_db_messages(
                                    "target_sequence",
                                    json.dumps(result["content"]),
                                    chat.id,
                                )
                                yield {
                                    "message_id": target_sequence_message.id,
                                    "type": "target_sequence",
                                    "slug": chat.slug,
                                    "content": result["content"],
                                }

                            elif result["type"] == "programs":
                                # Save programs message
                                program_message = self._save_db_messages(
                                    "programs", json.dumps(result["content"]), chat.id
                                )
                                yield {
                                    "message_id": program_message.id,
                                    "type": "programs",
                                    "slug": chat.slug,
                                    "content": result["content"],
                                }

                            elif result["type"] == "synthesizing":
                                yield {
                                    "message_id": random.randint(1, 1000000),
                                    "type": "synthesizing",
                                    "slug": chat.slug,
                                    "content": result["content"],
                                }

                            elif result["type"] == "synthesized_steps":
                                # Save synthesized steps and create program
                                synthesized_steps_message = (
                                    self._save_synthesized_program_and_message(
                                        product_id=chat_config.product_id,
                                        target_sequence=result["target_sequence"],
                                        steps=result["content"],
                                        chat_id=chat.id,
                                    )
                                )

                                yield {
                                    "message_id": synthesized_steps_message.id,
                                    "type": "synthesized_steps",
                                    "slug": chat.slug,
                                    "content": synthesized_steps_message.content,
                                    "program_id": synthesized_steps_message.program_id,
                                }
                except Exception as processing_err: # Broader catch for this block
                    logger.error(
                        f"Processing pipeline error for chat {chat.id}: {processing_err}",
                        exc_info=True,
                    )
                    # Check if the caught exception is already a ChatServiceError
                    if isinstance(processing_err, ChatServiceError):
                        error_message = str(
                            processing_err
                        )  # Use the specific message from ChatServiceError
                    else:
                        # Fallback for other unexpected exceptions in this block
                        error_message = "Sorry, an unexpected issue occurred while preparing program details. Please try again."

                    yield {
                        "message_id": random.randint(1, 1000000),
                        "type": "error",
                        "slug": chat.slug,
                        "content": error_message,
                    }
                    self._save_db_messages("error", error_message, chat.id)

            else:
                # Agent finished with a text response (clarification)
                logger.info(
                    f"Massage agent returned text clarification for chat {chat.id}"
                )

            logger.info(f"Chat processing finished for chat {chat.id}")

        except ChatServiceError as e:
            logger.error(f"Chat service error: {e}", exc_info=True)
            # Yield a generic error to the client
            yield {
                "type": "error",
                "content": f"An internal error occurred: {e}",
            }
        except HTTPException:
            raise  # Re-raise HTTP exceptions to be handled by FastAPI
        except Exception as e:
            logger.error(f"Unexpected error in chat service: {e}", exc_info=True)
            yield {
                "type": "error",
                "content": "An unexpected internal error occurred. Please try again later.",
            }

    # --- Helper Methods (Private) ---

    def _get_or_create_chat_and_config(
        self, user: User, slug: Optional[str], chat_config_id: int
    ) -> Tuple[Chat, ChatConfig]:
        """Gets or creates a chat session and retrieves its configuration."""
        logger.info(f"Getting/Creating chat for slug: {slug}, config: {chat_config_id}")
        chat = None
        if slug:
            chat = (
                self.db.query(Chat)
                .filter(Chat.slug == slug, Chat.user_id == user.id)
                .first()
            )
            if not chat:
                logger.warning(f"Chat not found for slug: {slug}, user: {user.id}")
                raise HTTPException(
                    status_code=404, detail="Chat not found or not owned by user"
                )
            chat.updated_at = datetime.utcnow()  # Update the timestamp
            logger.info(f"Found existing chat with ID: {chat.id}, updated timestamp.")
        else:
            logger.info(
                f"Creating new chat for user: {user.id}, config: {chat_config_id}"
            )
            chat = Chat(
                slug=secrets.token_urlsafe(32),
                user_id=user.id,
                config_id=chat_config_id,
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow(),
            )
            self.db.add(chat)
            self.db.commit()
            self.db.refresh(chat)
            logger.info(f"Created new chat with ID: {chat.id}, slug: {chat.slug}")

        chat_config = (
            self.db.query(ChatConfig).filter(ChatConfig.id == chat_config_id).first()
        )
        if not chat_config:
            logger.warning(f"Chat config not found: ID {chat_config_id}")
            # Don't raise HTTPException directly from service, raise custom or return None/Tuple
            raise ChatServiceError(f"Chat config ID {chat_config_id} not found.")
        logger.info(
            f"Using chat config ID: {chat_config.id}, Product ID: {chat_config.product_id}"
        )

        return chat, chat_config

    def _load_chat_history(self, chat_id: int) -> List[ModelMessage]:
        """Loads and formats message history for the agent."""
        logger.info(f"Loading message history for chat ID: {chat_id}")
        db_messages = (
            self.db.query(Message)
            .filter(Message.chat_id == chat_id)
            .filter(
                Message.role.notin_(
                    ["target_sequence", "programs", "synthesized_steps", "massage_program_details"]
                )
            )
            .order_by(Message.created_at)
            .all()
        )
        agent_history = self._db_messages_to_agent_history(db_messages)
        logger.info(f"Loaded {len(db_messages)} messages from history.")
        return agent_history

    def _db_messages_to_agent_history(
        self, db_messages: List[Message]
    ) -> List[ModelMessage]:
        agent_history: List[ModelMessage] = []
        for msg in db_messages:
            timestamp = msg.created_at
            if msg.role == "user":
                agent_history.append(
                    ModelRequest(
                        parts=[UserPromptPart(content=msg.content, timestamp=timestamp)]
                    )
                )
            elif msg.role == "assistant" and msg.content:
                agent_history.append(
                    ModelResponse(
                        parts=[TextPart(content=msg.content)],
                        model_name=msg.model_name,
                        timestamp=timestamp,
                    )
                )
            elif msg.role == "tool_call":
                try:
                    data = json.loads(msg.content)
                    agent_history.append(
                        ModelResponse(
                            parts=[
                                ToolCallPart(
                                    tool_name=data["tool_name"],
                                    args=data["args"],
                                    tool_call_id=data["tool_call_id"],
                                )
                            ],
                            model_name=msg.model_name,
                            timestamp=timestamp,
                        )
                    )
                except (json.JSONDecodeError, KeyError):
                    logger.warning(
                        f"Could not parse tool_call message content: {msg.id}"
                    )
            elif msg.role == "tool_return":
                try:
                    data = json.loads(msg.content)
                    agent_history.append(
                        ModelRequest(
                            parts=[
                                ToolReturnPart(
                                    tool_name=data["tool_name"],
                                    content=data["content"],
                                    tool_call_id=data["tool_call_id"],
                                    timestamp=timestamp,
                                )
                            ],
                        )
                    )
                except (json.JSONDecodeError, KeyError):
                    logger.warning(
                        f"Could not parse tool_return message content: {msg.id}"
                    )

        return agent_history

    def _save_db_messages(
        self, role: str, content: str, chat_id: int, model_name: str = None
    ) -> Message:
        """Saves the user message and creates a placeholder assistant message."""
        logger.info(f"Saving user message for chat ID: {chat_id}")
        message = Message(
            role=role,
            content=content,
            chat_id=chat_id,
            created_at=datetime.utcnow(),
        )
        if model_name:
            message.model_name = model_name
        self.db.add(message)
        self.db.commit()
        self.db.refresh(message)
        logger.info(f"Saved message ID: {message.id}")
        return message




    def _save_synthesized_program_and_message(
        self,
        product_id: int,
        target_sequence: TargetSequence = None,
        steps: HighEndProductSteps = None,
        chat_id: int = None,
        source_note_prefix: str = ""
    ) -> Message:
        """Saves the synthesized steps and creates a message."""
        generated_category = self._get_or_create_program_category("generated programs")

        # Your predefined mappings
        predefined_column_mappings = {
            "step_number": "No.",
            "roller_action_description": "ローラー動作内容",
            "air_action_description": "エアー動作内容",
            "kneading_speed": "もみSPD",
            "tapping_speed": "叩きSPD",
            "position_3d": "3D位置",
            "width": "幅",
            # Add other known mappings here if you have them
        }

        # Dynamically generate the full column mapping
        dynamic_column_mapping = {}
        for field_name in HighEndProductStep.model_fields.keys():
            # Use the predefined Japanese name if available, otherwise use the field name itself, title-cased
            dynamic_column_mapping[field_name] = predefined_column_mappings.get(
                field_name, field_name.replace("_", " ").title()
            )

        # Set initial version number
        initial_version_number = 1

        # Handle segment-aware synthesis (no target_sequence)
        if target_sequence is None:
            program_name = "Segment-Aware Generated Program"
            logic_technique = "Segment-Aware Synthesis"
            program_description = {"synthesis_strategy": "segment-aware", "generated_by": "AI"}
            source_notes = f"{source_note_prefix}Generated by AI using segment-aware synthesis."
        else:
            program_name = target_sequence.name
            logic_technique = getattr(target_sequence, "objective", "AI Generated Program")
            program_description = target_sequence.model_dump()
            source_notes = f"{source_note_prefix}Generated by AI. Based on: {target_sequence.objective[:200]}..."

        program = Program(
            product_id=product_id,
            user_id=self.user.id,
            name=program_name,
            logic_technique=logic_technique,
            program_description=program_description,
            category_id=generated_category.id,
            steps=[s.model_dump() for s in steps.steps],
            column_mapping_used=dynamic_column_mapping,
            source_notes=source_notes,
            current_version_number=initial_version_number,  # Set initial version number
            status=ProgramStatus.DRAFT,
        )
        self.db.add(program)
        self.db.flush()  # Flush to get program.id without committing transaction

        # Create initial version record
        initial_version = ProgramVersion(
            program_id=program.id,
            version_number=initial_version_number,
            steps=program.steps,
            version_notes="Initial version created by AI",
        )
        self.db.add(initial_version)
        self.db.commit()
        self.db.refresh(program)

        synthesized_steps_message = Message(
            role="synthesized_steps",
            program_id=program.id,
            content=json.dumps(program.steps),
            chat_id=chat_id,
            created_at=datetime.utcnow(),
        )
        self.db.add(synthesized_steps_message)
        self.db.commit()
        self.db.refresh(synthesized_steps_message)
        logger.info(
            f"Saved synthesized steps message ID: {synthesized_steps_message.id}"
        )
        return synthesized_steps_message

    def _get_or_create_program_category(self, category_name: str) -> ProgramCategory:
        """Gets or creates a program category."""
        category = (
            self.db.query(ProgramCategory)
            .filter(ProgramCategory.name == category_name)
            .first()
        )
        if not category:
            category = ProgramCategory(name=category_name)
            self.db.add(category)
            self.db.commit()
            self.db.refresh(category)
        return category




