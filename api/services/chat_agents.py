import logging
import os

from typing import Union
from pydantic_ai import Agent
from schemas.chat_schemas import (
    MassageProgramDetails,
    TargetSequence,
    HighEndProductSteps
)
from config import settings
from pydantic_ai.models.openai import OpenAIModel
from pydantic_ai.providers.openai import OpenAIProvider

# --- Constants ---
FEW_SHOT_EXAMPLES_PATH = "data/example_sequences/program_sequence_examples.json"
GENERATOR_MODEL_NAME = "gpt-4o-mini"

# --- Logging ---
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


# --- Load Few-Shot Examples ---
few_shot_examples_content = ""
try:
    # Correct path relative to the execution directory (assuming root)
    if os.path.exists(FEW_SHOT_EXAMPLES_PATH):
        with open(FEW_SHOT_EXAMPLES_PATH, "r") as f:
            few_shot_examples_content = f.read()
        logger.info(
            f"Successfully loaded few-shot examples from {FEW_SHOT_EXAMPLES_PATH}"
        )
    else:
        # Use absolute path for clarity in error message
        abs_path = os.path.abspath(FEW_SHOT_EXAMPLES_PATH)
        raise FileNotFoundError(
            f"Few-shot examples file not found at expected path: {abs_path}"
        )
except Exception as e:
    logger.error(f"Error loading few-shot examples: {e}", exc_info=True)
    raise


# Define the agent with explicit provider
massage_agent: Agent[None, Union[MassageProgramDetails, str]] = Agent(
    model=OpenAIModel(
        "gpt-4o", provider=OpenAIProvider(api_key=settings.OPENAI_API_KEY)
    ),
    output_type=Union[MassageProgramDetails, str],
    system_prompt="""
You are an AI assistant specialized in understanding user requests for custom massage programs.
Your primary task is to carefully read the user's chat message and extract all relevant details to populate the `MassageProgramDetails` model.

CRITICAL REQUIREMENTS:

1. COMPULSORY FIELDS (must be provided or ask for clarification):
   - Problem/Desired Outcome: REQUIRED - The main goal or problem the user wants the massage to address. If not provided, ask the user to specify their desired outcome.
   - Duration: REQUIRED - Must be either "15" or "30" minutes only. If user mentions other durations, ask them to choose between 15 or 30 minutes.

2. FIELDS WITH AUTOMATIC DEFAULTS (apply if not specified):
   - Target Market: Defaults to "18 to 65" if not specified by user
   - Focus Area: Defaults to ["Full body"] if not specified by user

3. ENUM CONSTRAINTS (must use exact values):
   - Duration: Only "15" or "30" (minutes)
   - Intensity: Only "weak", "medium", or "strong"
   - Warm Air: "Off" (default), "Low", or "High" (only if user explicitly mentions turning on warm air)
   - Vibration: "Auto" (default), "Low", "High", or "Off"
   - Air Bags: "Wide" or "Narrow" (if mentioned)

4. MASSAGE SETTINGS DEFAULTS:
   - Warm Air: Default "Off" - only set to "Low" or "High" if user explicitly mentions turning on warm air
   - Vibration: Default "Auto" - can be changed to "Low", "High", or "Off" based on user preference
   - Scent: Default True (on)
   - Mood Light: Default True (on)
   - Air Bags: Only set if user mentions preference for "Wide" or "Narrow"

5. OPTIONAL FIELDS (can be null if not mentioned):
   - Program Flow (Highlights, Signature Moves, Logic/Techniques)

VALIDATION RULES:
- If Problem/Desired Outcome is missing, return a string asking: "I need to understand your goal for this massage. What problem would you like to address or what outcome are you hoping to achieve? (e.g., pain relief, relaxation, muscle recovery, stress reduction)"
- If Duration is not 15 or 30 minutes, return a string asking: "Please choose a duration of either 15 or 30 minutes for your massage program."
- If Intensity is mentioned but not "weak", "medium", or "strong", ask for clarification using these exact terms.

IMPORTANT:
- Only respond to questions related to massage routines or programs.
- If a user asks a question that is unrelated to massage, respond with: "I'm here to help you create a massage program. Please tell me the area(s) you'd like to focus on, how long you'd like it to be, and the level of intensity you're looking for!"
""",
    # Add retries if desired
    retries=1,
)


# --- Sequence Generator Agent ---
sequence_generator_agent: Agent[str, TargetSequence] = Agent(
    model=OpenAIModel(
        GENERATOR_MODEL_NAME,
        provider=OpenAIProvider(api_key=settings.OPENAI_API_KEY),
    ),
    output_type=TargetSequence,
    system_prompt=f"""
You are an expert massage program designer. Your task is to generate a concise description of an *ideal* massage program sequence tailored to specific user focus areas.

You MUST output the result using the `TargetSequence` structured format.
This requires providing values for:
1.  `objective`: A string describing the overall goal.
2.  `programme_sequence`: An object containing the sequence details. This object MUST have the following string keys and values:
    - `"Front Sequence"`: Description of the initial phase.
    - `"Main Sequence"`: Description of the main focus phase.
    - `"Cooling Sequence"`: Description of the concluding phase.
3.  `signature_moves`: A list of 2-4 key massage technique strings.

Ensure ALL THREE main fields (`objective`, `programme_sequence`, `signature_moves`) and ALL THREE sub-fields within `programme_sequence` are present in your final JSON output.

Use the following JSON examples ONLY as a guide for the desired output structure and style. DO NOT simply copy them. Generate a NEW, UNIQUE sequence description based *specifically* on the User Concerns provided in the user prompt.

Examples (JSON format):
```json
{few_shot_examples_content}
```
""",
    retries=1,
)

def sft_program_generator_agent_factory(model_name: str) -> Agent:
    llm_instance = OpenAIModel(
        model_name,
        provider=OpenAIProvider(api_key=settings.OPENAI_API_KEY),
    )
    agent = Agent(
        model=llm_instance,
        output_schema=HighEndProductSteps,
        description=f"Agent specialized in generating full massage program steps using the {model_name} model."
    )
    return agent