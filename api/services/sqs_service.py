import json
import logging
import boto3
from typing import Dict, Any, Optional
from botocore.exceptions import Client<PERSON>rror

from config.settings import settings

logger = logging.getLogger(__name__)


class SQSService:
    """Service for managing SQS operations for segmentation jobs."""
    
    def __init__(self):
        self.sqs_client = boto3.client(
            'sqs',
            region_name=settings.AWS_REGION,
            aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
            aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY
        )
        self.queue_url = settings.SEGMENTATION_QUEUE_URL
        self.dlq_url = settings.SEGMENTATION_DLQ_URL
    
    def send_segmentation_job(self, program_id: int, job_id: int, product_job_id: Optional[int] = None) -> Optional[str]:
        """
        Send a segmentation job message to SQS.

        Args:
            program_id: ID of the program to segment
            job_id: ID of the segmentation job for tracking
            product_job_id: Optional ID of the parent product segmentation job

        Returns:
            Message ID if successful, None if failed
        """
        if not self.queue_url:
            logger.error("SEGMENTATION_QUEUE_URL not configured")
            return None

        message_body = {
            "job_type": "segmentation",
            "program_id": program_id,
            "job_id": job_id
        }

        if product_job_id:
            message_body["product_job_id"] = product_job_id

        message_attributes = {
            'JobType': {
                'StringValue': 'segmentation',
                'DataType': 'String'
            },
            'ProgramId': {
                'StringValue': str(program_id),
                'DataType': 'Number'
            },
            'JobId': {
                'StringValue': str(job_id),
                'DataType': 'Number'
            }
        }

        if product_job_id:
            message_attributes['ProductJobId'] = {
                'StringValue': str(product_job_id),
                'DataType': 'Number'
            }

        try:
            response = self.sqs_client.send_message(
                QueueUrl=self.queue_url,
                MessageBody=json.dumps(message_body),
                MessageAttributes=message_attributes
            )

            message_id = response.get('MessageId')
            logger.info(f"Sent segmentation job to SQS: program_id={program_id}, job_id={job_id}, product_job_id={product_job_id}, message_id={message_id}")
            return message_id

        except ClientError as e:
            logger.error(f"Failed to send segmentation job to SQS: {e}")
            return None

    def send_product_segmentation_job(self, product_id: int, job_id: int) -> Optional[str]:
        """
        Send a product segmentation job message to SQS.

        Args:
            product_id: ID of the product to segment all programs for
            job_id: ID of the product segmentation job for tracking

        Returns:
            Message ID if successful, None if failed
        """
        if not self.queue_url:
            logger.error("SEGMENTATION_QUEUE_URL not configured")
            return None

        message_body = {
            "job_type": "product_segmentation",
            "product_id": product_id,
            "job_id": job_id
        }

        try:
            response = self.sqs_client.send_message(
                QueueUrl=self.queue_url,
                MessageBody=json.dumps(message_body),
                MessageAttributes={
                    'JobType': {
                        'StringValue': 'product_segmentation',
                        'DataType': 'String'
                    },
                    'ProductId': {
                        'StringValue': str(product_id),
                        'DataType': 'Number'
                    },
                    'JobId': {
                        'StringValue': str(job_id),
                        'DataType': 'Number'
                    }
                }
            )

            message_id = response.get('MessageId')
            logger.info(f"Sent product segmentation job to SQS: product_id={product_id}, job_id={job_id}, message_id={message_id}")
            return message_id

        except ClientError as e:
            logger.error(f"Failed to send product segmentation job to SQS: {e}")
            return None
    
    def receive_messages(self, max_messages: int = 1, wait_time: int = 20) -> list:
        """
        Receive messages from the segmentation queue.

        Args:
            max_messages: Maximum number of messages to receive (1-10)
            wait_time: Long polling wait time in seconds (0-20)

        Returns:
            List of message dictionaries
        """
        if not self.queue_url:
            logger.error("SEGMENTATION_QUEUE_URL not configured")
            return []

        try:
            response = self.sqs_client.receive_message(
                QueueUrl=self.queue_url,
                MaxNumberOfMessages=min(max_messages, 10),
                WaitTimeSeconds=min(wait_time, 20),
                MessageAttributeNames=['All'],
                AttributeNames=['All']
            )

            messages = response.get('Messages', [])
            logger.debug(f"Received {len(messages)} messages from SQS")
            return messages

        except ClientError as e:
            logger.error(f"Failed to receive messages from SQS: {e}")
            logger.error(f"Error details: {e.response}")
            return []
    
    def delete_message(self, receipt_handle: str) -> bool:
        """
        Delete a message from the queue after successful processing.
        
        Args:
            receipt_handle: Receipt handle of the message to delete
            
        Returns:
            True if successful, False otherwise
        """
        if not self.queue_url:
            logger.error("SEGMENTATION_QUEUE_URL not configured")
            return False
            
        try:
            self.sqs_client.delete_message(
                QueueUrl=self.queue_url,
                ReceiptHandle=receipt_handle
            )
            logger.debug(f"Deleted message from SQS: {receipt_handle}")
            return True
            
        except ClientError as e:
            logger.error(f"Failed to delete message from SQS: {e}")
            return False
    
    def change_message_visibility(self, receipt_handle: str, visibility_timeout: int) -> bool:
        """
        Change the visibility timeout of a message (useful for extending processing time).
        
        Args:
            receipt_handle: Receipt handle of the message
            visibility_timeout: New visibility timeout in seconds (0-43200)
            
        Returns:
            True if successful, False otherwise
        """
        if not self.queue_url:
            logger.error("SEGMENTATION_QUEUE_URL not configured")
            return False
            
        try:
            self.sqs_client.change_message_visibility(
                QueueUrl=self.queue_url,
                ReceiptHandle=receipt_handle,
                VisibilityTimeout=min(visibility_timeout, 43200)
            )
            logger.debug(f"Changed message visibility: {receipt_handle}, timeout={visibility_timeout}")
            return True
            
        except ClientError as e:
            logger.error(f"Failed to change message visibility: {e}")
            return False
    
    def get_queue_attributes(self) -> Dict[str, Any]:
        """
        Get queue attributes for monitoring.

        Returns:
            Dictionary of queue attributes
        """
        if not self.queue_url:
            logger.error("SEGMENTATION_QUEUE_URL not configured")
            return {}

        try:
            response = self.sqs_client.get_queue_attributes(
                QueueUrl=self.queue_url,
                AttributeNames=[
                    'ApproximateNumberOfMessages',
                    'ApproximateNumberOfMessagesNotVisible',
                    'ApproximateNumberOfMessagesDelayed',
                    'VisibilityTimeout',
                    'MessageRetentionPeriod',
                    'RedrivePolicy'
                ]
            )

            return response.get('Attributes', {})

        except ClientError as e:
            logger.error(f"Failed to get queue attributes: {e}")
            return {}

    def get_dlq_attributes(self) -> Dict[str, Any]:
        """
        Get dead letter queue attributes for monitoring.

        Returns:
            Dictionary of DLQ attributes
        """
        if not self.dlq_url:
            logger.warning("SEGMENTATION_DLQ_URL not configured")
            return {}

        try:
            response = self.sqs_client.get_queue_attributes(
                QueueUrl=self.dlq_url,
                AttributeNames=[
                    'ApproximateNumberOfMessages',
                    'ApproximateNumberOfMessagesNotVisible',
                    'ApproximateNumberOfMessagesDelayed'
                ]
            )

            return response.get('Attributes', {})

        except ClientError as e:
            logger.error(f"Failed to get DLQ attributes: {e}")
            return {}


# Global instance
sqs_service = SQSService()
