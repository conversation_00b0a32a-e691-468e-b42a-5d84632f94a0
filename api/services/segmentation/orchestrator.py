import asyncio
import logging
from typing import List, Dict, Any, Optional
from sqlalchemy.orm import Session
from datetime import datetime, timezone

from models.program_models import Program, ProgramSegment, SegmentationJob, SegmentationJobStatus
from schemas.segmentation_schemas import SegmentOntology, MacroPhaseSegmentation, MicroChunkDiscovery
from services.segmentation.macro_phase_agent import get_macro_phases
from services.segmentation.micro_chunk_agent import discover_micro_chunks
from services.segmentation.tagging_agent import get_segment_ontology
from utils.program_helpers import extract_program_context
from tasks.embedding_tasks import batch_upsert_segment_embeddings
from config.settings import settings

logger = logging.getLogger(__name__)


class SegmentationOrchestrator:
    """
    Segmentation orchestrator that processes macro phases and micro chunks
    concurrently for significant performance improvements.
    """

    def __init__(self, db_session: Session, enable_embedding: bool = True, job_id: Optional[int] = None):
        self.db = db_session
        self.enable_embedding = enable_embedding
        self.job_id = job_id
        self._cancelled = False
        self.max_concurrent_phases = settings.MAX_CONCURRENT_PHASES
        self.max_concurrent_chunks = settings.MAX_CONCURRENT_CHUNKS

        # Global semaphores for true parallelism
        self.global_chunk_semaphore = asyncio.Semaphore(self.max_concurrent_chunks)

        logger.info(f"Initialized SegmentationOrchestrator with MAX_CONCURRENT_PHASES={self.max_concurrent_phases}, MAX_CONCURRENT_CHUNKS={self.max_concurrent_chunks}")

        # Progress tracking
        self._completed_phases = 0
        self._total_phases = 0
        self._completed_chunks = 0
        self._total_chunks = 0
        self._progress_lock = asyncio.Lock()
    
    def _check_cancellation(self) -> bool:
        """Check if the job has been cancelled."""
        if not self.job_id:
            return False
            
        job = self.db.query(SegmentationJob).filter(SegmentationJob.id == self.job_id).first()
        if job and job.status == SegmentationJobStatus.CANCELLED:
            self._cancelled = True
            logger.info(f"Job {self.job_id} has been cancelled")
            return True
        return False
    
    def _update_job_progress(
        self,
        status: Optional[SegmentationJobStatus] = None,
        progress: Optional[int] = None,
        current_step: Optional[str] = None,
        completed_steps: Optional[int] = None,
        error_message: Optional[str] = None,
    ):
        """Update job progress in database."""
        if not self.job_id:
            return

        job = self.db.query(SegmentationJob).filter(SegmentationJob.id == self.job_id).first()
        if not job:
            return

        if status is not None:
            job.status = status
            if status == SegmentationJobStatus.RUNNING and not job.started_at:
                job.started_at = datetime.now(timezone.utc)
            elif status in [SegmentationJobStatus.COMPLETED, SegmentationJobStatus.FAILED, SegmentationJobStatus.CANCELLED]:
                job.completed_at = datetime.now(timezone.utc)

        if progress is not None:
            job.progress_percentage = progress
        if current_step is not None:
            job.current_step = current_step
        if completed_steps is not None:
            job.completed_steps = completed_steps
        if error_message is not None:
            job.error_message = error_message

        self.db.commit()

    async def _update_parallel_progress(self, phase_completed: bool = False, chunk_completed: bool = False):
        """Update progress based on completed parallel work."""
        async with self._progress_lock:
            if phase_completed:
                self._completed_phases += 1
            if chunk_completed:
                self._completed_chunks += 1

            # Calculate progress: 20% for macro analysis, 70% for processing, 10% for embeddings
            if self._total_phases > 0:
                phase_progress = (self._completed_phases / self._total_phases) * 70
                chunk_progress = 0
                if self._total_chunks > 0:
                    chunk_progress = (self._completed_chunks / self._total_chunks) * 70

                # Use the more granular progress (chunks are more frequent updates)
                processing_progress = max(phase_progress, chunk_progress)
                total_progress = min(20 + processing_progress, 90)  # Cap at 90% until embeddings

                current_step = f"Processing: {self._completed_phases}/{self._total_phases} phases, {self._completed_chunks}/{self._total_chunks} chunks"

                self._update_job_progress(
                    progress=int(total_progress),
                    current_step=current_step,
                    completed_steps=self._completed_phases + self._completed_chunks
                )

    async def process_program(self, program: Program):
        """
        Process a program with parallel macro phase and micro chunk processing.
        """
        logger.info(f"Starting segmentation for Program ID: {program.id}...")

        # Update job status to running
        self._update_job_progress(
            status=SegmentationJobStatus.RUNNING,
            current_step="Starting macro-phase analysis",
            progress=5
        )

        try:
            # Extract program context for enhanced tagging
            program_context = extract_program_context(program)
            logger.info(f"Extracted program context: {program_context.get('program_name', 'Unknown')}")

            # 1. Get Macro-Phases (sequential - needed for planning)
            self._update_job_progress(current_step="Analyzing macro-phases", progress=10)

            if self._check_cancellation():
                return
                
            macro_segmentation = await get_macro_phases(program.steps, program.program_description)
            logger.info(f"Identified {len(macro_segmentation.phases)} macro-phases for Program ID: {program.id}")

            # Calculate total work for progress tracking
            self._total_phases = len(macro_segmentation.phases)

            # We'll update total_chunks after discovering them, but estimate for now
            estimated_micro_chunks = self._total_phases * 4  # Estimate 4 micro chunks per phase
            total_work_units = self._total_phases + estimated_micro_chunks

            if self.job_id:
                job = self.db.query(SegmentationJob).filter(SegmentationJob.id == self.job_id).first()
                if job:
                    job.total_steps = total_work_units
                    self.db.commit()

            # 2. Process all macro phases in parallel
            self._update_job_progress(
                current_step="Processing macro-phases",
                progress=20
            )
            
            if self._check_cancellation():
                return
            
            # Create semaphore to limit concurrent phases
            phase_semaphore = asyncio.Semaphore(self.max_concurrent_phases)
            
            # Process all phases concurrently
            phase_tasks = []
            for i, macro_phase in enumerate(macro_segmentation.phases):
                task = self._process_macro_phase_parallel(
                    program.id, macro_phase, program.steps, phase_semaphore, i, program_context
                )
                phase_tasks.append(task)
            
            # Wait for all phases to complete
            phase_results = await asyncio.gather(*phase_tasks, return_exceptions=True)

            # Commit all segments to database after parallel processing
            try:
                self.db.commit()
                logger.info(f"Successfully committed all segments for Program ID: {program.id}")
            except Exception as e:
                logger.error(f"Failed to commit segments: {e}")
                self.db.rollback()
                raise

            # Check for any failures
            failed_phases = [r for r in phase_results if isinstance(r, Exception)]
            if failed_phases:
                error_msg = f"Failed to process {len(failed_phases)} macro phases: {failed_phases[0]}"
                logger.error(error_msg)
                self._update_job_progress(
                    status=SegmentationJobStatus.FAILED,
                    error_message=error_msg
                )
                return

            # Update job total_steps with actual chunk count
            if self.job_id and self._total_chunks > 0:
                job = self.db.query(SegmentationJob).filter(SegmentationJob.id == self.job_id).first()
                if job:
                    job.total_steps = self._total_phases + self._total_chunks
                    self.db.commit()
            
            # 3. Batch generate embeddings for all segments
            if self.enable_embedding:
                self._update_job_progress(
                    current_step="Generating embeddings in batch",
                    progress=90
                )
                await self._batch_generate_embeddings(program.id)

            self.db.commit()
            logger.info(f"Successfully processed all segments for Program ID: {program.id}")

            # Mark job as completed
            self._update_job_progress(
                status=SegmentationJobStatus.COMPLETED,
                progress=100,
                current_step="Segmentation completed successfully"
            )

        except Exception as e:
            logger.error(f"Error in segmentation for program {program.id}: {e}", exc_info=True)
            self._update_job_progress(
                status=SegmentationJobStatus.FAILED,
                error_message=str(e)
            )
            raise
    
    async def _process_macro_phase_parallel(
        self,
        program_id: int,
        macro_phase,
        all_steps: List[Dict[str, Any]],
        semaphore: asyncio.Semaphore,
        phase_index: int,
        program_context: Dict[str, Any]
    ) -> ProgramSegment:
        """Process a single macro phase and its micro chunks in parallel."""
        async with semaphore:
            if self._check_cancellation():
                return None
                
            logger.info(f"Processing macro-phase {phase_index + 1}: '{macro_phase.phase_name}'")
            
            macro_phase_steps = all_steps[macro_phase.start_index : macro_phase.end_index + 1]
            
            # Process macro phase tagging and micro chunk discovery in parallel
            macro_tag_task = get_segment_ontology(macro_phase_steps, program_context)
            micro_discovery_task = discover_micro_chunks(macro_phase_steps)
            
            # Wait for both to complete
            macro_ontology, micro_chunk_discovery = await asyncio.gather(
                macro_tag_task, micro_discovery_task
            )
            
            # Save macro phase segment
            parent_segment = self._save_segment(
                program_id=program_id,
                phase_name=macro_phase.phase_name,
                ontology=macro_ontology,
                steps=macro_phase_steps,
                parent_id=None,
                description=f"Macro-phase: {macro_phase.phase_name}"
            )
            
            logger.info(f"Saved macro-phase '{parent_segment.phase}' with {len(micro_chunk_discovery.chunks)} micro-chunks")

            # Update total chunks count for this phase
            chunk_count = len(micro_chunk_discovery.chunks)
            async with self._progress_lock:
                self._total_chunks += chunk_count

            # Process micro chunks in parallel using global semaphore for true parallelism
            chunk_tasks = []

            # Calculate semantic positions for micro chunks
            total_chunks = len(micro_chunk_discovery.chunks)
            semantic_positions = self._calculate_semantic_positions(total_chunks)

            for i, micro_chunk in enumerate(micro_chunk_discovery.chunks):
                semantic_position = semantic_positions[i]
                task = self._process_micro_chunk_parallel(
                    program_id, micro_chunk, macro_phase_steps, parent_segment.id, self.global_chunk_semaphore, program_context, semantic_position
                )
                chunk_tasks.append(task)

            # Wait for all micro chunks to complete
            if chunk_tasks:
                await asyncio.gather(*chunk_tasks, return_exceptions=True)

            # Report phase completion
            await self._update_parallel_progress(phase_completed=True)

            return parent_segment
    
    async def _process_micro_chunk_parallel(
        self,
        program_id: int,
        micro_chunk,
        macro_phase_steps: List[Dict[str, Any]],
        parent_segment_id: int,
        semaphore: asyncio.Semaphore,
        program_context: Dict[str, Any],
        position_in_parent: str
    ):
        """Process a single micro chunk in parallel."""
        async with semaphore:
            if self._check_cancellation():
                return None

            logger.info(f"Processing micro-chunk for parent_segment_id={parent_segment_id}, position={position_in_parent}")
            micro_chunk_steps = macro_phase_steps[micro_chunk.start_index : micro_chunk.end_index + 1]
            
            # Tag the micro chunk
            micro_ontology = await get_segment_ontology(micro_chunk_steps, program_context)
            
            # Save micro chunk segment
            micro_segment = self._save_segment(
                program_id=program_id,
                phase_name="discovered",
                ontology=micro_ontology,
                steps=micro_chunk_steps,
                parent_id=parent_segment_id,
                description=micro_chunk.description,  # Capture LLM-generated description
                position_in_parent=position_in_parent
            )

            # Report chunk completion
            await self._update_parallel_progress(chunk_completed=True)

            return micro_segment
    
    async def _batch_generate_embeddings(self, program_id: int):
        """Generate embeddings for all segments using proper bulk embedding."""
        if not self.enable_embedding:
            return

        # Get all segment IDs for this program
        segments = self.db.query(ProgramSegment).filter(
            ProgramSegment.original_program_id == program_id
        ).all()

        segment_ids = [segment.id for segment in segments]

        if not segment_ids:
            logger.info("No segments found for embedding generation")
            return

        logger.info(f"Starting bulk embedding generation for {len(segment_ids)} segments")

        # Use the proper bulk embedding function in a thread pool to avoid blocking
        # This function handles batching, API optimization, and Pinecone bulk operations
        await asyncio.get_event_loop().run_in_executor(
            None,
            batch_upsert_segment_embeddings,
            segment_ids,
            10  # batch_size - same as program embeddings
        )

        logger.info(f"Completed bulk embedding generation for {len(segment_ids)} segments")

    def _calculate_semantic_positions(self, total_chunks: int) -> List[str]:
        """
        Calculate semantic position labels for micro chunks within a macro phase.

        Args:
            total_chunks: Total number of micro chunks in the macro phase

        Returns:
            List of semantic position labels ("front", "mid", "last")
        """
        if total_chunks == 1:
            return ["front"]
        elif total_chunks == 2:
            return ["front", "last"]
        elif total_chunks == 3:
            return ["front", "mid", "last"]
        else:
            # For 4+ chunks: first is "front", last is "last", middle ones are "mid"
            positions = ["front"]
            for i in range(1, total_chunks - 1):
                positions.append("mid")
            positions.append("last")
            return positions

    def _save_segment(
        self,
        program_id: int,
        phase_name: str,
        ontology: SegmentOntology,
        steps: List[Dict[str, Any]],
        parent_id: int = None,
        description: str = None,
        position_in_parent: str = None,
    ) -> ProgramSegment:
        """Save a segment to database (optimized for batch processing)."""
        duration = len(steps)

        segment = ProgramSegment(
            original_program_id=program_id,
            phase=phase_name,
            parent_segment_id=parent_id,
            position_in_parent=position_in_parent,
            entry_state=ontology.entry_state,
            exit_state=ontology.exit_state,
            purpose_tags=ontology.purpose_tags,
            technique_tags=ontology.technique_tags,
            body_part_tags=ontology.body_part_tags,
            intensity_score=ontology.intensity_score,
            duration_seconds=duration,
            description=description,  # Save LLM-generated description
            steps=steps,
        )
        self.db.add(segment)
        # Don't commit here - let the caller handle batched commits
        self.db.flush()  # Flush to get the ID without committing
        self.db.refresh(segment)
        return segment
