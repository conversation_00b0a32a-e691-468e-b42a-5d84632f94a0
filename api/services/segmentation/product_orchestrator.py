import logging
import asyncio
from typing import List, Optional
from sqlalchemy.orm import Session
from datetime import datetime, timezone

from models.program_models import (
    Program,
    ProductSegmentationJob,
    SegmentationJob,
    SegmentationJobStatus,
    ProgramSegment
)
from services.sqs_service import sqs_service
from utils.program_helpers import should_segment_program

logger = logging.getLogger(__name__)


class ProductSegmentationOrchestrator:
    """
    Orchestrates segmentation for all programs within a product.
    Creates individual program segmentation jobs and tracks overall progress.
    """
    
    def __init__(self, db: Session, job_id: int):
        self.db = db
        self.job_id = job_id
        
    def _update_job_progress(
        self,
        status: Optional[SegmentationJobStatus] = None,
        current_step: Optional[str] = None,
        progress: Optional[int] = None,
        completed_programs: Optional[int] = None,
        failed_programs: Optional[int] = None,
        error_message: Optional[str] = None
    ):
        """Update the product segmentation job progress."""
        if not self.job_id:
            return
            
        job = self.db.query(ProductSegmentationJob).filter(
            ProductSegmentationJob.id == self.job_id
        ).first()
        
        if not job:
            logger.warning(f"Product segmentation job {self.job_id} not found")
            return
            
        if status is not None:
            job.status = status
            if status == SegmentationJobStatus.RUNNING and not job.started_at:
                job.started_at = datetime.now(timezone.utc)
            elif status in [SegmentationJobStatus.COMPLETED, SegmentationJobStatus.FAILED, SegmentationJobStatus.CANCELLED]:
                job.completed_at = datetime.now(timezone.utc)
                
        if current_step is not None:
            job.current_step = current_step
            
        if progress is not None:
            job.progress_percentage = min(100, max(0, progress))
            
        if completed_programs is not None:
            job.completed_programs = completed_programs
            
        if failed_programs is not None:
            job.failed_programs = failed_programs
            
        if error_message is not None:
            job.error_message = error_message
            
        try:
            self.db.commit()
            logger.debug(f"Updated product job {self.job_id}: status={status}, progress={progress}%, step='{current_step}'")
        except Exception as e:
            logger.error(f"Failed to update product job {self.job_id}: {e}")
            self.db.rollback()
    
    def _check_cancellation(self) -> bool:
        """Check if the product segmentation job has been cancelled."""
        if not self.job_id:
            return False
            
        job = self.db.query(ProductSegmentationJob).filter(
            ProductSegmentationJob.id == self.job_id
        ).first()
        
        if job and job.status == SegmentationJobStatus.CANCELLED:
            logger.info(f"Product segmentation job {self.job_id} was cancelled")
            return True
            
        return False
    
    async def process_product(self, product_id: int):
        """
        Process all programs in a product for segmentation.
        Creates individual segmentation jobs for each program.
        """
        logger.info(f"Starting product segmentation for Product ID: {product_id}...")
        
        try:
            # Update job status to running
            self._update_job_progress(
                status=SegmentationJobStatus.RUNNING,
                current_step="Initializing product segmentation",
                progress=5
            )
            
            # Check for cancellation
            if self._check_cancellation():
                return
            
            # Get all programs for this product
            all_programs_query = self.db.query(Program).filter(
                Program.product_id == product_id
            ).all()

            # Filter out programs that should not be segmented based on category
            all_programs = [program for program in all_programs_query if should_segment_program(program)]

            if not all_programs:
                logger.warning(f"No programs found for product {product_id}")
                self._update_job_progress(
                    status=SegmentationJobStatus.COMPLETED,
                    current_step="No programs found to segment",
                    progress=100
                )
                return

            # Filter out programs that already have segments (smart resume)
            programs_to_process = []
            programs_already_segmented = []

            for program in all_programs:
                # Check if program already has segments
                existing_segments = self.db.query(ProgramSegment).filter(
                    ProgramSegment.original_program_id == program.id
                ).first()

                if existing_segments:
                    programs_already_segmented.append(program)
                    logger.info(f"Program {program.id} already has segments, skipping")
                else:
                    programs_to_process.append(program)

            total_programs = len(all_programs)
            programs_to_segment = len(programs_to_process)
            programs_skipped = len(programs_already_segmented)

            logger.info(f"Product {product_id} analysis: {total_programs} total programs, {programs_to_segment} to segment, {programs_skipped} already segmented")

            if programs_to_segment == 0:
                logger.info(f"All programs in product {product_id} are already segmented")
                self._update_job_progress(
                    status=SegmentationJobStatus.COMPLETED,
                    current_step=f"All {total_programs} programs already segmented",
                    progress=100
                )
                return
            
            # Update total programs count
            self._update_job_progress(
                current_step=f"Found {total_programs} programs to segment",
                progress=10
            )
            
            # Update the job with total programs count (use programs_to_segment for accurate tracking)
            job = self.db.query(ProductSegmentationJob).filter(
                ProductSegmentationJob.id == self.job_id
            ).first()
            if job:
                job.total_programs = programs_to_segment  # Only count programs that need segmentation
                self.db.commit()

            completed_programs = 0
            failed_programs = 0

            # Process each program that needs segmentation
            for i, program in enumerate(programs_to_process):
                if self._check_cancellation():
                    return
                
                try:
                    logger.info(f"Processing program {program.id} ({i+1}/{programs_to_segment})")

                    # Update progress
                    progress = 10 + int((i / programs_to_segment) * 80)  # 10-90% for processing
                    self._update_job_progress(
                        current_step=f"Processing program {program.id}: {program.program_title or program.name} ({i+1}/{programs_to_segment})",
                        progress=progress
                    )
                    
                    # Check if there's already a running job for this program
                    existing_job = self.db.query(SegmentationJob).filter(
                        SegmentationJob.program_id == program.id,
                        SegmentationJob.status.in_([SegmentationJobStatus.PENDING, SegmentationJobStatus.RUNNING])
                    ).first()
                    
                    if existing_job:
                        logger.warning(f"Skipping program {program.id} - segmentation already running")
                        continue
                    
                    # Create individual program segmentation job
                    program_job = SegmentationJob(
                        program_id=program.id,
                        product_job_id=self.job_id,  # Link to parent product job
                        status=SegmentationJobStatus.PENDING,
                        current_step="Queuing program segmentation job"
                    )
                    self.db.add(program_job)
                    self.db.commit()
                    self.db.refresh(program_job)
                    
                    # Send individual program job to SQS
                    message_id = sqs_service.send_segmentation_job(
                        program.id, 
                        program_job.id, 
                        product_job_id=self.job_id
                    )
                    
                    if message_id:
                        program_job.sqs_message_id = message_id
                        program_job.current_step = "Program segmentation job queued successfully"
                        self.db.commit()
                        logger.info(f"Queued segmentation for program {program.id}")
                    else:
                        # Failed to queue job
                        program_job.status = SegmentationJobStatus.FAILED
                        program_job.error_message = "Failed to queue program segmentation job"
                        self.db.commit()
                        failed_programs += 1
                        logger.error(f"Failed to queue segmentation for program {program.id}")
                    
                except Exception as e:
                    logger.error(f"Error processing program {program.id}: {e}")
                    failed_programs += 1
                    continue
            
            # Final status update
            if self._check_cancellation():
                return
            
            # All programs have been queued - the actual segmentation will happen asynchronously
            successful_queued = programs_to_segment - failed_programs
            self._update_job_progress(
                status=SegmentationJobStatus.COMPLETED,
                current_step=f"Queued {successful_queued} programs for segmentation ({programs_skipped} already segmented, {failed_programs} failed to queue)",
                progress=100,
                failed_programs=failed_programs
            )

            logger.info(f"Product segmentation orchestration completed for product {product_id}: {successful_queued} queued, {programs_skipped} skipped, {failed_programs} failed")
            
        except Exception as e:
            logger.error(f"Error in product segmentation for product {product_id}: {e}", exc_info=True)
            self._update_job_progress(
                status=SegmentationJobStatus.FAILED,
                current_step="Product segmentation failed",
                error_message=str(e),
                progress=0
            )
