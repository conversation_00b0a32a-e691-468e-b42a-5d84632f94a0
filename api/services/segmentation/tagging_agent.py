import logging
from pydantic_ai import Agent
from pydantic_ai.models.gemini import GeminiModel
from pydantic_ai.providers.google_gla import GoogleGLAProvider
from typing import List, Dict, Any, Optional

from config import settings
from schemas.segmentation_schemas import SegmentOntology
from utils.program_helpers import format_steps_for_analysis, extract_step_state

logger = logging.getLogger(__name__)

# --- Constants ---
ANALYSIS_MODEL_NAME = "gemini-2.5-flash-preview-05-20"

# --- Ontology & Interface Tagging Agent ---

ontology_tagging_agent = Agent(
    model=GeminiModel(
        ANALYSIS_MODEL_NAME, provider=GoogleGLAProvider(api_key=settings.GEMINI_API_KEY)
    ),
    output_type=SegmentOntology,
    system_prompt="""
You are a massage therapist analyzing OSIM massage chair steps. Look at the actual massage data and create specific purpose tags based on what you observe.

**ANALYSIS PROCESS:**

1. **EXAMINE THE MASSAGE STEPS**: Look at speeds, positions, techniques, and patterns
2. **IDENTIFY WHAT'S HAPPENING**: What type of massage work is being performed?
3. **CREATE SPECIFIC TAGS**: Make purpose tags that describe the actual therapeutic effect

**BE CREATIVE AND SPECIFIC:**
- Don't use generic tags like "tension_release" or "relaxation"
- Create tags that capture what this specific massage does
- Use the actual massage characteristics you observe
- Make tags that would help someone find this type of massage

**TECHNIQUE MAPPING:**
- kneading_speed > 0 = kneading technique
- tapping_speed > 0 = percussion technique
- air_action_description = air compression
- position changes = movement/rolling

**BODY REGION MAPPING:**
- Positions 1-8: neck/upper_shoulders
- Positions 9-15: shoulders/upper_back
- Positions 16-22: mid_back/lower_back
- Positions 23-30: lumbar/lower_back

Return JSON only: {"purpose_tags": [...], "technique_tags": [...], "body_part_tags": [...], "intensity_score": X, "entry_state": {...}, "exit_state": {...}}
""",
    retries=3,
)

async def get_segment_ontology(
    steps: List[Dict[str, Any]],
    program_context: Optional[Dict[str, Any]] = None
) -> SegmentOntology:
    """
    Analyzes a list of program steps with program context to extract ontology.

    Args:
        steps: A list of step dictionaries for a single program segment.
        program_context: Optional program context for enhanced purpose tag generation.

    Returns:
        A SegmentOntology object containing the analysis.
    """
    if not steps:
        raise ValueError("Input 'steps' list cannot be empty.")

    # Prepare the simplified text representation for the agent
    analysis_text = format_steps_for_analysis(steps)

    try:

        # Create step-focused prompt with raw context
        context_prompt = ""
        if program_context and program_context.get('raw_program_description'):
            context_prompt = f"""
**PROGRAM CONTEXT FOR CREATIVE ENHANCEMENT:**
Program: {program_context['program_name']}

**COMPLETE PROGRAM DESCRIPTION:**
{program_context['raw_program_description']}

**CREATIVE INTEGRATION:**
After analyzing the massage steps, use this program description to:
- Discover unique therapeutic purposes mentioned in the description
- Create purpose tags that reflect the program's specific goals
- Incorporate any special techniques or benefits described
- Let the program's language inspire creative, specific purpose tags
"""

        # Create simple, direct analysis prompt
        prompt = f"""
Analyze these massage steps and create specific purpose tags based on what you observe:

**MASSAGE STEPS:**
{analysis_text}

**WHAT DO YOU SEE?**
- Speeds: Look at kneading_speed and tapping_speed values
- Positions: Look at position_3d values and changes
- Techniques: Look at roller_action_description and air_action_description
- Patterns: How do these change through the steps?

**CREATE PURPOSE TAGS:**
Based on what you observe, create 2-4 specific purpose tags that describe what this massage does.

{context_prompt if program_context else ""}

**REQUIREMENTS:**
- Be specific and creative with purpose tags
- Don't use generic terms like "tension_release"
- Base tags on actual massage characteristics
- Extract entry_state and exit_state from first/last steps
"""

        # Run the agent
        result = await ontology_tagging_agent.run(prompt)

        # Ensure entry_state and exit_state are properly populated
        ontology = result.output

        # If the agent didn't extract states properly, do it manually
        if not ontology.entry_state or not ontology.exit_state:
            logger.warning("Agent didn't extract entry/exit states properly, extracting manually")
            if not ontology.entry_state:
                ontology.entry_state = extract_step_state(steps[0])
            if not ontology.exit_state:
                ontology.exit_state = extract_step_state(steps[-1])

        return ontology
    except Exception as e:
        logger.error(f"Failed to get segment ontology: {e}")

        # Create fallback ontology with context-guided purposes
        try:
            logger.warning("Creating fallback ontology with context guidance")

            # Extract entry and exit states
            entry_state = extract_step_state(steps[0])
            exit_state = extract_step_state(steps[-1])

            # Simple fallback purposes
            fallback_purposes = ["massage_therapy", "muscle_work"]

            # Create fallback ontology
            fallback_ontology = SegmentOntology(
                purpose_tags=fallback_purposes,
                technique_tags=["kneading"],
                body_part_tags=["back"],
                intensity_score=5,
                entry_state=entry_state,
                exit_state=exit_state
            )

            logger.warning(f"Created fallback ontology: {fallback_ontology}")
            return fallback_ontology

        except Exception as fallback_error:
            logger.error(f"Enhanced fallback ontology creation failed: {fallback_error}")
            raise e
