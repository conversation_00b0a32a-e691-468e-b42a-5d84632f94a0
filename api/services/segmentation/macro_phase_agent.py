from pydantic_ai import Agent
from pydantic_ai.models.gemini import GeminiModel
from pydantic_ai.providers.google_gla import GoogleGLAProvider
from typing import List, Dict, Any

from config import settings
from schemas.segmentation_schemas import MacroPhaseSegmentation
from utils.program_helpers import format_steps_for_analysis
from utils.program_helpers import _standardize_program_sequence, _format_sequence_for_llm

# --- Constants ---
ANALYSIS_MODEL_NAME = "gemini-2.5-flash-preview-05-20"


# --- Macro-Phase Segmentation Agent ---

macro_phase_agent = Agent(
    model=GeminiModel(
        ANALYSIS_MODEL_NAME, provider=GoogleGLAProvider(api_key=settings.GEMINI_API_KEY)
    ),
    output_type=MacroPhaseSegmentation,
    system_prompt="""
You are a certified massage therapist and OSIM massage chair programming expert. Your task is to analyze a complete massage program and identify the precise boundaries of its three therapeutic phases: 'front', 'main', and 'cooling'.

**MASSAGE PROGRAM STRUCTURE EXPERTISE:**

**FRONT PHASE (Preparation/Warm-up):**
- Purpose: Prepare the body, activate circulation, gentle muscle awakening
- Characteristics: Gentle techniques, gradual position introduction
- Duration: Typically 5-10% of total program
- Techniques: Light kneading, gentle rolling, minimal tapping
- Progression: Gradual increase in intensity

**MAIN PHASE (Primary Treatment):**
- Purpose: Core therapeutic work, targeted treatment, primary massage goals
- Characteristics: Multiple techniques, sustained focus, peak therapeutic work
- Duration: Typically 80-90% of total program
- Techniques: Intensive kneading, vigorous tapping, air compression, rolling
- Progression: Peak intensity, complex technique combinations

**COOLING PHASE (Recovery/Relaxation):**
- Purpose: Muscle recovery, stress relief, gentle conclusion
- Characteristics: Soothing techniques, relaxing positions
- Duration: Typically 5-10% of total program
- Techniques: Gentle kneading, light rolling, minimal tapping
- Progression: Gradual decrease to gentle conclusion

**BOUNDARY ANALYSIS INSTRUCTIONS:**
1. Analyze the therapeutic progression from warm up → main massage treatment → cool down
2. Detect technique complexity changes between phases
3. Analyze position progression and body area coverage
4. Identify natural transition points between therapeutic phases
5. Ensure each phase has sufficient duration for therapeutic effectiveness
6. Validate that boundaries create coherent therapeutic segments
7. Consider the program description as guidance for intended phase characteristics

**EXPECTED PHASE CHARACTERISTICS:**
- Front: Preparation and warm-up (typically 5-10% of program)
- Main: Primary therapeutic work (typically 80-90% of program)
- Cooling: Recovery and relaxation (typically 5-10% of program)

**BOUNDARY DETECTION METHODOLOGY:**

1. **ANALYZE PROGRAM DESCRIPTION:**
   - Extract intended phase characteristics from description
   - Identify key therapeutic goals for each phase
   - Note any specific timing or intensity guidance

2. **DETECT TECHNIQUE COMPLEXITY:**
   - Front: Simple, single techniques
   - Main: Complex combinations, multiple simultaneous techniques
   - Cooling: Return to simple, gentle techniques

3. **ANALYZE POSITION PROGRESSION:**
   - Look for systematic body area coverage
   - Identify focus shifts (neck→back→lumbar progression)
   - Note position stability vs. exploration patterns

4. **IDENTIFY TRANSITION MARKERS:**
   - Technique introduction/removal
   - Position focus shifts
   - Air compression pattern changes

**BOUNDARY DETERMINATION RULES:**
- Front phase MUST start at index 0
- Phases must be contiguous (no gaps)
- Each phase must have minimum 3 steps
- Main phase should be the longest
- Cooling phase MUST end at final step
- Boundaries should align with natural therapeutic progression

**CRITICAL ANALYSIS REQUIREMENTS:**
- Base decisions on ACTUAL step data, not assumptions
- Look for SUSTAINED changes, not momentary fluctuations
- Consider THERAPEUTIC LOGIC in boundary placement
- Ensure phases have MEANINGFUL duration
- Validate that boundaries create COHERENT therapeutic segments

**Output Format:**
Return ONLY a valid JSON object with exactly three phases: 'front', 'main', 'cooling' in that order.
""",
    retries=3,
)

async def get_macro_phases(
    steps: List[Dict[str, Any]],
    program_description: Dict[str, Any] = None
) -> MacroPhaseSegmentation:
    """
    Analyzes a full program's steps and description to identify its three macro-phases.

    Args:
        steps: The full list of step dictionaries for the program.
        program_description: The high-level description of the program (optional).
                           May contain 'programme_sequence' with phase descriptions.

    Returns:
        A MacroPhaseSegmentation object containing the boundaries of the three phases.
    """
    if not steps:
        raise ValueError("Input 'steps' list cannot be empty.")

    analysis_text = format_steps_for_analysis(steps)

    # Extract and standardize programme_sequence if available
    standardized_sequence = None
    if program_description and isinstance(program_description, dict):
        programme_sequence = program_description.get('programme_sequence')
        if programme_sequence and isinstance(programme_sequence, dict):
            standardized_sequence = _standardize_program_sequence(programme_sequence)

    # Build the prompt based on whether we have phase descriptions
    if standardized_sequence and any(standardized_sequence.values()):
        # We have phase descriptions - format them nicely for the LLM
        sequence_text = _format_sequence_for_llm(standardized_sequence)
        prompt = f"""
**PROGRAM ANALYSIS CONTEXT:**
- Total program steps: {len(steps)}
- Task: Identify 'front', 'main', and 'cooling' phase boundaries
- Requirement: Each phase must be therapeutically coherent and meaningful
- Available guidance: Program includes intended phase descriptions

**PROGRAM THERAPEUTIC DESCRIPTION:**
{sequence_text}

Analyze the below complete massage program and determine the precise boundaries for the three therapeutic phases.

**DETAILED PROGRAM STEPS WITH THERAPEUTIC CONTEXT:**
{analysis_text}
"""
    else:
        # No phase descriptions available - analyze steps only
        prompt = f"""
**PROGRAM ANALYSIS CONTEXT:**
- Total program steps: {len(steps)}
- Task: Identify 'front', 'main', and 'cooling' phase boundaries
- Requirement: Each phase must be therapeutically coherent and meaningful

Analyze the below complete massage program and determine the precise boundaries for the three therapeutic phases.

**DETAILED PROGRAM STEPS WITH THERAPEUTIC CONTEXT:**
{analysis_text}
"""

    result = await macro_phase_agent.run(prompt)

    # The agent's output is already a MacroPhaseSegmentation object
    return result.output