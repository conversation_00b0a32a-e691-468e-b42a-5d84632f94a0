from pydantic_ai import Agent
from pydantic_ai.models.gemini import GeminiModel
from pydantic_ai.providers.google_gla import GoogleGLAProvider
from typing import List, Dict, Any

from config import settings
from schemas.segmentation_schemas import MicroChunkDiscovery
from utils.program_helpers import format_steps_for_analysis

# --- Constants ---
ANALYSIS_MODEL_NAME = "gemini-2.5-flash-preview-05-20"

# --- Micro-Chunk Discovery Agent ---

micro_chunk_agent = Agent(
    model=GeminiModel(
        ANALYSIS_MODEL_NAME, provider=GoogleGLAProvider(api_key=settings.GEMINI_API_KEY)
    ),
    output_type=MicroChunkDiscovery,
    system_prompt="""
You are a massage therapist identifying substantial therapeutic sequences. Your goal is to find 2-4 major therapeutic chunks within a massage segment, not every small variation.

**CHUNK IDENTIFICATION PRINCIPLES:**

**What Makes a Substantial Therapeutic Chunk:**
- **Significant therapeutic work**: 5-15 steps that accomplish a major therapeutic goal
- **Clear technique dominance**: One primary technique or logical technique progression
- **Meaningful body region focus**: Substantial work on a specific area
- **Complete therapeutic arc**: Beginning, development, and completion of therapeutic intent

**LOOK FOR MAJOR PATTERNS ONLY:**

1. **Preparation Phase**: Gentle warm-up work (if present)
2. **Primary Treatment**: Main therapeutic work with dominant technique
3. **Secondary Treatment**: Different technique or body region (if significantly different)
4. **Completion Phase**: Wind-down or finishing work (if present)

**AVOID OVER-SEGMENTATION:**
- Don't create chunks for every small technique variation
- Don't separate chunks just because air compression changes
- Don't create chunks for minor position adjustments
- Focus on MAJOR therapeutic transitions only

**CHUNK SIZE GUIDELINES:**
- **Minimum**: 5 steps (substantial therapeutic work)
- **Preferred**: 8-15 steps (complete therapeutic sequence)
- **Target**: 2-4 chunks per segment (not 10-20!)

**BOUNDARY DETECTION:**
Only create new chunks when there's a **MAJOR** change in:
- Primary therapeutic technique (e.g. kneading → tapping)
- Body region focus (e.g. neck → lumbar)
- Therapeutic intent (e.g. preparation → treatment → recovery)
- Intensity level (e.g. gentle → intensive)

**DESCRIPTION GUIDELINES:**
- Focus on the MAIN therapeutic accomplishment
- Mention dominant technique and primary body region
- Keep descriptions concise and meaningful

**CRITICAL REQUIREMENTS:**
- Create FEWER, more substantial chunks (2-4 total)
- Each chunk should represent significant therapeutic work
- Ensure COMPLETE coverage - every step must be included in a chunk
- Avoid micro-managing every small variation
- Focus on major therapeutic phases only

Return ONLY a valid JSON object with 2-4 substantial therapeutic chunks covering ALL steps.
""",
    retries=3,
)

async def discover_micro_chunks(steps: List[Dict[str, Any]]) -> MicroChunkDiscovery:
    """
    Analyzes a segment of program steps to discover thematic micro-chunks within it.

    Args:
        steps: A list of step dictionaries for a single program segment (e.g., a macro-phase).

    Returns:
        A MicroChunkDiscovery object containing a list of identified chunks.
    """
    if not steps:
        raise ValueError("Input 'steps' list cannot be empty.")

    analysis_text = format_steps_for_analysis(steps)

    prompt = f"""
Analyze this massage segment and identify 2-4 MAJOR therapeutic chunks. Don't over-segment!

**SEGMENT TO ANALYZE:**
Total steps: {len(steps)}
Target: Find 2-4 substantial therapeutic sequences (not 10+ micro-chunks)

**MASSAGE STEPS:**
{analysis_text}
"""
    
    result = await micro_chunk_agent.run(prompt)
    
    # The agent's output is already a MicroChunkDiscovery object
    return result.output