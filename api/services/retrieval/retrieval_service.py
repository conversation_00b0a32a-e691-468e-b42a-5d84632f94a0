from langchain_pinecone import PineconeVectorStore
from langchain_openai import OpenAIEmbeddings
import os
import logging

from config import settings

logger = logging.getLogger(__name__)
EXAMPLE_SEQUENCES_DIR = "data/example_sequences"


async def get_context_documents(chat_config, search_query):
    """Retrieve relevant context documents from vector store."""
    if not chat_config.source_id:
        return []

    openai_embeddings = OpenAIEmbeddings(api_key=settings.OPENAI_API_KEY)
    vectorstore = PineconeVectorStore(
        index_name=settings.PINECONE_INDEX_NAME,
        embedding=openai_embeddings,
        pinecone_api_key=settings.PINECONE_API_KEY,
        namespace=str(chat_config.source_id),
    )

    # Configure search parameters
    base_search_kwargs = {
        "k": chat_config.similar_docs if hasattr(chat_config, "similar_docs") else 2
    }

    # Get retriever based on search type
    if chat_config.search_type == "mmr":
        retriever = vectorstore.as_retriever(
            search_type="mmr",
            search_kwargs={
                **base_search_kwargs,
                "fetch_k": chat_config.fetch_k,
                "lambda_mult": chat_config.lambda_mult,
            },
        )
    elif chat_config.search_type == "similarity_score_threshold":
        retriever = vectorstore.as_retriever(
            search_type="similarity_score_threshold",
            search_kwargs={
                **base_search_kwargs,
                "score_threshold": chat_config.score_threshold,
            },
        )
    else:  # default to similarity search
        retriever = vectorstore.as_retriever(
            search_type="similarity",
            search_kwargs=base_search_kwargs,
        )

    # Get relevant documents
    context_docs = await retriever.ainvoke(search_query)

    # Format documents for storage
    return [
        {"page_content": doc.page_content, "metadata": dict(doc.metadata)}
        for doc in context_docs
    ]


def load_example_sequences() -> str:
    """Loads and combines content from all .md files in a directory."""
    directory = EXAMPLE_SEQUENCES_DIR
    logger.info(f"Attempting to load example sequences from: {directory}")
    combined_content = []
    if not os.path.isdir(directory):
        logger.warning(f"Example sequences directory not found: {directory}")
        return ""

    try:
        for filename in os.listdir(directory):
            if filename.endswith(".md"):
                filepath = os.path.join(directory, filename)
                try:
                    with open(filepath, "r", encoding="utf-8") as f:
                        content = f.read()
                        combined_content.append(
                            f"\n\n---\n\nExample from {filename}:\n\n{content}"
                        )
                        logger.debug(f"Loaded example: {filename}")
                except FileNotFoundError:
                    logger.warning(f"Example file not found during read: {filepath}")
                except IOError as e:
                    logger.error(f"Error reading example file {filepath}: {e}")
    except Exception as e:
        logger.error(f"Error listing files in example directory {directory}: {e}")
        return ""  # Return empty if listing fails

    if not combined_content:
        logger.info("No example sequences found or loaded.")
        return ""

    logger.info(f"Successfully loaded {len(combined_content)} example sequence files.")
    return "".join(combined_content)
