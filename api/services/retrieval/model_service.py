import boto3
from fastapi import HTT<PERSON><PERSON>xception
from sqlalchemy.orm import Session
from langchain_aws import ChatBedrockConverse
from langchain_openai import ChatOpenAI

from models import ChatModels
from config import settings


async def get_model(db: Session, model_id: int):
    """Get LLM model instance based on the model ID."""
    db_model = db.query(ChatModels).filter(ChatModels.id == model_id).first()
    if not db_model:
        raise HTTPException(status_code=404, detail="Model not found")

    if db_model.provider == "openai":
        return ChatOpenAI(
            model_name=db_model.name,
            openai_api_key=settings.OPENAI_API_KEY,
        )
    elif db_model.provider == "anthropic":
        client = boto3.client(
            "bedrock-runtime",
            region_name="us-east-1",
            aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
            aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY,
        )
        return ChatBedrockConverse(
            model=db_model.name,
            client=client,
            region_name="us-east-1",
        )
    else:
        raise HTTPException(
            status_code=400, detail=f"Unsupported provider: {db_model.provider}"
        )
