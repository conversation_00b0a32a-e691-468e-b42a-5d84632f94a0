from fastapi import APIRout<PERSON>, Depends, HTTPException, BackgroundTasks, Response
from sqlalchemy.orm import Session
from typing import List, Optional
from fastapi import status
from fastapi.responses import StreamingResponse, JSONResponse

from auth.cognito import get_current_user
from core import get_db
from views.program_views import (
    get_program_categories_for_product,
    list_subroutines_paginated,
    list_programs_by_category,
    get_program_by_id,
    update_program,
    update_program_status,
    get_subroutine_by_id,
    get_programs_for_bulk_embedding,
    get_program_version_steps_by_version_number,
    migrate_programs_to_versioning_handler,
    delete_program_version_by_number,
)
from views.export_views import (
    export_program_steps_to_excel,
    export_program_info_to_docx,
)
from views.import_views import (
    populate_program_handler,
    clear_programs_and_subroutines,
)
from schemas.program_schemas import (
    ProductCategoryWithCountSchema,
    SubRoutineListResponse,
    ProgramListResponse,
    ProgramSchema,
    ProgramUpdate,
    SubRoutineSchema,
    ProgramStatusUpdate,
    ProgramCategoryCreate,
    ProgramCategorySchema,
    ProgramCategoryDeleteResponse,
    BulkEmbeddingRequest,
    BulkEmbeddingResponse,
    ProgramVersionSchema,
)
from auth.dependencies import require_admin
from models.program_models import ProgramCategory, Program
from tasks.embedding_tasks import (
    upsert_program_embedding,
    batch_upsert_program_embeddings,
)
program_router = APIRouter(prefix="/api2", tags=["Program & SubRoutine"])


# --- Admin Routes ---


@program_router.post("/program/populate")
@require_admin
async def populate_program(
    db: Session = Depends(get_db),
    user: dict = Depends(get_current_user),
):
    return await populate_program_handler(db)


@program_router.delete("/program/clear")
@require_admin
async def clear_programs(
    db: Session = Depends(get_db),
    user: dict = Depends(get_current_user),
):
    return await clear_programs_and_subroutines(db)


@program_router.post("/program/migrate-to-versioning")
@require_admin
async def migrate_programs_to_versioning(
    db: Session = Depends(get_db),
    user: dict = Depends(get_current_user),
):
    """
    Migrate existing programs to use the versioning system.
    Creates initial versions for programs that don't have versions yet.
    """
    # Call the migration handler in views
    return await migrate_programs_to_versioning_handler(db)


# --- Public/User Routes ---


@program_router.get(
    "/products/{product_id}/program_categories",
    response_model=List[ProductCategoryWithCountSchema],
    summary="Get Program Categories with Counts for a Product",
)
def read_program_categories_for_product(
    product_id: int,
    db: Session = Depends(get_db),
    user: dict = Depends(get_current_user),
):
    """
    Retrieve program categories associated with a specific product,
    including the count of programs within each category for that product.
    """
    categories = get_program_categories_for_product(db=db, product_id=product_id)
    if not categories:
        return []
    return categories


@program_router.get(
    "/subroutines/list",
    response_model=SubRoutineListResponse,
    summary="Get paginated list of subroutines",
)
def list_subroutines(
    skip: int = 0,
    limit: int = 20,
    product_id: Optional[int] = None,
    search: Optional[str] = None,
    sort_field: Optional[str] = None,
    sort_direction: Optional[str] = None,
    db: Session = Depends(get_db),
    user: dict = Depends(get_current_user),
):
    """
    Retrieve a paginated list of subroutines with optional filtering by product, search, and sorting.
    """
    return list_subroutines_paginated(
        db=db,
        skip=skip,
        limit=limit,
        product_id=product_id,
        search=search,
        sort_field=sort_field,
        sort_direction=sort_direction,
    )


# Fetch a single subroutine by ID
@program_router.get(
    "/subroutines/{subroutine_id}",
    response_model=SubRoutineSchema,
    summary="Get a subroutine by ID",
)
def get_subroutine_by_id_route(
    subroutine_id: int,
    db: Session = Depends(get_db),
    user: dict = Depends(get_current_user),
):
    return get_subroutine_by_id(db, subroutine_id)


@program_router.get(
    "/categories/{category_id}/programs",
    response_model=ProgramListResponse,
    summary="Get paginated list of programs for a category",
)
def get_programs_by_category(
    category_id: int,
    skip: int = 0,
    limit: int = 20,
    product_id: Optional[int] = None,
    search: Optional[str] = None,
    sort_field: Optional[str] = "created_at",
    sort_direction: Optional[str] = "desc",
    db: Session = Depends(get_db),
    user: dict = Depends(get_current_user),
):
    """
    Retrieve a paginated list of programs for a specific category with optional filtering by product.
    Supports searching by program title or name and sorting by different fields.
    """
    return list_programs_by_category(
        db=db,
        category_id=category_id,
        skip=skip,
        limit=limit,
        product_id=product_id,
        search=search,
        sort_field=sort_field,
        sort_direction=sort_direction,
    )


# --- Program Detail Routes ---


@program_router.get(
    "/programs/{program_id}",
    response_model=ProgramSchema,
    summary="Get a specific program by ID",
)
def read_program(
    program_id: int,
    db: Session = Depends(get_db),
    user: dict = Depends(get_current_user),
):
    """
    Retrieve a specific program by its ID, including all details and associated subroutines.
    """
    return get_program_by_id(db=db, program_id=program_id)


@program_router.put(
    "/programs/{program_id}", response_model=ProgramSchema, summary="Update a program"
)
def update_program_endpoint(
    program_id: int,
    program_data: ProgramUpdate,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db),
    user: dict = Depends(get_current_user),
):
    """
    Update an existing program with the provided data.
    Triggers a background task to update embeddings if relevant fields change.
    Creates a new version of the program steps.
    """
    # Call the view function which now returns a dict
    update_result = update_program(
        db=db,
        program_id=program_id,
        program_data=program_data,  # Pass the Pydantic model directly
        user=user,
    )

    # Check the flag and schedule background task if needed
    if update_result["embedding_update_needed"]:
        print(
            f"INFO: Scheduling background embedding update for Program ID: {program_id}"
        )
        background_tasks.add_task(upsert_program_embedding, program_id=program_id)

    # Return the updated program object from the result dict
    return update_result["program"]


@program_router.patch(
    "/programs/{program_id}/status",
    response_model=ProgramSchema,
    summary="Update Program Status",
)
def update_program_status_endpoint(
    program_id: int,
    status_data: ProgramStatusUpdate,
    db: Session = Depends(get_db),
    user: dict = Depends(get_current_user),
):
    """
    Update the status of a specific program.
    """
    return update_program_status(db, program_id, status_data.status)


@program_router.get(
    "/programs/{program_id}/versions/{version_number}",
    response_model=ProgramVersionSchema,
    summary="Get a specific version of a program by program ID and version number",
)
def read_program_version(
    program_id: int,
    version_number: int,
    db: Session = Depends(get_db),
    user: dict = Depends(get_current_user),
):
    """
    Retrieve a specific version of a program, including its steps.
    """
    version_data = get_program_version_steps_by_version_number(
        db=db, program_id=program_id, version_number=version_number
    )
    if not version_data:
        raise HTTPException(status_code=404, detail="Program version not found")
    return version_data


@program_router.delete(
    "/programs/{program_id}/versions/{version_number}",
    status_code=status.HTTP_204_NO_CONTENT,
    summary="Delete a specific program version",
)
def delete_program_version_endpoint(
    program_id: int,
    version_number: int,
    db: Session = Depends(get_db),
    user: dict = Depends(get_current_user),
):
    """
    Delete a specific version of a program.
    A user can only delete versions they created.
    The current active version of a program cannot be deleted.
    """
    delete_program_version_by_number(
        db=db,
        program_id=program_id,
        version_number=version_number,
        user_id=user.id,
    )
    return Response(status_code=status.HTTP_204_NO_CONTENT)


@program_router.post(
    "/program-category/create",
    response_model=ProgramCategorySchema,
    summary="Create a new program category (admin only)",
)
@require_admin
def create_program_category(
    category_in: ProgramCategoryCreate,
    db: Session = Depends(get_db),
    user=Depends(get_current_user),
):
    category = ProgramCategory(
        name=category_in.name,
        description=getattr(category_in, "description", None),
    )
    db.add(category)
    db.commit()
    db.refresh(category)
    return category


@program_router.delete(
    "/program-category/{category_id}/delete",
    response_model=ProgramCategoryDeleteResponse,
    summary="Delete a program category (admin only)",
)
@require_admin
def delete_program_category(
    category_id: int,
    db: Session = Depends(get_db),
    user=Depends(get_current_user),
):
    category = (
        db.query(ProgramCategory).filter(ProgramCategory.id == category_id).first()
    )
    if not category:
        raise HTTPException(status_code=404, detail="Program category not found")
    db.delete(category)
    db.commit()
    return ProgramCategoryDeleteResponse(success=True, message="Category deleted")


@program_router.post(
    "/programs/bulk-embedding",
    response_model=BulkEmbeddingResponse,
    summary="Schedule bulk embedding updates for programs",
)
@require_admin
def schedule_bulk_embedding(
    request: BulkEmbeddingRequest,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db),
    user: dict = Depends(get_current_user),
):
    """
    Schedule bulk embedding updates for programs with optional filtering.
    Processes programs in batches to minimize OpenAI API and Pinecone calls.
    """
    try:
        # Get program IDs based on filters
        program_ids = get_programs_for_bulk_embedding(
            db=db,
            product_id=request.product_id,
            category_id=request.category_id,
            limit=request.limit or 100,
        )

        if not program_ids:
            return BulkEmbeddingResponse(
                success=False,
                message="No programs found matching the criteria",
                scheduled_count=0,
            )

        # Schedule the background task
        batch_size = request.batch_size or 10
        background_tasks.add_task(
            batch_upsert_program_embeddings,
            program_ids=program_ids,
            batch_size=batch_size,
        )

        return BulkEmbeddingResponse(
            success=True,
            message=f"Successfully scheduled {len(program_ids)} programs for embedding updates in batches of {batch_size}",
            scheduled_count=len(program_ids),
        )

    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to schedule bulk embedding updates: {str(e)}",
        )


@program_router.get(
    "/programs/{program_id}/export",
    summary="Export program steps as Excel file via S3 presigned URL",
    response_model=dict,
)
def export_program_excel(
    program_id: int,
    db: Session = Depends(get_db),
    # user: dict = Depends(get_current_user),
):
    """
    Export the steps of a program as an Excel file.
    The file is uploaded to S3 and a presigned URL is returned for download.
    """
    try:
        print(f"Exporting program {program_id} to S3 and generating presigned URL...")
        presigned_url = export_program_steps_to_excel(db, program_id)

        if not presigned_url:
            # This case should ideally be handled within export_program_steps_to_excel
            # by raising an HTTPException, but as a safeguard:
            raise HTTPException(
                status_code=500, detail="Failed to generate presigned URL."
            )

        # Get program name for context if needed (already used in view for S3 naming)
        program = db.query(Program).filter(Program.id == program_id).first()
        if not program:
            # Should not happen if presigned_url was generated, but good for safety
            raise HTTPException(
                status_code=404, detail="Program not found after S3 export attempt."
            )

        safe_filename_base = (
            program.name.replace(" ", "_").replace("/", "_").replace("\\\\", "_")
        )
        filename = f"{safe_filename_base}_steps.xlsx"

        print(
            f"Generated presigned URL for program {program_id} (filename hint: {filename}): {presigned_url}"
        )

        return JSONResponse(
            content={"presigned_url": presigned_url, "filename": filename},
            status_code=status.HTTP_200_OK,
        )
    except HTTPException:  # Re-raise known HTTPExceptions
        raise
    except Exception as e:
        print(
            f"Error in export_program_excel endpoint for program {program_id}: {str(e)}"
        )
        import traceback

        traceback.print_exc()
        raise HTTPException(
            status_code=500, detail=f"Failed to export program: {str(e)}"
        )


@program_router.get(
    "/programs/{program_id}/export-info",
    summary="Export program information as Word document via S3 presigned URL",
    response_model=dict,
)
def export_program_info_docx(
    program_id: int,
    db: Session = Depends(get_db),
    # user: dict = Depends(get_current_user),
):
    """
    Export the program information as a Word document.
    The file is uploaded to S3 and a presigned URL is returned for download.
    """
    try:
        print(f"Exporting program info {program_id} to S3 and generating presigned URL...")
        presigned_url = export_program_info_to_docx(db, program_id)

        if not presigned_url:
            raise HTTPException(
                status_code=500, detail="Failed to generate presigned URL."
            )

        # Get program name for context
        program = db.query(Program).filter(Program.id == program_id).first()
        if not program:
            raise HTTPException(
                status_code=404, detail="Program not found after S3 export attempt."
            )

        safe_filename_base = (
            (program.name or program.program_title).replace(" ", "_").replace("/", "_").replace("\\\\", "_")
        )
        filename = f"{safe_filename_base}_info.docx"

        print(
            f"Generated presigned URL for program info {program_id} (filename hint: {filename}): {presigned_url}"
        )

        return JSONResponse(
            content={"presigned_url": presigned_url, "filename": filename},
            status_code=status.HTTP_200_OK,
        )
    except HTTPException:  # Re-raise known HTTPExceptions
        raise
    except Exception as e:
        print(
            f"Error in export_program_info_docx endpoint for program {program_id}: {str(e)}"
        )
        import traceback

        traceback.print_exc()
        raise HTTPException(
            status_code=500, detail=f"Failed to export program info: {str(e)}"
        )
