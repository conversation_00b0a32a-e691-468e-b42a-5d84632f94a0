from fastapi import APIRouter, Depends, HTTPException
from fastapi.responses import StreamingResponse
from sqlalchemy.orm import Session
from sqlalchemy import func, or_, text
from pydantic import BaseModel
from typing import Optional

from auth.cognito import get_current_user
from auth.dependencies import require_ai_engineer
from schemas.chat_schemas import (
    ChatConfigResponse,
    ChatConfigResponseWithName,
    ChatConfigCreate,
    ChatConfigUpdate,
    ModelsList,
    ChatThreadResponse,
    ChatThreadsList,
    ChatThreadMessage,
    ChatThreadMessagesList,
    ChatConfigResponseWithNameList,
    ChatStarRequest,
    ChatStarResponse,
)
from models import (
    ChatModels,
    ChatConfig,
    Chat,
    Message,
    Product,
    Program,
    ProgramCategory,
)
from core import get_db
from views.chat_config_views import (
    create_chat_config_handler,
    update_chat_config_handler,
    get_config,
)


chat_router = APIRouter(prefix="/api2", tags=["Chat"])


# New schemas for program interaction
class ProgramActionRequest(BaseModel):
    program_id: int


class ProgramActionResponse(BaseModel):
    success: bool
    liked: Optional[bool] = False
    disliked: Optional[bool] = False
    saved: Optional[bool] = False


@chat_router.get("/chat/models", response_model=ModelsList)
@require_ai_engineer
async def get_chat_models(
    db: Session = Depends(get_db), user: dict = Depends(get_current_user)
):
    """
    Get a list of available chat models.

    Returns:
        ModelsList: A list of chat models containing their ID, name, and provider.

    Note:
        - This endpoint can only be accessed by AI engineers
        - Requires authentication
    """
    db_models = db.query(ChatModels).order_by(ChatModels.id).all()
    models = [
        {
            "id": model.id,
            "name": model.name,
            "provider": model.provider,
        }
        for model in db_models
    ]
    return ModelsList(models=models)


@chat_router.post(
    "/chat/create_config", response_model=ChatConfigResponse, status_code=201
)
@require_ai_engineer
async def create_chat_config(
    chat_config: ChatConfigCreate,
    db: Session = Depends(get_db),
    user: dict = Depends(get_current_user),
):
    """
    Create a new chat configuration.

    Args:
        chat_config (ChatConfigCreate): The chat configuration to create

    Returns:
        ChatConfigResponse: The created chat configuration

    Note:
        - This endpoint can only be accessed by AI engineers
        - Requires authentication
    """
    return await create_chat_config_handler(db, user, chat_config)


@chat_router.put(
    "/chat/update_config/{chat_config_id}", response_model=ChatConfigResponse
)
@require_ai_engineer
async def update_chat_config(
    chat_config_id: int,
    chat_config: ChatConfigUpdate,
    db: Session = Depends(get_db),
    user: dict = Depends(get_current_user),
):
    return await update_chat_config_handler(db, user, chat_config_id, chat_config)


@chat_router.get("/chat/list", response_model=ChatConfigResponseWithNameList)
async def get_chat_configs(
    db: Session = Depends(get_db),
    user: dict = Depends(get_current_user),
    skip: int = 0,
    limit: int = 20,
):
    """
    Get a list of Chat Apps

    Returns:
        ChatConfigResponseWithNameList: A list of chat configurations with model and source names and pagination

    Note:
        - This endpoint can only be accessed everyone
        - Requires authentication
    """
    total = db.query(ChatConfig).count()
    db_chat_configs = (
        db.query(ChatConfig, ChatModels, Product)
        .join(ChatModels, ChatConfig.model_id == ChatModels.id)
        .join(Product, ChatConfig.product_id == Product.id)
        .order_by(ChatConfig.created_at.desc())
        .offset(skip)
        .limit(limit)
        .all()
    )

    chat_configs = [
        ChatConfigResponseWithName(
            id=chat_config.ChatConfig.id,
            name=chat_config.ChatConfig.name,
            url=chat_config.ChatConfig.url,
            model_id=chat_config.ChatConfig.model_id,
            model_name=chat_config.ChatModels.name,
            product_id=chat_config.ChatConfig.product_id,
            product_name=chat_config.Product.name,
            sft_model_name=chat_config.ChatConfig.sft_model_name,
            use_sft_direct_generation=chat_config.ChatConfig.use_sft_direct_generation
            if chat_config.ChatConfig.use_sft_direct_generation is not None
            else False,  # Provide default if DB value is None
            created_at=chat_config.ChatConfig.created_at,
            updated_at=chat_config.ChatConfig.updated_at,
        )
        for chat_config in db_chat_configs
    ]
    return ChatConfigResponseWithNameList(chat_configs=chat_configs, total=total)


@chat_router.get("/chat/list/{config_id}", response_model=ChatConfigResponse)
async def get_chat_config(
    config_id: int,
    db: Session = Depends(get_db),
    user: dict = Depends(get_current_user),
):
    """
    Get a chat config by ID

    Args:
        config_id (int): The ID of the chat config to retrieve

    Returns:
        ChatConfigResponse: The retrieved chat config

    Note:
        - This endpoint can only be accessed by AI engineers
        - Requires authentication
    """
    return await get_config(db, config_id)


@chat_router.delete("/chat/{config_id}", status_code=204)
async def delete_chat_config(
    config_id: int,
    db: Session = Depends(get_db),
    user: dict = Depends(get_current_user),
):
    """
    Delete a chat config by ID

    Args:
        config_id (int): The ID of the chat config to delete

    Returns:
        None: No content

    Note:
        - This endpoint can only be accessed by AI engineers
        - Requires authentication
    """
    # First delete all messages associated with chats of this config
    db.query(Message).filter(
        Message.chat_id.in_(db.query(Chat.id).filter(Chat.config_id == config_id))
    ).delete(synchronize_session=False)
    # Then delete all chats associated with this config
    db.query(Chat).filter(Chat.config_id == config_id).delete(synchronize_session=False)
    # Finally delete the chat config
    db.query(ChatConfig).filter(ChatConfig.id == config_id).delete()
    db.commit()
    return None


@chat_router.get("/chat/threads", response_model=ChatThreadsList)
async def get_chat_threads(
    db: Session = Depends(get_db),
    user: dict = Depends(get_current_user),
    skip: int = 0,
    limit: int = 20,
    search: Optional[str] = None,
    sort_field: Optional[str] = "updated_at",  # Default sort field
    sort_direction: Optional[str] = "desc",  # Default sort direction
    starred_only: bool = False,  # Add starred filter parameter
):
    # 1. Base query for counting total matching threads for the user, including search filters
    base_query_for_total_count = db.query(Chat.id).filter(Chat.user_id == user.id)

    # Add starred filter if requested
    if starred_only:
        base_query_for_total_count = base_query_for_total_count.filter(Chat.is_starred == True)

    if search and search.strip():
        search_term = f"%{search.strip()}%"
        message_search_subquery = (
            db.query(Message.chat_id)
            .outerjoin(Program, Message.program_id == Program.id)
            .filter(
                or_(
                    Message.content.ilike(search_term),
                    Program.name.ilike(search_term),
                    Program.program_title.ilike(search_term),
                )
            )
            .distinct()
        )
        base_query_for_total_count = base_query_for_total_count.filter(
            Chat.id.in_(message_search_subquery)
        )

    total = base_query_for_total_count.count()

    # 2. Inner query to select distinct chat threads with their first message and config name.
    # The DISTINCT ON clause ensures we get one row per chat.id.
    # The inner ORDER BY (Chat.id, Message.created_at.asc()) helps DISTINCT ON pick the *first* message consistently.

    first_message_sq = (
        db.query(
            Message.chat_id,
            func.min(Message.created_at).label("first_message_time"),
        )
        .filter(Message.role == "user")
        .group_by(Message.chat_id)
        .subquery("first_message_sq")
    )

    distinct_chat_ids_with_first_message_content = (
        db.query(
            Chat.id.label("chat_id"),
            Chat.slug,
            Chat.config_id,
            Chat.is_starred,
            Chat.created_at,
            Chat.updated_at,
            ChatConfig.name.label("config_name"),
            Message.content.label("first_message"),
        )
        .select_from(Chat)
        .join(ChatConfig, Chat.config_id == ChatConfig.id)
        .join(first_message_sq, first_message_sq.c.chat_id == Chat.id)
        .join(
            Message,
            (Message.chat_id == Chat.id)
            & (Message.created_at == first_message_sq.c.first_message_time)
            & (Message.role == "user"),
        )
        .filter(Chat.user_id == user.id)
    )

    # Add starred filter to main query if requested
    if starred_only:
        distinct_chat_ids_with_first_message_content = distinct_chat_ids_with_first_message_content.filter(Chat.is_starred == True)

    if search and search.strip():
        search_term_inner = f"%{search.strip()}%"
        message_search_subquery_inner = (
            db.query(Message.chat_id)
            .outerjoin(Program, Message.program_id == Program.id)
            .filter(
                or_(
                    Message.content.ilike(search_term_inner),
                    Program.name.ilike(search_term_inner),
                    Program.program_title.ilike(search_term_inner),
                )
            )
            .distinct()
        )
        # Apply search filter: chat must be in the list of chats that have a matching message
        distinct_chat_ids_with_first_message_content = (
            distinct_chat_ids_with_first_message_content.filter(
                Chat.id.in_(message_search_subquery_inner)
            )
        )

    # Create a CTE (Common Table Expression) from the distinct chat information query
    # This makes it easier to sort and paginate in the final step.
    chat_cte = distinct_chat_ids_with_first_message_content.cte("chat_cte")

    # 3. Define sortable columns based on the CTE's columns
    sortable_cte_fields = {
        "config_name": chat_cte.c.config_name,
        "first_message": chat_cte.c.first_message,
        "created_at": chat_cte.c.created_at,
        "updated_at": chat_cte.c.updated_at,
    }

    # Determine the actual column to sort on from the CTE
    final_sort_column = sortable_cte_fields.get(sort_field, chat_cte.c.updated_at)

    if sort_direction.lower() == "asc":
        final_order_expression = final_sort_column.asc()
    else:
        final_order_expression = final_sort_column.desc()

    # 4. Query from the CTE, apply the final desired sorting, and then pagination.
    final_results_query = db.query(chat_cte).order_by(final_order_expression)

    db_chat_threads = final_results_query.offset(skip).limit(limit).all()

    threads = [
        ChatThreadResponse(
            id=chat.chat_id,
            slug=chat.slug,
            first_message=chat.first_message or "",
            config_id=chat.config_id,
            config_name=chat.config_name,
            is_starred=chat.is_starred or False,
            created_at=chat.created_at,
            updated_at=chat.updated_at,
        )
        for chat in db_chat_threads
    ]

    return ChatThreadsList(threads=threads, total=total)


@chat_router.get("/chat/thread/{slug}/messages", response_model=ChatThreadMessagesList)
async def get_chat_thread_messages(
    slug: str, db: Session = Depends(get_db), user=Depends(get_current_user)
):
    chat_thread = db.query(Chat).filter(Chat.slug == slug).first()
    if not chat_thread:
        raise HTTPException(status_code=404, detail="Chat not found")

    # Get the config name
    chat_config = (
        db.query(ChatConfig).filter(ChatConfig.id == chat_thread.config_id).first()
    )

    # Format the thread response
    thread_response = ChatThreadResponse(
        id=chat_thread.id,
        slug=chat_thread.slug,
        config_id=chat_thread.config_id,
        config_name=chat_config.name if chat_config else None,
        is_starred=chat_thread.is_starred,
        created_at=chat_thread.created_at,
        updated_at=chat_thread.updated_at,
    )
    # Get all messages
    db_messages = (
        db.query(Message)
        .filter(Message.chat_id == chat_thread.id)
        .filter(Message.role.notin_(["tool_return", "tool_call"]))
        .order_by(Message.created_at.asc())
        .all()
    )

    messages_response = []
    for message in db_messages:
        program_liked: Optional[bool] = None
        program_disliked: Optional[bool] = None
        program_saved: Optional[bool] = None

        if message.program_id:
            program = db.query(Program).filter(Program.id == message.program_id).first()
            if program:
                program_liked = program.liked
                program_disliked = program.disliked
                program_saved = program.saved

        messages_response.append(
            ChatThreadMessage(
                id=message.id,
                role=message.role,
                content=message.content,
                documents=message.documents,
                program_id=message.program_id,
                program_liked=program_liked,
                program_disliked=program_disliked,
                program_saved=program_saved,
            )
        )

    return ChatThreadMessagesList(thread=thread_response, messages=messages_response)


# New routes for program interactions


@chat_router.post("/program/like", response_model=ProgramActionResponse)
async def like_program(
    request: ProgramActionRequest,
    db: Session = Depends(get_db),
    user=Depends(get_current_user),
):
    """
    Like a program. If the program was previously disliked, the dislike is removed.

    Args:
        request (ProgramActionRequest): The program to like

    Returns:
        ProgramActionResponse: Status of the action with updated state
    """
    program = db.query(Program).filter(Program.id == request.program_id).first()
    if not program:
        raise HTTPException(status_code=404, detail="Program not found")

    # Toggle like status
    program.liked = not program.liked

    # If liking, remove dislike if present
    if program.liked and program.disliked:
        program.disliked = False

    db.commit()

    return ProgramActionResponse(
        success=True,
        liked=program.liked,
        disliked=program.disliked,
        saved=program.saved,
    )


@chat_router.post("/program/dislike", response_model=ProgramActionResponse)
async def dislike_program(
    request: ProgramActionRequest,
    db: Session = Depends(get_db),
    user=Depends(get_current_user),
):
    """
    Dislike a program. If the program was previously liked, the like is removed.

    Args:
        request (ProgramActionRequest): The program to dislike

    Returns:
        ProgramActionResponse: Status of the action with updated state
    """
    program = db.query(Program).filter(Program.id == request.program_id).first()
    if not program:
        raise HTTPException(status_code=404, detail="Program not found")

    # Toggle dislike status
    program.disliked = not program.disliked

    # If disliking, remove like if present
    if program.disliked and program.liked:
        program.liked = False

    db.commit()

    return ProgramActionResponse(
        success=True,
        liked=program.liked,
        disliked=program.disliked,
        saved=program.saved,
    )


@chat_router.post("/program/save", response_model=ProgramActionResponse)
async def save_program(
    request: ProgramActionRequest,
    db: Session = Depends(get_db),
    user=Depends(get_current_user),
):
    """
    Save or unsave a program.

    Args:
        request (ProgramActionRequest): The program to save

    Returns:
        ProgramActionResponse: Status of the action with updated state
    """
    program = db.query(Program).filter(Program.id == request.program_id).first()
    if not program:
        raise HTTPException(status_code=404, detail="Program not found")

    # Get or create the "ai programs" category
    ai_category = db.query(ProgramCategory).filter_by(name="ai programs").first()
    if not ai_category:
        ai_category = ProgramCategory(
            name="ai programs", description="User saved ai programs"
        )
        db.add(ai_category)
        db.flush()
    # Get the "generated programs" category
    generated_category = (
        db.query(ProgramCategory).filter_by(name="generated programs").first()
    )
    # Toggle save status and update category
    program.saved = not program.saved
    if program.saved:
        # If saving, move to "ai programs" category
        program.category_id = ai_category.id
    elif generated_category:
        # If unsaving, move back to "generated programs" category
        program.category_id = generated_category.id
    db.commit()

    return ProgramActionResponse(
        success=True,
        liked=program.liked,
        disliked=program.disliked,
        saved=program.saved,
    )


@chat_router.post("/chat/star", response_model=ChatStarResponse)
async def star_chat(
    request: ChatStarRequest,
    db: Session = Depends(get_db),
    user: dict = Depends(get_current_user),
):
    """
    Toggle star status for a chat thread.

    Args:
        request (ChatStarRequest): The chat to star/unstar

    Returns:
        ChatStarResponse: Status of the action with updated star state
    """
    chat = db.query(Chat).filter(
        Chat.id == request.chat_id,
        Chat.user_id == user.id
    ).first()

    if not chat:
        raise HTTPException(status_code=404, detail="Chat not found")

    # Toggle star status without updating updated_at using raw SQL
    new_starred_status = not chat.is_starred
    db.execute(
        text("UPDATE chats SET is_starred = :starred WHERE id = :chat_id"),
        {"starred": new_starred_status, "chat_id": request.chat_id}
    )
    db.commit()

    return ChatStarResponse(
        success=True,
        is_starred=new_starred_status
    )
