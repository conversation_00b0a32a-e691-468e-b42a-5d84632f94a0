from typing import List, Optional  # Added List, Optional
from fastapi import (
    APIRouter,
    Depends,
    HTTPException,
    UploadFile,
    File,
    Form,  # Added Form
)  # Added UploadFile, File
from sqlalchemy.orm import Session

from auth.cognito import get_current_user
from schemas.product_schemas import (
    ProductCreate,
    ProductUpdate,
    ProductResponse,
    ProductListResponse,
)
from models import Product
from core import get_db
from views.product_views import (
    create_product_handler,
    update_product_handler,
    delete_product_handler,
    generate_s3_presigned_url,  # Added import for presigned URL function
)
from auth.dependencies import require_admin

product_router = APIRouter(prefix="/api2", tags=["Product"])


@product_router.get("/product/list", response_model=ProductListResponse)
def get_products(db: Session = Depends(get_db), skip: int = 0, limit: int = 20):
    total = db.query(Product).count()
    db_products = (
        db.query(Product).order_by(Product.created_at).offset(skip).limit(limit).all()
    )
    products_response = []
    for product in db_products:
        presigned_image_url = None
        if product.image_url:  # product.image_url is an S3 object key
            presigned_image_url = generate_s3_presigned_url(product.image_url)

        products_response.append(
            ProductResponse(
                id=product.id,
                name=product.name,
                description=product.description,
                model=product.model,
                series=product.series,
                type=product.type,
                subtype=product.subtype,
                features=product.features,
                image_url=presigned_image_url,  # Use presigned URL if available
                created_at=product.created_at,
                updated_at=product.updated_at,
            )
        )
    return ProductListResponse(products=products_response, total=total)


@product_router.post("/product/create", status_code=201, response_model=ProductResponse)
@require_admin
async def create_product(
    # Explicitly declare form fields that were causing issues
    name: str = Form(...),
    model: str = Form(...),
    description: Optional[str] = Form(None),
    series: Optional[str] = Form(None),
    type: Optional[str] = Form(None),
    subtype: Optional[str] = Form(None),
    features: Optional[List[str]] = Form(
        None
    ),  # Assuming features are sent as multiple form fields with the same name
    image: UploadFile | None = File(None),
    db: Session = Depends(get_db),
    user: dict = Depends(get_current_user),
):
    # Construct the product object from individual form fields
    product_create = ProductCreate(
        name=name,
        model=model,
        description=description,
        series=series,
        type=type,
        subtype=subtype,
        features=features,
    )
    return await create_product_handler(
        db=db, user=user, product=product_create, image=image
    )


@product_router.put("/product/{product_id}", response_model=ProductResponse)
@require_admin
async def update_product(
    product_id: int,
    # Explicitly declare form fields
    name: str = Form(...),
    model: str = Form(...),
    description: Optional[str] = Form(None),
    series: Optional[str] = Form(None),
    type: Optional[str] = Form(None),
    subtype: Optional[str] = Form(None),
    features: Optional[List[str]] = Form(
        None
    ),  # Assuming features are sent as multiple form fields
    image: UploadFile | None = File(None),
    # Add image_url form field to capture the removal signal (empty string)
    image_url: Optional[str] = Form(None),
    db: Session = Depends(get_db),
    user: dict = Depends(get_current_user),
):
    # Construct the product object from individual form fields
    # Note: We don't include image_url here as ProductUpdate doesn't have it.
    # We pass the signal separately to the handler.
    product_update = ProductUpdate(
        name=name,
        model=model,
        description=description,
        series=series,
        type=type,
        subtype=subtype,
        features=features,
    )
    return await update_product_handler(
        db=db,
        user=user,
        product_id=product_id,
        product=product_update,
        image=image,
        image_url_signal=image_url,  # Pass the signal explicitly
    )


@product_router.delete("/product/{product_id}/delete")
@require_admin
async def delete_product(
    product_id: int,
    db: Session = Depends(get_db),
    user: dict = Depends(get_current_user),
):
    return await delete_product_handler(db, user, product_id)


@product_router.get("/product/{product_id}", response_model=ProductResponse)
async def get_product(
    product_id: int,
    db: Session = Depends(get_db),
):
    product = db.query(Product).filter(Product.id == product_id).first()
    if not product:
        raise HTTPException(status_code=404, detail="Product not found")

    presigned_image_url = None
    if product.image_url:  # product.image_url is an S3 object key
        presigned_image_url = generate_s3_presigned_url(product.image_url)

    return ProductResponse(
        id=product.id,
        name=product.name,
        description=product.description,
        model=product.model if product.model else "N/A",
        series=product.series,
        type=product.type,
        subtype=product.subtype,
        features=product.features,
        image_url=presigned_image_url,  # Use presigned URL if available
        created_at=product.created_at,
        updated_at=product.updated_at,
    )
