from fastapi import (
    APIRouter,
    Depends,
    File,
    UploadFile,
    BackgroundTasks,
)
from sqlalchemy.orm import Session

from auth.cognito import get_current_user
from schemas.source_schemas import (
    SourceCreate,
    SourceUpdate,
    SourceResponse,
    SourceListResponse,
)
from models import Source
from core import get_db
from views.source_views import (
    create_source_handler,
    update_source_handler,
    get_files_handler,
    get_file_detail_handler,
    upload_files_handler,
    delete_file_handler,
    delete_source_handler,
)
from auth.dependencies import require_admin

source_router = APIRouter(prefix="/api2", tags=["Source"])


@source_router.get("/source/list", response_model=SourceListResponse)
def get_sources(db: Session = Depends(get_db), skip: int = 0, limit: int = 20):
    total = db.query(Source).count()
    db_sources = (
        db.query(Source)
        .order_by(Source.created_at.desc())
        .offset(skip)
        .limit(limit)
        .all()
    )
    sources = [
        SourceResponse(
            id=source.id,
            name=source.name,
            created_at=source.created_at,
            description=source.description,
        )
        for source in db_sources
    ]
    return SourceListResponse(sources=sources, total=total)


@source_router.post("/source/create", status_code=201)
@require_admin
async def create_source(
    source: SourceCreate,
    db: Session = Depends(get_db),
    user: dict = Depends(get_current_user),
):
    return await create_source_handler(db, user, source)


@source_router.put("/source/{source_id}", response_model=SourceResponse)
@require_admin
async def update_source(
    source_id: int,
    source: SourceUpdate,
    db: Session = Depends(get_db),
    user: dict = Depends(get_current_user),
):
    return await update_source_handler(db, user, source_id, source)


@source_router.get("/source/{source_id}/files")
@require_admin
async def get_source_files(
    source_id: int,
    db: Session = Depends(get_db),
    user: dict = Depends(get_current_user),
):
    return await get_files_handler(db, user, source_id)


@source_router.get("/source/{source_id}/file/{file_id}")
@require_admin
async def get_source_file_detail(
    source_id: int,
    file_id: int,
    db: Session = Depends(get_db),
    user: dict = Depends(get_current_user),
):
    return await get_file_detail_handler(db, user, source_id, file_id)


@source_router.post("/source/{source_id}/files/upload")
@require_admin
async def upload_files_to_source(
    source_id: int,
    files: list[UploadFile] = File(...),
    db: Session = Depends(get_db),
    user: dict = Depends(get_current_user),
    background_tasks: BackgroundTasks = BackgroundTasks(),
):
    return await upload_files_handler(db, background_tasks, user, source_id, files)


@source_router.delete("/source/{source_id}/{file_id}/delete")
@require_admin
async def delete_file_from_source(
    source_id: int,
    file_id: int,
    db: Session = Depends(get_db),
    user: dict = Depends(get_current_user),
    background_tasks: BackgroundTasks = BackgroundTasks(),
):
    return await delete_file_handler(db, background_tasks, user, source_id, file_id)


@source_router.delete("/source/{source_id}/delete")
@require_admin
async def delete_source(
    source_id: int,
    db: Session = Depends(get_db),
    user: dict = Depends(get_current_user),
    background_tasks: BackgroundTasks = BackgroundTasks(),
):
    return await delete_source_handler(db, background_tasks, user, source_id)
