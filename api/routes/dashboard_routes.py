from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from sqlalchemy import func
from typing import Dict, Any
from datetime import datetime, timedelta, date
from pydantic import BaseModel
from typing import List

from core.database import get_db
from models import Program, Product, ProgramCategory, Chat, User, ChatConfig

dashboard_router = APIRouter(prefix="/api2/dashboard", tags=["dashboard"])


@dashboard_router.get("/stats", response_model=Dict)
async def get_dashboard_stats(db: Session = Depends(get_db)):
    """
    Retrieves dashboard statistics including total counts and programs by category.
    """
    try:
        # Count total programs
        total_programs = db.query(func.count(Program.id)).scalar() or 0

        # Count total products
        total_products = db.query(func.count(Product.id)).scalar() or 0

        # Count total chat sessions
        total_chat_sessions = db.query(func.count(Chat.id)).scalar() or 0

        # Count total users
        total_users = db.query(func.count(User.id)).scalar() or 0

        # Count programs by category
        programs_by_category_query = (
            db.query(ProgramCategory.name, func.count(Program.id))
            .join(
                Program, Program.category_id == ProgramCategory.id
            )  # Explicit join condition
            .group_by(ProgramCategory.name)
            .all()
        )
        # Convert list of tuples to dictionary
        programs_by_category: Dict[str, int] = {
            name: count for name, count in programs_by_category_query
        }

        stats: Dict[str, Any] = {
            "totalPrograms": total_programs,
            "totalProducts": total_products,
            "totalChatSessions": total_chat_sessions,
            "programsByCategory": programs_by_category,
            "totalUsers": total_users,
        }
        return stats
    except Exception as e:
        # TODO: Add proper logging
        print(f"Error fetching dashboard stats: {e}")  # Basic print for now
        raise HTTPException(
            status_code=500, detail="Failed to retrieve dashboard statistics"
        )


# Response model for chats per day
class ChatCountPerDay(BaseModel):
    date: str
    count: int


@dashboard_router.get("/chats-per-day", response_model=List[ChatCountPerDay])
async def get_chats_per_day(days: int = 30, db: Session = Depends(get_db)):
    """
    Retrieves the count of chat sessions per day for the specified number of past days.
    Includes days with zero chats within the range.
    """
    try:
        # Calculate the date range
        end_date = datetime.utcnow().date()
        start_date = end_date - timedelta(days=days - 1)  # Inclusive of the start day

        # Query to get chat counts grouped by date within the range
        chat_counts_query = (
            db.query(
                func.date(Chat.created_at).label("chat_date"),
                func.count(Chat.id).label("count"),
            )
            .filter(func.date(Chat.created_at) >= start_date)
            .filter(
                func.date(Chat.created_at) <= end_date
            )  # Ensure we don't get future dates if clock skewed
            .group_by(func.date(Chat.created_at))
            .order_by(func.date(Chat.created_at))
            .all()
        )

        # Create a dictionary for quick lookup of counts by date
        counts_by_date: Dict[date, int] = {
            result.chat_date: result.count for result in chat_counts_query
        }

        # Generate the full list of dates in the range
        result_list: List[ChatCountPerDay] = []
        current_date = start_date
        while current_date <= end_date:
            date_str = current_date.strftime("%Y-%m-%d")
            count = counts_by_date.get(current_date, 0)
            result_list.append(ChatCountPerDay(date=date_str, count=count))
            current_date += timedelta(days=1)

        return result_list

    except Exception as e:
        # TODO: Add proper logging
        print(f"Error fetching chats per day: {e}")  # Basic print for now
        raise HTTPException(
            status_code=500, detail="Failed to retrieve chat counts per day"
        )


# --- Product Program Generation Activity Endpoint ---
@dashboard_router.get("/product-program-generation-activity")
async def get_product_program_generation_activity(
    period: str = "6m",
    granularity: str = "monthly",
    product_ids: str = None,
    limit: int = None,
    db: Session = Depends(get_db),
):
    """
    Returns program generation activity per product over time, grouped by interval,
    filtered for specific program categories.
    """

    TARGET_CATEGORIES = ["ai programs", "generated programs"]

    # --- 1. Parse and validate query params ---
    now = datetime.utcnow()
    period_map = {
        "1w": timedelta(weeks=1),
        "1m": timedelta(days=30),
        "3m": timedelta(days=90),
        "6m": timedelta(days=180),
        "12m": timedelta(days=365),
        "all": None,
    }
    if period not in period_map:
        period = "6m"
    if period == "all":
        # Query considering categories if possible, though min date is global for programs
        start_date_query_result = db.query(func.min(Program.created_at)).scalar()
        start_date = (
            start_date_query_result
            if start_date_query_result
            else (now - timedelta(days=180))
        )
    else:
        start_date = now - period_map[period]
    end_date = now

    # --- 2. Determine intervals ---
    def generate_intervals(start, end, granularity):
        intervals = []
        current = start.replace(hour=0, minute=0, second=0, microsecond=0)
        if granularity == "daily":
            delta = timedelta(days=1)
            end_with_day = end.replace(
                hour=23, minute=59, second=59, microsecond=999999
            )
            while current <= end_with_day:
                intervals.append(current)
                current += delta
        elif granularity == "weekly":
            while current <= end:
                intervals.append(current)
                current += timedelta(weeks=1)
        elif granularity == "monthly":
            while current <= end:
                intervals.append(current)
                year = current.year + (current.month // 12)
                month = (current.month % 12) + 1
                current = current.replace(year=year, month=month, day=1)
        else:
            while current <= end:
                intervals.append(current)
                year = current.year + (current.month // 12)
                month = (current.month % 12) + 1
                current = current.replace(year=year, month=month, day=1)
        return intervals

    intervals = generate_intervals(start_date, end_date, granularity)
    if not intervals or intervals[-1].replace(
        hour=0, minute=0, second=0, microsecond=0
    ) < end_date.replace(hour=0, minute=0, second=0, microsecond=0):
        intervals.append(end_date.replace(hour=0, minute=0, second=0, microsecond=0))

    if intervals:
        intervals = sorted(list(set(intervals)))

    # --- 3. Identify target products ---
    product_ids_requested = None
    limit_applied = None
    products_returned = []

    if product_ids:
        product_ids_requested = [
            pid.strip() for pid in product_ids.split(",") if pid.strip()
        ]
        products_returned = product_ids_requested
    elif limit is not None:
        top_products_query = (
            db.query(Program.product_id, func.count(Program.id).label("program_count"))
            .join(ProgramCategory, Program.category_id == ProgramCategory.id)
            .filter(Program.created_at >= start_date, Program.created_at <= end_date)
            .filter(Program.product_id != None)
            .filter(ProgramCategory.name.in_(TARGET_CATEGORIES))
            .group_by(Program.product_id)
            .order_by(func.count(Program.id).desc())
            .limit(limit)
        )
        top_products = top_products_query.all()
        products_returned = [str(row[0]) for row in top_products if row[0] is not None]
        limit_applied = limit
    else:
        top_products_query = (
            db.query(Program.product_id, func.count(Program.id).label("program_count"))
            .join(ProgramCategory, Program.category_id == ProgramCategory.id)
            .filter(Program.created_at >= start_date, Program.created_at <= end_date)
            .filter(Program.product_id != None)
            .filter(ProgramCategory.name.in_(TARGET_CATEGORIES))
            .group_by(Program.product_id)
            .order_by(func.count(Program.id).desc())
            .limit(10)
        )
        top_products = top_products_query.all()
        products_returned = [str(row[0]) for row in top_products if row[0] is not None]
        limit_applied = 10

    products_returned = [pid for pid in products_returned if pid is not None]

    # --- 4. Fetch product names ---
    product_names = {}
    if products_returned:
        # Assuming product IDs in products_returned are strings and need to be cast to int for IN clause
        product_ids_as_int = []
        for pid_str in products_returned:
            try:
                product_ids_as_int.append(int(pid_str))
            except ValueError:
                # Handle cases where a product_id might not be a valid integer string
                # This might happen if product_ids_requested contained non-integer values
                pass
        if product_ids_as_int:  # Proceed only if we have valid integer IDs
            product_objects = (
                db.query(Product.id, Product.name)
                .filter(Product.id.in_(product_ids_as_int))
                .all()
            )
            product_names = {str(pid): name for pid, name in product_objects}

    # --- 5. Aggregate program counts per interval and product ---
    data = []

    if len(intervals) >= 1 and products_returned:
        product_ids_int_for_query = [int(pid) for pid in products_returned]

        for i in range(len(intervals)):
            interval_start = intervals[i]
            interval_end = intervals[i + 1] if i + 1 < len(intervals) else end_date

            if interval_start >= interval_end and not (
                i == len(intervals) - 1 and interval_end == end_date
            ):
                continue

            program_counts_query = (
                db.query(Program.product_id, func.count(Program.id))
                .join(ProgramCategory, Program.category_id == ProgramCategory.id)
                .filter(Program.product_id.in_(product_ids_int_for_query))
                .filter(ProgramCategory.name.in_(TARGET_CATEGORIES))
                .filter(Program.created_at >= interval_start)
            )
            if interval_end == end_date:
                program_counts_query = program_counts_query.filter(
                    Program.created_at <= interval_end
                )
            else:
                program_counts_query = program_counts_query.filter(
                    Program.created_at < interval_end
                )

            program_counts_result = program_counts_query.group_by(
                Program.product_id
            ).all()

            product_program_counts = {pid_str: 0 for pid_str in products_returned}
            for pid, count in program_counts_result:
                if pid is not None:
                    product_program_counts[str(pid)] = count

            data.append(
                {
                    "timestamp": interval_start.isoformat() + "Z",
                    "product_program_counts": product_program_counts,
                    "product_names": product_names,
                }
            )

    data = sorted(data, key=lambda x: x["timestamp"])

    # --- 6. Build query_details ---
    query_details = {
        "period": period,
        "granularity": granularity,
        "start_date": start_date.isoformat() + "Z",
        "end_date": end_date.isoformat() + "Z",
        "product_ids_requested": product_ids_requested if product_ids_requested else [],
        "limit_applied": limit_applied,
        "products_returned": products_returned,
        "categories_filtered": TARGET_CATEGORIES,
    }

    return {
        "data": data,
        "query_details": query_details,
    }
