import json
import logging
from typing import As<PERSON><PERSON><PERSON>ator

from fastapi import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Depends, APIRouter
from fastapi.responses import StreamingResponse
from sqlalchemy.orm import Session

from schemas.chat_schemas import ChatRequest

from auth.cognito import get_current_user
from core import get_db

# Import the service
from services.chat_service import ChatService, ChatServiceError

# --- Logging Setup ---
logger = logging.getLogger(__name__)


chat_program_router = APIRouter(prefix="/api2", tags=["Chat Program"])


@chat_program_router.post("/chat/program")
async def chat_handler(
    chat_request: ChatRequest,
    db: Session = Depends(get_db),
    user: dict = Depends(get_current_user),
) -> StreamingResponse:
    """
    Main chat handler: Delegates processing to ChatService and streams results.
    """
    logger.info(
        f"Chat request received for slug: {chat_request.slug}, config: {chat_request.chat_config_id}"
    )
    # Use the factory method to create the service with default implementations
    chat_service = ChatService.create_with_default_services(db, user)

    async def response_generator() -> AsyncGenerator[str, None]:
        """Consumes events from ChatService and formats them for SSE."""
        try:
            async for event_data in chat_service.process_chat_message(
                message_content=chat_request.message,
                user=user,
                slug=chat_request.slug,
                chat_config_id=chat_request.chat_config_id,
            ):
                # Format the dictionary yielded by the service into SSE format
                yield f"data: {json.dumps(event_data)}\n\n"
        except HTTPException as http_exc:
            # Log and re-raise HTTP exceptions to let FastAPI handle them
            logger.warning(
                f"HTTPException encountered during chat processing: {http_exc.detail}"
            )
            # Optionally format as an SSE error before re-raising if needed by client?
            # For now, just let FastAPI handle the exception.
            # yield f"data: {json.dumps({'type': 'error', 'content': http_exc.detail})}\n\n"
            raise http_exc
        except ChatServiceError as service_exc:
            # Log service errors and send a generic SSE error message
            logger.error(f"ChatServiceError encountered: {service_exc}", exc_info=True)
            yield f"data: {json.dumps({'type': 'error', 'content': f'Service error: {service_exc}'})}\n\n"
        except Exception as e:
            # Log unexpected errors and send a generic SSE error message
            logger.error(f"Unexpected error in chat route: {e}", exc_info=True)
            yield f"data: {json.dumps({'type': 'error', 'content': 'An unexpected server error occurred.'})}\n\n"

    return StreamingResponse(response_generator(), media_type="text/event-stream")
