import logging
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from sqlalchemy import func
from typing import List, Optional
from fastapi import status

from auth.cognito import get_current_user
from core import get_db
from services.sqs_service import sqs_service
from views.segment_views import (
    list_program_segments,
    get_segment_by_id,
    list_segments_by_program,
    get_segment_hierarchy,
    get_segment_stats,
    clear_segments_and_jobs
)
from schemas.program_schemas import (
    ProgramSegmentSchema,
    ProgramSegmentListResponse,
    SegmentCleanupResponse
)
from schemas.segmentation_job_schemas import (
    SegmentationJobCreate,
    SegmentationJobResponse,
    SegmentationJobListResponse
)
from schemas.product_segmentation_schemas import (
    ProductSegmentationJobCreate,
    ProductSegmentationJobResponse,
    ProductSegmentationJobListResponse
)

logger = logging.getLogger(__name__)
from auth.dependencies import require_admin
from models.program_models import Program, SegmentationJob, SegmentationJobStatus, ProductSegmentationJob, Product
from services.segmentation.orchestrator import SegmentationOrchestrator

segment_router = APIRouter(prefix="/api2", tags=["Program Segments"])


@segment_router.get(
    "/segments",
    response_model=ProgramSegmentListResponse,
    summary="Get paginated list of program segments"
)
def get_segments(
    skip: int = Query(0, ge=0, description="Number of segments to skip"),
    limit: int = Query(20, ge=1, le=1000, description="Number of segments to return"),
    program_id: Optional[int] = Query(None, description="Filter by program ID"),
    phase: Optional[str] = Query(None, description="Filter by phase (front, main, cooling, discovered)"),
    is_macro_phase: Optional[bool] = Query(None, description="Filter by macro phases only"),
    product_id: Optional[int] = Query(None, description="Filter by product ID"),
    search: Optional[str] = Query(None, description="Search in purpose tags, technique tags, or body part tags"),
    sort_field: Optional[str] = Query("id", description="Field to sort by"),
    sort_direction: Optional[str] = Query("asc", description="Sort direction (asc/desc)"),
    db: Session = Depends(get_db),
    user: dict = Depends(get_current_user),
):
    """
    Retrieve a paginated list of program segments with optional filtering and sorting.
    """
    return list_program_segments(
        db=db,
        skip=skip,
        limit=limit,
        program_id=program_id,
        phase=phase,
        is_macro_phase=is_macro_phase,
        product_id=product_id,
        search=search,
        sort_field=sort_field,
        sort_direction=sort_direction,
    )


@segment_router.get(
    "/segments/stats",
    summary="Get segment statistics"
)
def get_segments_stats(
    program_id: Optional[int] = Query(None, description="Filter by program ID"),
    phase: Optional[str] = Query(None, description="Filter by phase (front, main, cooling, discovered)"),
    is_macro_phase: Optional[bool] = Query(None, description="Filter by macro phases only"),
    product_id: Optional[int] = Query(None, description="Filter by product ID"),
    search: Optional[str] = Query(None, description="Search in purpose tags, technique tags, or body part tags"),
    db: Session = Depends(get_db),
    user: dict = Depends(get_current_user),
):
    """
    Get aggregated statistics for segments with optional filtering.
    Returns counts, averages, and distributions without fetching all segment data.
    """
    return get_segment_stats(
        db=db,
        program_id=program_id,
        phase=phase,
        is_macro_phase=is_macro_phase,
        product_id=product_id,
        search=search,
    )


@segment_router.get(
    "/segments/{segment_id}",
    response_model=ProgramSegmentSchema,
    summary="Get a specific segment by ID"
)
def get_segment(
    segment_id: int,
    db: Session = Depends(get_db),
    user: dict = Depends(get_current_user),
):
    """
    Retrieve a specific program segment by its ID.
    """
    return get_segment_by_id(db=db, segment_id=segment_id)


@segment_router.get(
    "/programs/{program_id}/segments",
    response_model=List[ProgramSegmentSchema],
    summary="Get all segments for a specific program"
)
def get_program_segments(
    program_id: int,
    include_micro_chunks: bool = Query(True, description="Include micro chunks in the response"),
    db: Session = Depends(get_db),
    user: dict = Depends(get_current_user),
):
    """
    Retrieve all segments for a specific program, organized by hierarchy.
    """
    return list_segments_by_program(
        db=db,
        program_id=program_id,
        include_micro_chunks=include_micro_chunks
    )


@segment_router.get(
    "/programs/{program_id}/segments/hierarchy",
    summary="Get segment hierarchy for a specific program"
)
def get_program_segment_hierarchy(
    program_id: int,
    db: Session = Depends(get_db),
    user: dict = Depends(get_current_user),
):
    """
    Retrieve the hierarchical structure of segments for a specific program.
    Returns macro phases with their nested micro chunks.
    """
    return get_segment_hierarchy(db=db, program_id=program_id)


# --- Segmentation Job Endpoints ---

async def run_segmentation_task(program_id: int, job_id: int, db: Session):
    """Background task to run segmentation for a program."""
    try:
        # Get the program
        program = db.query(Program).filter(Program.id == program_id).first()
        if not program:
            # Update job as failed
            job = db.query(SegmentationJob).filter(SegmentationJob.id == job_id).first()
            if job:
                job.status = SegmentationJobStatus.FAILED
                job.error_message = f"Program {program_id} not found"
                db.commit()
            return

        # Run segmentation
        orchestrator = SegmentationOrchestrator(db, enable_embedding=True, job_id=job_id)
        await orchestrator.process_program(program)

    except Exception as e:
        # Update job as failed
        job = db.query(SegmentationJob).filter(SegmentationJob.id == job_id).first()
        if job:
            job.status = SegmentationJobStatus.FAILED
            job.error_message = str(e)
            db.commit()


@segment_router.post(
    "/programs/{program_id}/segmentation/start",
    response_model=SegmentationJobResponse,
    summary="Start segmentation for a program"
)
@require_admin
def start_program_segmentation(
    program_id: int,
    db: Session = Depends(get_db),
    user: dict = Depends(get_current_user),
):
    """
    Start the segmentation process for a specific program.
    Creates a job to track progress and queues segmentation job in SQS.
    """
    # Check if program exists
    program = db.query(Program).filter(Program.id == program_id).first()
    if not program:
        raise HTTPException(status_code=404, detail="Program not found")

    # Check if there's already a running job for this program
    existing_job = db.query(SegmentationJob).filter(
        SegmentationJob.program_id == program_id,
        SegmentationJob.status.in_([SegmentationJobStatus.PENDING, SegmentationJobStatus.RUNNING])
    ).first()

    if existing_job:
        raise HTTPException(
            status_code=400,
            detail="Segmentation is already running for this program"
        )

    # Create new segmentation job
    job = SegmentationJob(
        program_id=program_id,
        status=SegmentationJobStatus.PENDING,
        current_step="Queuing segmentation job"
    )
    db.add(job)
    db.commit()
    db.refresh(job)

    # Send job to SQS
    message_id = sqs_service.send_segmentation_job(program_id, job.id)
    if not message_id:
        # Failed to queue job, update status
        job.status = SegmentationJobStatus.FAILED
        job.error_message = "Failed to queue segmentation job"
        db.commit()
        raise HTTPException(status_code=500, detail="Failed to queue segmentation job")

    # Update job with SQS message ID for tracking
    job.sqs_message_id = message_id
    job.current_step = "Segmentation job queued successfully"
    db.commit()

    return SegmentationJobResponse.model_validate(job)


@segment_router.post(
    "/segmentation-jobs/{job_id}/cancel",
    response_model=SegmentationJobResponse,
    summary="Cancel a running segmentation job"
)
@require_admin
def cancel_segmentation_job(
    job_id: int,
    db: Session = Depends(get_db),
    user: dict = Depends(get_current_user),
):
    """
    Cancel a running or pending segmentation job.
    """
    job = db.query(SegmentationJob).filter(SegmentationJob.id == job_id).first()
    if not job:
        raise HTTPException(status_code=404, detail="Segmentation job not found")

    # Check if job can be cancelled
    if job.status not in [SegmentationJobStatus.PENDING, SegmentationJobStatus.RUNNING]:
        raise HTTPException(
            status_code=400,
            detail=f"Cannot cancel job with status: {job.status.value}"
        )

    # Update job status to cancelled
    job.status = SegmentationJobStatus.CANCELLED
    job.current_step = "Job cancelled by user"
    job.completed_at = func.now()
    db.commit()

    return SegmentationJobResponse.model_validate(job)


@segment_router.get(
    "/segmentation-jobs/{job_id}",
    response_model=SegmentationJobResponse,
    summary="Get segmentation job status"
)
def get_segmentation_job(
    job_id: int,
    db: Session = Depends(get_db),
    user: dict = Depends(get_current_user),
):
    """
    Get the status and progress of a specific segmentation job.
    """
    job = db.query(SegmentationJob).filter(SegmentationJob.id == job_id).first()
    if not job:
        raise HTTPException(status_code=404, detail="Segmentation job not found")

    return SegmentationJobResponse.model_validate(job)


@segment_router.get(
    "/programs/{program_id}/segmentation-jobs",
    response_model=SegmentationJobListResponse,
    summary="Get segmentation jobs for a program"
)
def get_program_segmentation_jobs(
    program_id: int,
    skip: int = Query(0, ge=0, description="Number of jobs to skip"),
    limit: int = Query(10, ge=1, le=50, description="Number of jobs to return"),
    db: Session = Depends(get_db),
    user: dict = Depends(get_current_user),
):
    """
    Get all segmentation jobs for a specific program.
    """
    # Check if program exists
    program = db.query(Program).filter(Program.id == program_id).first()
    if not program:
        raise HTTPException(status_code=404, detail="Program not found")

    # Get jobs
    query = db.query(SegmentationJob).filter(SegmentationJob.program_id == program_id)
    total = query.count()

    jobs = query.order_by(SegmentationJob.created_at.desc()).offset(skip).limit(limit).all()

    return SegmentationJobListResponse(
        jobs=[SegmentationJobResponse.model_validate(job) for job in jobs],
        total=total,
        page=skip // limit + 1,
        limit=limit
    )


# --- Cleanup Endpoints ---

@segment_router.delete(
    "/segments/clear",
    response_model=SegmentCleanupResponse,
    summary="Clear program segments and segmentation jobs (admin only)"
)
@require_admin
async def clear_segments_and_segmentation_jobs(
    product_id: Optional[int] = Query(None, description="Filter by product ID to clear segments only for specific product"),
    program_id: Optional[int] = Query(None, description="Filter by program ID to clear segments only for specific program"),
    db: Session = Depends(get_db),
    user: dict = Depends(get_current_user),
):
    """
    Clear program segments and segmentation jobs with optional filtering.

    This endpoint follows the same pattern as the bulk program cleanup procedure:
    - Deletes vectors from Pinecone vector database
    - Handles hierarchical segment relationships (micro-chunks before macro-phases)
    - Cleans up segmentation jobs
    - Optionally resets database sequences
    - Provides detailed cleanup statistics

    **Filtering Options:**
    - No filters: Clears ALL segments and jobs across all products/programs
    - product_id: Clears segments and jobs only for the specified product
    - program_id: Clears segments and jobs only for the specified program

    **Safety Note:** This operation is irreversible. Use with caution.
    """
    return await clear_segments_and_jobs(
        db=db,
        product_id=product_id,
        program_id=program_id
    )


# --- Product Segmentation Endpoints ---

@segment_router.post(
    "/products/{product_id}/segmentation",
    response_model=ProductSegmentationJobResponse,
    summary="Start segmentation for all programs in a product"
)
@require_admin
def start_product_segmentation(
    product_id: int,
    db: Session = Depends(get_db),
    user: dict = Depends(get_current_user),
):
    """
    Start the segmentation process for all programs in a specific product.
    Creates a product job to track overall progress and individual program jobs.
    """
    # Check if product exists
    product = db.query(Product).filter(Product.id == product_id).first()
    if not product:
        raise HTTPException(status_code=404, detail="Product not found")

    # Check if there's already a running job for this product
    existing_job = db.query(ProductSegmentationJob).filter(
        ProductSegmentationJob.product_id == product_id,
        ProductSegmentationJob.status.in_([SegmentationJobStatus.PENDING, SegmentationJobStatus.RUNNING])
    ).first()

    if existing_job:
        raise HTTPException(
            status_code=400,
            detail=f"Product segmentation is already running for this product (Job ID: {existing_job.id}). Cancel the existing job first if you want to restart."
        )

    # Check for previous jobs to provide resume information
    previous_jobs = db.query(ProductSegmentationJob).filter(
        ProductSegmentationJob.product_id == product_id
    ).order_by(ProductSegmentationJob.created_at.desc()).limit(3).all()

    if previous_jobs:
        latest_job = previous_jobs[0]
        logger.info(f"Previous product segmentation jobs found for product {product_id}. Latest job {latest_job.id} status: {latest_job.status}")

        # Count programs that already have segments
        from models.program_models import ProgramSegment
        programs_with_segments = db.query(Program.id).join(
            ProgramSegment, Program.id == ProgramSegment.original_program_id
        ).filter(Program.product_id == product_id).distinct().count()

        # Get all programs for this product and filter by category
        all_programs_query = db.query(Program).filter(Program.product_id == product_id).all()

        # Import the helper function
        from utils.program_helpers import should_segment_program

        # Filter programs that should be segmented
        eligible_programs = [program for program in all_programs_query if should_segment_program(program)]
        total_programs = len(eligible_programs)

        if programs_with_segments > 0:
            logger.info(f"Resume mode: {programs_with_segments}/{total_programs} programs already have segments and will be skipped")

    # Create new product segmentation job
    job = ProductSegmentationJob(
        product_id=product_id,
        status=SegmentationJobStatus.PENDING,
        current_step="Queuing product segmentation job"
    )
    db.add(job)
    db.commit()
    db.refresh(job)

    # Send job to SQS
    message_id = sqs_service.send_product_segmentation_job(product_id, job.id)
    if not message_id:
        # Failed to queue job, update status
        job.status = SegmentationJobStatus.FAILED
        job.error_message = "Failed to queue product segmentation job"
        db.commit()
        raise HTTPException(status_code=500, detail="Failed to queue product segmentation job")

    # Update job with SQS message ID for tracking
    job.sqs_message_id = message_id
    job.current_step = "Product segmentation job queued successfully"
    db.commit()

    return ProductSegmentationJobResponse.model_validate(job)


@segment_router.post(
    "/product-jobs/{job_id}/cancel",
    response_model=ProductSegmentationJobResponse,
    summary="Cancel a running product segmentation job"
)
@require_admin
def cancel_product_segmentation_job(
    job_id: int,
    db: Session = Depends(get_db),
    user: dict = Depends(get_current_user),
):
    """
    Cancel a running or pending product segmentation job.
    This will also cancel all related program segmentation jobs.
    """
    job = db.query(ProductSegmentationJob).filter(ProductSegmentationJob.id == job_id).first()
    if not job:
        raise HTTPException(status_code=404, detail="Product segmentation job not found")

    # Check if job can be cancelled
    if job.status not in [SegmentationJobStatus.PENDING, SegmentationJobStatus.RUNNING]:
        raise HTTPException(
            status_code=400,
            detail=f"Cannot cancel job with status: {job.status.value}"
        )

    # Update job status to cancelled
    job.status = SegmentationJobStatus.CANCELLED
    job.current_step = "Job cancelled by user"
    job.completed_at = func.now()

    # Also cancel all related program jobs
    related_jobs = db.query(SegmentationJob).filter(
        SegmentationJob.product_job_id == job_id,
        SegmentationJob.status.in_([SegmentationJobStatus.PENDING, SegmentationJobStatus.RUNNING])
    ).all()

    for program_job in related_jobs:
        program_job.status = SegmentationJobStatus.CANCELLED
        program_job.current_step = "Cancelled due to product job cancellation"
        program_job.completed_at = func.now()

    db.commit()

    return ProductSegmentationJobResponse.model_validate(job)


@segment_router.get(
    "/product-jobs/{job_id}",
    response_model=ProductSegmentationJobResponse,
    summary="Get product segmentation job status"
)
def get_product_segmentation_job(
    job_id: int,
    db: Session = Depends(get_db),
    user: dict = Depends(get_current_user),
):
    """
    Get the status and progress of a specific product segmentation job.
    """
    job = db.query(ProductSegmentationJob).filter(ProductSegmentationJob.id == job_id).first()
    if not job:
        raise HTTPException(status_code=404, detail="Product segmentation job not found")

    return ProductSegmentationJobResponse.model_validate(job)


@segment_router.get(
    "/products/{product_id}/jobs",
    response_model=ProductSegmentationJobListResponse,
    summary="List product segmentation jobs"
)
def list_product_segmentation_jobs(
    product_id: int,
    skip: int = Query(0, ge=0, description="Number of jobs to skip"),
    limit: int = Query(10, ge=1, le=100, description="Number of jobs to return"),
    db: Session = Depends(get_db),
    user: dict = Depends(get_current_user),
):
    """
    Get a paginated list of segmentation jobs for a specific product.
    """
    # Check if product exists
    product = db.query(Product).filter(Product.id == product_id).first()
    if not product:
        raise HTTPException(status_code=404, detail="Product not found")

    # Get jobs
    query = db.query(ProductSegmentationJob).filter(ProductSegmentationJob.product_id == product_id)
    total = query.count()

    jobs = query.order_by(ProductSegmentationJob.created_at.desc()).offset(skip).limit(limit).all()

    return ProductSegmentationJobListResponse(
        jobs=[ProductSegmentationJobResponse.model_validate(job) for job in jobs],
        total=total,
        page=skip // limit + 1,
        limit=limit
    )
