from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from schemas.account_schemas import (
    AccountCreate,
    AccountsList,
    Account,
    UpdateRoleRequest,
)
from models import User
from core import get_db
from auth.cognito import get_current_user, CognitoAuth
from auth.dependencies import require_admin

account_router = APIRouter(prefix="/api2", tags=["Account"])
cognito_service = CognitoAuth()


@account_router.post("/account/register", status_code=status.HTTP_201_CREATED)
def register_account(account: AccountCreate, db: Session = Depends(get_db)):
    if account.token == "78c4c9673a3bb88b4b69":
        db_user = db.query(User).filter(User.email == account.email).first()
        if db_user:
            raise HTTPException(status_code=400, detail="User already exists")
        user = User(
            email=account.email,
            username=account.username,
            cognito_id=account.cognito_id,
        )
        db.add(user)
        db.commit()
        db.refresh(user)
        return user


@account_router.get("/account/get/{cognito_id}", response_model=Account)
def get_account(
    cognito_id: str,
    user: dict = Depends(get_current_user),
):
    # TODO: Remove Cognito ID from URL
    return Account(
        email=user.email,
        username=user.username,
        cognito_id=user.cognito_id,
    )


@account_router.get("/account/list", response_model=AccountsList)
@require_admin
async def get_accounts(
    db: Session = Depends(get_db),
    user: dict = Depends(get_current_user),
    skip: int = 0,
    limit: int = 20,
):
    total = db.query(User).count()
    db_accounts = db.query(User).order_by(User.email).offset(skip).limit(limit).all()
    accounts = []
    for user in db_accounts:
        roles = cognito_service.get_user_groups(user.username)
        accounts.append(
            Account(
                email=user.email,
                username=user.username,
                cognito_id=user.cognito_id,
                roles=roles,
            )
        )
    return AccountsList(accounts=accounts, total=total)


@account_router.put("/account/{cognito_id}/roles")
@require_admin
async def update_account_roles(
    cognito_id: str,
    request: UpdateRoleRequest,
    db: Session = Depends(get_db),
    user: dict = Depends(get_current_user),
):
    # Find user in database
    current_user = db.query(User).filter(User.cognito_id == cognito_id).first()
    if not current_user:
        raise HTTPException(status_code=404, detail="User not found")

    # Update user's groups in Cognito
    cognito_service.update_user_groups(current_user.username, request.roles)
    return {"message": "Roles updated successfully"}
