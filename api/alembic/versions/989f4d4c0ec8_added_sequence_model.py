"""Added sequence model

Revision ID: 989f4d4c0ec8
Revises: f8cafe33f2db
Create Date: 2025-03-11 12:04:35.191884

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '989f4d4c0ec8'
down_revision: Union[str, None] = 'f8cafe33f2db'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('sequences',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(), nullable=True),
    sa.Column('action', sa.Enum('ROLL_NECK_UPPER_1', 'ROLL_NECK_UPPER', 'ROLL_NECK', 'ROLL_SHOULDER_UPPER', 'ROLL_SHOULDER', 'ROLL_SHOULDER_LOWER', 'ROLL_SHOULDER_LOWER_1', 'ROLL_SHOULDER_BLADE_UPPER', 'ROLL_SHOULDER_BLADE', 'ROLL_SHOULDER_BLADE_LOWER', 'ROLL_SHOULDER_BLADE_LOWER_1', 'ROLL_BACK_UPPER', 'ROLL_BACK', 'ROLL_BACK_LOWER', 'ROLL_WAIST_UPPER', 'ROLL_WAIST', 'TIMER', 'CYCLE', 'WIDTH_CHANGE', 'SET_3D_VALUE', 'RETURN_NEUTRAL', 'RETURN_NEUTRAL_WAIST', 'RETURN_NEUTRAL_WAIST_UPPER', 'ROLLING_UP', 'ROLLING_DOWN', 'COOLDOWN', 'REPEAT_LOOP', 'SEAT_VIBRATION', 'LEG_PROGRAM', 'SUBROUTINE', name='actiontype'), nullable=True),
    sa.Column('params', sa.JSON(), nullable=True),
    sa.Column('speed', sa.Enum('SLOW_KNEAD', 'FAST_KNEAD', 'SLOW_TAP', 'FAST_TAP', 'SLOW_KNEAD_TAP', 'FAST_KNEAD_TAP', 'SLOW_KNEAD_FAST_TAP', 'FAST_KNEAD_SLOW_TAP', 'ROLLING', name='speedtype'), nullable=True),
    sa.Column('position3D', sa.Integer(), nullable=True),
    sa.Column('width', sa.Enum('NARROW', 'MID', 'WIDE', name='widthtype'), nullable=True),
    sa.Column('message_id', sa.Integer(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['message_id'], ['messages.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_sequences_id'), 'sequences', ['id'], unique=False)
    op.add_column('messages', sa.Column('is_sequence', sa.Boolean(), nullable=True))
    op.add_column('messages', sa.Column('sequence_id', sa.Integer(), nullable=True))
    op.create_foreign_key(None, 'messages', 'sequences', ['sequence_id'], ['id'])
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'messages', type_='foreignkey')
    op.drop_column('messages', 'sequence_id')
    op.drop_column('messages', 'is_sequence')
    op.drop_index(op.f('ix_sequences_id'), table_name='sequences')
    op.drop_table('sequences')
    # ### end Alembic commands ###
