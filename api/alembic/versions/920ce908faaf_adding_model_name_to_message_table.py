"""Adding model name to message table

Revision ID: 920ce908faaf
Revises: 6aec968c9f5b
Create Date: 2025-05-08 17:17:33.464871

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '920ce908faaf'
down_revision: Union[str, None] = '6aec968c9f5b'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('messages', sa.Column('model_name', sa.String(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('messages', 'model_name')
    # ### end Alembic commands ###
