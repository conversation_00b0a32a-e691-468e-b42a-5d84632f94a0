"""add_product_segmentation_job_model

Revision ID: 2496957f65b9
Revises: 398a44b4b67f
Create Date: 2025-06-12 13:57:37.682387

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql


# revision identifiers, used by Alembic.
revision: str = '2496957f65b9'
down_revision: Union[str, None] = '398a44b4b67f'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('product_segmentation_jobs',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('product_id', sa.Integer(), nullable=False),
    sa.Column('status', postgresql.ENUM('PENDING', 'RUNNING', 'COMPLETED', 'FAILED', 'CANCELLED', name='segmentationjobstatus', create_type=False), nullable=False),
    sa.Column('progress_percentage', sa.Integer(), nullable=False),
    sa.Column('current_step', sa.String(), nullable=True),
    sa.Column('total_programs', sa.Integer(), nullable=False),
    sa.Column('completed_programs', sa.Integer(), nullable=False),
    sa.Column('failed_programs', sa.Integer(), nullable=False),
    sa.Column('error_message', sa.Text(), nullable=True),
    sa.Column('sqs_message_id', sa.String(), nullable=True),
    sa.Column('started_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('completed_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.ForeignKeyConstraint(['product_id'], ['products.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_product_segmentation_jobs_id'), 'product_segmentation_jobs', ['id'], unique=False)
    op.create_index(op.f('ix_product_segmentation_jobs_product_id'), 'product_segmentation_jobs', ['product_id'], unique=False)
    op.create_index(op.f('ix_product_segmentation_jobs_sqs_message_id'), 'product_segmentation_jobs', ['sqs_message_id'], unique=False)
    op.create_index(op.f('ix_product_segmentation_jobs_status'), 'product_segmentation_jobs', ['status'], unique=False)
    op.add_column('segmentation_jobs', sa.Column('product_job_id', sa.Integer(), nullable=True))
    op.create_index(op.f('ix_segmentation_jobs_product_job_id'), 'segmentation_jobs', ['product_job_id'], unique=False)
    op.create_foreign_key(None, 'segmentation_jobs', 'product_segmentation_jobs', ['product_job_id'], ['id'])
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'segmentation_jobs', type_='foreignkey')
    op.drop_index(op.f('ix_segmentation_jobs_product_job_id'), table_name='segmentation_jobs')
    op.drop_column('segmentation_jobs', 'product_job_id')
    op.drop_index(op.f('ix_product_segmentation_jobs_status'), table_name='product_segmentation_jobs')
    op.drop_index(op.f('ix_product_segmentation_jobs_sqs_message_id'), table_name='product_segmentation_jobs')
    op.drop_index(op.f('ix_product_segmentation_jobs_product_id'), table_name='product_segmentation_jobs')
    op.drop_index(op.f('ix_product_segmentation_jobs_id'), table_name='product_segmentation_jobs')
    op.drop_table('product_segmentation_jobs')
    # ### end Alembic commands ###
