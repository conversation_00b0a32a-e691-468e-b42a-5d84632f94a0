"""Adding Category to Program

Revision ID: fef5e5ff7752
Revises: 149ac6e86b30
Create Date: 2025-04-16 14:27:40.743215

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'fef5e5ff7752'
down_revision: Union[str, None] = '149ac6e86b30'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('programs', sa.Column('category', sa.String(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('programs', 'category')
    # ### end Alembic commands ###
