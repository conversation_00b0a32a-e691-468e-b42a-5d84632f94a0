"""Add segmentation jobs table

Revision ID: 01d6a3bccbe3
Revises: fc1f60fb8c0a
Create Date: 2025-06-10 16:30:30.977470

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '01d6a3bccbe3'
down_revision: Union[str, None] = 'fc1f60fb8c0a'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('segmentation_jobs',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('program_id', sa.Integer(), nullable=False),
    sa.Column('status', sa.Enum('PENDING', 'RUNNING', 'COMPLETED', 'FAILED', name='segmentationjobstatus'), nullable=False),
    sa.Column('progress_percentage', sa.Integer(), nullable=False),
    sa.Column('current_step', sa.String(), nullable=True),
    sa.Column('total_steps', sa.Integer(), nullable=False),
    sa.Column('completed_steps', sa.Integer(), nullable=False),
    sa.Column('error_message', sa.Text(), nullable=True),
    sa.Column('started_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('completed_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.ForeignKeyConstraint(['program_id'], ['programs.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_segmentation_jobs_id'), 'segmentation_jobs', ['id'], unique=False)
    op.create_index(op.f('ix_segmentation_jobs_program_id'), 'segmentation_jobs', ['program_id'], unique=False)
    op.create_index(op.f('ix_segmentation_jobs_status'), 'segmentation_jobs', ['status'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_segmentation_jobs_status'), table_name='segmentation_jobs')
    op.drop_index(op.f('ix_segmentation_jobs_program_id'), table_name='segmentation_jobs')
    op.drop_index(op.f('ix_segmentation_jobs_id'), table_name='segmentation_jobs')
    op.drop_table('segmentation_jobs')
    # ### end Alembic commands ###
