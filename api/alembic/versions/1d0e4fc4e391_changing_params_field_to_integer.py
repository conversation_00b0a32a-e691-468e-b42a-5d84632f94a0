"""Changing params field to integer

Revision ID: 1d0e4fc4e391
Revises: c56aa07d8fbb
Create Date: 2025-03-26 15:10:42.787535

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = "1d0e4fc4e391"
down_revision: Union[str, None] = "c56aa07d8fbb"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # First, create a temporary column
    op.add_column("sequences", sa.Column("params_new", sa.Integer(), nullable=True))

    # Update the new column with converted values
    op.execute(
        """
        UPDATE sequences 
        SET params_new = CASE 
            WHEN params::text IN ('"Deep Relief"', '""', '') THEN NULL
            WHEN params::text ~ '^"[0-9]+"$' OR params::text ~ '^[0-9]+$' 
                THEN regexp_replace(params::text, '"', '', 'g')::integer
            ELSE NULL  -- Handle any other unexpected values as NULL
        END
    """
    )

    # Drop the old column and rename the new one
    op.drop_column("sequences", "params")
    op.alter_column("sequences", "params_new", new_column_name="params")


def downgrade() -> None:
    # Create a temporary column
    op.add_column(
        "sequences",
        sa.Column("params_old", postgresql.JSON(astext_type=sa.Text()), nullable=True),
    )

    # Convert integers back to JSON
    op.execute(
        """
        UPDATE sequences 
        SET params_old = to_jsonb(params::text)
        WHERE params IS NOT NULL
    """
    )

    # Drop the old column and rename the new one
    op.drop_column("sequences", "params")
    op.alter_column("sequences", "params_old", new_column_name="params")
