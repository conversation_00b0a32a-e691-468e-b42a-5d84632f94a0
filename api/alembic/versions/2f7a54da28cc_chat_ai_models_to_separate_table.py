"""Chat AI models to separate table

Revision ID: 2f7a54da28cc
Revises: c9f87ad5516f
Create Date: 2025-02-12 14:41:11.297801

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2f7a54da28cc'
down_revision: Union[str, None] = 'c9f87ad5516f'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('chat_models', sa.Column('provider', sa.String(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('chat_models', 'provider')
    # ### end Alembic commands ###
