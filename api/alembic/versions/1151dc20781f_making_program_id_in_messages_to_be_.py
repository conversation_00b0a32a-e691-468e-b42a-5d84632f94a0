"""Making program_id in messages to be null if program get's deleted

Revision ID: 1151dc20781f
Revises: 482145b7373e
Create Date: 2025-05-08 16:07:18.049805

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '1151dc20781f'
down_revision: Union[str, None] = '482145b7373e'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint('messages_sequence_set_id_fkey', 'messages', type_='foreignkey')
    op.drop_constraint('messages_program_id_fkey', 'messages', type_='foreignkey')
    op.create_foreign_key(None, 'messages', 'programs', ['program_id'], ['id'], ondelete='SET NULL')
    op.create_foreign_key(None, 'messages', 'sequence_sets', ['sequence_set_id'], ['id'], ondelete='SET NULL')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'messages', type_='foreignkey')
    op.drop_constraint(None, 'messages', type_='foreignkey')
    op.create_foreign_key('messages_program_id_fkey', 'messages', 'programs', ['program_id'], ['id'])
    op.create_foreign_key('messages_sequence_set_id_fkey', 'messages', 'sequence_sets', ['sequence_set_id'], ['id'])
    # ### end Alembic commands ###
