"""Chat AI models to separate table

Revision ID: c9f87ad5516f
Revises: a712131f2a49
Create Date: 2025-02-12 14:30:46.114802

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'c9f87ad5516f'
down_revision: Union[str, None] = 'a712131f2a49'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('chat_models',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_chat_models_id'), 'chat_models', ['id'], unique=False)
    op.create_index(op.f('ix_chat_models_name'), 'chat_models', ['name'], unique=False)
    op.add_column('chat_configs', sa.Column('model_id', sa.Integer(), nullable=True))
    op.create_foreign_key(None, 'chat_configs', 'chat_models', ['model_id'], ['id'])
    op.drop_column('chat_configs', 'model')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('chat_configs', sa.Column('model', sa.VARCHAR(), autoincrement=False, nullable=True))
    op.drop_constraint(None, 'chat_configs', type_='foreignkey')
    op.drop_column('chat_configs', 'model_id')
    op.drop_index(op.f('ix_chat_models_name'), table_name='chat_models')
    op.drop_index(op.f('ix_chat_models_id'), table_name='chat_models')
    op.drop_table('chat_models')
    # ### end Alembic commands ###
