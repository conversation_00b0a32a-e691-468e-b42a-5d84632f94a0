"""Adding program versions table

Revision ID: e31b80032d28
Revises: b77a7fb7554d
Create Date: 2025-05-19 12:04:37.252834

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'e31b80032d28'
down_revision: Union[str, None] = 'b77a7fb7554d'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('program_versions', 'created_by_user_id',
               existing_type=sa.INTEGER(),
               nullable=True)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('program_versions', 'created_by_user_id',
               existing_type=sa.INTEGER(),
               nullable=False)
    # ### end Alembic commands ###
