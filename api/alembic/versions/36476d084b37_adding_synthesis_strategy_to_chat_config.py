"""Adding synthesis strategy to chat config

Revision ID: 36476d084b37
Revises: 921da942b143
Create Date: 2025-06-06 16:07:48.113381

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '36476d084b37'
down_revision: Union[str, None] = '921da942b143'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('chat_configs', sa.Column('synthesis_strategy', sa.String(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('chat_configs', 'synthesis_strategy')
    # ### end Alembic commands ###
