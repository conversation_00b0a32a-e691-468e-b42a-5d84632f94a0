"""Added cognito id to User table

Revision ID: a4d41d9496aa
Revises: b9d682a981dd
Create Date: 2025-02-06 12:09:47.751705

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'a4d41d9496aa'
down_revision: Union[str, None] = 'b9d682a981dd'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('users', sa.Column('cognito_id', sa.String(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('users', 'cognito_id')
    # ### end Alembic commands ###
