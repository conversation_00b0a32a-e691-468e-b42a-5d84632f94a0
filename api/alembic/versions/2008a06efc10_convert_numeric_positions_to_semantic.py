"""convert_numeric_positions_to_semantic

Revision ID: 2008a06efc10
Revises: 019b5b92da50
Create Date: 2025-06-19 12:08:11.828648

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2008a06efc10'
down_revision: Union[str, None] = '019b5b92da50'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Convert existing numeric positions to semantic labels

    # For macro phases with 1 chunk: 1 -> front
    op.execute("""
        UPDATE program_segments
        SET position_in_parent = 'front'
        WHERE parent_segment_id IN (
            SELECT parent_segment_id
            FROM program_segments
            WHERE position_in_parent IS NOT NULL
            GROUP BY parent_segment_id
            HAVING COUNT(*) = 1
        ) AND position_in_parent = '1'
    """)

    # For macro phases with 2 chunks: 1->front, 2->last
    op.execute("""
        UPDATE program_segments
        SET position_in_parent = CASE
            WHEN position_in_parent = '1' THEN 'front'
            WHEN position_in_parent = '2' THEN 'last'
        END
        WHERE parent_segment_id IN (
            SELECT parent_segment_id
            FROM program_segments
            WHERE position_in_parent IS NOT NULL
            GROUP BY parent_segment_id
            HAVING COUNT(*) = 2
        ) AND position_in_parent IN ('1', '2')
    """)

    # For macro phases with 3 chunks: 1->front, 2->mid, 3->last
    op.execute("""
        UPDATE program_segments
        SET position_in_parent = CASE
            WHEN position_in_parent = '1' THEN 'front'
            WHEN position_in_parent = '2' THEN 'mid'
            WHEN position_in_parent = '3' THEN 'last'
        END
        WHERE parent_segment_id IN (
            SELECT parent_segment_id
            FROM program_segments
            WHERE position_in_parent IS NOT NULL
            GROUP BY parent_segment_id
            HAVING COUNT(*) = 3
        ) AND position_in_parent IN ('1', '2', '3')
    """)

    # For macro phases with 4+ chunks: 1->front, 2,3,...->mid, last->last
    op.execute("""
        UPDATE program_segments
        SET position_in_parent = CASE
            WHEN position_in_parent = '1' THEN 'front'
            WHEN position_in_parent::int = (
                SELECT COUNT(*)
                FROM program_segments ps2
                WHERE ps2.parent_segment_id = program_segments.parent_segment_id
                AND ps2.position_in_parent IS NOT NULL
            ) THEN 'last'
            ELSE 'mid'
        END
        WHERE parent_segment_id IN (
            SELECT parent_segment_id
            FROM program_segments
            WHERE position_in_parent IS NOT NULL
            GROUP BY parent_segment_id
            HAVING COUNT(*) >= 4
        ) AND position_in_parent ~ '^[0-9]+$'
    """)


def downgrade() -> None:
    # Convert semantic labels back to numeric positions
    # This is a simplified downgrade - we'll just convert back to 1,2,3,4...
    # Note: This won't perfectly restore the original state but provides a reasonable fallback

    op.execute("""
        WITH numbered_segments AS (
            SELECT id,
                   ROW_NUMBER() OVER (PARTITION BY parent_segment_id ORDER BY id) as new_position
            FROM program_segments
            WHERE position_in_parent IN ('front', 'mid', 'last')
        )
        UPDATE program_segments
        SET position_in_parent = numbered_segments.new_position::text
        FROM numbered_segments
        WHERE program_segments.id = numbered_segments.id
    """)
