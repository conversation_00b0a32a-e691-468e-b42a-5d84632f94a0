"""Chat config model added configurartion fields

Revision ID: d03ecbcdefa4
Revises: 5d3d451a99ba
Create Date: 2025-02-20 15:31:21.535965

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'd03ecbcdefa4'
down_revision: Union[str, None] = '5d3d451a99ba'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('chat_configs', sa.Column('temperature', sa.Float(), nullable=True))
    op.add_column('chat_configs', sa.Column('use_reranking', sa.<PERSON>an(), nullable=True))
    op.add_column('chat_configs', sa.Column('search_type', sa.String(), nullable=True))
    op.add_column('chat_configs', sa.Column('similar_docs', sa.Integer(), nullable=True))
    op.add_column('chat_configs', sa.Column('top_k', sa.Integer(), nullable=True))
    op.add_column('chat_configs', sa.Column('compressor_model', sa.String(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('chat_configs', 'compressor_model')
    op.drop_column('chat_configs', 'top_k')
    op.drop_column('chat_configs', 'similar_docs')
    op.drop_column('chat_configs', 'search_type')
    op.drop_column('chat_configs', 'use_reranking')
    op.drop_column('chat_configs', 'temperature')
    # ### end Alembic commands ###
