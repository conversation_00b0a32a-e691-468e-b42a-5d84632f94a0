"""Added template field to sequence set table

Revision ID: 3038ab40b627
Revises: 8a457864f3ae
Create Date: 2025-03-17 17:34:29.089359

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '3038ab40b627'
down_revision: Union[str, None] = '8a457864f3ae'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('sequence_sets', sa.Column('template', sa.JSON(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('sequence_sets', 'template')
    # ### end Alembic commands ###
