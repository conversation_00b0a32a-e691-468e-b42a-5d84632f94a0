"""Added user id and product id to sequence set

Revision ID: 25b4b64f2c45
Revises: 5c6f0799f287
Create Date: 2025-03-13 13:02:19.137867

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '25b4b64f2c45'
down_revision: Union[str, None] = '5c6f0799f287'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('sequence_sets', sa.Column('user_id', sa.Integer(), nullable=True))
    op.add_column('sequence_sets', sa.Column('product_id', sa.Integer(), nullable=True))
    op.create_foreign_key(None, 'sequence_sets', 'products', ['product_id'], ['id'])
    op.create_foreign_key(None, 'sequence_sets', 'users', ['user_id'], ['id'])
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'sequence_sets', type_='foreignkey')
    op.drop_constraint(None, 'sequence_sets', type_='foreignkey')
    op.drop_column('sequence_sets', 'product_id')
    op.drop_column('sequence_sets', 'user_id')
    # ### end Alembic commands ###
