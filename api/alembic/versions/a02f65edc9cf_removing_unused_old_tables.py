"""Removing unused old tables

Revision ID: a02f65edc9cf
Revises: 5f915dc18721
Create Date: 2025-05-14 13:36:00.562080

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = "a02f65edc9cf"
down_revision: Union[str, None] = "5f915dc18721"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index("ix_items_description", table_name="items")
    op.drop_index("ix_items_id", table_name="items")
    op.drop_index("ix_items_name", table_name="items")
    op.drop_table("items")
    op.drop_index("ix_sequences_id", table_name="sequences")
    op.drop_table("sequences")
    op.drop_index("ix_sequence_sets_id", table_name="sequence_sets")
    op.drop_table("sequence_sets")
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "sequences",
        sa.Column("id", sa.INTEGER(), autoincrement=True, nullable=False),
        sa.Column("name", sa.VARCHAR(), autoincrement=False, nullable=True),
        sa.Column(
            "action",
            postgresql.ENUM(
                "ROLL_NECK_UPPER_1",
                "ROLL_NECK_UPPER",
                "ROLL_NECK",
                "ROLL_SHOULDER_UPPER",
                "ROLL_SHOULDER",
                "ROLL_SHOULDER_LOWER",
                "ROLL_SHOULDER_LOWER_1",
                "ROLL_SHOULDER_BLADE_UPPER",
                "ROLL_SHOULDER_BLADE",
                "ROLL_SHOULDER_BLADE_LOWER",
                "ROLL_SHOULDER_BLADE_LOWER_1",
                "ROLL_BACK_UPPER",
                "ROLL_BACK",
                "ROLL_BACK_LOWER",
                "ROLL_WAIST_UPPER",
                "ROLL_WAIST",
                "TIMER",
                "CYCLE",
                "WIDTH_CHANGE",
                "SET_3D_VALUE",
                "RETURN_NEUTRAL",
                "RETURN_NEUTRAL_WAIST",
                "RETURN_NEUTRAL_WAIST_UPPER",
                "ROLLING_UP",
                "ROLLING_DOWN",
                "COOLDOWN",
                "REPEAT_LOOP",
                "SEAT_VIBRATION",
                "LEG_PROGRAM",
                "SUBROUTINE",
                name="actiontype",
            ),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column(
            "speed",
            postgresql.ENUM(
                "SLOW_KNEAD",
                "FAST_KNEAD",
                "SLOW_TAP",
                "FAST_TAP",
                "SLOW_KNEAD_TAP",
                "FAST_KNEAD_TAP",
                "SLOW_KNEAD_FAST_TAP",
                "FAST_KNEAD_SLOW_TAP",
                "ROLLING",
                name="speedtype",
            ),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column("position3D", sa.INTEGER(), autoincrement=False, nullable=True),
        sa.Column(
            "width",
            postgresql.ENUM("NARROW", "MID", "WIDE", name="widthtype"),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column(
            "created_at", postgresql.TIMESTAMP(), autoincrement=False, nullable=True
        ),
        sa.Column(
            "updated_at", postgresql.TIMESTAMP(), autoincrement=False, nullable=True
        ),
        sa.Column("sequence_set_id", sa.INTEGER(), autoincrement=False, nullable=True),
        sa.Column("order", sa.INTEGER(), autoincrement=False, nullable=True),
        sa.Column("params", sa.INTEGER(), autoincrement=False, nullable=True),
        sa.ForeignKeyConstraint(
            ["sequence_set_id"],
            ["sequence_sets.id"],
            name="sequences_sequence_set_id_fkey",
        ),
        sa.PrimaryKeyConstraint("id", name="sequences_pkey"),
    )
    op.create_index("ix_sequences_id", "sequences", ["id"], unique=False)
    op.create_table(
        "sequence_sets",
        sa.Column("id", sa.INTEGER(), autoincrement=True, nullable=False),
        sa.Column("name", sa.VARCHAR(), autoincrement=False, nullable=True),
        sa.Column("description", sa.TEXT(), autoincrement=False, nullable=True),
        sa.Column(
            "created_at", postgresql.TIMESTAMP(), autoincrement=False, nullable=True
        ),
        sa.Column(
            "updated_at", postgresql.TIMESTAMP(), autoincrement=False, nullable=True
        ),
        sa.Column("user_id", sa.INTEGER(), autoincrement=False, nullable=True),
        sa.Column("product_id", sa.INTEGER(), autoincrement=False, nullable=True),
        sa.Column(
            "template",
            postgresql.JSON(astext_type=sa.Text()),
            autoincrement=False,
            nullable=True,
        ),
        sa.ForeignKeyConstraint(
            ["product_id"], ["products.id"], name="sequence_sets_product_id_fkey"
        ),
        sa.ForeignKeyConstraint(
            ["user_id"], ["users.id"], name="sequence_sets_user_id_fkey"
        ),
        sa.PrimaryKeyConstraint("id", name="sequence_sets_pkey"),
    )
    op.create_index("ix_sequence_sets_id", "sequence_sets", ["id"], unique=False)
    op.create_table(
        "items",
        sa.Column("id", sa.INTEGER(), autoincrement=True, nullable=False),
        sa.Column("name", sa.VARCHAR(), autoincrement=False, nullable=True),
        sa.Column("description", sa.VARCHAR(), autoincrement=False, nullable=True),
        sa.PrimaryKeyConstraint("id", name="items_pkey"),
    )
    op.create_index("ix_items_name", "items", ["name"], unique=False)
    op.create_index("ix_items_id", "items", ["id"], unique=False)
    op.create_index("ix_items_description", "items", ["description"], unique=False)
    # ### end Alembic commands ###
