"""Adding sqs message id to sengmentation job

Revision ID: d0cc2cfc9301
Revises: 01d6a3bccbe3
Create Date: 2025-06-11 19:29:07.758172

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'd0cc2cfc9301'
down_revision: Union[str, None] = '01d6a3bccbe3'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('segmentation_jobs', sa.Column('sqs_message_id', sa.String(), nullable=True))
    op.create_index(op.f('ix_segmentation_jobs_sqs_message_id'), 'segmentation_jobs', ['sqs_message_id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_segmentation_jobs_sqs_message_id'), table_name='segmentation_jobs')
    op.drop_column('segmentation_jobs', 'sqs_message_id')
    # ### end Alembic commands ###
