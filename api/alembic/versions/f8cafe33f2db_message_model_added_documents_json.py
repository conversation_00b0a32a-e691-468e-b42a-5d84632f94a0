"""Message model added documents json

Revision ID: f8cafe33f2db
Revises: c142aef5e96b
Create Date: 2025-02-25 09:50:51.903534

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'f8cafe33f2db'
down_revision: Union[str, None] = 'c142aef5e96b'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('messages', sa.Column('documents', sa.JSON(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('messages', 'documents')
    # ### end Alembic commands ###
