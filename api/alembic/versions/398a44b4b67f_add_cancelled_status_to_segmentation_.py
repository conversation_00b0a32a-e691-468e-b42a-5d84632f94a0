"""add_cancelled_status_to_segmentation_job_enum

Revision ID: 398a44b4b67f
Revises: 45a9ecad38fd
Create Date: 2025-06-11 20:13:37.705066

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '398a44b4b67f'
down_revision: Union[str, None] = '45a9ecad38fd'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Add 'CANCELLED' value to the existing segmentationjobstatus enum
    op.execute("ALTER TYPE segmentationjobstatus ADD VALUE 'CANCELLED'")


def downgrade() -> None:
    # Note: PostgreSQL doesn't support removing enum values directly
    # This would require recreating the enum type, which is complex
    # For now, we'll leave the enum value in place
    pass
