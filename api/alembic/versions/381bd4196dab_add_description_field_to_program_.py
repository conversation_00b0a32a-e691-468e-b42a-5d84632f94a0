"""Add description field to program_segments

Revision ID: 381bd4196dab
Revises: 2496957f65b9
Create Date: 2025-06-13 14:12:59.608078

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '381bd4196dab'
down_revision: Union[str, None] = '2496957f65b9'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Add description column to program_segments table
    op.add_column('program_segments', sa.Column('description', sa.Text(), nullable=True))


def downgrade() -> None:
    # Remove description column from program_segments table
    op.drop_column('program_segments', 'description')
