"""Adding productid to subroutines

Revision ID: ef0ec5147b1a
Revises: 53346e490c3a
Create Date: 2025-04-16 12:44:11.929735

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'ef0ec5147b1a'
down_revision: Union[str, None] = '53346e490c3a'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('sub_routines', sa.Column('product_id', sa.Integer(), nullable=True))
    op.create_foreign_key(None, 'sub_routines', 'products', ['product_id'], ['id'])
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'sub_routines', type_='foreignkey')
    op.drop_column('sub_routines', 'product_id')
    # ### end Alembic commands ###
