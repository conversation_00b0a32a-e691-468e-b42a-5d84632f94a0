"""Updating source table

Revision ID: bc92243d907d
Revises: 6ace9ca9ce7f
Create Date: 2025-02-10 12:24:28.947978

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'bc92243d907d'
down_revision: Union[str, None] = '6ace9ca9ce7f'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('sources', sa.Column('pinecone_index', sa.String(), nullable=True))
    op.add_column('sources', sa.Column('s3_bucket_url', sa.String(), nullable=True))
    op.add_column('sources', sa.Column('status', sa.String(), nullable=True))
    op.add_column('sources', sa.Column('owner_id', sa.Integer(), nullable=True))
    op.create_index(op.f('ix_sources_name'), 'sources', ['name'], unique=True)
    op.drop_constraint('sources_user_id_fkey', 'sources', type_='foreignkey')
    op.create_foreign_key(None, 'sources', 'users', ['owner_id'], ['id'])
    op.drop_column('sources', 'url')
    op.drop_column('sources', 'indexed')
    op.drop_column('sources', 'user_id')
    op.drop_column('sources', 'vector_store')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('sources', sa.Column('vector_store', sa.VARCHAR(), autoincrement=False, nullable=True))
    op.add_column('sources', sa.Column('user_id', sa.INTEGER(), autoincrement=False, nullable=True))
    op.add_column('sources', sa.Column('indexed', sa.BOOLEAN(), autoincrement=False, nullable=True))
    op.add_column('sources', sa.Column('url', sa.VARCHAR(), autoincrement=False, nullable=True))
    op.drop_constraint(None, 'sources', type_='foreignkey')
    op.create_foreign_key('sources_user_id_fkey', 'sources', 'users', ['user_id'], ['id'])
    op.drop_index(op.f('ix_sources_name'), table_name='sources')
    op.drop_column('sources', 'owner_id')
    op.drop_column('sources', 'status')
    op.drop_column('sources', 's3_bucket_url')
    op.drop_column('sources', 'pinecone_index')
    # ### end Alembic commands ###
