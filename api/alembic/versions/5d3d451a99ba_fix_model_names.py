"""Fix Model names

Revision ID: 5d3d451a99ba
Revises: a22007ac7929
Create Date: 2025-02-20 11:40:26.915968

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = "5d3d451a99ba"
down_revision: Union[str, None] = "a22007ac7929"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Update OpenAI model names to remove 'openai/' prefix
    op.execute(
        """
        UPDATE chat_models 
        SET name = REPLACE(name, 'openai/', '')
        WHERE provider = 'openai'
    """
    )


def downgrade() -> None:
    # Restore the 'openai/' prefix
    op.execute(
        """
        UPDATE chat_models 
        SET name = 'openai/' || name
        WHERE provider = 'openai'
    """
    )
