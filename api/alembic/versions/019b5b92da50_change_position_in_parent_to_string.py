"""change_position_in_parent_to_string

Revision ID: 019b5b92da50
Revises: 65bf8cd73293
Create Date: 2025-06-19 12:06:38.360881

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '019b5b92da50'
down_revision: Union[str, None] = '65bf8cd73293'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Change position_in_parent column from Integer to String
    op.alter_column('program_segments', 'position_in_parent',
                    existing_type=sa.Integer(),
                    type_=sa.String(),
                    existing_nullable=True)


def downgrade() -> None:
    # Change position_in_parent column back from String to Integer
    op.alter_column('program_segments', 'position_in_parent',
                    existing_type=sa.String(),
                    type_=sa.Integer(),
                    existing_nullable=True)
