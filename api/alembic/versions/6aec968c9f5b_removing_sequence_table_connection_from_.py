"""Removing sequence table connection from messages since migrating to programs table

Revision ID: 6aec968c9f5b
Revises: 1151dc20781f
Create Date: 2025-05-08 16:10:07.530051

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '6aec968c9f5b'
down_revision: Union[str, None] = '1151dc20781f'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint('messages_sequence_set_id_fkey', 'messages', type_='foreignkey')
    op.drop_column('messages', 'is_sequence')
    op.drop_column('messages', 'sequence_set_id')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('messages', sa.Column('sequence_set_id', sa.INTEGER(), autoincrement=False, nullable=True))
    op.add_column('messages', sa.Column('is_sequence', sa.BOOLEAN(), autoincrement=False, nullable=True))
    op.create_foreign_key('messages_sequence_set_id_fkey', 'messages', 'sequence_sets', ['sequence_set_id'], ['id'], ondelete='SET NULL')
    # ### end Alembic commands ###
