"""Adding ProgramSegment table

Revision ID: fc1f60fb8c0a
Revises: 36476d084b37
Create Date: 2025-06-10 14:53:58.738568

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'fc1f60fb8c0a'
down_revision: Union[str, None] = '36476d084b37'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('program_segments',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('original_program_id', sa.Integer(), nullable=False),
    sa.Column('phase', sa.String(), nullable=False),
    sa.Column('parent_segment_id', sa.Integer(), nullable=True),
    sa.Column('entry_state', sa.JSON(), nullable=False),
    sa.Column('exit_state', sa.JSON(), nullable=False),
    sa.Column('purpose_tags', sa.JSON(), nullable=True),
    sa.Column('technique_tags', sa.JSON(), nullable=True),
    sa.Column('body_part_tags', sa.JSON(), nullable=True),
    sa.Column('intensity_score', sa.Integer(), nullable=True),
    sa.Column('duration_seconds', sa.Integer(), nullable=True),
    sa.Column('steps', sa.JSON(), nullable=False),
    sa.ForeignKeyConstraint(['original_program_id'], ['programs.id'], ),
    sa.ForeignKeyConstraint(['parent_segment_id'], ['program_segments.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_program_segments_id'), 'program_segments', ['id'], unique=False)
    op.create_index(op.f('ix_program_segments_original_program_id'), 'program_segments', ['original_program_id'], unique=False)
    op.create_index(op.f('ix_program_segments_parent_segment_id'), 'program_segments', ['parent_segment_id'], unique=False)
    op.create_index(op.f('ix_program_segments_phase'), 'program_segments', ['phase'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_program_segments_phase'), table_name='program_segments')
    op.drop_index(op.f('ix_program_segments_parent_segment_id'), table_name='program_segments')
    op.drop_index(op.f('ix_program_segments_original_program_id'), table_name='program_segments')
    op.drop_index(op.f('ix_program_segments_id'), table_name='program_segments')
    op.drop_table('program_segments')
    # ### end Alembic commands ###
