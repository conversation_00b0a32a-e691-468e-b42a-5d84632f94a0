"""Adding star column to the chats

Revision ID: 5a53e17acaf1
Revises: 2a0fb6ffc913
Create Date: 2025-06-23 10:32:34.321898

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '5a53e17acaf1'
down_revision: Union[str, None] = '2a0fb6ffc913'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('chats', sa.Column('is_starred', sa.<PERSON>(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('chats', 'is_starred')
    # ### end Alembic commands ###
