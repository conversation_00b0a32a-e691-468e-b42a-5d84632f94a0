"""add status to program model

Revision ID: 2a0fb6ffc913
Revises: 2008a06efc10
Create Date: 2025-06-20 16:40:46.628552

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2a0fb6ffc913'
down_revision: Union[str, None] = '2008a06efc10'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    programstatus = sa.Enum('Draft', 'In Progress', 'Ready for Testing', 'Tested', 'Verified', name='programstatus')
    programstatus.create(op.get_bind())
    op.add_column('programs', sa.Column('status', programstatus, nullable=True, default='Draft'))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('programs', 'status')
    programstatus = sa.Enum('Draft', 'In Progress', 'Ready for Testing', 'Tested', 'Verified', name='programstatus')
    programstatus.drop(op.get_bind())
    # ### end Alembic commands ###
