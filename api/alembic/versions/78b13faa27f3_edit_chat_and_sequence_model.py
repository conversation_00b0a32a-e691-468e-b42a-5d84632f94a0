"""Edit chat and sequence model

Revision ID: 78b13faa27f3
Revises: 989f4d4c0ec8
Create Date: 2025-03-11 12:09:18.517153

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '78b13faa27f3'
down_revision: Union[str, None] = '989f4d4c0ec8'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint('sequences_message_id_fkey', 'sequences', type_='foreignkey')
    op.drop_column('sequences', 'message_id')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('sequences', sa.Column('message_id', sa.INTEGER(), autoincrement=False, nullable=True))
    op.create_foreign_key('sequences_message_id_fkey', 'sequences', 'messages', ['message_id'], ['id'])
    # ### end Alembic commands ###
