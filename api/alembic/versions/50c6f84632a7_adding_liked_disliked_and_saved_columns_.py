"""Adding liked, disliked and saved columns to program

Revision ID: 50c6f84632a7
Revises: 920ce908faaf
Create Date: 2025-05-12 09:45:43.635440

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '50c6f84632a7'
down_revision: Union[str, None] = '920ce908faaf'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('programs', sa.Column('liked', sa.<PERSON>(), nullable=True))
    op.add_column('programs', sa.Column('disliked', sa.<PERSON>(), nullable=True))
    op.add_column('programs', sa.Column('saved', sa.<PERSON>(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('programs', 'saved')
    op.drop_column('programs', 'disliked')
    op.drop_column('programs', 'liked')
    # ### end Alembic commands ###
