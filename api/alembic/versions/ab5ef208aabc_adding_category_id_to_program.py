"""Adding Category id to Program

Revision ID: ab5ef208aabc
Revises: fef5e5ff7752
Create Date: 2025-04-16 14:51:57.404043

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'ab5ef208aabc'
down_revision: Union[str, None] = 'fef5e5ff7752'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('program_categories',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(), nullable=True),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_program_categories_id'), 'program_categories', ['id'], unique=False)
    op.add_column('programs', sa.Column('category_id', sa.Integer(), nullable=True))
    op.create_foreign_key(None, 'programs', 'program_categories', ['category_id'], ['id'])
    op.drop_column('programs', 'category')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('programs', sa.Column('category', sa.VARCHAR(), autoincrement=False, nullable=True))
    op.drop_constraint(None, 'programs', type_='foreignkey')
    op.drop_column('programs', 'category_id')
    op.drop_index(op.f('ix_program_categories_id'), table_name='program_categories')
    op.drop_table('program_categories')
    # ### end Alembic commands ###
