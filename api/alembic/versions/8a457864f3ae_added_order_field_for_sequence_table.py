"""Added order field for sequence table

Revision ID: 8a457864f3ae
Revises: 25b4b64f2c45
Create Date: 2025-03-13 20:58:32.369231

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '8a457864f3ae'
down_revision: Union[str, None] = '25b4b64f2c45'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('sequences', sa.Column('order', sa.Integer(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('sequences', 'order')
    # ### end Alembic commands ###
