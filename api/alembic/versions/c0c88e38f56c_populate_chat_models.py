"""populate chat models

Revision ID: c0c88e38f56c
Revises: 2f7a54da28cc
Create Date: 2025-02-12 14:41:21.476250

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = "c0c88e38f56c"
down_revision: Union[str, None] = "2f7a54da28cc"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    chat_models = sa.table(
        "chat_models",
        sa.column("id", sa.Integer),
        sa.column("name", sa.String),
        sa.column("provider", sa.String),
    )
    op.bulk_insert(
        chat_models,
        [
            {"id": 1, "name": "openai/gpt-4o", "provider": "openai"},
            {"id": 2, "name": "openai/gpt-4o-mini", "provider": "openai"},
            {"id": 3, "name": "openai/o1", "provider": "openai"},
            {"id": 4, "name": "openai/o1-mini", "provider": "openai"},
            {"id": 5, "name": "openai/o3-mini", "provider": "openai"},
            {
                "id": 6,
                "name": "anthropic.claude-3-sonnet-20240229-v1:0",
                "provider": "anthropic",
            },
            {
                "id": 7,
                "name": "anthropic.claude-3-opus-20240229-v1:0",
                "provider": "anthropic",
            },
            {
                "id": 8,
                "name": "anthropic.claude-3-haiku-20240307-v1:0",
                "provider": "anthropic",
            },
        ],
    )


def downgrade() -> None:
    op.execute("DELETE FROM chat_models")
