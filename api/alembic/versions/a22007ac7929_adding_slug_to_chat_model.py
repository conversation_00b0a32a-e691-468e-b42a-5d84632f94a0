"""Adding slug to Chat model

Revision ID: a22007ac7929
Revises: b8bb2d013560
Create Date: 2025-02-12 17:30:29.502026

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'a22007ac7929'
down_revision: Union[str, None] = 'b8bb2d013560'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('chats', sa.Column('slug', sa.String(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('chats', 'slug')
    # ### end Alembic commands ###
