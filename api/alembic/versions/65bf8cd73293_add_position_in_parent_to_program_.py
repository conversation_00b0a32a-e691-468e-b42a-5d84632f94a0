"""add_position_in_parent_to_program_segments

Revision ID: 65bf8cd73293
Revises: 381bd4196dab
Create Date: 2025-06-19 11:15:11.119880

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '65bf8cd73293'
down_revision: Union[str, None] = '381bd4196dab'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Add position_in_parent column to program_segments table
    op.add_column('program_segments', sa.Column('position_in_parent', sa.Integer(), nullable=True))
    op.create_index(op.f('ix_program_segments_position_in_parent'), 'program_segments', ['position_in_parent'], unique=False)


def downgrade() -> None:
    # Remove position_in_parent column and its index
    op.drop_index(op.f('ix_program_segments_position_in_parent'), table_name='program_segments')
    op.drop_column('program_segments', 'position_in_parent')
