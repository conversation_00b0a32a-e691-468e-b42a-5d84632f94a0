"""Chat config model added more configurartion fields

Revision ID: c142aef5e96b
Revises: d03ecbcdefa4
Create Date: 2025-02-20 16:46:48.356265

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'c142aef5e96b'
down_revision: Union[str, None] = 'd03ecbcdefa4'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('chat_configs', sa.Column('fetch_k', sa.Integer(), nullable=True))
    op.add_column('chat_configs', sa.Column('lambda_mult', sa.Float(), nullable=True))
    op.add_column('chat_configs', sa.Column('score_threshold', sa.Float(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('chat_configs', 'score_threshold')
    op.drop_column('chat_configs', 'lambda_mult')
    op.drop_column('chat_configs', 'fetch_k')
    # ### end Alembic commands ###
