"""add_cancelled_status_to_segmentation_job_enum

Revision ID: 45a9ecad38fd
Revises: d0cc2cfc9301
Create Date: 2025-06-11 20:13:17.036912

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '45a9ecad38fd'
down_revision: Union[str, None] = 'd0cc2cfc9301'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    pass


def downgrade() -> None:
    pass
