"""Added Sequence Set and modifying relationships

Revision ID: 5c6f0799f287
Revises: 163a8e313a69
Create Date: 2025-03-13 10:45:47.318142

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '5c6f0799f287'
down_revision: Union[str, None] = '163a8e313a69'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('sequence_sets',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(), nullable=True),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_sequence_sets_id'), 'sequence_sets', ['id'], unique=False)
    op.add_column('messages', sa.Column('sequence_set_id', sa.Integer(), nullable=True))
    op.drop_constraint('messages_sequence_id_fkey', 'messages', type_='foreignkey')
    op.create_foreign_key(None, 'messages', 'sequence_sets', ['sequence_set_id'], ['id'])
    op.drop_column('messages', 'sequence_id')
    op.add_column('sequences', sa.Column('sequence_set_id', sa.Integer(), nullable=True))
    op.create_foreign_key(None, 'sequences', 'sequence_sets', ['sequence_set_id'], ['id'])
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'sequences', type_='foreignkey')
    op.drop_column('sequences', 'sequence_set_id')
    op.add_column('messages', sa.Column('sequence_id', sa.INTEGER(), autoincrement=False, nullable=True))
    op.drop_constraint(None, 'messages', type_='foreignkey')
    op.create_foreign_key('messages_sequence_id_fkey', 'messages', 'sequences', ['sequence_id'], ['id'])
    op.drop_column('messages', 'sequence_set_id')
    op.drop_index(op.f('ix_sequence_sets_id'), table_name='sequence_sets')
    op.drop_table('sequence_sets')
    # ### end Alembic commands ###
