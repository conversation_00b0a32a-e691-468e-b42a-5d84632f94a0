"""Splitting template to tool call template and sequence template

Revision ID: 163a8e313a69
Revises: ca4f4a913e95
Create Date: 2025-03-12 15:32:23.153850

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = "163a8e313a69"
down_revision: Union[str, None] = "ca4f4a913e95"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    tool_template = """You are a helpful massage sequence assistant. 
Your goal is to create personalized massage sequences for users.
    
If the user provides all necessary information, use the SequenceParameters function to create a structured sequence with:
    - Duration in minutes (default to 15 if not specified)
    - Focus area (e.g., lower back, neck, shoulders)
    - Intensity (gentle, moderate, deep)
    - Techniques to use (kneading, tapping, rolling, or combinations)
    
If the user doesn't provide enough information, ask clarifying questions first to gather all required details.
Only use the function call when you have enough information to create a complete massage sequence.
    
Engage in a natural conversation and be helpful in guiding the user to specify their needs."""

    seq_template = """You are an AI assistant specialising in information retrieval and analysis. Answer the following question based only on the given context:
      Context: {context}
      Question: {question}"""
    op.add_column(
        "chat_configs",
        sa.Column(
            "tool_call_template", sa.Text(), nullable=True, server_default=tool_template
        ),
    )
    op.add_column(
        "chat_configs",
        sa.Column(
            "sequence_template", sa.Text(), nullable=True, server_default=seq_template
        ),
    )
    op.drop_column("chat_configs", "prompt_template")
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "chat_configs",
        sa.Column("prompt_template", sa.TEXT(), autoincrement=False, nullable=True),
    )
    op.drop_column("chat_configs", "sequence_template")
    op.drop_column("chat_configs", "tool_call_template")
    # ### end Alembic commands ###
