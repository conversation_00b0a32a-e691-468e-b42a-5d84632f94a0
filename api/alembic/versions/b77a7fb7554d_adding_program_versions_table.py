"""Adding program versions table

Revision ID: b77a7fb7554d
Revises: a02f65edc9cf
Create Date: 2025-05-19 12:00:11.568718

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = "b77a7fb7554d"
down_revision: Union[str, None] = "a02f65edc9cf"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "program_versions",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.<PERSON>umn("program_id", sa.Integer(), nullable=False),
        sa.<PERSON>umn("version_number", sa.Integer(), nullable=False),
        sa.<PERSON>umn("steps", sa.<PERSON>(), nullable=False),
        sa.<PERSON>umn("created_at", sa.DateTime(), nullable=True),
        sa.Column("created_by_user_id", sa.Integer(), nullable=False),
        sa.Column("version_notes", sa.Text(), nullable=True),
        sa.ForeignKeyConstraint(
            ["created_by_user_id"],
            ["users.id"],
        ),
        sa.ForeignKeyConstraint(
            ["program_id"],
            ["programs.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_program_versions_id"), "program_versions", ["id"], unique=False
    )
    op.add_column(
        "programs",
        sa.Column(
            "current_version_number",
            sa.Integer(),
            nullable=False,
            server_default="1",
        ),
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("programs", "current_version_number")
    op.drop_index(op.f("ix_program_versions_id"), table_name="program_versions")
    op.drop_table("program_versions")
    # ### end Alembic commands ###
