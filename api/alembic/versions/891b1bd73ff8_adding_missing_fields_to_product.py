"""Adding missing fields to product

Revision ID: 891b1bd73ff8
Revises: ab5ef208aabc
Create Date: 2025-04-16 15:39:20.751042

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '891b1bd73ff8'
down_revision: Union[str, None] = 'ab5ef208aabc'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('products', sa.Column('model', sa.String(), nullable=True))
    op.add_column('products', sa.Column('series', sa.String(), nullable=True))
    op.add_column('products', sa.Column('type', sa.String(), nullable=True))
    op.add_column('products', sa.Column('subtype', sa.String(), nullable=True))
    op.add_column('products', sa.Column('features', sa.JSON(), nullable=True))
    op.create_index(op.f('ix_products_model'), 'products', ['model'], unique=True)
    op.create_index(op.f('ix_products_series'), 'products', ['series'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_products_series'), table_name='products')
    op.drop_index(op.f('ix_products_model'), table_name='products')
    op.drop_column('products', 'features')
    op.drop_column('products', 'subtype')
    op.drop_column('products', 'type')
    op.drop_column('products', 'series')
    op.drop_column('products', 'model')
    # ### end Alembic commands ###
