"""Adding image_url to product

Revision ID: fe54666205a1
Revises: 891b1bd73ff8
Create Date: 2025-04-20 10:12:35.751042

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = "fe54666205a1"
down_revision: Union[str, None] = "891b1bd73ff8"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("products", sa.Column("image_url", sa.String(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("products", "image_url")
    # ### end Alembic commands ###
