"""Adding programs and Subroutines Table

Revision ID: 53346e490c3a
Revises: 1d0e4fc4e391
Create Date: 2025-04-16 12:16:01.778651

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '53346e490c3a'
down_revision: Union[str, None] = '1d0e4fc4e391'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('sub_routines',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('subroutine_id_json', sa.String(), nullable=True),
    sa.Column('name', sa.String(), nullable=True),
    sa.Column('files', sa.JSON(), nullable=True),
    sa.Column('column_mapping_used', sa.JSON(), nullable=True),
    sa.Column('steps', sa.JSON(), nullable=True),
    sa.Column('overall_settings', sa.JSO<PERSON>(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_sub_routines_id'), 'sub_routines', ['id'], unique=False)
    op.create_index(op.f('ix_sub_routines_subroutine_id_json'), 'sub_routines', ['subroutine_id_json'], unique=True)
    op.create_table('programs',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('product_id', sa.Integer(), nullable=True),
    sa.Column('name', sa.String(), nullable=True),
    sa.Column('source_folder', sa.String(), nullable=True),
    sa.Column('source_filename', sa.String(), nullable=True),
    sa.Column('logic_technique', sa.Text(), nullable=True),
    sa.Column('program_title', sa.String(), nullable=True),
    sa.Column('source_notes', sa.Text(), nullable=True),
    sa.Column('program_description', sa.JSON(), nullable=True),
    sa.Column('overall_settings', sa.JSON(), nullable=True),
    sa.Column('steps', sa.JSON(), nullable=True),
    sa.Column('column_mapping_used', sa.JSON(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['product_id'], ['products.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_programs_id'), 'programs', ['id'], unique=False)
    op.create_table('program_subroutine_association',
    sa.Column('program_id', sa.Integer(), nullable=False),
    sa.Column('sub_routine_id', sa.Integer(), nullable=False),
    sa.ForeignKeyConstraint(['program_id'], ['programs.id'], ),
    sa.ForeignKeyConstraint(['sub_routine_id'], ['sub_routines.id'], ),
    sa.PrimaryKeyConstraint('program_id', 'sub_routine_id')
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('program_subroutine_association')
    op.drop_index(op.f('ix_programs_id'), table_name='programs')
    op.drop_table('programs')
    op.drop_index(op.f('ix_sub_routines_subroutine_id_json'), table_name='sub_routines')
    op.drop_index(op.f('ix_sub_routines_id'), table_name='sub_routines')
    op.drop_table('sub_routines')
    # ### end Alembic commands ###
