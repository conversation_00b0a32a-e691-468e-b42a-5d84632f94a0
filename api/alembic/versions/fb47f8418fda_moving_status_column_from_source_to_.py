"""Moving status column from Source to Files

Revision ID: fb47f8418fda
Revises: bc92243d907d
Create Date: 2025-02-11 13:40:07.776116

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'fb47f8418fda'
down_revision: Union[str, None] = 'bc92243d907d'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('files', sa.Column('status', sa.String(), nullable=True))
    op.add_column('files', sa.Column('vector_ids', sa.JSON(), nullable=True))
    op.drop_column('sources', 'status')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('sources', sa.Column('status', sa.VARCHAR(), autoincrement=False, nullable=True))
    op.drop_column('files', 'vector_ids')
    op.drop_column('files', 'status')
    # ### end Alembic commands ###
