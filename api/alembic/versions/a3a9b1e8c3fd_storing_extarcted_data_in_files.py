"""Storing extarcted data in files

Revision ID: a3a9b1e8c3fd
Revises: fb47f8418fda
Create Date: 2025-02-11 16:40:26.034878

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'a3a9b1e8c3fd'
down_revision: Union[str, None] = 'fb47f8418fda'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('files', sa.Column('extracted', sa.JSON(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('files', 'extracted')
    # ### end Alembic commands ###
