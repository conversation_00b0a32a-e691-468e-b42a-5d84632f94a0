"""Adding user to chat config - AI engineer

Revision ID: b8bb2d013560
Revises: c0c88e38f56c
Create Date: 2025-02-12 15:31:12.907961

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'b8bb2d013560'
down_revision: Union[str, None] = 'c0c88e38f56c'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('chat_configs', sa.Column('user_id', sa.Integer(), nullable=True))
    op.create_foreign_key(None, 'chat_configs', 'users', ['user_id'], ['id'])
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'chat_configs', type_='foreignkey')
    op.drop_column('chat_configs', 'user_id')
    # ### end Alembic commands ###
