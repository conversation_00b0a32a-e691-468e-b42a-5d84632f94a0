"""Adding sft model name and boolean flag to chatconfig

Revision ID: 8f48d5de31c3
Revises: e31b80032d28
Create Date: 2025-06-03 13:19:21.239495

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '8f48d5de31c3'
down_revision: Union[str, None] = 'e31b80032d28'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('chat_configs', sa.Column('sft_model_name', sa.String(), nullable=True))
    op.add_column('chat_configs', sa.Column('use_sft_direct_generation', sa.<PERSON>(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('chat_configs', 'use_sft_direct_generation')
    op.drop_column('chat_configs', 'sft_model_name')
    # ### end Alembic commands ###
