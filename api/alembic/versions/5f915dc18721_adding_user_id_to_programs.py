"""Adding user_id to programs

Revision ID: 5f915dc18721
Revises: 50c6f84632a7
Create Date: 2025-05-14 11:47:43.102139

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '5f915dc18721'
down_revision: Union[str, None] = '50c6f84632a7'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('programs', sa.Column('user_id', sa.Integer(), nullable=True))
    op.create_foreign_key(None, 'programs', 'users', ['user_id'], ['id'])
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'programs', type_='foreignkey')
    op.drop_column('programs', 'user_id')
    # ### end Alembic commands ###
