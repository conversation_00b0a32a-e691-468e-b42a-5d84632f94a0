"""Adding more models

Revision ID: c56aa07d8fbb
Revises: 3038ab40b627
Create Date: 2025-03-25 15:36:28.190523

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy import text


# revision identifiers, used by Alembic.
revision: str = "c56aa07d8fbb"
down_revision: Union[str, None] = "3038ab40b627"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    result = (
        op.get_bind()
        .execute(text("SELECT id FROM chat_models WHERE id = 9"))
        .fetchone()
    )
    if not result:
        op.bulk_insert(
            sa.table(
                "chat_models",
                sa.Column("id", sa.Integer),
                sa.Column("name", sa.String),
                sa.Column("provider", sa.String),
            ),
            [
                {
                    "id": 9,
                    "name": "us.anthropic.claude-3-7-sonnet-20250219-v1:0",
                    "provider": "anthropic",
                }
            ],
        )


def downgrade() -> None:
    op.execute("DELETE FROM chat_models WHERE id = 9")
