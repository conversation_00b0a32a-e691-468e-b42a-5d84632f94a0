"""Adding program_id to messages

Revision ID: 482145b7373e
Revises: fe54666205a1
Create Date: 2025-05-07 10:23:37.482068

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '482145b7373e'
down_revision: Union[str, None] = 'fe54666205a1'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('messages', sa.Column('program_id', sa.Integer(), nullable=True))
    op.create_foreign_key(None, 'messages', 'programs', ['program_id'], ['id'])
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'messages', type_='foreignkey')
    op.drop_column('messages', 'program_id')
    # ### end Alembic commands ###
