"""modifying updated date

Revision ID: 921da942b143
Revises: 8f48d5de31c3
Create Date: 2025-06-06 10:40:27.368883

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '921da942b143'
down_revision: Union[str, None] = '8f48d5de31c3'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('chat_configs', 'created_at',
               existing_type=postgresql.TIMESTAMP(),
               type_=sa.DateTime(timezone=True),
               server_default=sa.text('now()'),
               existing_nullable=True)
    op.alter_column('chat_configs', 'updated_at',
               existing_type=postgresql.TIMESTAMP(),
               type_=sa.DateTime(timezone=True),
               server_default=sa.text('now()'),
               existing_nullable=True)
    op.alter_column('chats', 'created_at',
               existing_type=postgresql.TIMESTAMP(),
               type_=sa.DateTime(timezone=True),
               server_default=sa.text('now()'),
               existing_nullable=True)
    op.alter_column('chats', 'updated_at',
               existing_type=postgresql.TIMESTAMP(),
               type_=sa.DateTime(timezone=True),
               server_default=sa.text('now()'),
               existing_nullable=True)
    op.alter_column('files', 'created_at',
               existing_type=postgresql.TIMESTAMP(),
               type_=sa.DateTime(timezone=True),
               server_default=sa.text('now()'),
               existing_nullable=True)
    op.alter_column('files', 'updated_at',
               existing_type=postgresql.TIMESTAMP(),
               type_=sa.DateTime(timezone=True),
               server_default=sa.text('now()'),
               existing_nullable=True)
    op.alter_column('messages', 'created_at',
               existing_type=postgresql.TIMESTAMP(),
               type_=sa.DateTime(timezone=True),
               server_default=sa.text('now()'),
               existing_nullable=True)
    op.alter_column('messages', 'updated_at',
               existing_type=postgresql.TIMESTAMP(),
               type_=sa.DateTime(timezone=True),
               server_default=sa.text('now()'),
               existing_nullable=True)
    op.alter_column('products', 'created_at',
               existing_type=postgresql.TIMESTAMP(),
               type_=sa.DateTime(timezone=True),
               server_default=sa.text('now()'),
               existing_nullable=True)
    op.alter_column('products', 'updated_at',
               existing_type=postgresql.TIMESTAMP(),
               type_=sa.DateTime(timezone=True),
               server_default=sa.text('now()'),
               existing_nullable=True)
    op.alter_column('program_categories', 'created_at',
               existing_type=postgresql.TIMESTAMP(),
               type_=sa.DateTime(timezone=True),
               server_default=sa.text('now()'),
               existing_nullable=True)
    op.alter_column('program_categories', 'updated_at',
               existing_type=postgresql.TIMESTAMP(),
               type_=sa.DateTime(timezone=True),
               server_default=sa.text('now()'),
               existing_nullable=True)
    op.alter_column('program_versions', 'created_at',
               existing_type=postgresql.TIMESTAMP(),
               type_=sa.DateTime(timezone=True),
               server_default=sa.text('now()'),
               existing_nullable=True)
    op.alter_column('programs', 'created_at',
               existing_type=postgresql.TIMESTAMP(),
               type_=sa.DateTime(timezone=True),
               server_default=sa.text('now()'),
               existing_nullable=True)
    op.alter_column('programs', 'updated_at',
               existing_type=postgresql.TIMESTAMP(),
               type_=sa.DateTime(timezone=True),
               server_default=sa.text('now()'),
               existing_nullable=True)
    op.alter_column('sources', 'created_at',
               existing_type=postgresql.TIMESTAMP(),
               type_=sa.DateTime(timezone=True),
               server_default=sa.text('now()'),
               existing_nullable=True)
    op.alter_column('sources', 'updated_at',
               existing_type=postgresql.TIMESTAMP(),
               type_=sa.DateTime(timezone=True),
               server_default=sa.text('now()'),
               existing_nullable=True)
    op.alter_column('sub_routines', 'created_at',
               existing_type=postgresql.TIMESTAMP(),
               type_=sa.DateTime(timezone=True),
               server_default=sa.text('now()'),
               existing_nullable=True)
    op.alter_column('sub_routines', 'updated_at',
               existing_type=postgresql.TIMESTAMP(),
               type_=sa.DateTime(timezone=True),
               server_default=sa.text('now()'),
               existing_nullable=True)
    op.alter_column('users', 'created_at',
               existing_type=postgresql.TIMESTAMP(),
               type_=sa.DateTime(timezone=True),
               server_default=sa.text('now()'),
               existing_nullable=True)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('users', 'created_at',
               existing_type=sa.DateTime(timezone=True),
               type_=postgresql.TIMESTAMP(),
               server_default=None,
               existing_nullable=True)
    op.alter_column('sub_routines', 'updated_at',
               existing_type=sa.DateTime(timezone=True),
               type_=postgresql.TIMESTAMP(),
               server_default=None,
               existing_nullable=True)
    op.alter_column('sub_routines', 'created_at',
               existing_type=sa.DateTime(timezone=True),
               type_=postgresql.TIMESTAMP(),
               server_default=None,
               existing_nullable=True)
    op.alter_column('sources', 'updated_at',
               existing_type=sa.DateTime(timezone=True),
               type_=postgresql.TIMESTAMP(),
               server_default=None,
               existing_nullable=True)
    op.alter_column('sources', 'created_at',
               existing_type=sa.DateTime(timezone=True),
               type_=postgresql.TIMESTAMP(),
               server_default=None,
               existing_nullable=True)
    op.alter_column('programs', 'updated_at',
               existing_type=sa.DateTime(timezone=True),
               type_=postgresql.TIMESTAMP(),
               server_default=None,
               existing_nullable=True)
    op.alter_column('programs', 'created_at',
               existing_type=sa.DateTime(timezone=True),
               type_=postgresql.TIMESTAMP(),
               server_default=None,
               existing_nullable=True)
    op.alter_column('program_versions', 'created_at',
               existing_type=sa.DateTime(timezone=True),
               type_=postgresql.TIMESTAMP(),
               server_default=None,
               existing_nullable=True)
    op.alter_column('program_categories', 'updated_at',
               existing_type=sa.DateTime(timezone=True),
               type_=postgresql.TIMESTAMP(),
               server_default=None,
               existing_nullable=True)
    op.alter_column('program_categories', 'created_at',
               existing_type=sa.DateTime(timezone=True),
               type_=postgresql.TIMESTAMP(),
               server_default=None,
               existing_nullable=True)
    op.alter_column('products', 'updated_at',
               existing_type=sa.DateTime(timezone=True),
               type_=postgresql.TIMESTAMP(),
               server_default=None,
               existing_nullable=True)
    op.alter_column('products', 'created_at',
               existing_type=sa.DateTime(timezone=True),
               type_=postgresql.TIMESTAMP(),
               server_default=None,
               existing_nullable=True)
    op.alter_column('messages', 'updated_at',
               existing_type=sa.DateTime(timezone=True),
               type_=postgresql.TIMESTAMP(),
               server_default=None,
               existing_nullable=True)
    op.alter_column('messages', 'created_at',
               existing_type=sa.DateTime(timezone=True),
               type_=postgresql.TIMESTAMP(),
               server_default=None,
               existing_nullable=True)
    op.alter_column('files', 'updated_at',
               existing_type=sa.DateTime(timezone=True),
               type_=postgresql.TIMESTAMP(),
               server_default=None,
               existing_nullable=True)
    op.alter_column('files', 'created_at',
               existing_type=sa.DateTime(timezone=True),
               type_=postgresql.TIMESTAMP(),
               server_default=None,
               existing_nullable=True)
    op.alter_column('chats', 'updated_at',
               existing_type=sa.DateTime(timezone=True),
               type_=postgresql.TIMESTAMP(),
               server_default=None,
               existing_nullable=True)
    op.alter_column('chats', 'created_at',
               existing_type=sa.DateTime(timezone=True),
               type_=postgresql.TIMESTAMP(),
               server_default=None,
               existing_nullable=True)
    op.alter_column('chat_configs', 'updated_at',
               existing_type=sa.DateTime(timezone=True),
               type_=postgresql.TIMESTAMP(),
               server_default=None,
               existing_nullable=True)
    op.alter_column('chat_configs', 'created_at',
               existing_type=sa.DateTime(timezone=True),
               type_=postgresql.TIMESTAMP(),
               server_default=None,
               existing_nullable=True)
    # ### end Alembic commands ###
