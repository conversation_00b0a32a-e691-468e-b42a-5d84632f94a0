"""Removing subroutine_id_json as a unique

Revision ID: 149ac6e86b30
Revises: ef0ec5147b1a
Create Date: 2025-04-16 12:45:54.746547

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '149ac6e86b30'
down_revision: Union[str, None] = 'ef0ec5147b1a'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index('ix_sub_routines_subroutine_id_json', table_name='sub_routines')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_index('ix_sub_routines_subroutine_id_json', 'sub_routines', ['subroutine_id_json'], unique=True)
    # ### end Alembic commands ###
