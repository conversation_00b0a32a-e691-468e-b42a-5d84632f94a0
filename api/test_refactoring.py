#!/usr/bin/env python3
"""
Simple test to verify the refactored ChatService and SplitAndStitchService work correctly.
This test checks that the new streaming interface maintains the same functionality.
"""

import asyncio
import sys
import os

# Add the api directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from unittest.mock import Mock, AsyncMock, patch
from services.chat_service import ChatService
from services.step_generation.split_and_stitch_service import SplitAndStitchService
from schemas.chat_schemas import MassageProgramDetails, TargetSequence, HighEndProductSteps, HighEndProductStep, ProgrammeSequenceDetail
from models.program_models import Program
from models.account_models import User


async def test_split_and_stitch_service_initialization():
    """Test that SplitAndStitchService can be initialized with a database session."""
    print("Testing SplitAndStitchService initialization...")
    
    # Mock database session
    mock_db = Mock()
    
    # Test initialization without db (should work)
    service_no_db = SplitAndStitchService()
    assert service_no_db.db is None
    assert service_no_db.pinecone_index is None
    print("✓ SplitAndStitchService initialization without DB works")
    
    # Test initialization with db
    with patch('services.step_generation.split_and_stitch_service.get_pinecone_index') as mock_pinecone:
        mock_pinecone.return_value = Mock()
        service_with_db = SplitAndStitchService(mock_db)
        assert service_with_db.db == mock_db
        assert service_with_db.pinecone_index is not None
        print("✓ SplitAndStitchService initialization with DB works")


async def test_chat_service_factory_method():
    """Test that ChatService factory method passes DB to SplitAndStitchService."""
    print("Testing ChatService factory method...")
    
    mock_db = Mock()
    mock_user = Mock(spec=User)
    
    with patch('services.step_generation.split_and_stitch_service.get_pinecone_index') as mock_pinecone:
        mock_pinecone.return_value = Mock()
        chat_service = ChatService.create_with_default_services(mock_db, mock_user)
        
        # Verify that the split_and_stitch_service has the database session
        assert chat_service.split_and_stitch_service.db == mock_db
        assert chat_service.split_and_stitch_service.pinecone_index is not None
        print("✓ ChatService factory method correctly passes DB to SplitAndStitchService")


async def test_streaming_interface_structure():
    """Test that the streaming interface yields the expected structure."""
    print("Testing streaming interface structure...")
    
    # Mock dependencies
    mock_db = Mock()
    mock_details = MassageProgramDetails(
        target_market="test",
        duration=30,
        focus_area=["back"],
        problem_desired_outcome="relaxation"
    )
    
    # Create service with mocked dependencies
    with patch('services.step_generation.split_and_stitch_service.get_pinecone_index') as mock_pinecone, \
         patch('services.step_generation.split_and_stitch_service.sequence_generator_agent') as mock_agent, \
         patch.object(SplitAndStitchService, '_perform_semantic_search') as mock_search, \
         patch.object(SplitAndStitchService, 'synthesize_steps') as mock_synthesize:
        
        mock_pinecone.return_value = Mock()
        
        # Mock target sequence generation
        mock_target_sequence = TargetSequence(
            name="Test Program",
            objective="Test objective",
            programme_sequence=ProgrammeSequenceDetail(**{
                "Front Sequence": "warm up",
                "Main Sequence": "main massage",
                "Cooling Sequence": "cool down"
            }),
            targeted_acupressure_points=[],
            signature_moves=[]
        )
        mock_agent_result = Mock()
        mock_agent_result.output = mock_target_sequence
        mock_agent.run = AsyncMock(return_value=mock_agent_result)
        
        # Mock semantic search
        mock_programs = [Mock(spec=Program)]
        mock_programs_data = [{"id": 1, "name": "Test Program"}]
        mock_search.return_value = (mock_programs, mock_programs_data)
        
        # Mock synthesis
        mock_steps = HighEndProductSteps(steps=[
            HighEndProductStep(step_number="1", type="action", roller_action_description="test")
        ])
        mock_synthesize.return_value = mock_steps
        
        # Test the streaming interface
        service = SplitAndStitchService(mock_db)
        results = []
        
        async for result in service.synthesize_steps_with_streaming(mock_details, 1):
            results.append(result)
        
        # Verify the expected sequence of results
        assert len(results) == 4, f"Expected 4 results, got {len(results)}"
        
        # Check target_sequence result
        assert results[0]["type"] == "target_sequence"
        assert "content" in results[0]
        assert "agent_result" in results[0]
        
        # Check programs result
        assert results[1]["type"] == "programs"
        assert "content" in results[1]
        assert "fetched_programs" in results[1]
        
        # Check synthesizing result
        assert results[2]["type"] == "synthesizing"
        assert "content" in results[2]
        
        # Check synthesized_steps result
        assert results[3]["type"] == "synthesized_steps"
        assert "content" in results[3]
        assert "target_sequence" in results[3]
        
        print("✓ Streaming interface yields expected structure")


async def main():
    """Run all tests."""
    print("Starting refactoring verification tests...\n")
    
    try:
        await test_split_and_stitch_service_initialization()
        print()
        
        await test_chat_service_factory_method()
        print()
        
        await test_streaming_interface_structure()
        print()
        
        print("🎉 All tests passed! The refactoring appears to be working correctly.")
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
