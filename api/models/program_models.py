from datetime import datetime

from sqlalchemy import (
    <PERSON>umn,
    <PERSON><PERSON><PERSON>,
    ForeignKey,
    String,
    Text,
    DateTime,
    JSON,
    Table,
    Boolean,
    func,
    Enum,
)
from sqlalchemy.orm import relationship
from core.database import Base
import enum


# --- Enums ---
class ProgramStatus(enum.Enum):
    DRAFT = "Draft"
    IN_PROGRESS = "In Progress"
    READY_FOR_TESTING = "Ready for Testing"
    TESTED = "Tested"
    VERIFIED = "Verified"

    def __str__(self):
        return self.value


class SegmentationJobStatus(enum.Enum):
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


# --- Product Model ---
class Product(Base):
    __tablename__ = "products"
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String)
    model = Column(String, unique=True, index=True)  # From JSON (Unique assumed)
    series = Column(String, nullable=True, index=True)  # From JSON
    type = Column(String, nullable=True)  # From JSON
    subtype = Column(String, nullable=True)  # From JSON
    features = Column(JSON, nullable=True)  # From JSON (List of strings)
    description = Column(Text)
    image_url = Column(String, nullable=True)  # Optional image URL
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    chat_configs = relationship("ChatConfig", back_populates="product")
    programs = relationship("Program", back_populates="product")
    sub_routines = relationship("SubRoutine", back_populates="product")

    def __repr__(self):
        return f"<Product(name={self.name})>"


# --- Association Table ---
# This table links Programs and SubRoutines for the many-to-many relationship
program_subroutine_association = Table(
    "program_subroutine_association",
    Base.metadata,
    Column("program_id", Integer, ForeignKey("programs.id"), primary_key=True),
    Column("sub_routine_id", Integer, ForeignKey("sub_routines.id"), primary_key=True),
)


# --- Program Model ---
class Program(Base):
    __tablename__ = "programs"
    id = Column(Integer, primary_key=True, index=True)
    product_id = Column(Integer, ForeignKey("products.id"))
    category_id = Column(Integer, ForeignKey("program_categories.id"))
    user_id = Column(Integer, ForeignKey("users.id"))
    name = Column(String, nullable=True)
    source_folder = Column(String)
    source_filename = Column(String)
    logic_technique = Column(Text, nullable=True)
    program_title = Column(String)
    source_notes = Column(Text, nullable=True)
    program_description = Column(JSON, nullable=True)
    overall_settings = Column(JSON, nullable=True)
    steps = Column(JSON)  # Represents the steps of the *active* version
    column_mapping_used = Column(JSON, nullable=True)
    liked = Column(Boolean, default=False)
    disliked = Column(Boolean, default=False)
    saved = Column(Boolean, default=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    current_version_number = Column(Integer, default=1, nullable=False)
    status = Column(Enum(ProgramStatus, values_callable=lambda obj: [e.value for e in obj]), nullable=True, default=ProgramStatus.DRAFT)

    product = relationship("Product", back_populates="programs")
    category = relationship("ProgramCategory", back_populates="programs")
    # Define the many-to-many relationship using the association table
    sub_routines = relationship(
        "SubRoutine",
        secondary=program_subroutine_association,
        back_populates="programs",
    )
    messages = relationship("Message", back_populates="program")
    user = relationship("User", back_populates="programs")
    versions = relationship(
        "ProgramVersion",
        back_populates="program",
        order_by="ProgramVersion.version_number.desc()",
    )

    def __repr__(self):
        return f"<Program(name={self.name}, id={self.id})>"

    @property
    def product_name(self):
        return self.product.name if self.product else None

    @property
    def product_model(self):
        return (
            self.product.model
            if self.product and hasattr(self.product, "model")
            else None
        )

    @property
    def category_name(self):
        return self.category.name if self.category else None

    @property
    def user_name(self):
        return (
            self.user.username if self.user and hasattr(self.user, "username") else None
        )


# --- Program Category Model ---
class ProgramCategory(Base):
    __tablename__ = "program_categories"
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String)
    description = Column(Text, nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    programs = relationship("Program", back_populates="category")

    def __repr__(self):
        return f"<ProgramCategory(name={self.name})>"


# --- SubRoutine Model ---
class SubRoutine(Base):
    __tablename__ = "sub_routines"
    id = Column(Integer, primary_key=True, index=True)
    product_id = Column(Integer, ForeignKey("products.id"))
    subroutine_id_json = Column(String)  # Store the JSON ID like "SUB_001"
    name = Column(String)
    files = Column(JSON, nullable=True)
    column_mapping_used = Column(JSON, nullable=True)
    steps = Column(JSON, nullable=True)  # Subroutine steps
    overall_settings = Column(JSON, nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    product = relationship("Product", back_populates="sub_routines")
    # Define the many-to-many relationship back to Program
    programs = relationship(
        "Program",
        secondary=program_subroutine_association,
        back_populates="sub_routines",
    )

    def __repr__(self):
        return f"<SubRoutine(name={self.name})>"


# --- Program Segment Model ---
class ProgramSegment(Base):
    __tablename__ = "program_segments"

    id = Column(Integer, primary_key=True, index=True)
    original_program_id = Column(Integer, ForeignKey("programs.id"), nullable=False, index=True)

    # --- Hierarchy and Granularity ---
    phase = Column(String, index=True, nullable=False) # "front", "main", "cooling", or "discovered"
    parent_segment_id = Column(Integer, ForeignKey("program_segments.id"), nullable=True, index=True) # NULL for macro-phases
    position_in_parent = Column(String, nullable=True, index=True) # Position within parent macro phase ("front", "mid", "last") - NULL for macro-phases

    # --- The Segment Interface Contract ---
    entry_state = Column(JSON, nullable=False)
    exit_state = Column(JSON, nullable=False)

    # --- Rich Ontology Tags ---
    purpose_tags = Column(JSON)
    technique_tags = Column(JSON)
    body_part_tags = Column(JSON)

    # --- Objective Metrics ---
    intensity_score = Column(Integer)
    duration_seconds = Column(Integer)

    # --- AI-Generated Description ---
    description = Column(Text, nullable=True)  # LLM-generated therapeutic description

    # --- Core Data ---
    steps = Column(JSON, nullable=False)

    # --- Relationships ---
    original_program = relationship("Program")
    parent_segment = relationship("ProgramSegment", remote_side=[id], backref="child_segments")

    def __repr__(self):
        return f"<ProgramSegment(id={self.id}, program_id={self.original_program_id}, phase='{self.phase}')>"

# --- Program Version Model ---
class ProgramVersion(Base):
    __tablename__ = "program_versions"
    id = Column(Integer, primary_key=True, index=True)
    program_id = Column(Integer, ForeignKey("programs.id"), nullable=False)
    version_number = Column(Integer, nullable=False)
    steps = Column(JSON, nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    created_by_user_id = Column(Integer, ForeignKey("users.id"), nullable=True)
    version_notes = Column(Text, nullable=True)
    program = relationship("Program", back_populates="versions")
    user = relationship("User", back_populates="program_versions")

    def __repr__(self):
        return f"<ProgramVersion(program_id={self.program_id}, version={self.version_number})>"


# --- Product Segmentation Job Model ---
class ProductSegmentationJob(Base):
    __tablename__ = "product_segmentation_jobs"

    id = Column(Integer, primary_key=True, index=True)
    product_id = Column(Integer, ForeignKey("products.id"), nullable=False, index=True)
    status = Column(Enum(SegmentationJobStatus), default=SegmentationJobStatus.PENDING, nullable=False, index=True)
    progress_percentage = Column(Integer, default=0, nullable=False)
    current_step = Column(String, nullable=True)
    total_programs = Column(Integer, default=0, nullable=False)
    completed_programs = Column(Integer, default=0, nullable=False)
    failed_programs = Column(Integer, default=0, nullable=False)
    error_message = Column(Text, nullable=True)
    sqs_message_id = Column(String, nullable=True, index=True)  # Track SQS message for cancellation
    started_at = Column(DateTime(timezone=True), nullable=True)
    completed_at = Column(DateTime(timezone=True), nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # Relationships
    product = relationship("Product")
    program_jobs = relationship("SegmentationJob", back_populates="product_job")

    def __repr__(self):
        return f"<ProductSegmentationJob(id={self.id}, product_id={self.product_id}, status={self.status.value})>"


# --- Segmentation Job Model ---
class SegmentationJob(Base):
    __tablename__ = "segmentation_jobs"

    id = Column(Integer, primary_key=True, index=True)
    program_id = Column(Integer, ForeignKey("programs.id"), nullable=False, index=True)
    product_job_id = Column(Integer, ForeignKey("product_segmentation_jobs.id"), nullable=True, index=True)  # Optional parent job
    status = Column(Enum(SegmentationJobStatus), default=SegmentationJobStatus.PENDING, nullable=False, index=True)
    progress_percentage = Column(Integer, default=0, nullable=False)
    current_step = Column(String, nullable=True)
    total_steps = Column(Integer, default=0, nullable=False)
    completed_steps = Column(Integer, default=0, nullable=False)
    error_message = Column(Text, nullable=True)
    sqs_message_id = Column(String, nullable=True, index=True)  # Track SQS message for cancellation
    started_at = Column(DateTime(timezone=True), nullable=True)
    completed_at = Column(DateTime(timezone=True), nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # Relationships
    program = relationship("Program")
    product_job = relationship("ProductSegmentationJob", back_populates="program_jobs")

    def __repr__(self):
        return f"<SegmentationJob(id={self.id}, program_id={self.program_id}, status={self.status})>"
