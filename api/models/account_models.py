from datetime import datetime
from sqlalchemy import (
    Column,
    Integer,
    String,
    DateTime,
    func,
)
from sqlalchemy.orm import relationship
from core.database import Base


class User(Base):
    __tablename__ = "users"

    id = Column(Integer, primary_key=True, index=True)
    cognito_id = Column(String)
    username = Column(String)
    name = Column(String)
    email = Column(String)
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    sources = relationship("Source", back_populates="owner")
    chats = relationship("Chat", back_populates="user")
    chat_configs = relationship("ChatConfig", back_populates="user")
    programs = relationship("Program", back_populates="user")
    program_versions = relationship("ProgramVersion", back_populates="user")

    def __repr__(self):
        return f"<User(id={self.id}, username={self.username}, email={self.email})>"
