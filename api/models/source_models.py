from datetime import datetime

from sqlalchemy import (
    Column,
    Integer,
    String,
    ForeignKey,
    BigInteger,
    Text,
    DateTime,
    JSON,
    func,
)
from sqlalchemy.orm import relationship
from core.database import Base


class Source(Base):
    __tablename__ = "sources"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, unique=True, index=True)
    description = Column(String)
    pinecone_index = Column(String)
    s3_bucket_url = Column(String, nullable=True)
    owner_id = Column(Integer, ForeignKey("users.id"))
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    owner = relationship("User", back_populates="sources")
    files = relationship("File", back_populates="source")
    chat_configs = relationship("ChatConfig", back_populates="source")

    def __repr__(self):
        return f"<Source(name={self.name})>"


class File(Base):
    __tablename__ = "files"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, index=True)
    user_id = Column(Integer, ForeignKey("users.id"))
    uploaded_name = Column(String, index=True)
    status = Column(String, default="pending")  # pending, processing, active, failed
    source_id = Column(Integer, ForeignKey("sources.id"))
    extracted = Column(JSON)
    vector_ids = Column(JSON)
    file_type = Column(String)
    vector_id = Column(String)
    size = Column(BigInteger, default=0)
    content = Column(Text)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    source = relationship("Source", back_populates="files")

    def __repr__(self):
        return f"<File(name={self.name})>"
