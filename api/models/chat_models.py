
import secrets
from datetime import datetime

from sqlalchemy import (
    Column,
    Integer,
    String,
    ForeignKey,
    Text,
    DateTime,
    Float,
    Boolean,
    JSON,
    func,
)
from sqlalchemy.orm import relationship
from core.database import Base


class ChatModels(Base):
    __tablename__ = "chat_models"
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, index=True)
    provider = Column(String)

    chat_configs = relationship("ChatConfig", back_populates="model")

    def __repr__(self):
        return f"<ChatModels(name={self.name})>"


class ChatConfig(Base):
    __tablename__ = "chat_configs"
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, index=True)
    url = Column(String)
    model_id = Column(Integer, ForeignKey("chat_models.id"))
    source_id = Column(Integer, ForeignKey("sources.id"))
    user_id = Column(Integer, ForeignKey("users.id"))
    product_id = Column(Integer, Foreign<PERSON>ey("products.id"))
    tool_call_template = Column(Text)
    sequence_template = Column(Text)
    temperature = Column(Float, default=2.0)
    use_reranking = Column(Boolean, default=False)
    search_type = Column(String, default="similarity")
    fetch_k = Column(Integer, default=4)  # For MMR search
    lambda_mult = Column(Float, default=0.5)  # For MMR search
    score_threshold = Column(
        Float, default=0.5
    )  # For similarity score threshold search
    similar_docs = Column(Integer, default=2)
    top_k = Column(Integer, default=5)
    compressor_model = Column(String, default="local-infinity")

    # for fine-tuned models
    sft_model_name = Column(String, nullable=True)
    use_sft_direct_generation = Column(Boolean, default=False)

    # step synthesis strategy selection
    synthesis_strategy = Column(String, default="segment_and_stitch", nullable=True)

    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    model = relationship("ChatModels", back_populates="chat_configs")
    source = relationship("Source", back_populates="chat_configs")
    chats = relationship("Chat", back_populates="config")
    user = relationship("User", back_populates="chat_configs")
    product = relationship("Product", back_populates="chat_configs")

    def __repr__(self):
        return f"<ChatConfig(name={self.name})>"


class Chat(Base):
    __tablename__ = "chats"

    id = Column(Integer, primary_key=True, index=True)
    slug = Column(String, default=secrets.token_urlsafe(32))
    user_id = Column(Integer, ForeignKey("users.id"))
    config_id = Column(Integer, ForeignKey("chat_configs.id"))
    is_starred = Column(Boolean, default=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    user = relationship("User", back_populates="chats")
    config = relationship("ChatConfig", back_populates="chats")
    messages = relationship("Message", back_populates="chat")

    def __repr__(self):
        return f"<Chat(user_id={self.user_id}, config_id={self.config_id})>"


class Message(Base):
    __tablename__ = "messages"

    id = Column(Integer, primary_key=True, index=True)
    role = Column(String, default="user")
    content = Column(Text)
    model_name = Column(String)
    chat_id = Column(Integer, ForeignKey("chats.id"))
    program_id = Column(
        Integer, ForeignKey("programs.id", ondelete="SET NULL"), nullable=True
    )
    documents = Column(JSON)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    chat = relationship("Chat", back_populates="messages")
    program = relationship("Program", back_populates="messages")

    def __repr__(self):
        return f"<Message(role={self.role}, content={self.content})>"
