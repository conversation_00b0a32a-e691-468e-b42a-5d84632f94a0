from sqlalchemy.orm import Session, joinedload
from sqlalchemy import or_, desc, asc, func, case, cast, String, text
from fastapi import HTTPException
from typing import List, Optional, Dict, Any
import logging

from models.program_models import ProgramSegment, Program, SegmentationJob, ProductSegmentationJob
from schemas.program_schemas import (
    ProgramSegmentSchema,
    ProgramSegmentListResponse,
    SegmentCleanupResponse,
    SegmentCleanupStatistics,
    SegmentCleanupFilters
)
from utils.pinecone_ops import get_pinecone_index
from config import settings

logger = logging.getLogger(__name__)


def list_program_segments(
    db: Session,
    skip: int = 0,
    limit: int = 20,
    program_id: Optional[int] = None,
    phase: Optional[str] = None,
    is_macro_phase: Optional[bool] = None,
    product_id: Optional[int] = None,
    search: Optional[str] = None,
    sort_field: str = "id",
    sort_direction: str = "asc",
) -> ProgramSegmentListResponse:
    """
    Retrieve a paginated list of program segments with optional filtering and sorting.
    """
    query = db.query(ProgramSegment).options(
        joinedload(ProgramSegment.original_program),
        joinedload(ProgramSegment.parent_segment)
    )

    # Apply filters
    if program_id:
        query = query.filter(ProgramSegment.original_program_id == program_id)
    
    if phase:
        query = query.filter(ProgramSegment.phase == phase)
    
    if is_macro_phase is not None:
        if is_macro_phase:
            query = query.filter(ProgramSegment.parent_segment_id.is_(None))
        else:
            query = query.filter(ProgramSegment.parent_segment_id.isnot(None))
    
    if product_id:
        query = query.join(Program).filter(Program.product_id == product_id)
    
    if search:
        # Search in purpose tags, technique tags, body part tags
        search_conditions = []
        search_term = f"%{search.lower()}%"

        # Search in JSON arrays (tags) using cast to String and ILIKE
        search_conditions.extend([
            cast(ProgramSegment.purpose_tags, String).ilike(search_term),
            cast(ProgramSegment.technique_tags, String).ilike(search_term),
            cast(ProgramSegment.body_part_tags, String).ilike(search_term),
        ])

        query = query.filter(or_(*search_conditions))

    # Apply sorting
    sort_column = getattr(ProgramSegment, sort_field, ProgramSegment.id)
    if sort_direction.lower() == "desc":
        query = query.order_by(desc(sort_column))
    else:
        query = query.order_by(asc(sort_column))

    # Get total count before applying pagination
    total = query.count()

    # Apply pagination
    segments = query.offset(skip).limit(limit).all()

    return ProgramSegmentListResponse(
        segments=[ProgramSegmentSchema.model_validate(segment) for segment in segments],
        total=total,
        page=skip // limit + 1,
        limit=limit
    )


def get_segment_by_id(db: Session, segment_id: int) -> ProgramSegmentSchema:
    """
    Retrieve a specific program segment by its ID.
    """
    segment = db.query(ProgramSegment).options(
        joinedload(ProgramSegment.original_program),
        joinedload(ProgramSegment.parent_segment)
    ).filter(ProgramSegment.id == segment_id).first()
    
    if not segment:
        raise HTTPException(status_code=404, detail="Segment not found")
    
    return ProgramSegmentSchema.model_validate(segment)


def list_segments_by_program(
    db: Session,
    program_id: int,
    include_micro_chunks: bool = True
) -> List[ProgramSegmentSchema]:
    """
    Retrieve all segments for a specific program.
    """
    query = db.query(ProgramSegment).options(
        joinedload(ProgramSegment.parent_segment)
    ).filter(ProgramSegment.original_program_id == program_id)
    
    if not include_micro_chunks:
        query = query.filter(ProgramSegment.parent_segment_id.is_(None))
    
    # Order by hierarchy: macro phases first, then micro chunks
    query = query.order_by(
        ProgramSegment.parent_segment_id.is_(None).desc(),  # Macro phases first
        ProgramSegment.parent_segment_id,  # Group micro chunks by parent
        ProgramSegment.id
    )
    
    segments = query.all()
    
    return [ProgramSegmentSchema.model_validate(segment) for segment in segments]


def get_segment_hierarchy(db: Session, program_id: int) -> Dict[str, Any]:
    """
    Retrieve the hierarchical structure of segments for a specific program.
    Returns macro phases with their nested micro chunks.
    """
    # Get all segments for the program
    segments = db.query(ProgramSegment).filter(
        ProgramSegment.original_program_id == program_id
    ).order_by(ProgramSegment.id).all()
    
    if not segments:
        # Return empty hierarchy structure instead of 404
        return {
            "program_id": program_id,
            "total_segments": 0,
            "macro_phases": [],
            "summary": {
                "macro_phase_count": 0,
                "micro_chunk_count": 0,
                "phases": {}
            }
        }
    
    # Separate macro phases and micro chunks
    macro_phases = [s for s in segments if s.parent_segment_id is None]
    micro_chunks = [s for s in segments if s.parent_segment_id is not None]
    
    # Group micro chunks by parent
    micro_chunks_by_parent = {}
    for chunk in micro_chunks:
        parent_id = chunk.parent_segment_id
        if parent_id not in micro_chunks_by_parent:
            micro_chunks_by_parent[parent_id] = []
        micro_chunks_by_parent[parent_id].append(chunk)
    
    # Build hierarchy
    hierarchy = {
        "program_id": program_id,
        "total_segments": len(segments),
        "macro_phases": [],
        "summary": {
            "macro_phase_count": len(macro_phases),
            "micro_chunk_count": len(micro_chunks),
            "phases": {}
        }
    }
    
    for macro_phase in macro_phases:
        macro_data = {
            "segment": ProgramSegmentSchema.model_validate(macro_phase).model_dump(),
            "micro_chunks": []
        }
        
        # Add micro chunks for this macro phase
        if macro_phase.id in micro_chunks_by_parent:
            for micro_chunk in micro_chunks_by_parent[macro_phase.id]:
                macro_data["micro_chunks"].append(
                    ProgramSegmentSchema.model_validate(micro_chunk).model_dump()
                )
        
        hierarchy["macro_phases"].append(macro_data)
        
        # Add to summary
        hierarchy["summary"]["phases"][macro_phase.phase] = {
            "macro_phase_id": macro_phase.id,
            "micro_chunk_count": len(macro_data["micro_chunks"]),
            "intensity_score": macro_phase.intensity_score,
            "duration_seconds": macro_phase.duration_seconds
        }
    
    return hierarchy


def get_segment_stats(
    db: Session,
    program_id: Optional[int] = None,
    phase: Optional[str] = None,
    is_macro_phase: Optional[bool] = None,
    product_id: Optional[int] = None,
    search: Optional[str] = None,
) -> Dict[str, Any]:
    """
    Get aggregated statistics for segments with optional filtering.
    Returns counts, averages, and distributions without fetching all segment data.
    """
    # Base query with the same filters as list_program_segments
    query = db.query(ProgramSegment)

    # Apply filters
    if program_id:
        query = query.filter(ProgramSegment.original_program_id == program_id)

    if phase:
        query = query.filter(ProgramSegment.phase == phase)

    if is_macro_phase is not None:
        if is_macro_phase:
            query = query.filter(ProgramSegment.parent_segment_id.is_(None))
        else:
            query = query.filter(ProgramSegment.parent_segment_id.isnot(None))

    if product_id:
        query = query.join(Program).filter(Program.product_id == product_id)

    if search:
        # Search in purpose tags, technique tags, body part tags
        search_conditions = []
        search_term = f"%{search.lower()}%"

        # Search in JSON arrays (tags) using cast to String and ILIKE
        search_conditions.extend([
            cast(ProgramSegment.purpose_tags, String).ilike(search_term),
            cast(ProgramSegment.technique_tags, String).ilike(search_term),
            cast(ProgramSegment.body_part_tags, String).ilike(search_term),
        ])

        query = query.filter(or_(*search_conditions))

    # Get aggregated statistics using SQL aggregation functions
    stats_query = query.with_entities(
        func.count(ProgramSegment.id).label('total_segments'),
        func.count(case((ProgramSegment.parent_segment_id.is_(None), 1))).label('macro_phases'),
        func.count(case((ProgramSegment.parent_segment_id.isnot(None), 1))).label('micro_chunks'),
        func.avg(ProgramSegment.intensity_score).label('avg_intensity'),
        func.sum(ProgramSegment.duration_seconds).label('total_duration'),
        func.count(case((ProgramSegment.phase == 'front', 1))).label('front_count'),
        func.count(case((ProgramSegment.phase == 'main', 1))).label('main_count'),
        func.count(case((ProgramSegment.phase == 'cooling', 1))).label('cooling_count'),
        func.count(case((ProgramSegment.phase == 'discovered', 1))).label('discovered_count'),
        func.count(func.distinct(ProgramSegment.original_program_id)).label('analyzed_programs'),
    ).first()

    # Convert to dictionary and handle None values
    total_segments = stats_query.total_segments or 0
    macro_phases = stats_query.macro_phases or 0
    micro_chunks = stats_query.micro_chunks or 0
    avg_intensity = float(stats_query.avg_intensity or 0)
    total_duration = stats_query.total_duration or 0
    analyzed_programs = stats_query.analyzed_programs or 0

    # Phase distribution
    phase_distribution = {
        'front': stats_query.front_count or 0,
        'main': stats_query.main_count or 0,
        'cooling': stats_query.cooling_count or 0,
        'discovered': stats_query.discovered_count or 0,
    }

    return {
        'total_segments': total_segments,
        'macro_phases': macro_phases,
        'micro_chunks': micro_chunks,
        'avg_intensity': round(avg_intensity, 1),
        'total_duration': total_duration,
        'phase_distribution': phase_distribution,
        'analyzed_programs': analyzed_programs,
    }


# --- Cleanup Function ---
async def clear_segments_and_jobs(
    db: Session,
    product_id: Optional[int] = None,
    program_id: Optional[int] = None
) -> SegmentCleanupResponse:
    """
    Clear program segments and segmentation jobs with optional filtering.
    Follows the same pattern as clear_programs_and_subroutines.

    Args:
        db: Database session
        product_id: Optional filter to clear segments only for specific product
        program_id: Optional filter to clear segments only for specific program

    Returns:
        Dictionary with cleanup results and statistics
    """
    logger.info("Starting cleanup of program segments and segmentation jobs...")

    try:
        # Build filter conditions
        segment_filter_conditions = []
        job_filter_conditions = []
        product_job_filter_conditions = []

        if program_id:
            segment_filter_conditions.append(ProgramSegment.original_program_id == program_id)
            job_filter_conditions.append(SegmentationJob.program_id == program_id)
            # For program-level cleanup, we don't delete product jobs (they may have other programs)
            logger.info(f"Filtering by program_id: {program_id}")
        elif product_id:
            # For product filtering, need to join with programs table
            segment_filter_conditions.append(
                ProgramSegment.original_program_id.in_(
                    db.query(Program.id).filter(Program.product_id == product_id)
                )
            )
            job_filter_conditions.append(
                SegmentationJob.program_id.in_(
                    db.query(Program.id).filter(Program.product_id == product_id)
                )
            )
            # Also delete product segmentation jobs for this product
            product_job_filter_conditions.append(ProductSegmentationJob.product_id == product_id)
            logger.info(f"Filtering by product_id: {product_id}")

        # Get counts before deletion for reporting
        segment_query = db.query(ProgramSegment)
        job_query = db.query(SegmentationJob)
        product_job_query = db.query(ProductSegmentationJob)

        if segment_filter_conditions:
            segment_query = segment_query.filter(*segment_filter_conditions)
        if job_filter_conditions:
            job_query = job_query.filter(*job_filter_conditions)
        if product_job_filter_conditions:
            product_job_query = product_job_query.filter(*product_job_filter_conditions)

        segments_to_delete = segment_query.count()
        jobs_to_delete = job_query.count()
        product_jobs_to_delete = product_job_query.count()

        # Get segment IDs for Pinecone cleanup
        segment_ids = [str(seg_id) for (seg_id,) in segment_query.with_entities(ProgramSegment.id).all()]

        logger.info(f"Found {segments_to_delete} segments, {jobs_to_delete} program jobs, and {product_jobs_to_delete} product jobs to delete")

        # 1. Delete from Pinecone vector database first
        if segment_ids:
            try:
                logger.info(f"Deleting {len(segment_ids)} vectors from Pinecone namespace '{settings.PROGRAM_SEGMENTS_EMBEDDING_NAMESPACE}'...")
                pinecone_index = get_pinecone_index()

                # Delete in batches to avoid API limits
                batch_size = 100
                for i in range(0, len(segment_ids), batch_size):
                    batch_ids = segment_ids[i:i + batch_size]
                    pinecone_index.delete(
                        ids=batch_ids,
                        namespace=settings.PROGRAM_SEGMENTS_EMBEDDING_NAMESPACE
                    )
                    logger.info(f"Deleted batch {i//batch_size + 1} ({len(batch_ids)} vectors) from Pinecone")

                logger.info("Successfully deleted all segment vectors from Pinecone")
            except Exception as e:
                logger.warning(f"Failed to delete vectors from Pinecone: {e}")
                # Continue with database cleanup even if Pinecone fails

        # 2. Delete segmentation jobs first (they reference product jobs via foreign key)
        if job_filter_conditions:
            num_jobs_deleted = job_query.delete(synchronize_session=False)
        else:
            num_jobs_deleted = db.query(SegmentationJob).delete(synchronize_session=False)
        logger.info(f"Deleted {num_jobs_deleted} program segmentation jobs")

        # 3. Delete product segmentation jobs (after program jobs are deleted)
        if product_job_filter_conditions:
            num_product_jobs_deleted = product_job_query.delete(synchronize_session=False)
        elif not program_id:  # Only delete all product jobs if not filtering by specific program
            num_product_jobs_deleted = db.query(ProductSegmentationJob).delete(synchronize_session=False)
        else:
            num_product_jobs_deleted = 0
        logger.info(f"Deleted {num_product_jobs_deleted} product segmentation jobs")

        # 4. Delete program segments (handle parent-child relationships)
        # Delete child segments first (micro-chunks), then parent segments (macro-phases)
        if segment_filter_conditions:
            # Delete micro-chunks first
            micro_chunks_deleted = segment_query.filter(
                ProgramSegment.parent_segment_id.isnot(None)
            ).delete(synchronize_session=False)

            # Delete macro-phases
            macro_phases_deleted = segment_query.filter(
                ProgramSegment.parent_segment_id.is_(None)
            ).delete(synchronize_session=False)

            num_segments_deleted = micro_chunks_deleted + macro_phases_deleted
        else:
            # Delete all micro-chunks first
            micro_chunks_deleted = db.query(ProgramSegment).filter(
                ProgramSegment.parent_segment_id.isnot(None)
            ).delete(synchronize_session=False)

            # Delete all macro-phases
            macro_phases_deleted = db.query(ProgramSegment).filter(
                ProgramSegment.parent_segment_id.is_(None)
            ).delete(synchronize_session=False)

            num_segments_deleted = micro_chunks_deleted + macro_phases_deleted

        logger.info(f"Deleted {num_segments_deleted} program segments ({micro_chunks_deleted} micro-chunks, {macro_phases_deleted} macro-phases)")

        # 5. Reset sequences if clearing all data
        if not segment_filter_conditions and not job_filter_conditions and not product_job_filter_conditions:
            logger.info("Resetting PostgreSQL sequences...")
            db.execute(text(f"TRUNCATE TABLE {ProgramSegment.__tablename__} RESTART IDENTITY CASCADE;"))
            db.execute(text(f"TRUNCATE TABLE {SegmentationJob.__tablename__} RESTART IDENTITY CASCADE;"))
            db.execute(text(f"TRUNCATE TABLE {ProductSegmentationJob.__tablename__} RESTART IDENTITY CASCADE;"))
            logger.info("PostgreSQL sequences reset")

        # Commit the transaction
        db.commit()
        logger.info("Successfully committed all cleanup operations")

        # Prepare response using Pydantic schemas
        cleanup_result = SegmentCleanupResponse(
            success=True,
            message="Successfully cleared segments and segmentation jobs",
            statistics=SegmentCleanupStatistics(
                segments_deleted=num_segments_deleted,
                micro_chunks_deleted=micro_chunks_deleted,
                macro_phases_deleted=macro_phases_deleted,
                jobs_deleted=num_jobs_deleted,
                product_jobs_deleted=num_product_jobs_deleted,
                vectors_deleted_from_pinecone=len(segment_ids),
                sequences_reset=not bool(segment_filter_conditions or job_filter_conditions or product_job_filter_conditions)
            ),
            filters_applied=SegmentCleanupFilters(
                product_id=product_id,
                program_id=program_id
            )
        )

        logger.info(f"Cleanup completed successfully: {cleanup_result.statistics.model_dump()}")
        return cleanup_result

    except Exception as e:
        logger.error(f"Error during segments and jobs cleanup: {e}", exc_info=True)
        db.rollback()
        raise HTTPException(
            status_code=500,
            detail=f"Failed to clear segments and jobs: {str(e)}"
        )
