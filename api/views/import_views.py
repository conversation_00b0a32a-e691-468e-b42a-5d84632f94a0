import json
import traceback

from fastapi import HTTP<PERSON>xception
from sqlalchemy.orm import Session
from sqlalchemy import text

from models.program_models import (
    Program,
    ProgramVersion,
    ProgramSegment,
    SegmentationJob,
    ProductSegmentationJob,
    SubRoutine,
    ProgramCategory,
    Product,
    program_subroutine_association
)

def normalize_category_name(name):
    """Normalize category names to handle variants like 'OneTouch Programs' and 'One Touch Scenarios'"""
    # Special case for "One Touch Scenario" - convert directly to "onetouch programs"
    if name == "One Touch Scenerio":
        name = "onetouch program"

    # Convert to lowercase for case-insensitive comparison
    normalized = name.lower()

    # Handle "One Touch" vs "OneTouch" variations
    normalized = normalized.replace(" touch ", "touch")
    normalized = normalized.replace("one touch", "onetouch")

    # Make plural consistent
    if not normalized.endswith("s"):
        normalized += "s"

    return normalized


def process_json_file(db: Session, data: dict, product_id: int):
    """Processes a loaded JSON data dictionary to populate Programs and SubRoutines."""

    # 1. Load/Check Unique Subroutines for this product file
    loaded_subroutines_map = {}  # Map JSON ID -> DB SubRoutine object
    print(f"Processing Unique Subroutines for product_id {product_id}...")
    for sub_data in data.get("unique_subroutines", []):
        json_sub_id = sub_data.get("subroutine_id")
        if not json_sub_id:
            print("  - Warning: Skipping subroutine entry with missing 'subroutine_id'")
            continue

        # Check if subroutine already exists in DB based on json ID
        existing_sub = (
            db.query(SubRoutine)
            .filter_by(subroutine_id_json=json_sub_id, product_id=product_id)
            .first()
        )
        if existing_sub:
            loaded_subroutines_map[json_sub_id] = existing_sub
            print(
                f"  - Found existing Subroutine: {existing_sub.name} (JSON ID: {json_sub_id})"
            )
        else:
            # Create new SubRoutine
            new_sub = SubRoutine(
                subroutine_id_json=json_sub_id,
                product_id=product_id,
                name=sub_data.get("subroutine_name"),
                files=sub_data.get("files"),
                column_mapping_used=sub_data.get("column_mapping_used"),
                steps=sub_data.get("steps"),
                overall_settings=sub_data.get("overall_settings"),
            )
            db.add(new_sub)
            loaded_subroutines_map[json_sub_id] = new_sub
            print(f"  - Creating Subroutine: {new_sub.name} (JSON ID: {json_sub_id})")
    db.flush()  # Ensure new subs get IDs before program association

    # 2. Process Aggregated Programs
    print(f"Processing Aggregated Programs for product_id {product_id}...")
    for program_entry in data.get("aggregated_programs", []):
        prog_analysis = program_entry.get("program_analysis", {})
        if not prog_analysis:
            print("  - Warning: Skipping program entry with missing 'program_analysis'")
            continue

        program_name = prog_analysis.get("program_title", "Untitled Program")
        print(f"Processing Program: {program_name}")

        # --- Check if program exists (simple check) ---
        existing_program = (
            db.query(Program)
            .filter_by(name=program_name, product_id=product_id)
            .first()
        )

        program_steps = prog_analysis.get("steps", [])

        if existing_program:
            print(f"  - Program '{program_name}' already exists for product ID {product_id}. Replacing with new data...")

            # Delete all existing versions for this program
            existing_versions = db.query(ProgramVersion).filter_by(program_id=existing_program.id).all()
            for version in existing_versions:
                db.delete(version)
            print(f"  - Deleted {len(existing_versions)} existing versions")

            # Update existing program fields with new data
            existing_program.source_folder = prog_analysis.get("source_folder")
            existing_program.source_filename = prog_analysis.get("source_filename")
            existing_program.program_title = prog_analysis.get("program_title")
            existing_program.source_notes = prog_analysis.get("source_notes")
            existing_program.program_description = prog_analysis.get("program_description")
            existing_program.overall_settings = prog_analysis.get("overall_settings")
            existing_program.column_mapping_used = prog_analysis.get("column_mapping_used")
            existing_program.steps = program_steps
            existing_program.current_version_number = 1  # Reset to version 1

            # Create new version 1 with the incoming data
            new_version = ProgramVersion(
                program_id=existing_program.id,
                version_number=1,
                steps=program_steps,
                version_notes="Replaced during JSON import.",
                created_by_user_id=None,  # System import
            )
            db.add(new_version)
            print(f"  - Created new version 1 for program: {program_name}")

            # Update subroutine associations
            existing_program.sub_routines.clear()  # Clear existing associations
            subroutine_ids = prog_analysis.get("subroutine_ids", [])
            for sub_id in subroutine_ids:
                if sub_id in loaded_subroutines_map:
                    existing_program.sub_routines.append(loaded_subroutines_map[sub_id])
                    print(f"  - Associated Subroutine: {loaded_subroutines_map[sub_id].name}")
                else:
                    print(f"  - Warning: Subroutine ID {sub_id} not found in loaded subroutines")

            print(f"  - Replaced Program: {program_name} (ID: {existing_program.id})")
            continue  # Continue to next program

        category_name_str = prog_analysis.get("source_folder", "").split("/")[0]
        if not category_name_str:
            print(
                f"  - Warning: Could not determine category from source_folder '{prog_analysis.get('source_folder')}' for program '{program_name}'. Skipping."
            )
            continue
        category = normalize_category_name(category_name_str)
        existing_category = db.query(ProgramCategory).filter_by(name=category).first()
        if not existing_category:
            existing_category = ProgramCategory(name=category)
            db.add(existing_category)
            db.flush()  # Ensure category gets ID before program association

        # Create the Program (program_steps already defined above)
        new_program = Program(
            product_id=product_id,
            category_id=existing_category.id,
            name=program_name,
            source_folder=prog_analysis.get("source_folder"),
            source_filename=prog_analysis.get("source_filename"),
            program_title=prog_analysis.get("program_title"),
            source_notes=prog_analysis.get("source_notes"),
            program_description=prog_analysis.get("program_description"),
            overall_settings=prog_analysis.get("overall_settings"),
            steps=program_steps,
            column_mapping_used=prog_analysis.get("column_mapping_used"),
            current_version_number=1,  # Set initial version number
        )
        db.add(new_program)
        db.flush()  # Ensure program gets ID before version and subroutine association

        # Create initial ProgramVersion
        initial_version = ProgramVersion(
            program_id=new_program.id,
            version_number=1,
            steps=program_steps,
            version_notes="Initial version created during JSON import.",
            created_by_user_id=None,  # System import, no specific user
        )
        db.add(initial_version)
        print(f"  - Created initial version for program: {program_name}")

        # Associate SubRoutines with the Program
        subroutine_ids = prog_analysis.get("subroutine_ids", [])
        for sub_id in subroutine_ids:
            if sub_id in loaded_subroutines_map:
                new_program.sub_routines.append(loaded_subroutines_map[sub_id])
                print(f"  - Associated Subroutine: {loaded_subroutines_map[sub_id].name}")
            else:
                print(f"  - Warning: Subroutine ID {sub_id} not found in loaded subroutines")

        print(f"  - Created Program: {program_name} (ID: {new_program.id})")

    print(f"Finished processing JSON file for product_id {product_id}")


async def populate_program_handler(db: Session):
    """Main handler for populating programs from JSON files."""
    # Using the passed-in db Session from Depends(get_db) presumably
    # Ensure Product table has entries with ID 2 and 3, or adjust logic to find/create them.
    ulo_prod = db.query(Product).filter(Product.id == 2).first()
    udr_prod = db.query(Product).filter(Product.id == 3).first()

    if not ulo_prod:
        raise HTTPException(
            status_code=404, detail="Product with ID 2 (uLove) not found in database."
        )
    if not udr_prod:
        raise HTTPException(
            status_code=404, detail="Product with ID 3 (uDream) not found in database."
        )

    ULOVE_PRODUCT_ID = ulo_prod.id
    UDREAM_PRODUCT_ID = udr_prod.id

    try:
        print("Loading uDream JSON data...")
        with open("data/udream_combined.json", "r", encoding="utf-8") as f:
            udream_data = json.load(f)
        print("Processing uDream data...")
        process_json_file(db, udream_data, UDREAM_PRODUCT_ID)
        db.commit()  # Commit after processing uDream
        print("uDream data processed and committed.")

    except FileNotFoundError:
        db.rollback()
        raise HTTPException(status_code=500, detail="uDream JSON file not found.")
    except json.JSONDecodeError as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Error decoding uDream JSON: {e}")
    except Exception as e:
        db.rollback()
        print(f"An error occurred during uDream processing: {e}")
        traceback.print_exc()
        raise HTTPException(
            status_code=500, detail=f"Error processing uDream data: {str(e)}"
        )

    try:
        print("Loading uLove JSON data...")
        with open("data/ulove_combined.json", "r", encoding="utf-8") as f:
            ulove_data = json.load(f)
        print("Processing uLove data...")
        process_json_file(db, ulove_data, ULOVE_PRODUCT_ID)
        db.commit()  # Commit after processing uLove
        print("uLove data processed and committed.")
        return {
            "message": "Successfully populated programs and subroutines for uDream and uLove"
        }

    except FileNotFoundError:
        db.rollback()  # Rollback uLove changes if file not found
        raise HTTPException(status_code=500, detail="uLove JSON file not found.")
    except json.JSONDecodeError as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Error decoding uLove JSON: {e}")
    except Exception as e:
        db.rollback()
        print(f"An error occurred during uLove processing: {e}")
        traceback.print_exc()
        raise HTTPException(
            status_code=500, detail=f"Error processing uLove data: {str(e)}"
        )

    # Note: The `finally` block for db.close() is usually handled
    # by the FastAPI `Depends(get_db)` pattern automatically.


async def clear_programs_and_subroutines(db: Session):
    """Clear all programs, subroutines, program versions, program segments, segmentation jobs, and related data from the database."""
    print("Attempting to clear Programs, ProgramVersions, ProgramSegments, SegmentationJobs, SubRoutines, and Association data...")
    try:
        # 1. Delete records from the association table first
        # Access the table object directly through Base.metadata
        assoc_table = program_subroutine_association
        print(f"Deleting from association table: {assoc_table.name}")
        # Use synchronize_session=False for bulk deletes for performance
        db.execute(assoc_table.delete())
        print("Association table cleared.")

        # 2. Delete SegmentationJobs first (due to foreign key constraint to programs)
        print(f"Deleting from table: {SegmentationJob.__tablename__}")
        num_segmentation_jobs_deleted = db.query(SegmentationJob).delete()
        print(f"Deleted {num_segmentation_jobs_deleted} segmentation jobs.")

        # 3. Delete ProgramSegments (due to foreign key constraint to programs)
        print(f"Deleting from table: {ProgramSegment.__tablename__}")
        num_segments_deleted = db.query(ProgramSegment).delete()
        print(f"Deleted {num_segments_deleted} program segments.")

        # 4. Delete ProgramVersions (due to foreign key constraint to programs)
        print(f"Deleting from table: {ProgramVersion.__tablename__}")
        num_versions_deleted = db.query(ProgramVersion).delete()
        print(f"Deleted {num_versions_deleted} program versions.")

        # 5. Delete records from the main tables
        print(f"Deleting from table: {Program.__tablename__}")
        num_programs_deleted = db.query(Program).delete()
        print(f"Deleted {num_programs_deleted} programs.")

        print(f"Deleting from table: {SubRoutine.__tablename__}")
        num_subroutines_deleted = db.query(SubRoutine).delete()
        print(f"Deleted {num_subroutines_deleted} subroutines.")

        print(f"Deleting from table: {ProgramCategory.__tablename__}")
        num_program_categories_deleted = db.query(ProgramCategory).delete()
        print(f"Deleted {num_program_categories_deleted} program categories.")

        # 6. Delete ProductSegmentationJobs (no foreign key to programs, but good to clean up)
        print(f"Deleting from table: {ProductSegmentationJob.__tablename__}")
        num_product_jobs_deleted = db.query(ProductSegmentationJob).delete()
        print(f"Deleted {num_product_jobs_deleted} product segmentation jobs.")

        print("Resetting PostgreSQL sequences...")
        db.execute(
            text(
                f"TRUNCATE TABLE {program_subroutine_association.name} RESTART IDENTITY CASCADE;"
            )
        )
        db.execute(
            text(f"TRUNCATE TABLE {SegmentationJob.__tablename__} RESTART IDENTITY CASCADE;")
        )
        db.execute(
            text(f"TRUNCATE TABLE {ProgramSegment.__tablename__} RESTART IDENTITY CASCADE;")
        )
        db.execute(
            text(f"TRUNCATE TABLE {ProgramVersion.__tablename__} RESTART IDENTITY CASCADE;")
        )
        db.execute(
            text(f"TRUNCATE TABLE {Program.__tablename__} RESTART IDENTITY CASCADE;")
        )
        db.execute(
            text(f"TRUNCATE TABLE {SubRoutine.__tablename__} RESTART IDENTITY CASCADE;")
        )
        db.execute(
            text(
                f"TRUNCATE TABLE {ProgramCategory.__tablename__} RESTART IDENTITY CASCADE;"
            )
        )
        db.execute(
            text(f"TRUNCATE TABLE {ProductSegmentationJob.__tablename__} RESTART IDENTITY CASCADE;")
        )
        print("PostgreSQL sequences reset.")

        # Commit the transaction
        db.commit()
        print("Successfully cleared tables and reset sequences (where applicable).")

    except Exception as e:
        print(f"An error occurred during clearing: {e}")
        traceback.print_exc()
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Failed to clear tables: {str(e)}")
