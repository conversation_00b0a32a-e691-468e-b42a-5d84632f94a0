import os

# import shutil # No longer needed for local file operations
import uuid
from fastapi import HTTP<PERSON>xception, UploadFile, File
from sqlalchemy.orm import Session
from typing import Optional
import boto3  # Added for S3
from botocore.exceptions import ClientError  # Added for S3 error handling
from config.settings import settings  # Import app settings

from models import (
    Product,
    User,
)

from schemas.product_schemas import ProductCreate, ProductUpdate, ProductResponse

# Define the directory to store product images - REMOVED
# UPLOAD_DIR = "uploads/products"
# os.makedirs(UPLOAD_DIR, exist_ok=True) # Create directory if it doesn't exist - REMOVED

# Initialize S3 client
s3_client = boto3.client(
    "s3",
    aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
    aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY,
    region_name=settings.AWS_REGION,
)
S3_BUCKET_NAME = settings.AWS_S3_BUCKET_NAME


def save_product_image(file: UploadFile, product_id: int) -> str:
    """Uploads an image to S3 and returns its object key."""
    try:
        file_extension = os.path.splitext(file.filename)[1]
        if file_extension.lower() not in [".png", ".jpg", ".jpeg", ".gif", ".webp"]:
            raise HTTPException(status_code=400, detail="Invalid image file type.")

        # S3 object key, e.g., products/1_random_uuid.jpg
        object_key = f"products/{product_id}_{uuid.uuid4()}{file_extension}"

        s3_client.upload_fileobj(
            file.file,
            S3_BUCKET_NAME,
            object_key,
            ExtraArgs={
                "ContentType": file.content_type
            },  # Set content type for proper serving
        )
        return object_key  # Return the S3 object key
    except ClientError as e:
        print(f"Error uploading to S3: {e}")
        # Consider specific error codes if needed, e.g., e.response['Error']['Code']
        raise HTTPException(
            status_code=500,
            detail=f"Could not save image to S3: {e.response.get('Error', {}).get('Message', 'Unknown S3 error')}",
        )
    except HTTPException as http_exc:
        raise http_exc
    except Exception as e:
        print(f"Error saving file (non-S3): {e}")
        raise HTTPException(
            status_code=500, detail="Could not process image file for S3 upload."
        )
    finally:
        if hasattr(file, "file") and file.file and not file.file.closed:
            file.file.close()


def delete_product_image(s3_object_key: str | None):
    """Deletes the image object from S3 if the key is provided."""
    if not s3_object_key:
        return
    try:
        print(
            f"Attempting to delete S3 object: {s3_object_key} from bucket {S3_BUCKET_NAME}"
        )
        s3_client.delete_object(Bucket=S3_BUCKET_NAME, Key=s3_object_key)
        print(f"Successfully deleted S3 object: {s3_object_key}")
    except ClientError as e:
        # Log error, but don't necessarily block the request if deletion fails
        print(f"Error deleting S3 object {s3_object_key}: {e}")
        # Depending on policy, you might not want to raise an HTTPException here,
        # as it could prevent the primary operation (e.g., product update/delete) from completing.
        # For now, we'll just log it.


def generate_s3_presigned_url(object_key: str, expiration: int = 3600) -> str | None:
    """Generate a presigned URL to share an S3 object.

    :param object_key: string
    :param expiration: Time in seconds for the presigned URL to remain valid.
    :return: Presigned URL as string. If error, returns None.
    """
    if not object_key:
        return None
    try:
        response = s3_client.generate_presigned_url(
            "get_object",
            Params={"Bucket": S3_BUCKET_NAME, "Key": object_key},
            ExpiresIn=expiration,
        )
        return response
    except ClientError as e:
        print(f"Error generating presigned URL for {object_key}: {e}")
        return None


async def create_product_handler(
    db: Session,
    user: User,
    product: ProductCreate,
    image: UploadFile | None = File(None),
):
    existing_product = db.query(Product).filter(Product.name == product.name).first()
    if existing_product:
        raise HTTPException(status_code=400, detail="Product name already exists")

    existing_product = db.query(Product).filter(Product.model == product.model).first()
    if existing_product:
        raise HTTPException(status_code=400, detail="Product model already exists")

    new_product = Product(
        name=product.name,
        description=product.description,
        model=product.model,
        series=product.series,
        type=product.type,
        subtype=product.subtype,
        features=product.features,
        # image_url (S3 object key) will be set after commit if image is uploaded
    )

    db.add(new_product)
    try:
        db.commit()
        db.refresh(new_product)
    except Exception as e:
        db.rollback()
        print(f"Error creating product entry: {e}")
        raise HTTPException(status_code=500, detail="Failed to create product entry.")

    s3_object_key_to_save = None
    if image:
        try:
            s3_object_key_to_save = save_product_image(image, new_product.id)
            new_product.image_url = s3_object_key_to_save  # Store S3 object key
            db.commit()
            db.refresh(new_product)
        except HTTPException as e:
            db.rollback()  # Rollback the image_url update attempt
            db.refresh(new_product)  # Refresh to pre-image-update state
            # Product is created, but S3 upload failed.
            # Re-raise the HTTPException from save_product_image or a more specific one.
            detail_message = f"Product created (ID: {new_product.id}), but image upload to S3 failed: {e.detail}"
            if hasattr(e, "status_code"):
                raise HTTPException(status_code=e.status_code, detail=detail_message)
            else:  # Fallback if original exception didn't have status_code (should not happen with our save_product_image)
                raise HTTPException(status_code=500, detail=detail_message)

        except Exception as e:  # Catch other unexpected errors during image processing
            db.rollback()
            print(
                f"Unexpected error during S3 image processing for new product {new_product.id}: {e}"
            )
            # Consider deleting the product entry or marking it as incomplete.
            # For now, raise an error indicating failure.
            raise HTTPException(
                status_code=500,
                detail="An error occurred processing the image with S3 after product creation.",
            )

    return ProductResponse(
        id=new_product.id,
        name=new_product.name,
        description=new_product.description,
        model=new_product.model,
        series=new_product.series,
        type=new_product.type,
        subtype=new_product.subtype,
        features=new_product.features,
        image_url=new_product.image_url,  # This is now an S3 object key or None
        created_at=new_product.created_at,
        # updated_at is handled by SQLAlchemy's onupdate
    )


async def update_product_handler(
    db: Session,
    user: User,
    product_id: int,
    product: ProductUpdate,
    image: UploadFile | None = File(None),
    image_url_signal: Optional[str] = None,
):
    db_product = db.query(Product).filter(Product.id == product_id).first()
    if not db_product:
        raise HTTPException(status_code=404, detail="Product not found")

    if product.name is not None and product.name != db_product.name:
        existing = (
            db.query(Product)
            .filter(Product.name == product.name, Product.id != product_id)
            .first()
        )
        if existing:
            raise HTTPException(status_code=400, detail="Product name already exists")

    if product.model is not None and product.model != db_product.model:
        existing = (
            db.query(Product)
            .filter(Product.model == product.model, Product.id != product_id)
            .first()
        )
        if existing:
            raise HTTPException(status_code=400, detail="Product model already exists")

    update_data = product.dict(exclude_unset=True)
    s3_object_key_to_save = db_product.image_url  # Keep existing S3 key by default

    if image:  # New image uploaded
        # Delete old S3 object if it exists
        if db_product.image_url:
            delete_product_image(db_product.image_url)

        # Save new image to S3
        try:
            s3_object_key_to_save = save_product_image(image, product_id)
        except HTTPException as e:  # Propagate image saving error
            # If saving the new image fails, we don't update the product's image_url
            # and the old image (if any) might have already been deleted.
            # This could leave the product without an image if the old one was deleted.
            # Consider transactionality or a more robust recovery strategy if this is critical.
            # For now, re-raise the exception.
            raise HTTPException(
                status_code=e.status_code,
                detail=f"Failed to upload new image to S3: {e.detail}",
            )
        except Exception as e:
            print(
                f"Unexpected error during S3 image processing for update {product_id}: {e}"
            )
            raise HTTPException(
                status_code=500,
                detail="An error occurred processing the new image with S3.",
            )

    elif image_url_signal == "" and not image:  # Signal to remove existing image
        if db_product.image_url:
            delete_product_image(db_product.image_url)
        s3_object_key_to_save = None

    # Apply other updates from the request body
    for key, value in update_data.items():
        setattr(db_product, key, value)

    # Set the final S3 object key (either new, old, or None)
    db_product.image_url = s3_object_key_to_save

    try:
        db.commit()
        db.refresh(db_product)
    except Exception as e:
        db.rollback()
        print(f"Error updating product {product_id}: {e}")
        raise HTTPException(status_code=500, detail="Failed to update product.")

    return ProductResponse(
        id=db_product.id,
        name=db_product.name,
        description=db_product.description,
        model=db_product.model,
        series=db_product.series,
        type=db_product.type,
        subtype=db_product.subtype,
        features=db_product.features,
        image_url=db_product.image_url,  # S3 object key or None
        created_at=db_product.created_at,
        updated_at=db_product.updated_at,
    )


async def delete_product_handler(db: Session, user: User, product_id: int):
    product = db.query(Product).filter(Product.id == product_id).first()
    if not product:
        raise HTTPException(status_code=404, detail="Product not found")

    # Delete associated S3 object before deleting the product record
    if product.image_url:
        delete_product_image(product.image_url)

    try:
        db.delete(product)
        db.commit()
    except Exception as e:
        db.rollback()
        print(f"Error deleting product {product_id}: {e}")
        raise HTTPException(status_code=500, detail="Failed to delete product.")

    return {"status": "success", "message": "Product deleted successfully"}
