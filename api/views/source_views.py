import tempfile
from fastapi import APIRouter, Depends, UploadFile, BackgroundTasks, HTTPException
from sqlalchemy.orm import Session
from sqlalchemy import and_
from models import Source, User, File
from schemas.source_schemas import (
    SourceCreate,
    SourceUpdate,
    SourceResponse,
    FileResponse,
    FileDetailResponse,
)
from utils.pinecone_ops import upsert_file, delete_file, delete_source


async def create_source_handler(db: Session, user: User, source: SourceCreate):
    existing_source = db.query(Source).filter(Source.name == source.name).first()
    if existing_source:
        raise HTTPException(status_code=400, detail="Source name already exists")

    new_source = Source(
        name=source.name,
        description=source.description,
        s3_bucket_url=source.s3_bucket_url,
        owner_id=user.id,
    )

    db.add(new_source)
    db.commit()
    db.refresh(new_source)

    return SourceResponse(
        id=new_source.id,
        name=new_source.name,
        description=new_source.description,
        created_at=new_source.created_at,
    )


async def update_source_handler(
    db: Session, user: User, source_id: int, source: SourceUpdate
):
    db_source = db.query(Source).filter(Source.id == source_id).first()
    if not db_source:
        raise HTTPException(status_code=404, detail="Source not found")

    if source.name and source.name != db_source.name:
        existing = db.query(Source).filter(Source.name == source.name).first()
        if existing:
            raise HTTPException(status_code=400, detail="Source name already exists")

    for key, value in source.dict(exclude_unset=True).items():
        setattr(db_source, key, value)

    db.commit()
    db.refresh(db_source)
    return SourceResponse(
        id=db_source.id,
        name=db_source.name,
        description=db_source.description,
        created_at=db_source.created_at,
    )


async def get_files_handler(db: Session, user: User, source_id: int):
    source = db.query(Source).filter(Source.id == source_id).first()
    if not source:
        raise HTTPException(status_code=404, detail="Source not found")

    if source.owner_id != user.id and "Admins" not in user.roles:
        raise HTTPException(status_code=403, detail="Unauthorized")

    source_response = SourceResponse(
        id=source.id,
        name=source.name,
        description=source.description,
        created_at=source.created_at,
    )
    files = db.query(File).filter(File.source_id == source_id).order_by(File.id).all()

    return {
        "source": source_response,
        "files": [FileResponse.from_orm(f) for f in files],
    }


async def get_file_detail_handler(
    db: Session, user: User, source_id: int, file_id: int
):
    file = db.query(File).filter(File.id == file_id).first()
    if not file:
        raise HTTPException(status_code=404, detail="File not found")

    if file.source_id != source_id:
        raise HTTPException(status_code=404, detail="File not found")

    if not file.extracted:
        return []

    return [FileDetailResponse(**data) for data in file.extracted]


async def upload_files_handler(
    db: Session,
    background_tasks: BackgroundTasks,
    user: User,
    source_id: int,
    files: list[UploadFile],
):
    source = db.query(Source).filter(Source.id == source_id).first()
    if not source:
        raise HTTPException(status_code=404, detail="Source not found")

    saved_files = []
    for file in files:
        file_data = File(
            name=file.filename,
            uploaded_name=file.filename,
            file_type=file.content_type,
            size=file.size,
            source_id=source_id,
            user_id=user.id,
        )
        db.add(file_data)
        saved_files.append(file_data)
        db.commit()
        db.refresh(file_data)
        with tempfile.NamedTemporaryFile(delete=False) as temp_file:
            content = file.file.read()
            temp_file.write(content)
            temp_path = temp_file.name
            # await upsert_file(db, str(source_id), file_data, temp_path)
            background_tasks.add_task(
                upsert_file, db, str(source_id), file_data, temp_path
            )
    return [FileResponse.from_orm(f) for f in saved_files]


async def delete_file_handler(
    db: Session,
    background_tasks: BackgroundTasks,
    user: User,
    source_id: int,
    file_id: int,
):
    file = (
        db.query(File)
        .filter(and_(File.id == file_id, File.source_id == source_id))
        .first()
    )

    if not file:
        raise HTTPException(status_code=404, detail="File not found")

    background_tasks.add_task(delete_file, file_id, source_id)

    db.delete(file)
    db.commit()
    return {"status": "success"}


async def delete_source_handler(
    db: Session, background_tasks: BackgroundTasks, user: User, source_id: int
):
    source = db.query(Source).filter(Source.id == source_id).first()
    if not source:
        raise HTTPException(status_code=404, detail="Source not found")
    background_tasks.add_task(delete_source, source.id)
    db.delete(source)
    db.commit()
    return {"status": "success"}
