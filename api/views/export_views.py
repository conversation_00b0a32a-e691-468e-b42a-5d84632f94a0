import json
import uuid
from typing import Optional
from io import BytesIO

import boto3
from botocore.exceptions import Client<PERSON>rror
from fastapi import HTT<PERSON>Exception
from sqlalchemy.orm import Session

from config import settings
from models.program_models import Program

# S3 Configuration
s3_client = boto3.client(
    "s3",
    aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
    aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY,
    region_name=settings.AWS_REGION,
)
S3_BUCKET_NAME = settings.AWS_S3_BUCKET_NAME
S3_PROGRAM_EXPORTS_PREFIX = "program_exports/"


def generate_s3_presigned_url(
    object_key: str, expiration: int = 3600, filename: Optional[str] = None
) -> str | None:
    """Generate a presigned URL to share an S3 object.
    
    Args:
        object_key: S3 object key
        expiration: Time in seconds for the presigned URL to remain valid
        filename: Optional filename for Content-Disposition header
        
    Returns:
        Presigned URL string or None if generation fails
    """
    try:
        params = {
            "Bucket": S3_BUCKET_NAME,
            "Key": object_key,
        }
        
        # Add Content-Disposition header if filename is provided
        if filename:
            params["ResponseContentDisposition"] = f'attachment; filename="{filename}"'
        
        response = s3_client.generate_presigned_url(
            "get_object",
            Params=params,
            ExpiresIn=expiration,
        )
        return response
    except ClientError as e:
        print(f"Error generating presigned URL: {e}")
        return None


def export_program_info_to_docx(db: Session, program_id: int):
    """
    Generates a Word document containing the program information,
    uploads it to S3, and returns a presigned URL for download.

    Args:
        db: Database session
        program_id: ID of the program to export

    Returns:
        str: The presigned URL for the Word document in S3.
    """
    from docx import Document
    from docx.shared import Inches, Pt
    from docx.enum.text import WD_ALIGN_PARAGRAPH
    from docx.oxml.shared import OxmlElement, qn
    from docx.shared import RGBColor

    # Get the program with all related data
    program = db.query(Program).filter(Program.id == program_id).first()
    
    if not program:
        raise HTTPException(
            status_code=404, detail=f"Program with ID {program_id} not found"
        )

    # Create a new Word document
    doc = Document()
    
    # Add title
    title = doc.add_heading(f'Program Information: {program.name or program.program_title}', 0)
    title.alignment = WD_ALIGN_PARAGRAPH.CENTER
    
    # Add basic program information
    doc.add_heading('Basic Information', level=1)
    
    # Create a table for basic info
    basic_info_table = doc.add_table(rows=0, cols=2)
    basic_info_table.style = 'Table Grid'
    
    # Helper function to add row to table
    def add_table_row(table, label, value):
        row = table.add_row()
        row.cells[0].text = label
        row.cells[1].text = str(value) if value is not None else 'Not specified'
        # Make label bold
        row.cells[0].paragraphs[0].runs[0].bold = True
    
    # Add basic information rows
    add_table_row(basic_info_table, 'Program ID', program.id)
    add_table_row(basic_info_table, 'Program Name', program.name or 'Not specified')
    add_table_row(basic_info_table, 'Program Title', program.program_title)
    add_table_row(basic_info_table, 'Source Folder', program.source_folder)
    add_table_row(basic_info_table, 'Source Filename', program.source_filename)
    add_table_row(basic_info_table, 'Product ID', program.product_id)
    add_table_row(basic_info_table, 'Category ID', program.category_id)
    add_table_row(basic_info_table, 'Status', program.status.value if program.status else 'Not specified')
    add_table_row(basic_info_table, 'Current Version', program.current_version_number)
    add_table_row(basic_info_table, 'Step Count', len(program.steps) if program.steps else 0)
    add_table_row(basic_info_table, 'Created At', program.created_at.strftime('%Y-%m-%d %H:%M:%S') if program.created_at else 'Not specified')
    add_table_row(basic_info_table, 'Updated At', program.updated_at.strftime('%Y-%m-%d %H:%M:%S') if program.updated_at else 'Not specified')
    
    # Add Logic Technique section
    doc.add_heading('Logic Technique', level=1)
    if program.logic_technique:
        doc.add_paragraph(program.logic_technique)
    else:
        not_specified_para = doc.add_paragraph('Not specified')
        # Make it italic to indicate it's a placeholder
        for run in not_specified_para.runs:
            run.italic = True
    
    # Add Program Description section
    if program.program_description:
        doc.add_heading('Program Description', level=1)
        description = program.program_description
        
        # Basic Information subsection
        if any(key in description for key in ['intensity', 'program_name', 'recommended_position']):
            doc.add_heading('Basic Information', level=2)
            desc_basic_table = doc.add_table(rows=0, cols=2)
            desc_basic_table.style = 'Table Grid'
            
            if 'intensity' in description and description['intensity'] is not None:
                add_table_row(desc_basic_table, 'Intensity', description['intensity'])
            if 'program_name' in description and description['program_name']:
                add_table_row(desc_basic_table, 'Program Name', description['program_name'])
            if 'recommended_position' in description and description['recommended_position']:
                add_table_row(desc_basic_table, 'Recommended Position', description['recommended_position'])
        
        # Target Group Objective
        if 'target_group_objective' in description and description['target_group_objective']:
            doc.add_heading('Target Group Objective', level=2)
            doc.add_paragraph(description['target_group_objective'])
        
        # Programme Sequence
        if 'programme_sequence' in description and description['programme_sequence']:
            doc.add_heading('Programme Sequence', level=2)
            for key, value in description['programme_sequence'].items():
                doc.add_heading(key.replace('_', ' ').title(), level=3)
                doc.add_paragraph(str(value))
        
        # Targeted Acupressure Points
        if 'targeted_acupressure_points' in description and description['targeted_acupressure_points']:
            doc.add_heading('Targeted Acupressure Points', level=2)
            for point in description['targeted_acupressure_points']:
                doc.add_paragraph(f'• {point}')
        
        # Signature Moves
        if 'signature_moves' in description and description['signature_moves']:
            doc.add_heading('Signature Moves', level=2)
            for move in description['signature_moves']:
                doc.add_paragraph(f'• {move}')
        
        # Limitations
        if 'limitations' in description and description['limitations']:
            doc.add_heading('Limitations', level=2)
            doc.add_paragraph(description['limitations'])
    
    # Add Source Notes section
    if program.source_notes:
        doc.add_heading('Source Notes', level=1)
        doc.add_paragraph(program.source_notes)
    
    # Add Overall Settings section
    if program.overall_settings:
        doc.add_heading('Overall Settings', level=1)
        settings_text = json.dumps(program.overall_settings, indent=2, ensure_ascii=False)
        # Use a monospace font for JSON formatting
        settings_paragraph = doc.add_paragraph(settings_text)
        # Set font to monospace for better JSON readability
        for run in settings_paragraph.runs:
            run.font.name = 'Courier New'
            run.font.size = Pt(10)  # 10pt font size
    
    # Save the document to a BytesIO object
    docx_bytes_io = BytesIO()
    doc.save(docx_bytes_io)
    docx_bytes_io.seek(0)
    
    # Generate safe filename
    safe_program_name = (
        (program.name or program.program_title).replace(" ", "_").replace("/", "_").replace("\\", "_")
    )
    download_filename = f"{safe_program_name}_info.docx"
    
    # Construct the S3 object key
    s3_object_key = f"{S3_PROGRAM_EXPORTS_PREFIX}{program_id}_{safe_program_name}_info_{uuid.uuid4()}.docx"
    
    try:
        print(f"Uploading Word document to S3: bucket={S3_BUCKET_NAME}, key={s3_object_key}")
        s3_client.upload_fileobj(
            docx_bytes_io,
            S3_BUCKET_NAME,
            s3_object_key,
            ExtraArgs={
                "ContentType": "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
            },
        )
        print(f"Successfully uploaded to S3: {s3_object_key}")
    except ClientError as e:
        print(f"Error uploading Word document to S3 for program {program_id}: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Could not save Word document export to S3: {e.response.get('Error', {}).get('Message', 'Unknown S3 error')}",
        )
    finally:
        docx_bytes_io.close()
    
    # Generate presigned URL for the uploaded file
    presigned_url = generate_s3_presigned_url(
        s3_object_key, expiration=3600, filename=download_filename
    )
    if not presigned_url:
        print(f"Failed to generate presigned URL for S3 object: {s3_object_key}")
        raise HTTPException(
            status_code=500,
            detail="Failed to generate download link for the exported file after S3 upload.",
        )
    
    print(f"Generated presigned URL for {s3_object_key}: {presigned_url}")
    return presigned_url


def export_program_steps_to_excel(db: Session, program_id: int):
    """
    Generates an Excel file containing the steps of a program,
    uploads it to S3, and returns a presigned URL for download.

    Args:
        db: Database session
        program_id: ID of the program to export

    Returns:
        str: The presigned URL for the Excel file in S3.
    """
    import openpyxl
    from openpyxl.styles import Font, Alignment, PatternFill, Border, Side
    from openpyxl.utils import get_column_letter

    # Fields to always exclude from the Excel output
    EXCLUDE_FIELDS = {"type", "id", "uuid", "created_at", "updated_at"}

    # Priority fields order (even if not displayed)
    PRIORITY_FIELDS = [
        "step_number",
        "type",  # Kept for processing but will be excluded from display
        "roller_action_description",
        "air_action_description",
        "kneading_speed",
        "tapping_speed",
        "position_3d",
        "width",
        "seat_program",
        "scent",
        "light",
        "notes",
    ]

    # Get the program with steps
    program = db.query(Program).filter(Program.id == program_id).first()

    if not program:
        raise HTTPException(
            status_code=404, detail=f"Program with ID {program_id} not found"
        )

    # Create Excel workbook
    wb = openpyxl.Workbook()
    ws = wb.active
    ws.title = "Steps"

    # Get column mappings if available
    column_mapping = {}
    if program.column_mapping_used and isinstance(program.column_mapping_used, dict):
        column_mapping = program.column_mapping_used

    # Handle steps
    if program.steps and isinstance(program.steps, list) and program.steps:
        # Extract all field names from steps to ensure we capture all possible fields
        all_fields = set()
        for step in program.steps:
            if isinstance(step, dict):
                all_fields.update(step.keys())

        # Remove technical fields that shouldn't be exported
        all_fields -= EXCLUDE_FIELDS

        # Create ordered fields list with priority fields first, then others alphabetically
        ordered_fields = []

        # Add priority fields that exist in our data (except excluded ones)
        for field in PRIORITY_FIELDS:
            if field in all_fields and field not in EXCLUDE_FIELDS:
                ordered_fields.append(field)
                all_fields.discard(field)

        # Add remaining fields alphabetically
        ordered_fields.extend(sorted(all_fields))

        # Prepare column headers - use column_mapping display names when available
        headers = []
        for field in ordered_fields:
            display_name = column_mapping.get(field, field)
            headers.append(display_name)

        # Add header row for steps
        ws.append(headers)
        header_row = 1

        # Style the header row
        thin_border = Border(
            left=Side(style="thin"),
            right=Side(style="thin"),
            top=Side(style="thin"),
            bottom=Side(style="thin"),
        )

        for col_num, _ in enumerate(headers, 1):
            cell = ws.cell(row=header_row, column=col_num)
            cell.font = Font(bold=True, color="FFFFFF")
            cell.fill = PatternFill(
                start_color="4472C4", end_color="4472C4", fill_type="solid"
            )
            cell.alignment = Alignment(
                horizontal="center", vertical="center", wrap_text=True
            )
            cell.border = thin_border

        # Add step data
        for step in program.steps:
            if isinstance(step, dict):
                row_data = []
                for field in ordered_fields:
                    if field in step and step[field] is not None:
                        # Special handling for complex data types
                        if isinstance(step[field], (dict, list)):
                            value = json.dumps(step[field], ensure_ascii=False)
                        else:
                            value = str(step[field])
                        row_data.append(value)
                    else:
                        row_data.append("")
                ws.append(row_data)

        # Auto-adjust column widths
        for column in ws.columns:
            max_length = 0
            column_letter = get_column_letter(column[0].column)

            # Get header length
            header_length = len(str(column[0].value)) if column[0].value else 0

            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass

            # Use the larger of header or content length
            adjusted_width = max(max_length, header_length)
            # Set a minimum/maximum width
            adjusted_width = max(min(adjusted_width + 2, 40), 8)
            ws.column_dimensions[column_letter].width = adjusted_width

            # Set row height for the header to accommodate wrapped text
            ws.row_dimensions[1].height = 30
    else:
        ws.append(["No steps found in this program"])

    # Save the workbook to a BytesIO object
    excel_bytes_io = BytesIO()
    wb.save(excel_bytes_io)
    excel_bytes_io.seek(0)

    # Get program name for the S3 object key
    program = db.query(Program).filter(Program.id == program_id).first()
    if not program:  # Should have been caught earlier, but good to double check
        raise HTTPException(status_code=404, detail="Program not found for S3 naming")

    safe_program_name = (
        program.name.replace(" ", "_").replace("/", "_").replace("\\\\", "_")
    )
    # Define the desired download filename (this will be part of the presigned URL's effect)
    download_filename = f"{safe_program_name}_steps.xlsx"

    # Construct the S3 object key (doesn't need to match download_filename exactly, can include UUIDs for uniqueness)
    s3_object_key = f"{S3_PROGRAM_EXPORTS_PREFIX}{program_id}_{safe_program_name}_{uuid.uuid4()}.xlsx"

    try:
        print(f"Uploading Excel to S3: bucket={S3_BUCKET_NAME}, key={s3_object_key}")
        s3_client.upload_fileobj(
            excel_bytes_io,
            S3_BUCKET_NAME,
            s3_object_key,
            ExtraArgs={
                "ContentType": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
            },
        )
        print(f"Successfully uploaded to S3: {s3_object_key}")
    except ClientError as e:
        print(f"Error uploading Excel to S3 for program {program_id}: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Could not save Excel export to S3: {e.response.get('Error', {}).get('Message', 'Unknown S3 error')}",
        )
    finally:
        excel_bytes_io.close()

    # Generate presigned URL for the uploaded file, now with the filename for Content-Disposition
    presigned_url = generate_s3_presigned_url(
        s3_object_key, expiration=3600, filename=download_filename  # 1 hour validity
    )
    if not presigned_url:
        print(f"Failed to generate presigned URL for S3 object: {s3_object_key}")
        raise HTTPException(
            status_code=500,
            detail="Failed to generate download link for the exported file after S3 upload.",
        )

    print(f"Generated presigned URL for {s3_object_key}: {presigned_url}")
    return presigned_url
