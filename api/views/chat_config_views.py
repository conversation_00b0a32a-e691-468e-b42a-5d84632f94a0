import secrets
from fastapi import HTTPException
from sqlalchemy.orm import Session
from models import Chat<PERSON>onfig, ChatModels, User
from schemas.chat_schemas import ChatConfigCreate, ChatConfigUpdate, ChatConfigResponse


# --- ChatConfig Management ---


async def create_chat_config_handler(
    db: Session, user: User, chat_config: ChatConfigCreate
) -> ChatConfigResponse:
    chat_model = (
        db.query(ChatModels).filter(ChatModels.id == chat_config.model_id).first()
    )
    if not chat_model:
        raise HTTPException(status_code=404, detail="Chat model not found")

    db_chat_config = ChatConfig(
        name=chat_config.name,
        model_id=chat_config.model_id,
        product_id=chat_config.product_id,
        sft_model_name=chat_config.sft_model_name,
        use_sft_direct_generation=True if chat_config.sft_model_name else False,
        synthesis_strategy=chat_config.synthesis_strategy,
        url=secrets.token_urlsafe(32),
        model=chat_model,
        user=user,
    )
    db.add(db_chat_config)
    db.commit()
    db.refresh(db_chat_config)
    return ChatConfigResponse(**db_chat_config.__dict__)


async def update_chat_config_handler(
    db: Session, user: User, chat_config_id: int, chat_config: ChatConfigUpdate
) -> ChatConfigResponse:
    db_chat_config = (
        db.query(ChatConfig).filter(ChatConfig.id == chat_config_id).first()
    )
    if not db_chat_config:
        raise HTTPException(status_code=404, detail="Chat config not found")

    update_data = chat_config.model_dump(exclude_unset=True)
    for key, value in update_data.items():
        setattr(db_chat_config, key, value)

    if "sft_model_name" in update_data:
        db_chat_config.sft_model_name = update_data["sft_model_name"]
        db_chat_config.use_sft_direct_generation = (
            True if update_data["sft_model_name"] else False
        )
    elif hasattr(chat_config, 'sft_model_name') and chat_config.sft_model_name is None and "sft_model_name" not in update_data:
        pass


    db.commit()
    db.refresh(db_chat_config)

    return ChatConfigResponse(**db_chat_config.__dict__)


async def get_config(db: Session, config_id: int) -> ChatConfigResponse:
    db_config = db.query(ChatConfig).filter(ChatConfig.id == config_id).first()
    if not db_config:
        raise HTTPException(status_code=404, detail="Config not found")

    # Handle NULL values for use_sft_direct_generation
    config_dict = db_config.__dict__.copy()
    if config_dict.get('use_sft_direct_generation') is None:
        config_dict['use_sft_direct_generation'] = False

    return ChatConfigResponse(**config_dict)
