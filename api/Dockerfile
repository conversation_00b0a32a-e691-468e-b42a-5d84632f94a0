FROM python:3.12-slim

# System dependencies - install first and keep them in a separate layer
# Consider if libreoffice is strictly needed in the runtime image.
# If it's for specific, infrequent tasks, a multi-stage build or a separate service might be better.
RUN apt-get update && apt-get install -y --no-install-recommends \
    libreoffice \
    libicu72 \
    libxinerama1 \
    libgl1 \
    libxrender1 \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Install uv, the Python package installer
RUN pip install uv

WORKDIR /app

# Copy only files needed for dependency installation first
COPY pyproject.toml uv.lock* ./ 
# If you have other dependency files like requirements.txt, copy them here too
# COPY requirements.txt ./

# Install Python dependencies
# This layer will be cached as long as pyproject.toml/uv.lock don't change
RUN uv sync --no-cache # --no-cache can sometimes resolve issues during builds, remove if not needed.

# Copy the rest of the application code
COPY . .

# Copy and prepare startup script
# Ensure startup.sh is executable and uses LF line endings
COPY startup.sh /startup.sh
RUN chmod +x /startup.sh

# Document that the container listens on port 8000
EXPOSE 8000

# Use ENTRYPOINT to ensure startup script always runs
ENTRYPOINT ["/startup.sh"]

# Tip: For even smaller images and better security, consider:
# 1. Multi-stage builds if some tools (like parts of libreoffice or build tools) are not needed at runtime.
# 2. Running as a non-root user: https://docs.docker.com/develop/develop-images/dockerfile_best-practices/#user
# 3. Using Docker BuildKit for faster builds: Set DOCKER_BUILDKIT=1 environment variable when building.