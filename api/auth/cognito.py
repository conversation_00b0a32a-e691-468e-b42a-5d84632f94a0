from typing import List
from jose import jwt
from fastapi import HTTPException, Security, Depends
from fastapi.security import HTTPAuthorizationCredentials, HTTPBearer
import requests
from sqlalchemy.orm import Session
import boto3
from botocore.exceptions import ClientError

from functools import lru_cache
from config.settings import settings
from core import get_db
from models import User


class CognitoAuth:
    def __init__(self):
        self.user_pool_id = settings.COGNITO_USER_POOL_ID
        self.client_id = settings.COGNITO_CLIENT_ID
        self.region = settings.AWS_REGION
        self.jwks_url = f"https://cognito-idp.{self.region}.amazonaws.com/{self.user_pool_id}/.well-known/jwks.json"
        self.jwks = self._get_jwks()
        self.client = boto3.client(
            "cognito-idp",
            region_name=settings.AWS_REGION,
            aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
            aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY
        )

    def _get_jwks(self):
        response = requests.get(self.jwks_url)
        return response.json()["keys"]

    async def verify_token(self, token: str) -> dict:
        try:
            headers = jwt.get_unverified_headers(token)
            key = next((k for k in self.jwks if k["kid"] == headers["kid"]), None)
            if not key:
                raise HTTPException(status_code=401, detail="Invalid token")

            claims = jwt.decode(
                token,
                key,
                algorithms=["RS256"],
                audience=self.client_id,
                issuer=f"https://cognito-idp.{self.region}.amazonaws.com/{self.user_pool_id}",
            )
            return claims

        except Exception as e:
            raise HTTPException(status_code=401, detail="Invalid token")

    def get_user_groups(self, username: str) -> List[str]:
        try:
            response = self.client.admin_list_groups_for_user(
                Username=username, UserPoolId=self.user_pool_id
            )
            return [group["GroupName"] for group in response["Groups"]]
        except ClientError as e:
            print(e)
            return []

    def update_user_groups(self, username: str, groups: List[str]) -> None:
        try:
            # First, remove user from all existing groups
            current_groups = self.get_user_groups(username)
            for group in current_groups:
                self.client.admin_remove_user_from_group(
                    UserPoolId=self.user_pool_id, Username=username, GroupName=group
                )

            # Then add user to new groups
            for group in groups:
                self.client.admin_add_user_to_group(
                    UserPoolId=self.user_pool_id, Username=username, GroupName=group
                )
        except ClientError as e:
            raise HTTPException(status_code=400, detail=str(e))


@lru_cache()
def get_cognito_auth():
    return CognitoAuth()


security = HTTPBearer()


async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Security(security),
    db: Session = Depends(get_db),
) -> dict:
    """Dependency to get current authenticated user"""
    cognito = get_cognito_auth()
    claims = await cognito.verify_token(credentials.credentials)
    user = db.query(User).filter(User.cognito_id == claims["sub"]).first()
    if user is None:
        raise HTTPException(status_code=401, detail="Invalid token")
    user.roles = claims.get("cognito:groups", [])
    return user
