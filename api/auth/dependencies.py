import inspect
from functools import wraps
from fastapi import Depends, HTTPException
from .cognito import get_current_user


def require_auth(func):
    @wraps(func)
    async def wrapper(*args, user: dict = Depends(get_current_user), **kwargs):
        return await func(*args, user=user, **kwargs)

    return wrapper


def require_admin(func):
    @wraps(func)
    async def wrapper(*args, user: dict = Depends(get_current_user), **kwargs):
        if "Admins" in user.roles:
            if inspect.iscoroutinefunction(func):
                return await func(*args, user=user, **kwargs)
            else:
                return func(*args, user=user, **kwargs)
        raise HTTPException(status_code=401, detail="Access denied for this resource")

    return wrapper


def require_ai_engineer(func):
    @wraps(func)
    async def wrapper(*args, user: dict = Depends(get_current_user), **kwargs):
        if "Admins" in user.roles or "AIEngineers" in user.roles:
            return await func(*args, user=user, **kwargs)
        raise HTTPException(status_code=401, detail="Access denied for this resource")

    return wrapper
