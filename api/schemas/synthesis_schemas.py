"""
Schemas for the segment-aware synthesis strategy.

This module defines the data structures used in the segment-aware synthesis approach,
which generates program plans that can be used for semantic search and segment retrieval.
"""

from typing import Optional, List
from pydantic import BaseModel, Field


class PhaseSearchInstruction(BaseModel):
    """
    Search instruction for finding segments within a specific phase.
    
    This represents what to look for when semantically searching for segments
    in the database for a particular phase of the massage program.
    """
    
    body_parts: Optional[List[str]] = Field(
        default=None,
        description="Body parts that should be targeted in this phase (e.g., ['neck', 'shoulders', 'lower back'])"
    )
    
    techniques: Optional[List[str]] = Field(
        default=None,
        description="Massage techniques that should be used in this phase (e.g., ['kneading', 'tapping', 'rolling'])"
    )
    
    purposes: Optional[List[str]] = Field(
        default=None,
        description="Therapeutic purposes for this phase (e.g., ['warm-up', 'deep tissue work', 'relaxation'])"
    )
    
    intensity_range: Optional[str] = Field(
        default=None,
        description="Desired intensity level for this phase (e.g., 'low', 'medium', 'high', 'medium-high')"
    )
    

    
    description_keywords: Optional[List[str]] = Field(
        default=None,
        description="Key descriptive terms to search for in segment descriptions (e.g., ['gentle preparation', 'deep pressure', 'cooling down'])"
    )


class ProgramPlan(BaseModel):
    """
    A comprehensive plan for constructing a massage program using segment-aware synthesis.
    
    This plan describes how to search for and select segments for each phase of the program,
    based on the user's requirements from MassageProgramDetails.
    """
    
    # Overall program strategy
    program_strategy: str = Field(
        description="High-level strategy for the entire program based on user requirements"
    )
    

    
    overall_intensity: Optional[str] = Field(
        default=None,
        description="Overall intensity level for the program"
    )
    
    # Phase-specific search instructions
    front_phase: PhaseSearchInstruction = Field(
        description="Search instructions for finding front phase segments"
    )
    
    main_phase: PhaseSearchInstruction = Field(
        description="Search instructions for finding main phase segments"
    )
    
    cooling_phase: PhaseSearchInstruction = Field(
        description="Search instructions for finding cooling phase segments"
    )
    
    # Additional constraints
    focus_priorities: Optional[List[str]] = Field(
        default=None,
        description="Prioritized list of focus areas that should guide segment selection"
    )
    
    avoid_characteristics: Optional[List[str]] = Field(
        default=None,
        description="Characteristics or techniques to avoid based on user preferences"
    )
    
    special_requirements: Optional[str] = Field(
        default=None,
        description="Any special requirements or considerations for this program"
    )


class SegmentSelection(BaseModel):
    """
    Result of segment selection for a specific phase.
    """

    selected_segment_ids: List[str] = Field(
        description="List of selected segment IDs for this phase"
    )

    selection_rationale: str = Field(
        description="Explanation of why these segments were selected"
    )
