from datetime import datetime
from typing import List, Optional
from pydantic import BaseModel, Field, ConfigDict


class ProductBase(BaseModel):
    name: str
    description: Optional[str] = None
    model: Optional[str] = None
    series: Optional[str] = None
    type: Optional[str] = None
    subtype: Optional[str] = None
    features: Optional[List[str]] = None
    image_url: Optional[str] = None


class ProductCreate(BaseModel):
    name: str
    model: str
    description: Optional[str] = None
    series: Optional[str] = None
    type: Optional[str] = None
    subtype: Optional[str] = None
    features: Optional[List[str]] = None


class ProductUpdate(BaseModel):
    name: str
    model: str
    description: Optional[str] = None
    series: Optional[str] = None
    type: Optional[str] = None
    subtype: Optional[str] = None
    features: Optional[List[str]] = None


class ProductResponse(ProductBase):
    id: int
    created_at: datetime

    model_config = ConfigDict(from_attributes=True)


class ProductListResponse(BaseModel):
    products: List[ProductResponse]
    total: int
