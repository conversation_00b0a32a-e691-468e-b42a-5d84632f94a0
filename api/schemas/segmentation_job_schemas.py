from pydantic import BaseModel, Field
from typing import Optional
from datetime import datetime
from models.program_models import SegmentationJobStatus


class SegmentationJobCreate(BaseModel):
    """Schema for creating a new segmentation job."""
    program_id: int = Field(description="ID of the program to segment")


class SegmentationJobResponse(BaseModel):
    """Schema for segmentation job response."""
    id: int
    program_id: int
    status: SegmentationJobStatus
    progress_percentage: int
    current_step: Optional[str] = None
    total_steps: int
    completed_steps: int
    error_message: Optional[str] = None
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class SegmentationJobUpdate(BaseModel):
    """Schema for updating segmentation job progress."""
    status: Optional[SegmentationJobStatus] = None
    progress_percentage: Optional[int] = None
    current_step: Optional[str] = None
    completed_steps: Optional[int] = None
    error_message: Optional[str] = None


class SegmentationJobListResponse(BaseModel):
    """Schema for listing segmentation jobs."""
    jobs: list[SegmentationJobResponse]
    total: int
    page: int
    limit: int
