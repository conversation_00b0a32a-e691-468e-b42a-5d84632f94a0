from datetime import datetime
from pydantic import BaseModel, ConfigDict
from typing import Optional, List


class SourceCreate(BaseModel):
    name: str
    description: str
    s3_bucket_url: Optional[str] = None


class FileResponse(BaseModel):
    id: int
    name: str
    status: str
    file_type: str
    size: int
    created_at: datetime

    model_config = ConfigDict(from_attributes=True)


class FileDetailResponse(BaseModel):
    page_content: str
    metadata: dict


class SourceUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    s3_bucket_url: Optional[str] = None


class SourceResponse(BaseModel):
    id: int
    name: str
    description: Optional[str]
    created_at: datetime

    model_config = ConfigDict(from_attributes=True)


class SourceListResponse(BaseModel):
    sources: List[SourceResponse]
    total: int
