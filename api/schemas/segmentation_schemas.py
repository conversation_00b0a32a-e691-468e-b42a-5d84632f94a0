from pydantic import BaseModel, Field
from typing import List, Dict, Any

class SegmentOntology(BaseModel):
    """
    Defines the rich metadata and interface contract for a program segment.
    This is the expected output of the Ontology & Interface Tagging Agent.
    """
    purpose_tags: List[str] = Field(
        description="A list of tags describing the therapeutic goal of the segment (e.g., 'tension_release', 'circulation_boost')."
    )
    technique_tags: List[str] = Field(
        description="A list of the primary massage techniques observed in the steps (e.g., 'percussive', 'deep_kneading')."
    )
    body_part_tags: List[str] = Field(
        description="A list of the main body parts targeted by this segment (e.g., 'lumbar', 'shoulders')."
    )
    intensity_score: int = Field(
        description="An integer from 1 (very gentle) to 10 (very intense) representing the segment's overall intensity."
    )
    
    # The Segment Interface Contract
    entry_state: Dict[str, Any] = Field(
        description="A dictionary summarizing the key parameters of the first step in the segment."
    )
    exit_state: Dict[str, Any] = Field(
        description="A dictionary summarizing the key parameters of the last step in the segment."
    )
class MacroPhase(BaseModel):
    """Defines the start and end indices for a single macro-phase."""
    phase_name: str = Field(description="The name of the phase, e.g., 'front', 'main', 'cooling'.")
    start_index: int = Field(description="The 0-based starting index of the phase in the original steps list.")
    end_index: int = Field(description="The 0-based ending index (inclusive) of the phase.")

class MacroPhaseSegmentation(BaseModel):
    """The structured output of the Macro-Phase Segmentation Agent."""
    phases: List[MacroPhase]
class MicroChunk(BaseModel):
    """Defines the boundaries and a brief description of a discovered micro-chunk."""
    description: str = Field(description="A brief, 1-sentence description of the chunk's therapeutic purpose.")
    start_index: int = Field(description="The 0-based starting index of the chunk within the provided segment.")
    end_index: int = Field(description="The 0-based ending index (inclusive) of the chunk.")

class MicroChunkDiscovery(BaseModel):
    """The structured output of the Micro-Chunk Discovery Agent."""
    chunks: List[MicroChunk]