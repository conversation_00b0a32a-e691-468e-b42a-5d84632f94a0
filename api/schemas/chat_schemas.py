from datetime import datetime
from pydantic import BaseModel, Field, ConfigDict
from typing import Optional, List, Union
from enum import Enum


class SearchType(str, Enum):
    SIMILARITY = "similarity"
    MMR = "mmr"
    SIMILARITY_SCORE_THRESHOLD = "similarity_score_threshold"


# Massage Agent Enums
class DurationEnum(str, Enum):
    FIFTEEN_MINUTES = "15"
    THIRTY_MINUTES = "30"


class IntensityEnum(str, Enum):
    WEAK = "weak"
    MEDIUM = "medium"
    STRONG = "strong"


class WarmAirSettingEnum(str, Enum):
    OFF = "Off"
    LOW = "Low"
    HIGH = "High"


class VibrationSettingEnum(str, Enum):
    LOW = "Low"
    HIGH = "High"
    AUTO = "Auto"
    OFF = "Off"


class AirBagsEnum(str, Enum):
    WIDE = "Wide"
    NARROW = "Narrow"


class ChatConfigResponse(BaseModel):
    id: int
    name: str
    url: str
    model_id: int
    product_id: Optional[int] = None
    sft_model_name: Optional[str] = None
    use_sft_direct_generation: bool = False
    synthesis_strategy: Optional[str] = "segment-aware-synthesis"
    user_id: int
    created_at: datetime
    updated_at: datetime


class ChatConfigResponseWithName(BaseModel):
    id: int
    name: str
    url: str
    model_id: int
    model_name: str
    product_id: Optional[int] = None
    product_name: Optional[str] = None
    sft_model_name: Optional[str] = None
    use_sft_direct_generation: bool = False
    synthesis_strategy: Optional[str] = "segment-aware-synthesis"
    created_at: datetime
    updated_at: datetime


class ChatConfigResponseWithNameList(BaseModel):
    chat_configs: list[ChatConfigResponseWithName]
    total: int


class ChatConfigCreate(BaseModel):
    name: str
    model_id: int
    product_id: Optional[int] = None
    sft_model_name: Optional[str] = None
    synthesis_strategy: Optional[str] = "segment-aware-synthesis"


class ChatConfigUpdate(BaseModel):
    name: Optional[str] = None
    model_id: Optional[int] = None
    product_id: Optional[int] = None
    sft_model_name: Optional[str] = None
    synthesis_strategy: Optional[str] = None
class ChatModels(BaseModel):
    id: int
    name: str
    provider: str


class ModelsList(BaseModel):
    models: list[ChatModels]


class ChatRequest(BaseModel):
    slug: Optional[str] = None
    chat_config_id: Optional[int] = None
    message: str


class ChatThreadResponse(BaseModel):
    id: int
    slug: str
    first_message: Optional[str] = Field(default=None, exclude=None)
    config_id: int
    config_name: Optional[str] = None
    is_starred: bool = False
    created_at: datetime
    updated_at: datetime

    model_config = ConfigDict(from_attributes=True, json_schema_extra={"example": {"first_message": None}})


class ChatThreadsList(BaseModel):
    threads: list[ChatThreadResponse]
    total: int


class Document(BaseModel):
    page_content: str
    metadata: dict


class ChatThreadMessage(BaseModel):
    id: int
    role: str
    content: str
    documents: Optional[list[Document]] = None
    is_sequence: Optional[bool] = None
    sequence_set_id: Optional[int] = None
    program_id: Optional[int] = None
    program_liked: Optional[bool] = None
    program_disliked: Optional[bool] = None
    program_saved: Optional[bool] = None


class ChatThreadMessagesList(BaseModel):
    thread: ChatThreadResponse
    messages: list[ChatThreadMessage]


class ChatStarRequest(BaseModel):
    chat_id: int


class ChatStarResponse(BaseModel):
    success: bool
    is_starred: bool


class MassageSettings(BaseModel):
    """Settings for various massage chair features."""
    
    warm_air: Optional[bool] = Field(
        default=None,
        description="Whether to use warm air feature during the massage."
    )
    vibration: Optional[bool] = Field(
        default=None,
        description="Whether to use vibration feature during the massage."
    )
    air_bags_compression: Optional[bool] = Field(
        default=None,
        description="Whether to use air bags compression feature during the massage."
    )
    scent: Optional[str] = Field(
        default=None,
        description="Scent preference for aromatherapy during the massage (e.g., lavender, eucalyptus, none)."
    )
    light: Optional[str] = Field(
        default=None,
        description="Light setting preference during the massage (e.g., dim, warm, cool, off)."
    )

class ProgrammeSequenceDetail(BaseModel):
    """Structure for the different phases of a massage sequence."""

    front_sequence: str = Field(
        ...,
        alias="Front Sequence",
        description="Description of the initial phase of the massage.",
        examples=["Warmup kneading"],
    )
    main_sequence: str = Field(
        ...,
        alias="Main Sequence",
        description="Description of the main focus phase of the massage.",
        examples=["Deep tissue focus on lower back"],
    )
    cooling_sequence: str = Field(
        ...,
        alias="Cooling Sequence",
        description="Description of the concluding phase of the massage.",
        examples=["Gentle rolling"],
    )

class UserMassageRequest(BaseModel):
    """Structure to hold extracted user massage preferences.

    Attributes:
        concern: List of specific body part suggestions based on the user's primary concern (e.g., neck, lower back) or general goals (e.g., relaxation, stress relief).
        session_time_minutes: Desired massage duration.
        intensity: Preferred intensity level.
        target_market_group: Who are we targeting? Designing this massage program for?
        desired_outcome: What kind of effects we want to achieve from this massage.
        program_flow: The massage sequence phases (front, main, cooling).
        massage_settings: Various massage chair settings and features.
    """

    concern: List[str] = Field(
        description="List of specific body part suggestions based on the user's primary concern (e.g., neck, lower back) or general goals (e.g., relaxation, stress relief)."
    )
    session_time_minutes: Optional[int] = Field(
        default=None,
        description="Desired massage duration in minutes (e.g., 10, 15, 30).",
    )
    intensity: Optional[str] = Field(
        default=None,
        description="Preferred intensity level (e.g., gentle, medium, strong).",
    )
    techniques: Optional[List[str]] = Field(
        default=None,
        description="List of massage techniques the user wants to use. This is a list of strings, each string is a massage technique.",
    )
    target_market_group: Optional[str] = Field(
        default=None,
        description="Target demographic or user group for the massage program (e.g., office workers, athletes, elderly)."
    )
    desired_outcome: Optional[str] = Field(
        default=None,
        description="Expected effects or benefits from the massage (e.g., pain relief, improved circulation, stress reduction)."
    )
    program_flow: Optional[ProgrammeSequenceDetail] = Field(
        default=None,
        description="The structured flow of the massage program with front, main, and cooling sequences."
    )
    massage_settings: Optional[MassageSettings] = Field(
        default=None,
        description="Various massage chair settings including warm air, vibration, air compression, scent, and light preferences."
    )





class TargetSequence(BaseModel):
    """Pydantic model for the structured output of the sequence generator agent.

    Now uses a nested model for programme_sequence.
    """

    name: str = Field(
        ...,
        description="A name for the massage sequence, typically based on the concerns and goals",
    )
    objective: str = Field(
        ...,
        description="A brief description of the overall goal and flow of the massage sequence, addressing the concerns",
    )
    programme_sequence: ProgrammeSequenceDetail = Field(
        ..., description="Structured details of the massage sequence phases."
    )
    targeted_acupressure_points: List[str] = Field(
        ...,
        description="A list of targeted acupressure points relevant to the concerns",
    )
    signature_moves: List[str] = Field(
        ...,
        description="A list of 2-4 key massage techniques or focus areas relevant to the concerns",
    )

    def to_formatted_string(self) -> str:
        """Converts the Pydantic model to the required string format for embedding."""
        moves_str = ", ".join(self.signature_moves)

        # Format programme_sequence using the nested model's attributes
        sequence_parts = [
            f"Front Sequence: {self.programme_sequence.front_sequence}",
            f"Main Sequence: {self.programme_sequence.main_sequence}",
            f"Cooling Sequence: {self.programme_sequence.cooling_sequence}",
        ]
        targeted_acupressure_points_str = ", ".join(self.targeted_acupressure_points)
        sequence_str = "; ".join(sequence_parts)

        return f"Objective: {self.objective}\\nProgramme Sequence: {sequence_str}\\nSignature Moves: {moves_str}\\nTargeted Acupressure Points: {targeted_acupressure_points_str}"


class StepType(str, Enum):
    ACTION = "action"
    SUBROUTINE = "subroutine"
    PROGRAM_NOTE = "program_note"
    COMMENT = "comment"


class HighEndProductStep(BaseModel):
    """Pydantic model for a single massage program step."""

    step_number: Optional[str] = Field(
        None,
        description="The identifier for this step in the sequence (e.g., '1a', '2', '22A')",
    )
    roller_action_description: Optional[str] = Field(
        None, description="Description of the roller movement and action"
    )
    air_action_description: Optional[str] = Field(
        None, description="Description of the air pressure actions"
    )
    kneading_speed: Optional[Union[int, str]] = Field(
        None, description="Speed setting for kneading action, typically 100-125"
    )
    tapping_speed: Optional[Union[int, str]] = Field(
        None, description="Speed setting for tapping action, typically 130-220"
    )
    position_3d: Optional[Union[List[Union[str, int, None]], str]] = Field(
        None,
        description="3D position coordinates, usually [position_code, position_number]",
    )
    width: Optional[str] = Field(
        None,
        description="Width setting, typically 'N' (narrow), 'M' (medium), or 'W' (wide)",
    )
    description: Optional[str] = Field(
        None,
        description="Description of the step, typically a combination of the action, speed, and position",
    )
    seat_program: Optional[str] = Field(
        None, description="Associated seat program if applicable"
    )
    scent: Optional[str] = Field(None, description="Scent setting if applicable")
    light: Optional[str] = Field(None, description="Light setting if applicable")
    type: StepType = Field(..., description="Type of step")
    subroutine_id: Optional[str] = Field(
        None, description="ID of the subroutine if type is 'subroutine'"
    )


class HighEndProductSteps(BaseModel):
    steps: List[HighEndProductStep]


class SegmentedSteps(BaseModel):
    """Holds the steps of a program segmented into phases."""

    front_steps: List[HighEndProductStep] = Field(
        default_factory=list, description="Steps corresponding to the Front Sequence"
    )
    main_steps: List[HighEndProductStep] = Field(
        default_factory=list, description="Steps corresponding to the Main Sequence"
    )
    cooling_steps: List[HighEndProductStep] = Field(
        default_factory=list, description="Steps corresponding to the Cooling Sequence"
    )


class StitchingInput(BaseModel):
    """Input structure for the agent responsible for stitching segmented steps."""

    segmented_program_1: SegmentedSteps = Field(
        ..., description="Segmented steps from the first relevant program."
    )
    segmented_program_2: SegmentedSteps = Field(
        ..., description="Segmented steps from the second relevant program."
    )
    segmented_program_3: SegmentedSteps = Field(
        ..., description="Segmented steps from the third relevant program."
    )
    target_sequence: TargetSequence = Field(
        ...,
        description="The high-level target sequence to guide the stitching process.",
    )


class ProgramFlowDetails(BaseModel):
    """Details about the structure and progression of the massage program."""
    highlights: Optional[str] = Field(
        default=None,
        description="Key highlights of the massage program flow (e.g., 'based on Lumbar program', 'start-main-cool down')."
    )
    signature_moves: Optional[str] = Field(
        default=None,
        description="Specific signature massage movements or types to be included (e.g., 'kneading', 'tapping', 'Lumbar program's key movements')."
    )
    logic_techniques: Optional[str] = Field(
        default=None,
        description="The logic or techniques behind the program's structure (e.g., 'Deep Tissue', 'Shiatsu')."
    )
    intensity: Optional[IntensityEnum] = Field(
        default=None,
        description="The overall intensity level of the massage. Must be 'weak', 'medium', or 'strong'."
    )

class MassageSettingsDetails(BaseModel):
    """Specific settings for the massage chair or environment."""
    warm_air: WarmAirSettingEnum = Field(
        default=WarmAirSettingEnum.OFF,
        description="Warm air setting. Default is 'Off'. Only set to 'Low' or 'High' if user explicitly mentions turning on warm air."
    )
    vibration: VibrationSettingEnum = Field(
        default=VibrationSettingEnum.AUTO,
        description="Vibration setting. Default is 'Auto' (on). Can be set to 'Low', 'High', 'Auto', or 'Off' based on user preference."
    )
    air_bags: Optional[AirBagsEnum] = Field(
        default=None,
        description="Air bag compression setting. Can be 'Wide' or 'Narrow' if requested by user."
    )
    scent: bool = Field(
        default=True,
        description="Whether scent/aromatherapy is enabled. Default is True (on)."
    )
    mood_light: bool = Field(
        default=True,
        description="Whether mood lighting is enabled. Default is True (on)."
    )

class MassageProgramDetails(BaseModel):
    """
    Structured information extracted from a user's request for a massage program.
    """
    target_market: str = Field(
        default="18 to 65",
        description="The intended user or group for the massage. Defaults to '18 to 65' if not specified (e.g., 'desk jobs', 'active lifestyle', 'frequent traveler')."
    )
    duration: DurationEnum = Field(
        ...,
        description="The requested duration of the massage. Must be either '15' or '30' minutes."
    )
    focus_area: List[str] = Field(
        default=["Full body"],
        description="The primary muscle groups or body regions the massage should target. Defaults to ['Full body'] if not specified (e.g., ['lower back', 'hip and thigh regions'], ['legs', 'feet', 'lower back'])."
    )
    problem_desired_outcome: str = Field(
        ...,
        description="REQUIRED: The main problem the user wants to address or the desired outcome of the massage (e.g., 'easing lower back discomfort', 'boosting circulation', 'emotional wellness', 'boost energy', 'muscle recovery', 'relieve body tension'). This field is compulsory and must be provided."
    )
    program_flow: Optional[ProgramFlowDetails] = Field(
        default=None,
        description="Details about the structure and progression of the massage program."
    )
    massage_settings: Optional[MassageSettingsDetails] = Field(
        default=None,
        description="Specific settings for the massage chair or environment."
    )