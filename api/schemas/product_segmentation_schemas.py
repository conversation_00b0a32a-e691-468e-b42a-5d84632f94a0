from pydantic import BaseModel, Field
from typing import Optional, List
from datetime import datetime
from models.program_models import SegmentationJobStatus


class ProductSegmentationJobCreate(BaseModel):
    """Schema for creating a new product segmentation job."""
    product_id: int = Field(description="ID of the product to segment all programs for")


class ProductSegmentationJobResponse(BaseModel):
    """Schema for product segmentation job response."""
    id: int
    product_id: int
    status: SegmentationJobStatus
    progress_percentage: int
    current_step: Optional[str] = None
    total_programs: int
    completed_programs: int
    failed_programs: int
    error_message: Optional[str] = None
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class ProductSegmentationJobListResponse(BaseModel):
    """Schema for paginated list of product segmentation jobs."""
    jobs: List[ProductSegmentationJobResponse]
    total: int
    page: int
    limit: int


class ProductSegmentationJobWithPrograms(ProductSegmentationJobResponse):
    """Extended schema that includes related program jobs."""
    program_jobs: List["SegmentationJobResponse"] = Field(default_factory=list)

    class Config:
        from_attributes = True


# Import here to avoid circular imports
from schemas.segmentation_job_schemas import SegmentationJobResponse
ProductSegmentationJobWithPrograms.model_rebuild()
