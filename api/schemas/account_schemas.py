import datetime
from pydantic import BaseModel
from typing import List


class AccountCreate(BaseModel):
    email: str
    username: str
    cognito_id: str
    token: str


class Account(BaseModel):
    email: str
    username: str
    cognito_id: str
    roles: List[str] = []


class AccountsList(BaseModel):
    accounts: List[Account]
    total: int


class UpdateRoleRequest(BaseModel):
    cognito_id: str
    roles: List[str]
