from pydantic import BaseModel, Field, computed_field, ConfigDict
from typing import List, Optional, Dict, Any
from datetime import datetime
from models.program_models import ProgramStatus

# --- Base Schemas (Common fields) ---


class BaseSchema(BaseModel):
    id: int
    model_config = ConfigDict(from_attributes=True)


# --- SubRoutine Schemas ---


# Schema for List View (Simpler)
class SubRoutineListSchema(BaseModel):
    id: int
    name: str
    subroutine_id_json: str = Field(..., alias="subroutineIdJson")
    product_id: int
    product_name: Optional[str] = None
    actions_count: Optional[int] = Field(0, alias="actionsCount")
    created_at: datetime
    updated_at: Optional[datetime] = None

    model_config = ConfigDict(from_attributes=True, populate_by_name=True) # Allow alias population


# Response schema for paginated subroutine list
class SubRoutineListResponse(BaseModel):
    subroutines: List[SubRoutineListSchema]
    total: int

    model_config = ConfigDict(from_attributes=True, populate_by_name=True)


# Full Schema (Keep for potential detail view or creation/update)
class SubRoutineBase(BaseModel):
    name: str
    subroutine_id_json: str = Field(..., alias="subroutineIdJson")
    files: Optional[List[str]] = None
    steps: Optional[List[Dict[str, Any]]] = None
    overall_settings: Optional[Dict[str, Any]] = Field(None, alias="overallSettings")
    column_mapping_used: Optional[Dict[str, Any]] = Field(None)


class SubRoutineCreate(SubRoutineBase):
    product_id: int = Field(..., alias="productId")


# Add created_at/updated_at here if needed for full schema
class SubRoutineSchema(SubRoutineBase):
    id: int
    product_id: int = Field(..., alias="productId")
    created_at: datetime
    updated_at: datetime

    model_config = ConfigDict(from_attributes=True, populate_by_name=True) # Keep for compatibility if used elsewhere


# --- Program Segment Schemas ---

class ProgramSegmentSchema(BaseModel):
    id: int
    original_program_id: int
    phase: str
    parent_segment_id: Optional[int] = None

    # Interface Contract
    entry_state: Dict[str, Any]
    exit_state: Dict[str, Any]

    # Ontology
    purpose_tags: Optional[List[str]] = []
    technique_tags: Optional[List[str]] = []
    body_part_tags: Optional[List[str]] = []

    # Metrics
    intensity_score: Optional[int] = None
    duration_seconds: Optional[int] = None

    # Core Data
    steps: List[Dict[str, Any]]

    model_config = ConfigDict(from_attributes=True)
# --- Program Version Schemas ---


# Minimal User schema for ProgramVersion context
class UserMinimalSchemaForProgramVersion(BaseModel):
    username: str

    model_config = ConfigDict(from_attributes=True)


class ProgramVersionSchema(BaseModel):
    id: int
    program_id: int
    version_number: int
    steps: List[Dict[str, Any]]
    created_at: datetime
    created_by_user_id: Optional[int] = None
    version_notes: Optional[str] = None
    user: Optional["UserMinimalSchemaForProgramVersion"] = Field(
        default=None, exclude=True
    )

    @computed_field
    @property
    def created_by_username(self) -> Optional[str]:
        if self.user:
            return self.user.username
        return None

    model_config = ConfigDict(from_attributes=True)


# Version of ProgramVersionSchema without steps for program listing
class ProgramVersionMetaSchema(BaseModel):
    id: int
    program_id: int
    version_number: int
    created_at: datetime
    created_by_user_id: Optional[int] = None
    version_notes: Optional[str] = None
    user: Optional["UserMinimalSchemaForProgramVersion"] = Field(
        default=None, exclude=True
    )

    @computed_field
    @property
    def created_by_username(self) -> Optional[str]:
        if self.user:
            return self.user.username
        return None

    model_config = ConfigDict(from_attributes=True)


# --- Program Schemas ---


class ProgramBase(BaseModel):
    name: Optional[str] = None
    source_folder: Optional[str] = None
    source_filename: Optional[str] = None
    logic_technique: Optional[str] = Field(None)
    program_title: Optional[str] = Field(None)
    source_notes: Optional[str] = Field(None)
    program_description: Optional[Dict[str, Any]] = Field(None)
    overall_settings: Optional[Dict[str, Any]] = Field(None, alias="overallSettings")
    steps: List[Dict[str, Any]]  # Represents the steps of the *active* version
    column_mapping_used: Optional[Dict[str, Any]] = Field(None)
    status: Optional[ProgramStatus] = None


class ProgramCreate(ProgramBase):
    product_id: int = Field(..., alias="productId")
    category_id: int = Field(..., alias="categoryId")
    sub_routine_ids: Optional[List[int]] = Field(None, alias="subRoutineIds")


class ProgramSchema(ProgramBase, BaseSchema):
    product_id: int = Field(..., alias="productId")
    category_id: int = Field(..., alias="categoryId")
    current_version_number: int
    sub_routines: List[SubRoutineSchema] = Field([], alias="subRoutines")
    versions: List[ProgramVersionMetaSchema] = Field(
        [], alias="versions"
    )

    product_name: Optional[str] = None
    product_model: Optional[str] = None
    category_name: Optional[str] = None

    model_config = ConfigDict(from_attributes=True, populate_by_name=True)


class ProgramUpdate(BaseModel):
    name: Optional[str] = None
    program_title: Optional[str] = Field(None, alias="programTitle")
    steps: Optional[List[Dict[str, Any]]] = None
    logic_technique: Optional[str] = None
    program_description: Optional[Dict[str, Any]] = None
    version_notes: Optional[str] = None
    save_action: Optional[str] = Field("new", alias="saveAction")
    status: Optional[ProgramStatus] = None

    model_config = ConfigDict(from_attributes=True, populate_by_name=True)


class ProgramStatusUpdate(BaseModel):
    status: ProgramStatus


# --- Program List Response Schema ---
class ProgramListSchema(BaseModel):
    id: int
    name: Optional[str] = None
    program_title: Optional[str] = None
    source_folder: Optional[str] = None
    source_filename: Optional[str] = None
    product_id: int
    product_name: Optional[str] = None
    user_id: Optional[int] = None
    user_name: Optional[str] = None
    created_at: datetime
    updated_at: Optional[datetime] = None
    status: Optional[ProgramStatus] = None

    model_config = ConfigDict(from_attributes=True, populate_by_name=True)


class ProgramListResponse(BaseModel):
    programs: List[ProgramListSchema]
    total: int

    model_config = ConfigDict(from_attributes=True, populate_by_name=True)


# --- Program Segment List Response Schema ---
class ProgramSegmentListResponse(BaseModel):
    segments: List[ProgramSegmentSchema]
    total: int
    page: int
    limit: int

    model_config = ConfigDict(from_attributes=True, populate_by_name=True)


# --- Program Category Schemas ---


class ProgramCategoryBase(BaseModel):
    name: str
    description: Optional[str] = None


class ProgramCategoryCreate(ProgramCategoryBase):
    pass


class ProgramCategorySchema(ProgramCategoryBase, BaseSchema):
    description: Optional[str] = None
    created_at: datetime
    updated_at: Optional[datetime] = None

    model_config = ConfigDict(from_attributes=True)


# --- Schema for Frontend Category View ---
# Represents a category with its program count for the specific product view


class ProductCategoryWithCountSchema(ProgramCategorySchema):
    program_count: int = Field(0, alias="programCount")

    model_config = ConfigDict(from_attributes=True, populate_by_name=True)


class ProgramCategoryDeleteResponse(BaseModel):
    success: bool
    message: str


# --- Bulk Embedding Schemas ---


class BulkEmbeddingRequest(BaseModel):
    """Request schema for bulk embedding updates"""

    product_id: Optional[int] = None  # Filter by product ID
    category_id: Optional[int] = None  # Filter by category ID
    limit: Optional[int] = Field(
        100, description="Maximum number of programs to process"
    )
    batch_size: Optional[int] = Field(
        10, description="Number of programs to process in each batch"
    )


class BulkEmbeddingResponse(BaseModel):
    """Response schema for bulk embedding request"""

    success: bool
    message: str
    scheduled_count: int


# --- Segment Cleanup Schemas ---

class SegmentCleanupStatistics(BaseModel):
    """Statistics for segment cleanup operation"""

    segments_deleted: int
    micro_chunks_deleted: int
    macro_phases_deleted: int
    jobs_deleted: int  # Program segmentation jobs deleted
    product_jobs_deleted: int  # Product segmentation jobs deleted
    vectors_deleted_from_pinecone: int
    sequences_reset: bool


class SegmentCleanupFilters(BaseModel):
    """Filters applied during cleanup"""

    product_id: Optional[int] = None
    program_id: Optional[int] = None


class SegmentCleanupResponse(BaseModel):
    """Response schema for segment cleanup operation"""

    success: bool
    message: str
    statistics: SegmentCleanupStatistics
    filters_applied: SegmentCleanupFilters
