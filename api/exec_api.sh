#!/bin/bash

# Get the service name dynamically
SERVICE_NAME=$(aws ecs list-services \
  --cluster keoni-lang-1-test-Cluster-FJDVzfumLPnw \
  --query 'serviceArns[?contains(@, `api-Service`)]' \
  --output text | awk -F'/' '{print $NF}')

if [ -z "$SERVICE_NAME" ]; then
    echo "No API service found"
    exit 1
fi

echo "Found service: $SERVICE_NAME"

# Get the latest task ID for the cluster
TASK_ID=$(aws ecs list-tasks \
  --cluster keoni-lang-1-test-Cluster-FJDVzfumLPnw \
  --service-name "$SERVICE_NAME" \
  --query 'taskArns[0]' \
  --output text | awk -F'/' '{print $3}')

if [ -z "$TASK_ID" ]; then
    echo "No running tasks found"
    exit 1
fi

echo "Connecting to task: $TASK_ID"

aws ecs execute-command \
  --cluster keoni-lang-1-test-Cluster-FJDVzfumLPnw \
  --task arn:aws:ecs:us-east-1:038462746066:task/keoni-lang-1-test-Cluster-FJDVzfumLPnw/$TASK_ID \
  --container api \
  --command "/bin/sh" \
  --interactive
