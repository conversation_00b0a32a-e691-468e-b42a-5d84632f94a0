import logging
import sys
from contextlib import asynccontextmanager

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
import logfire
from pydantic_ai import Agent
from config.settings import settings
from services.sqs_consumer import start_sqs_consumer, stop_sqs_consumer
from routes import (
    account_router,
    source_router,
    chat_router,
    chat_program_router,
    product_router,
    program_router,
    segment_router,
    dashboard_router,
)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[logging.StreamHandler(sys.stdout)],
)

# Configure logfire
logfire.configure(
    token=settings.LOGFIRE_TOKEN,
    environment="local" if settings.DEBUG else "production",
)

Agent.instrument_all()


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Manage application lifespan events."""
    # Startup
    logger = logging.getLogger(__name__)
    logger.info("Starting SQS consumer...")
    start_sqs_consumer()

    yield

    # Shutdown
    logger.info("Stopping SQS consumer...")
    stop_sqs_consumer()


allowed_origins = [
    "http://localhost:3000",
]

app = FastAPI(
    title="LangChain Server",
    version="1.0",
    description="A simple api server using Langchain's Runnable interfaces",
    docs_url="/api2/docs",
    redoc_url=None,
    openapi_url="/api2/openapi.json",
    lifespan=lifespan,
)

app.add_middleware(
    CORSMiddleware,
    allow_origins=allowed_origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Mount static files directory
# This will serve files from 'uploads' directory under the path '/uploads'
# e.g., a file at uploads/products/image.jpg will be accessible at /uploads/products/image.jpg
app.mount("/uploads", StaticFiles(directory="uploads"), name="uploads")


@app.get("/api2")
async def redirect_root_to_docs():
    return {"Welcome to LangChain Server!"}


@app.get("/api2/health")
async def pong():
    return {"ping": "pong"}


app.include_router(account_router)
app.include_router(source_router)
app.include_router(chat_router)
app.include_router(chat_program_router)
app.include_router(product_router)
app.include_router(program_router)
app.include_router(segment_router)
app.include_router(dashboard_router)
