# Massage.ai Project Guidelines

This document provides conventions for working on this project.

## Project Overview

This is a full-stack application with a Python (FastAPI) backend in `api/` and a Next.js (TypeScript) frontend in `ui/`.

## Common Commands

### Backend (Python/FastAPI)

- **Run dev server**: `uvicorn api.main:app --reload --host 0.0.0.0 --port 8000`
- **Run tests**: `pytest`
- **Run tests (specific file)**: `pytest api/tests/test_chat_routes.py`
- **Create migration**: `cd api && uv run alembic revision -m "your_migration_message"`
- **Apply migration**: `cd api && uv run alembic upgrade head`

### Frontend (Next.js/TypeScript)

- **Run dev server**: `pnpm --filter ui dev`
- **Lint**: `pnpm --filter ui lint`
- **Lint fix**: `pnpm --filter ui lint:fix`
- **Format check**: `pnpm --filter ui format:check`

## Code Style

### General

- Make minimal, focused changes.
- Prioritize readability and maintainability.

### Python (api/)

- Follow PEP 8 guidelines.
- Use type hints for all function signatures.
- Use f-strings for string formatting.
- When changing SQLAlchemy models, create an Alembic migration.

### TypeScript/JavaScript (ui/)

- Adhere to ESLint and Prettier configurations.
- Prefer functional components with Hooks.
- Use TypeScript for all new code.
