# Project Overview

This is a full-stack application with:

- A Python (FastAPI) backend in the 'api/' directory
  - Uses SQLAlchemy for ORM, Alembic for migrations
  - Integrates with PyDanticAI, Langchain, OpenAI, Pinecone, and AWS services (boto3)
  - Uses 'uv' for package management, targeting Python >=3.12
- A Next.js (React, TypeScript) frontend in the 'ui/' directory
  - Uses Tailwind CSS for styling
  - Uses ESLint for linting and Prettier for formatting
  - Uses 'pnpm' for package management

## Code Style

### General

- Make minimal, focused changes
- Follow DRY, KISS, and YAGNI principles
- Comments in English only
- Write comments to explain _why_ something is done, not _what_, if the code isn't self-explanatory
- Prioritize readability and maintainability

### Python (api/)

- Adhere to PEP 8 guidelines
- Use type hints for all function signatures and complex variables
- Prefer f-strings for string formatting
- When working with SQLAlchemy, follow established patterns for models, sessions, and queries
- For FastAPI, follow common patterns for routers, request/response models (Pydantic)
- Ensure Boto3 calls are clear and handle exceptions appropriately using custom exceptions where beneficial
- When using PyDanticAI and Langchain, ensure chains, agents, and integrations are constructed logically and efficiently

### TypeScript/JavaScript (ui/)

- Adhere to ESLint and Prettier configurations present in the ui/ directory
- Prefer functional components with Hooks in React
- Use TypeScript for all new frontend code; ensure strong typing
- Prefer `interface` for object shapes and `type` for unions/primitives
- Follow Next.js conventions for page structure (App Router), routing, and data fetching (Server Components preferred)
- Utilize Tailwind CSS utility classes for styling; avoid custom CSS where possible unless creating truly reusable, encapsulated component styles
- Ensure React components are well-structured and props are clearly defined with TypeScript interfaces

## Libraries and Dependencies

### Python (api/)

- When adding or modifying dependencies, update api/pyproject.toml
- Instruct to run uv sync in the api/ directory after changing pyproject.toml
- Dependencies should be installed in the virtual environment managed by uv

### TypeScript/JavaScript (ui/)

- When adding or modifying dependencies, update ui/package.json
- Instruct to use pnpm install or pnpm add [package] in the ui/ directory for managing Node.js dependencies

## Terminal Usage

- When running the Python backend, use uvicorn api.main:app --reload --host 0.0.0.0 --port 8000 from the project root (assuming main.py is in api/ and contains app)
- When running the Next.js frontend, use pnpm --filter ui dev (if using pnpm workspaces) or cd ui && pnpm run dev
- For Alembic migrations: uv run alembic revision -m "your_migration_message" and uv run alembic upgrade head. Run these from the api/ directory or ensure alembic.ini is configured for project root execution
- For Git commands, use non-interactive flags where possible (e.g., git --no-pager diff)

## Code Changes

- You MUST respect existing code style and patterns
- You MUST suggest only minimal changes related to the current user dialog
- You MUST change as few lines as possible while solving the problem
- You MUST understand the existing codebase before suggesting changes
- Start by reading related files if context is insufficient
- For API changes, consider implications for the UI and vice-versa, though focus on the specific area of the request
- When suggesting changes to SQLAlchemy models in api/, YOU MUST also prompt the user to create an Alembic migration or suggest the migration script content

## Repository Practices

- The api/ directory contains the backend code (Python, FastAPI)
- The ui/ directory contains the frontend code (Next.js, TypeScript, React)
- Configuration for the API is managed via environment variables (see api/env.example) and loaded using Pydantic settings or Python Decouple
- Docker is used for containerization (Dockerfile in api/ and ui/, docker-compose-dev.yml)
