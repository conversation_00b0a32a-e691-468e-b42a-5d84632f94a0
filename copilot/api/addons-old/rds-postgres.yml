Parameters:
  App:
    Type: String
    Description: Your application's name.
  Env:
    Type: String
    Description: The environment name your service, job, or workflow is being deployed to.
  Name:
    Type: String
    Description: The name of the service, job, or workflow being deployed.


Resources:
  DBSubnetGroup:
    Type: AWS::RDS::DBSubnetGroup
    Properties:
      DBSubnetGroupDescription: Group of subnets to place DB into
      SubnetIds: !Split [ ',', { 'Fn::ImportValue': !Sub '${App}-${Env}-PrivateSubnets' } ]

  DatabaseSecurityGroup:
    Metadata:
      'aws:copilot:description': 'A security group to access the DB cluster'
    Type: AWS::EC2::SecurityGroup
    Properties:
      GroupDescription: "DB Security Group"
      VpcId: { 'Fn::ImportValue': !Sub '${App}-${Env}-VpcId' }
  # Enable ingress from other ECS services created within the environment.
  DBIngress:
    Metadata:
      'aws:copilot:description': 'Allow ingress from containers in my application to the DB cluster'
    Type: AWS::EC2::SecurityGroupIngress
    Properties:
      Description: Ingress from Fargate containers
      GroupId: !Ref 'DatabaseSecurityGroup'
      IpProtocol: tcp
      FromPort: 5432
      ToPort: 5432
      SourceSecurityGroupId: { 'Fn::ImportValue': !Sub '${App}-${Env}-EnvironmentSecurityGroup' }
  # The cluster itself.
  DBInstance:
    Metadata:
      'aws:copilot:description': 'DB cluster'
    Type: AWS::RDS::DBInstance
    Properties:
      Engine: postgres
      EngineVersion: '15.7'
      DBInstanceClass: 'db.t3.micro'
      AllocatedStorage: 20
      StorageType: gp2
      MultiAZ: false
      AllowMajorVersionUpgrade: false
      AutoMinorVersionUpgrade: true
      DeletionProtection: false
      BackupRetentionPeriod: 1
      EnablePerformanceInsights: false
      PubliclyAccessible: false
      DBName: 'keoni'
      MasterUsername: 'postgres'
      MasterUserPassword: !Sub '{{resolve:ssm-secure:/copilot/${App}/${Env}/secrets/DATABASE_PASSWORD:1}}'
      DBSubnetGroupName: !Ref 'DBSubnetGroup'
      VPCSecurityGroups:
        - !Ref DatabaseSecurityGroup
      # Add monitoring interval
      MonitoringInterval: 0
      # Add required tags
      Tags:
        - Key: copilot-application
          Value: !Ref App
        - Key: copilot-environment
          Value: !Ref Env
  EndpointAddressParam:
    Type: AWS::SSM::Parameter
    Properties:
      Name: !Sub "/copilot/${App}/${Env}/secrets/DATABASE_ENDPOINT"
      Type: String
      Value: !GetAtt DBInstance.Endpoint.Address
Outputs:
  DatabaseSecurityGroup:
    Description: Security group for DB
    Value: !Ref DatabaseSecurityGroup