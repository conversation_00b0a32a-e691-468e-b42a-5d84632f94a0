# The manifest for the "ui" service.
# Read the full specification for the "Load Balanced Web Service" type at:
#  https://aws.github.io/copilot-cli/docs/manifest/lb-web-service/

# Your service name will be used in naming your resources like log groups, ECS services, etc.
name: ui
type: Load Balanced Web Service

# Distribute traffic to your service.
http:
  path: '/'
  healthcheck: '/'

# Configuration for your containers and service.
image:
  # Docker build arguments. For additional overrides: https://aws.github.io/copilot-cli/docs/manifest/lb-web-service/#image-build
  build: ui/Dockerfile
  # Port exposed through your container to route traffic to it.
  port: 3000

cpu: 256        # Number of CPU units for the task (reduced for dev environment).
memory: 512     # Amount of memory in MiB used by the task (reduced for dev environment).
count:          # Auto-scaling configuration
  range: 1-2
  cpu_percentage: 70
  memory_percentage: 80
exec: true     # Enable running commands in your container.
network:
  connect: true # Enable Service Connect for intra-environment traffic between services.

# storage:
  # readonly_fs: true       # Limit to read-only access to mounted root filesystems.

# Optional fields for more advanced use-cases.
#
variables:                    # Pass environment variables as key value pairs.
 NEXTAUTH_URL: http://keoni--publi-qoqj0tk9mdu3-475965145.us-east-1.elb.amazonaws.com
 AUTH_TRUST_HOST: keoni--publi-qoqj0tk9mdu3-475965145.us-east-1.elb.amazonaws.com
 NEXTAUTH_SECRET: b476f879a370ea81b2631a804751087b6788816215a839d9605ec29ff5603d27
 GITHUB_ID: GITHUB_ID
 GITHUB_SECRET: GITHUB_SECRET
 NEXT_PUBLIC_USER_POOL_ID: us-east-1_Qkklle9VL
 NEXT_PUBLIC_USER_POOL_CLIENT_ID: 5lnolt60mqg5un19kimnekapb


secrets:
  COGNITO_USER_POOL_ID: /copilot/${COPILOT_APPLICATION_NAME}/${COPILOT_ENVIRONMENT_NAME}/secrets/COGNITO_USER_POOL_ID
  COGNITO_CLIENT_ID: /copilot/${COPILOT_APPLICATION_NAME}/${COPILOT_ENVIRONMENT_NAME}/secrets/COGNITO_CLIENT_ID
  # COGNITO_CLIENT_SECRET: /copilot/${COPILOT_APPLICATION_NAME}/${COPILOT_ENVIRONMENT_NAME}/secrets/COGNITO_CLIENT_SECRET
  # COGNITO_ISSUER: /copilot/${COPILOT_APPLICATION_NAME}/${COPILOT_ENVIRONMENT_NAME}/secrets/COGNITO_ISSUER

# You can override any of the values defined above by environment.
#environments:
#  test:
#    count: 2               # Number of tasks to run for the "test" environment.
#    deployment:            # The deployment strategy for the "test" environment.
#       rolling: 'recreate' # Stops existing tasks before new ones are started for faster deployments.