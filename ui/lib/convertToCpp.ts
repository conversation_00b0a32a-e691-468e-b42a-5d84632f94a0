// Type definitions for the massage program data
interface Position3D {
  value: string | number | null;
  depth: number | null;
}

// Updated ProgramStep interface
interface ProgramStep {
  starting_timing: number | null;
  step_number: string | null; // Can be null for comments/notes
  roller_action_description: string | null;
  air_action_description: string | null;
  kneading_speed: number | string | null; // Can be a range string
  tapping_speed: number | string | null; // Can be a range string
  position_3d: (string | number | null)[] | string | null; // Can be a string for comments
  width: string | null;
  seat_program: string | null;
  notes: string | null;
  scent: string | null;
  light: string | null;
  type: 'action' | 'subroutine' | 'comment' | 'program_note';
  subroutine_id?: string;
  description?: string; // For program_note type
}

// Interface for Subroutine definition
interface Subroutine {
  name: string;
  subroutineIdJson: string;
  steps: ProgramStep[];
}

// Interface for the overall settings from the new JSON
interface OverallSettings {
    strength: string;
    position: string;
    leg_program: string;
    default_ranges: {
        kneading_speed: string;
        tapping_speed: string;
        position_3d: string;
    };
}

// Interface for the new detailed program data structure
interface DetailedProgramData {
  id: number;
  name: string;
  source_folder: string;
  source_filename: string;
  logic_technique: string;
  program_title: string;
  source_notes: string;
  program_description: any; // Keeping as any for brevity, can be typed out
  overallSettings: OverallSettings;
  steps: ProgramStep[];
  column_mapping_used: any; // Keeping as any
  productId: number;
  categoryId: number;
  current_version_number: number;
  subRoutines?: Subroutine[]; // Optional, as it might not always be present
}

// Comprehensive mapping of roller actions to C++ operation codes (Table 5 from OSIM doc)
const OPERATION_MAP: Record<string, string> = {
  // Japanese patterns
  'もみ下げロール': 'M_KNEAD_ROLPART',
  'もみ下げ': 'M_KNEAD',
  'もみ上げ': 'M_KNEAD',
  '叩き': 'M_TAP',
  '叩きロール': 'M_TAP_ROLPART',
  'ロール': 'M_ROLPART',
  
  // English patterns
  'squeeze': 'M_KNEAD',
  'knead': 'M_KNEAD',
  'tap': 'M_TAP',
  'roll': 'M_ROLPART',
  'osi': 'M_OSI',
  
  // Extended operation modes from OSIM Table 5
  'knead_tap': 'M_KNEAD_TAP',
  'knead_rolpart': 'M_KNEAD_ROLPART',
  'knead_tap_rolpart': 'M_KNEAD_TAP_ROLPART',
  'tap_rolpart': 'M_TAP_ROLPART',
  'osi_rolpart': 'M_OSI_ROLPART',
  'rolpart': 'M_ROLPART',
  'combination': 'M_KNEAD_TAP',
  'combined': 'M_KNEAD_TAP',
};

// Comprehensive mapping of position descriptions to C++ position constants (Table 2 from OSIM doc)
const POSITION_MAP: Record<string, string> = {
  // Japanese patterns
  '肩上': 'POS_SHOULDER_UPPER',
  '肩': 'POS_SHOULDER',
  '肩下': 'POS_SHOULDER_LOWER',
  '肩下2': 'POS_SHOULDER_LOWER2',
  '首': 'POS_NECK',
  '首上': 'POS_NECK_UPPER',
  '首上1': 'POS_NECK_UPPER1',
  '腰': 'POS_WAIST',
  '腰上': 'POS_WAIST_UPPER1',
  '背中': 'POS_BACK',
  '背中上': 'POS_BACK_UPPER',
  '臀部': 'POS_HIPS',
  '肩甲骨': 'POS_SHDBLADE',
  '肩甲骨上': 'POS_SHDBLADE_UPPER',
  '肩甲骨下': 'POS_SHDBLADE_LOWER',
  
  // English patterns
  'shoulder': 'POS_SHOULDER',
  'shoulder_upper': 'POS_SHOULDER_UPPER',
  'shoulder_lower': 'POS_SHOULDER_LOWER',
  'neck': 'POS_NECK',
  'neck_upper': 'POS_NECK_UPPER',
  'back': 'POS_BACK',
  'back_upper': 'POS_BACK_UPPER',
  'waist': 'POS_WAIST',
  'waist_upper': 'POS_WAIST_UPPER1',
  'hips': 'POS_HIPS',
  'shoulder_blade': 'POS_SHDBLADE',
  'blade': 'POS_SHDBLADE',
  'uplimit': 'POS_UPLIMIT',
  'downlimit': 'POS_DOWNLIMIT',
};

// 3D Position mapping (Table 3 from OSIM doc)
const THREE_D_POSITION_MAP: Record<string, string> = {
  'FIT_POS0': 'FIT_POS0',
  'FIT_POS1': 'FIT_POS1',
  'FIT_POS2': 'FIT_POS2',
  'FIT_POS3': 'FIT_POS3',
  'FIT_POS4': 'FIT_POS4',
  'FIT_POS5': 'FIT_POS5',
  'FIT_POS6': 'FIT_POS6',
  'FIT_POS7': 'FIT_POS7',
  'FIT_POS8': 'FIT_POS8',
  'FIT_POS9': 'FIT_POS9',
  'FIT_POS10': 'FIT_POS10',
  'FIT_POS_SHD': 'FIT_POS_SHD',
};

// Roller Width and Direction mapping (Byte 5 logic from OSIM doc)
const ROLLER_WIDTH_DIRECTION_MAP: Record<string, string> = {
  // These are direct mappings for width codes, direction is handled separately
  'N': 'PN',
  'M': 'PM',
  'W': 'PW',
  'NARROW': 'PN',
  'MEDIUM': 'PM',
  'WIDE': 'PW'
};

// Air and Scent configuration mapping (Byte 6 & 7 logic from OSIM doc)
const AIR_SCENT_CONFIG_MAP: Record<string, number> = {
  'shoulder_right': 0x0080,
  'shoulder_left': 0x0040,
  'shoulder_both': 0x00C0,
  'arm_lower_right': 0x0020,
  'arm_lower_left': 0x0010,
  'arm_lower_both': 0x0030,
  'scent_right': 0x0008,
  'scent_left': 0x0004,
  'scent_both': 0x000C,
  // 'nohold' is a suffix, not a bitmask part here
};

// Foot Program mapping (Table 6 from OSIM doc)
const FOOT_PROGRAM_MAP: Record<string, string> = {
  'auto1': 'MODE_AUTO1',
  'auto2': 'MODE_AUTO2',
  'auto3': 'MODE_AUTO3',
  'auto4': 'MODE_AUTO4',
  'auto5': 'MODE_AUTO5',
  'auto6': 'MODE_AUTO6',
  'auto7': 'MODE_AUTO7',
  'auto8': 'MODE_AUTO8',
  'auto9': 'MODE_AUTO9',
};

// LED Color mapping (Table 7 from OSIM doc)
const LED_COLOR_MAP: Record<string, string> = {
  'red': 'COLOUR_RED',
  'blue': 'COLOUR_BLUE',
  'green': 'COLOUR_GREEN',
  'yellow': 'COLOUR_YELLOW',
  'purple': 'COLOUR_PURPLE',
  'cyan': 'COLOUR_CYAN',
  'white': 'COLOUR_WHITE',
  'colour339': 'COLOUR339',
  'colour340': 'COLOUR340',
};

// Seat Program mapping (Table 8 from OSIM doc)
const SEAT_PROGRAM_MAP: Record<string, string> = {
  'auto21': 'SM_AUTO21',
  'auto23': 'SM_AUTO23',
  'auto24': 'SM_AUTO24',
  'auto29': 'SM_AUTO29',
  'auto34': 'SM_AUTO34',
  'auto45': 'SM_AUTO45',
  'auto53': 'SM_AUTO53',
  'auto73': 'SM_AUTO73',
  'auto74': 'SM_AUTO74',
  'auto76': 'SM_AUTO76',
};

// Helper function to parse width values
function parseWidth(width: string | null): string {
  if (!width) return '0';
  
  const widthMap: Record<string, string> = {
    'N': 'PN',
    'M': 'PM',
    'W': 'PW',
    'NARROW': 'PN',
    'MEDIUM': 'PM',
    'WIDE': 'PW',
  };
  
  return widthMap[width.toUpperCase()] || '0';
}

// Helper function to parse 3D position
function parse3DPosition(position3d: (string | number | null)[] | string | null): [string, number] {
  if (!position3d || typeof position3d === 'string') return ['0', 0]; // Handle string case for comments etc.
  if (position3d.length < 1) return ['0', 0]; // Allow for single element if depth is implied or null

  const [pos, depthOrNull] = position3d;
  let posStr = '0';
  
  if (typeof pos === 'string') {
    // Handle formats like "N", "N+4", "N+6"
    if (pos.startsWith('N')) {
      posStr = pos.replace('N', 'PN');
    } else {
      posStr = pos;
    }
  } else if (typeof pos === 'number') {
    posStr = pos.toString();
  }
  
  const depthVal = typeof depthOrNull === 'number' ? depthOrNull : 0;
  
  return [posStr, depthVal];
}

// Helper function to detect operation type from description
function detectOperationType(description: string | null): string {
  if (!description) return 'M_KNEAD';
  
  const lowerDesc = description.toLowerCase();
  
  // Check for specific patterns
  for (const [pattern, operation] of Object.entries(OPERATION_MAP)) {
    if (lowerDesc.includes(pattern) || description.includes(pattern)) {
      return operation;
    }
  }
  
  // Default operations based on keywords
  if (lowerDesc.includes('subroutine')) {
    return '0'; // Subroutine call
  }
  if (lowerDesc.includes('timer')) {
    return '0';
  }
  if (lowerDesc.includes('width')) {
    return '0';
  }
  if (lowerDesc.includes('3d')) {
    return '0';
  }
  
  return 'M_KNEAD'; // Default
}

// Enhanced function to detect command type from description and step fields
function detectCommandType(description: string | null, step: ProgramStep): string {
  if (!description) return CPP_CMD_TIMER; // Default for empty description

  const lowerDesc = description.toLowerCase();

  // 1. Non-actionable control flow or specific comments
  if (/no\.\s*\d+\s*～\s*\d+\s*.*繰り返し/i.test(description)) {
    return CPP_CMD_AS_COMMENT;
  }
  if (description.trim().startsWith("//") || description.trim().startsWith("Comment:")) {
    return CPP_CMD_AS_COMMENT;
  }

  // 2. LED Color commands (infer from description keywords ONLY if step.light is not already handling it)
  // This check is primarily for descriptions that might *only* specify LED action.
  // Explicit step.light is handled by generateOperationObjectsFromSteps directly.
  if (!step.light && (lowerDesc.includes('led') || lowerDesc.includes('color') || lowerDesc.includes('colour'))) {
    return CPP_CMD_LED_COLOR;
  }

  // 3. Seat program commands (infer from description keywords ONLY if step.seat_program is not already handling it)
  // Explicit step.seat_program is handled by generateOperationObjectsFromSteps directly.
  if (!step.seat_program && (lowerDesc.includes('seat program start') || lowerDesc.includes('seatm_start') || (lowerDesc.includes('seat') && lowerDesc.includes('program')))) {
    return CPP_CMD_SEATM_START;
  }

  // 4. 3D Control commands
  if (lowerDesc.includes('threed_ctrl') || lowerDesc.includes('3d_ctrl')) {
    if (lowerDesc.includes('enable')) return CPP_CMD_THREED_CTRL_ENABLE;
    if (lowerDesc.includes('disable')) return CPP_CMD_THREED_CTRL_DISABLE;
  }

  // 5. Cooldown commands
  if (lowerDesc.includes('cooldown')) {
    return CPP_CMD_COOLDOWN;
  }

  // 6. Vibration commands
  if (lowerDesc.includes('vib_set') || lowerDesc.includes('vibration set')) {
    return CPP_CMD_VIB_SET;
  }
  if (lowerDesc.includes('vib_time') || lowerDesc.includes('vibration time')) {
    return CPP_CMD_VIB_TIME;
  }

  // 7. Position movement commands (up/down)
  if (lowerDesc.includes('pos_up') || (lowerDesc.includes('up') && lowerDesc.includes('turn'))) {
    return CPP_CMD_POS_UP;
  }
  if (lowerDesc.includes('pos_down') || (lowerDesc.includes('down') && lowerDesc.includes('turn'))) {
    return CPP_CMD_POS_DOWN;
  }

  // 8. 3D position commands (enhanced detection)
  if (lowerDesc.includes('3d') || lowerDesc.includes('fit_pos')) {
    if (lowerDesc.includes('up')) return CPP_CMD_POS_3D_UP;
    if (lowerDesc.includes('down')) return CPP_CMD_POS_3D_DOWN;
    return CPP_CMD_POS_3D; // Default 3D command
  }

  // 9. Width commands
  if (lowerDesc.includes('width') || lowerDesc.includes('幅')) {
    return CPP_CMD_WIDTH_SET;
  }

  // 10. Rotation commands (enhanced detection)
  if (lowerDesc.includes('rotation') || (lowerDesc.includes('cyc') && !lowerDesc.includes('timer'))) {
    return CPP_CMD_ROTATION;
  }

  // 11. Timer commands (enhanced detection)
  if (lowerDesc.includes('timer') || lowerDesc.includes('hold') || 
      /\d+\s*(s|sec|seconds)/i.test(description) || 
      (lowerDesc.includes('cyc') && lowerDesc.includes('timer'))) {
    return CPP_CMD_TIMER;
  }

  // 12. Roller Actions (Primary Check) - enhanced pattern matching
  for (const pattern of Object.keys(OPERATION_MAP)) {
    if (lowerDesc.includes(pattern.toLowerCase()) || description.includes(pattern)) {
      return CPP_CMD_POS;
    }
  }

  // 13. Position commands (enhanced detection)
  if (lowerDesc.includes('まで') || lowerDesc.includes(' to ') || lowerDesc.includes('move')) {
    for (const key of Object.keys(POSITION_MAP)) {
      if (description.toLowerCase().includes(key.toLowerCase()) || description.includes(key)) {
        return CPP_CMD_POS;
      }
    }
  }

  // 14. Subroutine calls
  if (lowerDesc.includes('subroutine')) {
    return CPP_CMD_COMMENT;
  }

  // Default for unrecognized actions
  return CPP_CMD_TIMER;
}

// Helper function to parse position from description
function parsePositionFromDescription(description: string): string {
  if (!description) return '0'; // Added check for null/undefined description
  for (const [key, value] of Object.entries(POSITION_MAP)) {
    if (description.includes(key)) {
      return value;
    }
  }
  return '0';
}

// Enhanced function to parse air action codes using the new mapping
function parseAirAction(airActionDesc: string | null, scentDesc: string | null = null): string {
  if (!airActionDesc && !scentDesc) return '0X0000';
  
  const lowerAirAction = airActionDesc?.toLowerCase() || '';
  const lowerScent = scentDesc?.toLowerCase() || '';
  let code = 0x0000;

  // Parse air action
  if (lowerAirAction.includes('shoulder') || lowerAirAction.includes('肩')) {
    if (lowerAirAction.includes('left') || lowerAirAction.includes('左')) code |= AIR_SCENT_CONFIG_MAP['shoulder_left'];
    else if (lowerAirAction.includes('right') || lowerAirAction.includes('右')) code |= AIR_SCENT_CONFIG_MAP['shoulder_right'];
    else code |= AIR_SCENT_CONFIG_MAP['shoulder_both']; // Default to both if not specified
  }
  
  if (lowerAirAction.includes('arm') || lowerAirAction.includes('手腕')) {
    if (lowerAirAction.includes('left') || lowerAirAction.includes('左')) code |= AIR_SCENT_CONFIG_MAP['arm_lower_left'];
    else if (lowerAirAction.includes('right') || lowerAirAction.includes('右')) code |= AIR_SCENT_CONFIG_MAP['arm_lower_right'];
    else code |= AIR_SCENT_CONFIG_MAP['arm_lower_both']; // Default to both
  }

  // Parse scent
  if (lowerScent && lowerScent !== 'none') {
    if (lowerScent.includes('left') || lowerScent.includes('左')) code |= AIR_SCENT_CONFIG_MAP['scent_left'];
    else if (lowerScent.includes('right') || lowerScent.includes('右')) code |= AIR_SCENT_CONFIG_MAP['scent_right'];
    else code |= AIR_SCENT_CONFIG_MAP['scent_both']; // Default to both if specified generally
  }

  const hexCode = `0X${code.toString(16).toUpperCase().padStart(4, '0')}`;
  
  // Add NOHOLD suffix if there's any air/scent action, unless "hold" is specified
  if (code !== 0x0000) {
    if (lowerAirAction.includes('hold') || lowerScent.includes('hold')) {
      return hexCode; // Do not add +NOHOLD if "hold" is present
    }
    return `${hexCode}+NOHOLD`;
  }
  return hexCode;
}

// --- C++ Command Constants (Table 1 from OSIM doc) ---
const CPP_CMD_TRACK_SELECT = 'TRACK_SELECT';
const CPP_CMD_ASI_START = 'ASI_START';
const CPP_CMD_PG_END = 'PG_END';
const CPP_CMD_TIMER = 'TIMER';
const CPP_CMD_POS = 'POS';
const CPP_CMD_POS_UP = 'POS_UP';
const CPP_CMD_POS_DOWN = 'POS_DOWN';
const CPP_CMD_POS_3D_UP = 'POS_3D_UP';
const CPP_CMD_POS_3D_DOWN = 'POS_3D_DOWN';
const CPP_CMD_POS_3D = 'POS_3D';
const CPP_CMD_WIDTH_SET = 'WIDTH_SET';
const CPP_CMD_ROTATION = 'ROTATION';
const CPP_CMD_SEATM_START = 'SEATM_START';
const CPP_CMD_LED_COLOR = 'LED_COLOR';
const CPP_CMD_THREED_CTRL_ENABLE = 'THREED_CTRL_ENABLE';
const CPP_CMD_THREED_CTRL_DISABLE = 'THREED_CTRL_DISABLE';
const CPP_CMD_COOLDOWN = 'COOLDOWN';
const CPP_CMD_VIB_SET = 'VIB_SET';
const CPP_CMD_VIB_TIME = 'VIB_TIME';
const CPP_CMD_COMMENT = 'COMMENT'; // For subroutine calls in descriptions
const CPP_CMD_AS_COMMENT = 'AS_COMMENT'; // For steps to be treated purely as comments

const CPP_DEFAULT_PARAM = '0';
const CPP_DEFAULT_HEX_PARAM = '0X0000';

// --- End Constants ---

interface OperationObject {
  command: string;
  param1: string;
  param2: string;
  param3: string;
  param4: string;
  param5Hex: string;
  param6Mode: string;
  comment?: string; 
  isCommentLine?: boolean; 
  rawCommentText?: string; 
}

// Enhanced byte parsing functions for T_OPERATION structure

// Parse Byte 2 value (Position/Time/Cycles) based on command and step
function parseByte2Value(command: string, step: ProgramStep): string {
  if (command === CPP_CMD_POS || command === CPP_CMD_POS_UP || command === CPP_CMD_POS_DOWN) {
    const position = parsePositionFromDescription(step.roller_action_description || '');
    return position !== '0' ? position : CPP_DEFAULT_PARAM;
  }
  
  if (command === CPP_CMD_POS_3D || command === CPP_CMD_POS_3D_UP || command === CPP_CMD_POS_3D_DOWN) {
    // Check for explicit 3D position values like FIT_POS5
    const fitPosMatch = step.roller_action_description?.match(/FIT_POS(\d+|SHD)/i);
    if (fitPosMatch) {
      return `FIT_POS${fitPosMatch[1].toUpperCase()}`;
    }
    
    // Check for numeric 3D depth values
    const depthMatch = step.roller_action_description?.match(/3D\s*[:=]\s*(\d+)/i);
    if (depthMatch) {
      return depthMatch[1];
    }
    
    // Extract numeric value for 3D movement
    const numericMatch = step.roller_action_description?.match(/(\d+)/);
    return numericMatch ? numericMatch[1] : '0';
  }
  
  if (command === CPP_CMD_TIMER) {
    const timerMatch = step.roller_action_description?.match(/(\d+)\s*(s|sec|cyc)/i);
    return timerMatch ? timerMatch[1] : '1';
  }
  
  if (command === CPP_CMD_ROTATION) {
    const rotationMatch = step.roller_action_description?.match(/(\d+)\s*cyc/i);
    return rotationMatch ? rotationMatch[1] : '1';
  }
  
  if (command === CPP_CMD_POS_UP || command === CPP_CMD_POS_DOWN) {
    const turnMatch = step.roller_action_description?.match(/(\d+)\s*turn/i);
    return turnMatch ? turnMatch[1] : '1';
  }
  
  return CPP_DEFAULT_PARAM;
}

// Parse Byte 3 value (Kneading Speed / 3D Adjust)
function parseByte3Value(command: string, step: ProgramStep): string {
  if (typeof step.kneading_speed === 'number') {
    return step.kneading_speed.toString();
  }
  
  if (typeof step.kneading_speed === 'string') {
    const speedMatch = step.kneading_speed.match(/(\d+)/);
    if (speedMatch) return speedMatch[1];
  }
  
  // For 3D commands, check for adjustment values
  if (command.includes('3D')) {
    const adjustMatch = step.roller_action_description?.match(/position\s*[-+]\s*(\d+)/i);
    if (adjustMatch) return adjustMatch[1];
  }
  
  return CPP_DEFAULT_PARAM;
}

// Parse Byte 4 value (Tapping Speed / OSI Speed / Vib Strength)
function parseByte4Value(command: string, step: ProgramStep): string {
  if (typeof step.tapping_speed === 'number') {
    return step.tapping_speed.toString();
  }
  
  if (typeof step.tapping_speed === 'string') {
    const speedMatch = step.tapping_speed.match(/(\d+)/);
    if (speedMatch) return speedMatch[1];
  }
  
  // For vibration commands, extract strength
  if (command === CPP_CMD_VIB_SET || command === CPP_CMD_VIB_TIME) {
    const vibMatch = step.roller_action_description?.match(/strength\s*[:=]\s*(\d+)/i);
    if (vibMatch) return vibMatch[1];
  }
  
  return CPP_DEFAULT_PARAM;
}

// Parse Byte 5 value (Width + Direction)
function parseByte5Value(command: string, step: ProgramStep): string {
  const description = step.roller_action_description?.toLowerCase() || '';
  const stepWidth = step.width?.toUpperCase();
  
  let directionPrefix = '';
  // Determine direction from description for POS commands primarily
  if (command === CPP_CMD_POS || command === CPP_CMD_POS_UP || command === CPP_CMD_POS_DOWN || command === CPP_CMD_TIMER) {
    if (description.includes('up') || description.includes('もみ上げ') || description.includes('rewind')) {
      directionPrefix = 'REW+';
    }
    // If not 'up' or 'もみ上げ', it's implicitly 'down' or directionless for width setting, so no prefix.
  }
  // For ROTATION, OSIM examples suggest no REW+ prefix, just the width code.
  // For WIDTH_SET, direction is not applicable to the width code itself.

  const mappedWidth = ROLLER_WIDTH_DIRECTION_MAP[stepWidth || ''] || CPP_DEFAULT_PARAM; // Default to '0' if no width

  if (mappedWidth === CPP_DEFAULT_PARAM) {
    return CPP_DEFAULT_PARAM; // If width is '0', direction is irrelevant for this param
  }

  // Only add direction prefix if it's meaningful (i.e., not for ROTATION if it doesn't take REW+)
  if (command === CPP_CMD_ROTATION) {
    return mappedWidth; // ROTATION typically just uses PN, PM, PW
  }
  
  return directionPrefix + mappedWidth;
}

// Parse Byte 6 & 7 value (Airbag & Scent) - enhanced version
function parseByte6And7Value(step: ProgramStep): string {
  return parseAirAction(step.air_action_description, step.scent);
}

// Parse Byte 8 value (Movement/Program/Color)
function parseByte8Value(command: string, step: ProgramStep, overallSettings?: OverallSettings): string {
  if (command === CPP_CMD_LED_COLOR) {
    if (step.light) {
      const colorKey = step.light.toLowerCase();
      // Attempt to match specific color names first, then generic COLOURXXX
      if (LED_COLOR_MAP[colorKey]) return LED_COLOR_MAP[colorKey];
      const colorNumMatch = step.light.match(/COLOUR(\d+)/i);
      if (colorNumMatch) return `COLOUR${colorNumMatch[1]}`;
      return step.light; // Fallback to raw value if no map or pattern match
    }
    const descColorMatch = step.roller_action_description?.match(/COLOUR(\d+)/i);
    if (descColorMatch) return `COLOUR${descColorMatch[1]}`;
    return CPP_DEFAULT_PARAM;
  }
  
  if (command === CPP_CMD_ASI_START) {
    if (overallSettings?.leg_program) {
      const legProgKey = overallSettings.leg_program.toLowerCase().replace('mode_',''); // e.g. "auto4"
      if (FOOT_PROGRAM_MAP[legProgKey]) return FOOT_PROGRAM_MAP[legProgKey];
    }
    // Fallback: Extract foot program mode from description if not in overallSettings
    const modeMatch = step.roller_action_description?.match(/MODE_AUTO(\d+)/i);
    if (modeMatch && FOOT_PROGRAM_MAP[`auto${modeMatch[1]}`]) {
      return `MODE_AUTO${modeMatch[1]}`;
    }
    return FOOT_PROGRAM_MAP['auto1'] || 'MODE_AUTO1'; // Default if nothing found
  }
  
  if (command === CPP_CMD_SEATM_START) {
    if (step.seat_program) {
      let normalizedKey = step.seat_program.toLowerCase();
      // Attempt to extract just number if common prefixes are present
      const zlbMatch = normalizedKey.match(/zlb\s*#?(\d+)/);
      if (zlbMatch && zlbMatch[1]) {
        normalizedKey = `auto${zlbMatch[1]}`; // e.g., "zlb #53" -> "auto53"
      } else {
        normalizedKey = normalizedKey.replace(/sm_|auto|#|\s/g, ''); // General cleanup
        if (/^\d+$/.test(normalizedKey)) { // If it's just a number after cleanup
          normalizedKey = `auto${normalizedKey}`;
        }
      }
      
      if (SEAT_PROGRAM_MAP[normalizedKey]) {
        return SEAT_PROGRAM_MAP[normalizedKey];
      }
      
      // Fallback: construct SM_XXX, ensuring no double SM_ and removing #
      const upperSeatProg = step.seat_program.toUpperCase().replace(/#/g, '');
      if (upperSeatProg.startsWith('SM_')) {
        return upperSeatProg;
      }
      // If it was "ZLB 53", becomes "SM_ZLB 53"
      return `SM_${upperSeatProg.replace(/\s/g, '')}`; // Remove spaces too
    }
    // If command is SEATM_START but step.seat_program is not set, try to parse from description
    const descSeatMatch = step.roller_action_description?.match(/SM_AUTO(\d+)/i);
    if (descSeatMatch && SEAT_PROGRAM_MAP[`auto${descSeatMatch[1]}`]) {
      return `SM_AUTO${descSeatMatch[1]}`;
    }
    return SEAT_PROGRAM_MAP['auto23'] || 'SM_AUTO23'; // A common default
  }
  
  // For roller action commands, use operation detection
  if (command === CPP_CMD_POS || command === CPP_CMD_TIMER || command === CPP_CMD_ROTATION || command === CPP_CMD_POS_UP || command === CPP_CMD_POS_DOWN || command === CPP_CMD_POS_3D || command === CPP_CMD_POS_3D_UP || command === CPP_CMD_POS_3D_DOWN) {
    return detectOperationType(step.roller_action_description);
  }
  
  return CPP_DEFAULT_PARAM;
}

function generateOperationObjectsFromSteps(
  steps: ProgramStep[],
  subRoutinesMap: Map<string, Subroutine>,
  isSubroutineCall: boolean = false
): OperationObject[] {
  const operationObjects: OperationObject[] = [];

  steps.forEach((step, index) => {
    if (!step) return;

    const stepNumber = step.step_number || `gen_idx_${index}`;
    const overallSettingsFromMap = (isSubroutineCall ? undefined : subRoutinesMap.get('__overallSettings__') as any)?.overallSettings;

    if (step.type === 'comment') {
      operationObjects.push({
        isCommentLine: true,
        rawCommentText: `// Comment: ${step.roller_action_description || step.notes || ''}`,
        command: '', param1: '', param2: '', param3: '', param4: '', param5Hex: '', param6Mode: ''
      });
      return;
    }
    if (step.type === 'program_note') {
      operationObjects.push({
        isCommentLine: true,
        rawCommentText: `// Program Note: ${step.description || step.roller_action_description || ''}`,
        command: '', param1: '', param2: '', param3: '', param4: '', param5Hex: '', param6Mode: ''
      });
      return;
    }

    if (step.type === 'subroutine' && step.subroutine_id) {
      const subroutine = subRoutinesMap.get(step.subroutine_id);
      // ... (subroutine handling logic remains the same as original)
      if (subroutine && subroutine.steps) {
        operationObjects.push({
          isCommentLine: true,
          rawCommentText: `// Subroutine Start: ${subroutine.name} (ID: ${step.subroutine_id}) - Step ${stepNumber}`,
          command: '', param1: '', param2: '', param3: '', param4: '', param5Hex: '', param6Mode: ''
        });
        // Pass the original subRoutinesMap, not the extended one, for recursive calls to avoid issues with __overallSettings__
        const subroutineOps = generateOperationObjectsFromSteps(subroutine.steps, subRoutinesMap, true);
        operationObjects.push(...subroutineOps);
        operationObjects.push({
          isCommentLine: true,
          rawCommentText: `// Subroutine End: ${subroutine.name} (ID: ${step.subroutine_id})`,
          command: '', param1: '', param2: '', param3: '', param4: '', param5Hex: '', param6Mode: ''
        });
      } else if (subroutine && (!subroutine.steps || subroutine.steps.length === 0)) {
        operationObjects.push({
          isCommentLine: true,
          rawCommentText: `// Note: Subroutine ${subroutine.name || step.subroutine_id} (ID: ${step.subroutine_id}) is defined but has no steps. - Step ${stepNumber}`,
          command: '', param1: '', param2: '', param3: '', param4: '', param5Hex: '', param6Mode: ''
        });
      } else {
        operationObjects.push({
          isCommentLine: true,
          rawCommentText: `// Error: Subroutine ID ${step.subroutine_id} not found in map or subroutine is invalid - Step ${stepNumber}`,
          command: '', param1: '', param2: '', param3: '', param4: '', param5Hex: '', param6Mode: ''
        });
      }
      return;
    }

    // Logic for multi-line generation from a single ProgramStep
    const localOps: OperationObject[] = [];
    const stepNumberCommentBase = `// Step ${stepNumber}`;
    let mainActionDescription = step.roller_action_description;
    let mainActionProcessed = false;

    // 1. Handle SEATM_START if step.seat_program is present
    if (step.seat_program) {
      const seatCommand = CPP_CMD_SEATM_START;
      const seatP6 = parseByte8Value(seatCommand, step, overallSettingsFromMap);
      localOps.push({
        command: seatCommand,
        param1: CPP_DEFAULT_PARAM, param2: CPP_DEFAULT_PARAM, param3: CPP_DEFAULT_PARAM,
        param4: CPP_DEFAULT_PARAM, param5Hex: CPP_DEFAULT_HEX_PARAM, param6Mode: seatP6,
        comment: `${stepNumberCommentBase} (Seat Program: ${step.seat_program})`
      });
    }

    // 2. Handle LED_COLOR if step.light is present
    if (step.light) {
      const ledCommand = CPP_CMD_LED_COLOR;
      const ledP6 = parseByte8Value(ledCommand, step, overallSettingsFromMap);
      localOps.push({
        command: ledCommand,
        param1: CPP_DEFAULT_PARAM, param2: CPP_DEFAULT_PARAM, param3: CPP_DEFAULT_PARAM,
        param4: CPP_DEFAULT_PARAM, param5Hex: CPP_DEFAULT_HEX_PARAM, param6Mode: ledP6,
        comment: `${stepNumberCommentBase} (LED: ${step.light})`
      });
    }
    
    // 3. Handle the main action from roller_action_description
    if (mainActionDescription && mainActionDescription.trim() !== '') {
      const mainCommand = detectCommandType(mainActionDescription, step);

      const alreadyHandledByField = (mainCommand === CPP_CMD_SEATM_START && step.seat_program) ||
                                   (mainCommand === CPP_CMD_LED_COLOR && step.light);

      if (mainCommand !== CPP_CMD_AS_COMMENT && mainCommand !== CPP_CMD_COMMENT && !alreadyHandledByField) {
        const p1 = parseByte2Value(mainCommand, step);
        const p2 = parseByte3Value(mainCommand, step);
        const p3 = parseByte4Value(mainCommand, step);
        const p4 = parseByte5Value(mainCommand, step);
        const p5Hex = parseByte6And7Value(step);
        const p6Mode = parseByte8Value(mainCommand, step, overallSettingsFromMap);

        const descriptionComment = mainActionDescription.replace(/\n/g, ' ');
        localOps.push({
          command: mainCommand, param1: p1, param2: p2, param3: p3, param4: p4,
          param5Hex: p5Hex, param6Mode: p6Mode,
          comment: `${stepNumberCommentBase}: ${descriptionComment}`
        });
        mainActionProcessed = true;
      } else if (mainCommand === CPP_CMD_AS_COMMENT || mainCommand === CPP_CMD_COMMENT) {
        if (localOps.length === 0) { // Only add as comment if no other ops were generated for this step
          localOps.push({
            isCommentLine: true,
            rawCommentText: `// ${mainActionDescription || step.notes || ''} (${stepNumberCommentBase})`,
            command: '', param1: '', param2: '', param3: '', param4: '', param5Hex: '', param6Mode: ''
          });
          mainActionProcessed = true;
        }
      }
    }

    // If localOps is still empty, it means neither fields (seat, light) nor roller_action_description yielded an operation.
    // This could be an action step that only has notes, or an empty action step.
    if (localOps.length === 0 && !mainActionProcessed && step.type === 'action') {
      if (step.notes) {
        localOps.push({
          isCommentLine: true,
          rawCommentText: `// Note from Step ${stepNumber}: ${step.notes}`,
          command: '', param1: '', param2: '', param3: '', param4: '', param5Hex: '', param6Mode: ''
        });
      } else if (!step.roller_action_description && !step.light && !step.seat_program) {
        // Truly empty action step, might warrant a warning or be skipped.
        // For now, let's add a warning comment.
        localOps.push({
          isCommentLine: true,
          rawCommentText: `// Warning: Empty action step ${stepNumber}.`,
          command: '', param1: '', param2: '', param3: '', param4: '', param5Hex: '', param6Mode: ''
        });
      }
    }
    
    operationObjects.push(...localOps);
  });
  return operationObjects;
}

export function convertStepsToCpp(
  steps: ProgramStep[],
  programName: string = 'Auto Program',
  programIndex: number = 1,
  subRoutinesMap: Map<string, Subroutine>,
  overallSettings?: OverallSettings
): string {
  let cppHeader = `//***************************//****************************************************************************
// File Name  : download_course.cpp (Generated from: ${programName})
// Description: OSIM Massage Program
// Create Date: ${new Date().toISOString().split('T')[0]}
// Copy Right : OSIM
//****************************************************************************
#include "stdafx.h"
#include "common.h"

//------------------------------------------------------------------------------
// PG${programIndex}: ${programName}
//------------------------------------------------------------------------------
T_OPERATION osim_pg${programIndex}_course_tbl[] =
{\n`;

  const allOperationObjects: OperationObject[] = [];

  if (overallSettings) {
    const trackStrength = overallSettings.strength?.toUpperCase() === 'STRONG' ? 'STRONG' :
                         overallSettings.strength?.toUpperCase() === 'MEDIUM' ? 'MEDIUM' :
                         overallSettings.strength?.toUpperCase() === 'WEAK' ? 'WEAK' : 'LIGHT'; // Default to LIGHT
    allOperationObjects.push({
      command: CPP_CMD_TRACK_SELECT, param1: trackStrength, param2: CPP_DEFAULT_PARAM, param3: CPP_DEFAULT_PARAM, param4: CPP_DEFAULT_PARAM, param5Hex: CPP_DEFAULT_HEX_PARAM, param6Mode: CPP_DEFAULT_PARAM,
    });
    
    // Determine ASI_START mode based on overallSettings.leg_program or a default
    let asiStartMode = FOOT_PROGRAM_MAP['auto2'] || 'MODE_AUTO2'; // Default
    const LEG_PROGRAM_NAME_TO_KEY_MAP: Record<string, string> = {
        "circulation": "auto2",
        "relax": "auto3",
        "energies": "auto4", // Assuming 'Energies' maps to 'auto4'
        "detox": "auto6",   // Assuming 'Detox' maps to 'auto6'
        "sports recovery": "auto1",
        "night": "auto8",
        "sleep": "auto9"
    };

    if (overallSettings.leg_program) {
        const lowerLegProgram = overallSettings.leg_program.toLowerCase();
        const mappedKey = LEG_PROGRAM_NAME_TO_KEY_MAP[lowerLegProgram] || lowerLegProgram.replace('mode_','');
        
        if (FOOT_PROGRAM_MAP[mappedKey]) {
            asiStartMode = FOOT_PROGRAM_MAP[mappedKey];
        } else {
            // Fallback if direct key or mapped key isn't in FOOT_PROGRAM_MAP
            // Construct MODE_LEGPROGRAMNAME, ensuring it's a valid identifier if possible
            const fallbackMode = `MODE_${overallSettings.leg_program.toUpperCase().replace(/[\s#]/g, '_').replace('MODE_','')}`;
            asiStartMode = fallbackMode;
            // console.warn(`ASI_START: leg_program "${overallSettings.leg_program}" not found in FOOT_PROGRAM_MAP or LEG_PROGRAM_NAME_TO_KEY_MAP, using ${fallbackMode}`);
        }
    }

    allOperationObjects.push({
      command: CPP_CMD_ASI_START, param1: CPP_DEFAULT_PARAM, param2: CPP_DEFAULT_PARAM, param3: CPP_DEFAULT_PARAM, param4: CPP_DEFAULT_PARAM, param5Hex: CPP_DEFAULT_HEX_PARAM, param6Mode: asiStartMode,
    });
  }
  
  // Store overallSettings in the map if needed by parseByte8Value for main steps
  // This is a bit of a workaround to pass overallSettings down.
  const extendedSubRoutinesMap = new Map(subRoutinesMap);
  if (overallSettings) {
    extendedSubRoutinesMap.set('__overallSettings__', { name: '_overallSettings_', subroutineIdJson: '', steps: [], overallSettings } as any);
  }

  const mainStepObjects = generateOperationObjectsFromSteps(steps, extendedSubRoutinesMap, false);
  allOperationObjects.push(...mainStepObjects);

  // Add program end marker as specified in OSIM document (page 3, line 109)
  allOperationObjects.push({
    command: '0xff', param1: '0xff', param2: '0xff', param3: '0xff', param4: '0xff', param5Hex: '0xffff', param6Mode: '0xff',
  });
  
  let cppBody = allOperationObjects.map(op => {
    if (op.isCommentLine) {
      return op.rawCommentText || '';
    }
    return `\t${op.command.padEnd(18, ' ')},\t${op.param1.padEnd(20, ' ')},\t${op.param2.padEnd(3, ' ')},\t${op.param3.padEnd(3, ' ')},\t${op.param4.padEnd(3, ' ')},\t${op.param5Hex.padEnd(15, ' ')},\t${op.param6Mode.padEnd(20, ' ')}, ${op.comment || ''}`;
  }).join('\n');

  return `${cppHeader}${cppBody}\n};\n`;
}

// Function to parse the new JSON structure and call convertStepsToCpp
export function convertJsonToCpp(
  jsonDataString: string, 
  defaultProgramName: string = 'Auto Program', 
  defaultProgramIndex: number = 1
): string {
  try {
    const programData: DetailedProgramData = JSON.parse(jsonDataString);

    const programName = programData.program_title || defaultProgramName;
    const programIndex = programData.id || defaultProgramIndex; 

    const subRoutinesMap = new Map<string, Subroutine>();
    if (programData.subRoutines) {
      programData.subRoutines.forEach(parsedSub => {
        const validatedSub: Subroutine = {
          name: parsedSub.name || `Unnamed Subroutine ${parsedSub.subroutineIdJson}`,
          subroutineIdJson: parsedSub.subroutineIdJson,
          steps: Array.isArray(parsedSub.steps) ? parsedSub.steps : [],
        };
        if (validatedSub.subroutineIdJson) { // Ensure ID exists before adding
          subRoutinesMap.set(validatedSub.subroutineIdJson, validatedSub);
        }
      });
    }

    return convertStepsToCpp(
      programData.steps, 
      programName, 
      programIndex, 
      subRoutinesMap,
      programData.overallSettings
    );
  } catch (error) {
    console.error("Error parsing JSON data or converting to C++:", error);
    return `// Error during conversion: ${(error as Error).message}\n`;
  }
}
