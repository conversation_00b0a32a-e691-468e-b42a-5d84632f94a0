# Next auth https://next-auth.js.org/configuration/options
# Run this command to generate a a new NEXTAUTH_SECRET
# $ openssl rand -base64 32

NEXTAUTH_URL = http://localhost:3000
NEXTAUTH_SECRET=

# Go to github and setup the oauth configuration
# https://next-auth.js.org/providers/github#configuration
# https://github.com/settings/developers

GITHUB_ID = 
GITHUB_SECRET = 

# In github add these values
# Homepage URL : http://localhost:3000
# Authorization callback URL : http://localhost:3000/api/auth/callback/github