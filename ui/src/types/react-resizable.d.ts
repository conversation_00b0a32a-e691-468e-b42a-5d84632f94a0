declare module 'react-resizable' {
  import React from 'react';

  export interface ResizableProps {
    width: number;
    height: number;
    handle?: React.ReactNode;
    draggableOpts?: Record<string, any>;
    resizeHandles?: Array<'s' | 'w' | 'e' | 'n' | 'sw' | 'nw' | 'se' | 'ne'>;
    axis?: 'both' | 'x' | 'y' | 'none';
    minConstraints?: [number, number];
    maxConstraints?: [number, number];
    onResize?: (
      e: React.SyntheticEvent, 
      data: { 
        node: HTMLElement; 
        size: { width: number; height: number }; 
        handle: string 
      }
    ) => void;
    onResizeStart?: (
      e: React.SyntheticEvent, 
      data: { 
        node: HTMLElement; 
        size: { width: number; height: number }; 
        handle: string 
      }
    ) => void;
    onResizeStop?: (
      e: React.SyntheticEvent, 
      data: { 
        node: HTMLElement; 
        size: { width: number; height: number }; 
        handle: string 
      }
    ) => void;
    transformScale?: number;
    className?: string;
    style?: React.CSSProperties;
    children?: React.ReactNode;
  }
  
  export class Resizable extends React.Component<ResizableProps> {}
  
  export class ResizableBox extends React.Component<ResizableProps> {}
} 