/**
 * Program Expander Utility
 * 
 * Expands program steps by injecting subroutine steps into the main program flow.
 * This allows the body part classifier to analyze the complete execution flow
 * while maintaining markers for subroutine boundaries.
 */

export interface ExpandedStep {
  // Original step properties
  step_number: string;
  en_roller_action_description?: string;
  type: string;
  
  // Expansion metadata
  isSubroutineStart?: boolean;
  isSubroutineEnd?: boolean;
  subroutineName?: string;
  subroutineId?: string;
  originalStepNumber?: string; // For subroutine steps, this is their original step number
  parentStepNumber?: string; // For subroutine steps, this is the main program step that called them
  
  // Copy all other original properties
  [key: string]: any;
}

export interface SubroutineData {
  id: number;
  name: string;
  subroutine_id_json: string;
  steps: any[];
}

export interface ExpandedProgramResult {
  expandedSteps: ExpandedStep[];
  subroutineCount: number;
  totalExpandedSteps: number;
  subroutineMap: Map<string, SubroutineData>;
}

export interface ProgramSegment {
  type: 'body_part' | 'subroutine';
  id: string;
  name: string;
  steps: any[];
  stepCount: number;
  startStepNumber: string;
  endStepNumber: string;

  // For body part segments
  bodyPart?: string;
  displayName?: string;

  // For subroutine segments
  subroutineId?: string;
  subroutineName?: string;
  subroutineData?: SubroutineData;
  bodyPartAnalysis?: any; // Will contain the body part classification result for the subroutine
}

export interface ProgramSegmentationResult {
  segments: ProgramSegment[];
  subroutineCount: number;
  bodyPartCount: number;
  totalSteps: number;
  subroutineMap: Map<string, SubroutineData>;
}

/**
 * Segments a program into body part groups and subroutine groups
 */
export function segmentProgram(
  programSteps: any[],
  subroutines: SubroutineData[]
): ProgramSegmentationResult {
  if (!programSteps || programSteps.length === 0) {
    return {
      segments: [],
      subroutineCount: 0,
      bodyPartCount: 0,
      totalSteps: 0,
      subroutineMap: new Map()
    };
  }

  // Create a map for quick subroutine lookup
  const subroutineMap = new Map<string, SubroutineData>();
  subroutines.forEach(sub => {
    subroutineMap.set(sub.subroutine_id_json, sub);
    subroutineMap.set(sub.name, sub);
  });

  const segments: ProgramSegment[] = [];
  let subroutineCount = 0;
  let bodyPartCount = 0;
  let currentBodyPartSteps: any[] = [];
  let segmentIndex = 0;

  const flushBodyPartSteps = () => {
    if (currentBodyPartSteps.length > 0) {
      bodyPartCount++;
      segments.push({
        type: 'body_part',
        id: `body_part_${segmentIndex++}`,
        name: `Body Part Sequence ${bodyPartCount}`,
        steps: [...currentBodyPartSteps],
        stepCount: currentBodyPartSteps.length,
        startStepNumber: currentBodyPartSteps[0]?.step_number || '',
        endStepNumber: currentBodyPartSteps[currentBodyPartSteps.length - 1]?.step_number || ''
      });
      currentBodyPartSteps = [];
    }
  };

  programSteps.forEach((step) => {
    if (step.type === 'subroutine') {
      // Flush any accumulated body part steps
      flushBodyPartSteps();

      // Create subroutine segment
      const subroutine = findMatchingSubroutine(step, subroutineMap);

      if (subroutine && subroutine.steps && subroutine.steps.length > 0) {
        subroutineCount++;

        // Filter out comment steps for the subroutine
        const validSubroutineSteps = subroutine.steps.filter(
          subStep => subStep.type !== 'comment' && subStep.en_roller_action_description
        );

        segments.push({
          type: 'subroutine',
          id: `subroutine_${segmentIndex++}`,
          name: subroutine.name,
          steps: validSubroutineSteps,
          stepCount: validSubroutineSteps.length,
          startStepNumber: step.step_number,
          endStepNumber: step.step_number,
          subroutineId: subroutine.subroutine_id_json,
          subroutineName: subroutine.name,
          subroutineData: subroutine
        });
      } else {
        // Subroutine not found - create a placeholder segment
        segments.push({
          type: 'subroutine',
          id: `subroutine_unknown_${segmentIndex++}`,
          name: step.en_roller_action_description || 'Unknown Subroutine',
          steps: [step],
          stepCount: 1,
          startStepNumber: step.step_number,
          endStepNumber: step.step_number,
          subroutineName: step.en_roller_action_description || 'Unknown Subroutine'
        });
      }
    } else if (step.type !== 'comment') {
      // Regular action step - add to current body part sequence
      currentBodyPartSteps.push(step);
    }
  });

  // Flush any remaining body part steps
  flushBodyPartSteps();

  return {
    segments,
    subroutineCount,
    bodyPartCount,
    totalSteps: programSteps.length,
    subroutineMap
  };
}

/**
 * Finds a matching subroutine for a subroutine step
 */
function findMatchingSubroutine(
  step: any,
  subroutineMap: Map<string, SubroutineData>
): SubroutineData | undefined {
  const description = step.en_roller_action_description || '';

  // Try to find by exact name match first
  if (description && subroutineMap.has(description)) {
    return subroutineMap.get(description);
  }

  // Try to find by partial name match
  if (description) {
    for (const [key, subroutine] of Array.from(subroutineMap.entries())) {
      if (description.includes(key) || key.includes(description)) {
        return subroutine;
      }
    }
  }

  // Try to extract SUB_XXX pattern from description
  if (description) {
    const subIdMatch = description.match(/SUB_\d+/i);
    if (subIdMatch) {
      const subId = subIdMatch[0].toUpperCase();
      if (subroutineMap.has(subId)) {
        return subroutineMap.get(subId);
      }
    }
  }

  // For now, if no match found, return the first available subroutine as a fallback
  // This is a temporary solution until we have better subroutine mapping
  const subroutines = Array.from(subroutineMap.values());
  if (subroutines.length > 0) {
    // Try to find a reasonable match based on step number or return first one
    return subroutines[0];
  }

  return undefined;
}

/**
 * Checks if a step is part of a subroutine
 */
export function isSubroutineStep(step: ExpandedStep): boolean {
  return !!(step.subroutineName && !step.isSubroutineStart && !step.isSubroutineEnd);
}

/**
 * Checks if a step is a subroutine boundary (start or end)
 */
export function isSubroutineBoundary(step: ExpandedStep): boolean {
  return !!(step.isSubroutineStart || step.isSubroutineEnd);
}

/**
 * Gets subroutine information for a step
 */
export function getSubroutineInfo(step: ExpandedStep): {
  name: string;
  id: string;
  isStart: boolean;
  isEnd: boolean;
  isWithin: boolean;
} | null {
  if (!step.subroutineName) {
    return null;
  }

  return {
    name: step.subroutineName,
    id: step.subroutineId || '',
    isStart: !!step.isSubroutineStart,
    isEnd: !!step.isSubroutineEnd,
    isWithin: isSubroutineStep(step)
  };
}
