/**
 * Program Expander Utility
 * 
 * Expands program steps by injecting subroutine steps into the main program flow.
 * This allows the body part classifier to analyze the complete execution flow
 * while maintaining markers for subroutine boundaries.
 */

export interface ExpandedStep {
  // Original step properties
  step_number: string;
  en_roller_action_description?: string;
  type: string;
  
  // Expansion metadata
  isSubroutineStart?: boolean;
  isSubroutineEnd?: boolean;
  subroutineName?: string;
  subroutineId?: string;
  originalStepNumber?: string; // For subroutine steps, this is their original step number
  parentStepNumber?: string; // For subroutine steps, this is the main program step that called them
  
  // Copy all other original properties
  [key: string]: any;
}

export interface SubroutineData {
  id: number;
  name: string;
  subroutine_id_json: string;
  steps: any[];
}

export interface ExpandedProgramResult {
  expandedSteps: ExpandedStep[];
  subroutineCount: number;
  totalExpandedSteps: number;
  subroutineMap: Map<string, SubroutineData>;
}

/**
 * Expands a program's steps by replacing subroutine calls with their actual steps
 */
export function expandProgramSteps(
  programSteps: any[],
  subroutines: SubroutineData[]
): ExpandedProgramResult {
  if (!programSteps || programSteps.length === 0) {
    return {
      expandedSteps: [],
      subroutineCount: 0,
      totalExpandedSteps: 0,
      subroutineMap: new Map()
    };
  }

  // Create a map for quick subroutine lookup
  const subroutineMap = new Map<string, SubroutineData>();
  subroutines.forEach(sub => {
    // Map by both ID formats for flexibility
    subroutineMap.set(sub.subroutine_id_json, sub);
    subroutineMap.set(sub.name, sub);
  });

  const expandedSteps: ExpandedStep[] = [];
  let subroutineCount = 0;

  programSteps.forEach((step, index) => {
    if (step.type === 'subroutine') {
      // This is a subroutine call - expand it
      const subroutine = findMatchingSubroutine(step, subroutineMap);
      
      if (subroutine && subroutine.steps && subroutine.steps.length > 0) {
        subroutineCount++;
        
        // Add subroutine start marker
        expandedSteps.push({
          ...step,
          isSubroutineStart: true,
          subroutineName: subroutine.name,
          subroutineId: subroutine.subroutine_id_json,
          step_number: `${step.step_number}_start`
        });

        // Add all subroutine steps
        subroutine.steps.forEach((subStep, subIndex) => {
          // Skip comment steps and steps without descriptions
          if (subStep.type === 'comment' || !subStep.en_roller_action_description) {
            return;
          }

          expandedSteps.push({
            ...subStep,
            step_number: `${step.step_number}_sub_${subIndex + 1}`,
            originalStepNumber: subStep.step_number,
            parentStepNumber: step.step_number,
            subroutineName: subroutine.name,
            subroutineId: subroutine.subroutine_id_json
          });
        });

        // Add subroutine end marker
        expandedSteps.push({
          ...step,
          isSubroutineEnd: true,
          subroutineName: subroutine.name,
          subroutineId: subroutine.subroutine_id_json,
          step_number: `${step.step_number}_end`
        });
      } else {
        // Subroutine not found or has no steps - keep original step
        expandedSteps.push({
          ...step,
          isSubroutineStart: true,
          isSubroutineEnd: true,
          subroutineName: step.en_roller_action_description || 'Unknown Subroutine'
        });
      }
    } else {
      // Regular step - keep as is
      expandedSteps.push({ ...step });
    }
  });

  return {
    expandedSteps,
    subroutineCount,
    totalExpandedSteps: expandedSteps.length,
    subroutineMap
  };
}

/**
 * Finds a matching subroutine for a subroutine step
 */
function findMatchingSubroutine(
  step: any,
  subroutineMap: Map<string, SubroutineData>
): SubroutineData | undefined {
  const description = step.en_roller_action_description || '';
  
  // Try to find by exact name match first
  if (subroutineMap.has(description)) {
    return subroutineMap.get(description);
  }

  // Try to find by partial name match
  for (const [key, subroutine] of subroutineMap.entries()) {
    if (description.includes(key) || key.includes(description)) {
      return subroutine;
    }
  }

  // Try to extract SUB_XXX pattern from description
  const subIdMatch = description.match(/SUB_\d+/i);
  if (subIdMatch) {
    const subId = subIdMatch[0].toUpperCase();
    if (subroutineMap.has(subId)) {
      return subroutineMap.get(subId);
    }
  }

  return undefined;
}

/**
 * Checks if a step is part of a subroutine
 */
export function isSubroutineStep(step: ExpandedStep): boolean {
  return !!(step.subroutineName && !step.isSubroutineStart && !step.isSubroutineEnd);
}

/**
 * Checks if a step is a subroutine boundary (start or end)
 */
export function isSubroutineBoundary(step: ExpandedStep): boolean {
  return !!(step.isSubroutineStart || step.isSubroutineEnd);
}

/**
 * Gets subroutine information for a step
 */
export function getSubroutineInfo(step: ExpandedStep): {
  name: string;
  id: string;
  isStart: boolean;
  isEnd: boolean;
  isWithin: boolean;
} | null {
  if (!step.subroutineName) {
    return null;
  }

  return {
    name: step.subroutineName,
    id: step.subroutineId || '',
    isStart: !!step.isSubroutineStart,
    isEnd: !!step.isSubroutineEnd,
    isWithin: isSubroutineStep(step)
  };
}
