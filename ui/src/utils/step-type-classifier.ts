/**
 * Step Type Classification Utility
 *
 * Classifies program steps based on massage technique types
 * extracted from the en_roller_action_description field.
 */

export type StepType =
  | 'kneading'
  | 'tapping'
  | 'pressing'
  | 'rolling'
  | '3d_movement'
  | 'stretching'
  | 'holding'
  | 'circulation'
  | 'air_massage'
  | 'combination'
  | 'positioning'
  | 'unknown';

export interface StepTypeGroup {
  stepType: StepType;
  steps: any[];
  stepCount: number;
  startStepNumber: number;
  endStepNumber: number;
  displayName: string;
  color: string;
  icon: string;
}

export interface StepTypeClassificationResult {
  stepTypeGroups: StepTypeGroup[];
  totalSteps: number;
  totalGroups: number;
unclassifiedSteps: number;
  techniqueDistribution: Record<StepType, number>;
}

// Step type display configuration
export const STEP_TYPE_CONFIG: Record<StepType, {
  displayName: string;
  color: string;
  icon: string;
  description: string;
}> = {
  kneading: {
    displayName: 'Kneading',
    color: 'bg-blue-100 text-blue-800 border-blue-200',
    icon: '🤲',
    description: 'Circular kneading motions for muscle relaxation'
  },
  tapping: {
    displayName: 'Tapping',
    color: 'bg-green-100 text-green-800 border-green-200',
    icon: '👆',
    description: 'Rhythmic tapping movements for stimulation'
  },
  pressing: {
    displayName: 'Pressing',
    color: 'bg-purple-100 text-purple-800 border-purple-200',
    icon: '✋',
    description: 'Deep pressure point massage'
  },
  rolling: {
    displayName: 'Rolling',
    color: 'bg-orange-100 text-orange-800 border-orange-200',
    icon: '🌀',
    description: 'Smooth rolling motions along muscle lines'
  },
  '3d_movement': {
    displayName: '3D Movement',
    color: 'bg-cyan-100 text-cyan-800 border-cyan-200',
    icon: '🔄',
    description: 'Three-dimensional positioning and movement'
  },
  stretching: {
    displayName: 'Stretching',
    color: 'bg-yellow-100 text-yellow-800 border-yellow-200',
    icon: '↔️',
    description: 'Muscle stretching and extension'
  },
  holding: {
    displayName: 'Holding',
    color: 'bg-red-100 text-red-800 border-red-200',
    icon: '⏸️',
    description: 'Static pressure holds for deep relief'
  },
  circulation: {
    displayName: 'Circulation',
    color: 'bg-pink-100 text-pink-800 border-pink-200',
    icon: '💓',
    description: 'Blood circulation improvement techniques'
  },
  air_massage: {
    displayName: 'Air Massage',
    color: 'bg-indigo-100 text-indigo-800 border-indigo-200',
    icon: '💨',
    description: 'Air pressure massage for compression'
  },
  combination: {
    displayName: 'Combination',
    color: 'bg-emerald-100 text-emerald-800 border-emerald-200',
    icon: '🔀',
    description: 'Multiple techniques combined'
  },
  positioning: {
    displayName: 'Positioning',
    color: 'bg-amber-100 text-amber-800 border-amber-200',
    icon: '📍',
    description: 'Chair and roller positioning adjustments'
  },
  unknown: {
    displayName: 'Unknown/Other',
    color: 'bg-gray-100 text-gray-800 border-gray-200',
    icon: '❓',
    description: 'Unclassified or mixed techniques'
  }
};

// Classification rules ordered by specificity (most specific first)
const STEP_TYPE_RULES: Array<{
  test: (desc: string) => boolean;
  type: StepType;
}> = [
  // 3D Movement patterns
  {
    test: (desc) => desc.includes('3d') && (desc.includes('to') || desc.includes('with')),
    type: '3d_movement',
  },
  {
    test: (desc) => desc.includes('3d') && desc.includes('='),
    type: '3d_movement',
  },

  // Combination techniques (multiple actions in one step)
  {
    test: (desc) => (desc.includes('kneading') || desc.includes('knead')) &&
                    (desc.includes('beating') || desc.includes('tapping') || desc.includes('tap')),
    type: 'combination',
  },
  {
    test: (desc) => desc.includes('massage') && desc.includes('roll') && 
                    (desc.includes('and') || desc.includes('with') || desc.includes('plus')),
    type: 'combination',
  },
  {
    test: (desc) => desc.includes('push') && desc.includes('roll'),
    type: 'combination',
  },

  // Holding/Static pressure
  {
    test: (desc) => desc.includes('hold') || desc.includes('static'),
    type: 'holding',
  },
  {
    test: (desc) => /\d+s\s*(hold|pause|wait)/.test(desc),
    type: 'holding',
  },

  // Stretching
  {
    test: (desc) => desc.includes('stretch') || desc.includes('extend'),
    type: 'stretching',
  },
  {
    test: (desc) => desc.includes('protrud') || desc.includes('push') && desc.includes('outward'),
    type: 'stretching',
  },

  // Kneading patterns
  {
    test: (desc) => desc.includes('knead') || (desc.includes('massage') && !desc.includes('roll')),
    type: 'kneading',
  },
  {
    test: (desc) => desc.includes('sideburn'),
    type: 'kneading',
  },

  // Tapping patterns
  {
    test: (desc) => desc.includes('beat') || desc.includes('tap') || desc.includes('hitting'),
    type: 'tapping',
  },

  // Pressing
  {
    test: (desc) => desc.includes('press') && !desc.includes('roll'),
    type: 'pressing',
  },
  {
    test: (desc) => desc.includes('push') && !desc.includes('roll') && !desc.includes('outward'),
    type: 'pressing',
  },

  // Rolling
  {
    test: (desc) => desc.includes('roll') && !desc.includes('press') && !desc.includes('push'),
    type: 'rolling',
  },

  // Air massage rule removed as it cannot be determined from en_roller_action_description

  // Circulation
  {
    test: (desc) => desc.includes('circulation') || desc.includes('blood flow'),
    type: 'circulation',
  },

  // Positioning
  {
    test: (desc) => desc.includes('position') || desc.includes('adjust') || desc.includes('move to'),
    type: 'positioning',
  },
];

/**
 * Classifies a single step based on its en_roller_action_description
 */
export function classifyStepType(step: any): StepType {
  const description = step.en_roller_action_description || '';
  const desc = description.toLowerCase().trim();

  if (!description) {
    return 'unknown';
  }

  // Find matching rule
  const foundRule = STEP_TYPE_RULES.find(rule => rule.test(desc));
  return foundRule ? foundRule.type : 'unknown';
}

/**
 * Creates a step type group with proper numbering
 */
const createStepTypeGroup = (
  stepType: StepType,
  steps: any[],
  startStepNumber: number
): StepTypeGroup => {
  const config = STEP_TYPE_CONFIG[stepType];
  const endStepNumber = steps.length > 0
    ? (parseInt(steps[steps.length - 1].step_number) || startStepNumber + steps.length - 1)
    : startStepNumber;

  return {
    stepType,
    steps,
    stepCount: steps.length,
    startStepNumber,
    endStepNumber,
    displayName: config.displayName,
    color: config.color,
    icon: config.icon,
  };
};

/**
 * Groups program steps by step type, maintaining consecutive grouping logic
 */
export function groupStepsByType(steps: any[]): StepTypeClassificationResult {
  if (!steps || steps.length === 0) {
    return {
      stepTypeGroups: [],
      totalSteps: 0,
      totalGroups: 0,
      unclassifiedSteps: 0,
      techniqueDistribution: {} as Record<StepType, number>
    };
  }

  const groups: StepTypeGroup[] = [];
  let currentStepType: StepType | null = null;
  let currentGroup: any[] = [];
  let currentStartStep = 0;
  let unclassifiedCount = 0;
  const techniqueDistribution: Record<StepType, number> = {} as Record<StepType, number>;

  const validSteps = steps.filter(step => step.type !== 'comment');

  validSteps.forEach((step, index) => {
    const stepNumber = parseInt(step.step_number) || index + 1;
    const classifiedStepType = classifyStepType(step);

    // Count technique distribution
    techniqueDistribution[classifiedStepType] = (techniqueDistribution[classifiedStepType] || 0) + 1;

    if (classifiedStepType === 'unknown') {
      unclassifiedCount++;
    }

    // Group consecutive steps of the same type
    if (classifiedStepType !== currentStepType) {
      if (currentStepType && currentGroup.length > 0) {
        groups.push(createStepTypeGroup(currentStepType, currentGroup, currentStartStep));
      }
      currentStepType = classifiedStepType;
      currentGroup = [step];
      currentStartStep = stepNumber;
    } else {
      currentGroup.push(step);
    }
  });

  // Add the last remaining group
  if (currentStepType && currentGroup.length > 0) {
    groups.push(createStepTypeGroup(currentStepType, currentGroup, currentStartStep));
  }

  return {
    stepTypeGroups: groups,
    totalSteps: validSteps.length,
    totalGroups: groups.length,
    unclassifiedSteps: unclassifiedCount,
    techniqueDistribution
  };
}

/**
 * Get statistics about step type distribution
 */
export function getStepTypeStats(result: StepTypeClassificationResult) {
  const totalClassified = result.totalSteps - result.unclassifiedSteps;
  const classificationRate = result.totalSteps > 0
    ? ((totalClassified / result.totalSteps) * 100).toFixed(1)
    : '0';

  const percentageDistribution: Record<StepType, string> = {} as Record<StepType, string>;
  Object.entries(result.techniqueDistribution).forEach(([type, count]) => {
    const percentage = result.totalSteps > 0
      ? ((count / result.totalSteps) * 100).toFixed(1)
      : '0';
    percentageDistribution[type as StepType] = percentage;
  });

  return {
    totalClassified,
    classificationRate,
    techniqueDistribution: result.techniqueDistribution,
    percentageDistribution,
    mostCommonTechnique: Object.entries(result.techniqueDistribution)
      .sort(([,a], [,b]) => b - a)[0]?.[0] as StepType || 'unknown',
    techniqueVariety: Object.keys(result.techniqueDistribution).length
  };
}

/**
 * Get technique effectiveness score based on distribution
 */
export function getTechniqueEffectivenessScore(result: StepTypeClassificationResult): number {
  const { techniqueDistribution, totalSteps } = result;
  const techniques = Object.keys(techniqueDistribution).filter(t => t !== 'unknown');
  
  if (techniques.length === 0 || totalSteps === 0) return 0;

  // Calculate balance score (how evenly distributed the techniques are)
  const idealPercentage = 100 / techniques.length;
  const variance = techniques.reduce((acc, technique) => {
    const percentage = (techniqueDistribution[technique as StepType] / totalSteps) * 100;
    return acc + Math.pow(percentage - idealPercentage, 2);
  }, 0) / techniques.length;

  // Convert variance to score (lower variance = higher score)
  const balanceScore = Math.max(0, 100 - Math.sqrt(variance));

  // Penalty for too many unknown techniques
  const unknownPenalty = (techniqueDistribution.unknown || 0) / totalSteps * 50;

  return Math.max(0, Math.min(100, balanceScore - unknownPenalty));
}
