// Protecting routes with next-auth
// https://next-auth.js.org/configuration/nextjs#middleware
// https://nextjs.org/docs/app/building-your-application/routing/middleware
import { type NextRequest, NextResponse } from "next/server";

import NextAuth from 'next-auth';
// import authConfig from '@/lib/auth.config';

// const { auth } = NextAuth(authConfig);

export async function middleware(request: NextRequest) {
  // const response = NextResponse.next();
}

export const config = { matcher: ['/dashboard/:path*'] };