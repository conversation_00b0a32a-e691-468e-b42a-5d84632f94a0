@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --font-sans: 'Inter', system-ui, sans-serif;
    --font-heading: 'Inter', system-ui, sans-serif;
    --font-urban: 'Inter', system-ui, sans-serif;
    --font-geist: 'Inter', system-ui, sans-serif;
    --background: 20 66% 99%;
    --foreground: 20 51% 4%;
    --muted: 20 25% 86%;
    --muted-foreground: 20 7% 25%;
    --popover: 20 66% 98%;
    --popover-foreground: 20 51% 3%;
    --card: 20 66% 98%;
    --card-foreground: 20 51% 3%;
    --border: 20 15% 94%;
    --input: 20 15% 94%;
    --primary: 248 25% 84%;
    --primary-foreground: 20 48% 12%;
    --secondary: 20 12% 92%;
    --secondary-foreground: 20 12% 32%;
    --accent: 20 12% 92%;
    --accent-foreground: 20 12% 32%;
    --destructive: 11 80% 22%;
    --destructive-foreground: 11 80% 82%;
    --ring: 248 25% 84%;
    --radius: 0.5rem;
    --chart-1: 248 25% 84%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --sidebar-background: 20 66% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 20 25% 86%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 253 43% 3%;
    --foreground: 253 31% 98%;
    --muted: 253 7% 13%;
    --muted-foreground: 253 13% 63%;
    --popover: 253 43% 3%;
    --popover-foreground: 253 31% 98%;
    --card: 253 43% 4%;
    --card-foreground: 253 31% 99%;
    --border: 215 27.9% 16.9%;
    --input: 215 27.9% 16.9%;
    --primary: 253 91% 58%;
    --primary-foreground: 253 91% 98%;
    --secondary: 253 7% 9%;
    --secondary-foreground: 253 7% 69%;
    --accent: 253 13% 14%;
    --accent-foreground: 253 13% 74%;
    --destructive: 339.2 90.36% 51.18%;
    --destructive-foreground: 0 0% 100%;
    --ring: 253 91% 58%;
    --chart-1: 253 91% 58%;
    --chart-2: 253 13% 74%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
    --sidebar-background: 253 43% 4%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 253 91% 58%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 253 91% 58%%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply box-border bg-background text-foreground;
  }
}

/* Refined shadow utilities for better layering */
@layer utilities {
  .shadow-card-dark {
    box-shadow:
      0 1px 3px rgba(0, 0, 0, 0.2),
      0 1px 2px rgba(0, 0, 0, 0.4);
  }

  .shadow-elevation-dark {
    box-shadow:
      0 4px 6px rgba(0, 0, 0, 0.2),
      0 5px 15px rgba(0, 0, 0, 0.4);
  }

  /* New utility for subtle hover states */
  .hover-elevation-dark {
    transition: background-color 0.2s ease;
  }

  .hover-elevation-dark:hover {
    background-color: rgba(255, 255, 255, 0.03);
  }
}

@layer utilities {
  .min-h-screen {
    min-height: 100vh;
    /* Fallback */
    min-height: 100dvh;
  }

  .h-screen {
    height: 100vh;
    /* Fallback */
    height: 100dvh;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}

/* [data-amplify-authenticator] {
  --amplify-components-authenticator-router-box-shadow: 0 0 16px var(--amplify-colors-overlay-10);
  --amplify-components-authenticator-router-border-width: 0;
  --amplify-components-authenticator-form-padding: var(--amplify-space-medium) var(--amplify-space-xl) var(--amplify-space-xl);
  --amplify-components-button-primary-background-color: var(--amplify-colors-neutral-100);
  --amplify-components-fieldcontrol-focus-box-shadow: 0 0 0 2px var(--amplify-colors-purple-60);
  --amplify-components-tabs-item-active-border-color: var(--amplify-colors-neutral-100);
  --amplify-components-tabs-item-color: var(--amplify-colors-neutral-80);
  --amplify-components-tabs-item-active-color: var(--amplify-colors-purple-100);
  --amplify-components-button-link-color: var(--amplify-colors-purple-80);
} */


/* Amplify CSS Overrides */

.amplify-button--primary {
  background-color: hsl(20 25% 86% / .5) !important;
  color: hsl(20 48% 12%) !important;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
  transition: background-color 0.2s ease;
}

.amplify-button--primary:hover {
  background-color: hsl(248 25% 84% / 0.9);
}

.amplify-tabs__item--active {
  color: hsl(20 25% 86% / .5) !important;
  border-top-color: hsl(20 25% 86%) !important;
}

.amplify-tabs__item {
  color: hsl(0, 0%, 37%) !important;
  transition: color 0.2s ease;
}

.amplify-tabs__item:hover {
  color: hsl(20 25% 86%) !important;
}

.amplify-tabs__item:active {
  color: hsl(20 25% 86% / 0.7) !important;
}

.amplify-label {
  font-size: 16px !important;
  color: hsl(20 48% 12%) !important;
}

.amplify-button:hover {
  background-color: hsl(20 25% 86% / 0.5);
  border-color: hsl(20 25% 86% / 0.9);
}

.amplify-button:focus,
:focus {
  border-color: none !important;
  box-shadow: none !important;
  outline: none;
}

.amplify-button--link {
  color: hsl(20 48% 12%) !important;
}

.amplify-button--link:hover {
  color: hsl(20 48% 12%) !important;
}
.amplify-flex[data-amplify-copy="true"], .amplify-flex[data-amplify-copy-tooltip="true"] {
  color: hsl(20 48% 12%);
}

/* React-colorful styles */
.react-colorful {
  width: 100% !important;
  height: 160px !important;
}

.react-colorful__saturation {
  border-radius: 4px 4px 0 0;
  border-bottom: 1px solid #ccc;
}

.react-colorful__hue {
  height: 15px !important;
  margin-top: 10px;
  border-radius: 0 0 4px 4px;
}

.react-colorful__saturation-pointer,
.react-colorful__hue-pointer {
  width: 16px;
  height: 16px;
  border-width: 2px;
}

/* Dark mode adjustments */
.dark .react-colorful__saturation-pointer,
.dark .react-colorful__hue-pointer {
  border-color: #222;
}

/* Custom classes for our component */
.color-preview {
  border-radius: 4px;
  transition: all 0.2s ease;
}

.color-preview:hover {
  transform: scale(1.05);
}

.invalid-color {
  border-color: #f87171 !important;
  animation: shake 0.5s;
}

@keyframes shake {
  0% { transform: translateX(0); }
  25% { transform: translateX(-5px); }
  50% { transform: translateX(5px); }
  75% { transform: translateX(-5px); }
  100% { transform: translateX(0); }
}

/* Custom Slim Scrollbar Styles */
.slim-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: rgba(155, 155, 155, 0.5) transparent;
}

.slim-scrollbar::-webkit-scrollbar {
  width: 4px;
  height: 4px;
}

.slim-scrollbar::-webkit-scrollbar-track {
  background: transparent;
  margin: 3px;
}

.slim-scrollbar::-webkit-scrollbar-thumb {
  background-color: rgba(155, 155, 155, 0.5);
  border-radius: 20px;
  border: transparent;
}

.slim-scrollbar::-webkit-scrollbar-thumb:hover {
  background-color: rgba(155, 155, 155, 0.7);
}

/* Dark mode adjustments for scrollbar */
.dark .slim-scrollbar::-webkit-scrollbar-thumb {
  background-color: rgba(200, 200, 200, 0.4);
}

.dark .slim-scrollbar::-webkit-scrollbar-thumb:hover {
  background-color: rgba(200, 200, 200, 0.6);
}
