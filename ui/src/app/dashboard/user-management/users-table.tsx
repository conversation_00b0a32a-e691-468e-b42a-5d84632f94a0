'use client';
import { But<PERSON> } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  CardFooter
} from '@/components/ui/card';
import { LucideMessageCircleReply, PlusCircle } from 'lucide-react';
import { DataTable } from '@/components/ui/table/data-table';
import { ColumnDef } from '@tanstack/react-table';
import { useListAccounts, useUpdateAccountRoles } from '@/queries/users';
import { Application } from 'types';
import { redirect, useRouter } from 'next/navigation';
import { useAuthStore } from '@/stores/auth.store';
import { useEffect } from 'react';
import { ROUTES } from '@/constants/data';

export default function UsersTable() {
  const { data: users, isLoading, refetch } = useListAccounts();
  const updateRoles = useUpdateAccountRoles();

  const user = useAuthStore((state) => state.user);
  const router = useRouter();

  if (!user?.isAdmin) {
    redirect(ROUTES.OVERVIEW);
  }

  useEffect(() => {
    refetch();
  }, [refetch]);

  const columns: ColumnDef<any>[] = [
    {
      accessorKey: 'username',
      header: 'Username',
      cell: ({ row }) => (
        <div className='font-medium'>{row.getValue('username')}</div>
      )
    },
    {
      accessorKey: 'email',
      header: 'Email',
      cell: ({ row }) => (
        <div className='font-medium'>{row.getValue('email')}</div>
      )
    },
    {
      id: 'roles',
      header: 'Role',
      cell: ({ row }) => {
        const currentRole = row.original.roles?.[0] || 'User';
        return (
          <Select
            defaultValue={currentRole}
            disabled={updateRoles.isPending}
            onValueChange={(newRole) => {
              updateRoles.mutate(
                {
                  cognitoId: row.original.cognito_id,
                  roles: newRole === 'User' ? [] : [newRole]
                },
                {
                  onSuccess: () => {
                    refetch();
                  }
                }
              );
            }}
          >
            <SelectTrigger className='w-[180px]'>
              <SelectValue
                placeholder={
                  updateRoles.isPending ? 'Updating...' : 'Select role'
                }
              />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value='Admins'>Admin</SelectItem>
              <SelectItem value='AIEngineers'>AI Engineer</SelectItem>
              <SelectItem value='User'>User</SelectItem>
            </SelectContent>
          </Select>
        );
      },
      enableSorting: false,
      enableHiding: false
    }
  ];

  if (isLoading) {
    return (
      <Card className='w-full'>
        <CardHeader>
          <CardTitle>Users</CardTitle>
          <CardDescription>Loading users...</CardDescription>
        </CardHeader>
        <CardContent className='flex items-center justify-center p-8'>
          <div className='h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent'></div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className='w-full'>
      <CardHeader>
        <div className='flex items-center justify-between'>
          <div>
            <CardTitle>Users</CardTitle>
            <CardDescription>Manage user accounts and roles</CardDescription>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <DataTable data={users?.accounts || []} columns={columns} />
      </CardContent>
      <CardFooter>
        <div className='flex items-center justify-between'>
          <div className='text-sm text-muted-foreground'>
            Total users: {users?.total || 0}
          </div>
        </div>
      </CardFooter>
    </Card>
  );
}
