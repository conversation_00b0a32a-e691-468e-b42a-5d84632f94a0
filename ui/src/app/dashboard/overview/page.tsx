import PageContainer from '@/components/layout/page-container';
import { DashboardStatsCards } from '@/features/dashboard/components/dashboard-stats-cards';
import { ProgramsByCategoryCard } from '@/features/dashboard/components/programs-by-category-card';
import { ChatsPerDay<PERSON>hart } from '@/features/dashboard/components/chats-per-day-chart';
import { ProgramsPerProduct } from '@/features/dashboard/components/programs-per-product';
import { AiProgramsWidget } from '@/features/dashboard/components/AiProgramsWidget';

import QuickChatBar from '@/features/dashboard/components/quick-chat-bar';

export default function OverviewPage() {
  return (
    <PageContainer>
      <div className='flex flex-1 flex-col space-y-4'>
        <div className='flex items-center justify-between'>
          <h2 className='text-2xl font-bold tracking-tight'>
            Hi, Welcome back 👋
          </h2>
        </div>

        {/* QuickChatBar full width */}
        <QuickChatBar />

        {/* Display the new stats cards */}
        <div className='grid gap-4 md:grid-cols-2 lg:grid-cols-4'>
          <DashboardStatsCards />
        </div>

        {/* AiProgramsWidget and AreaGraph side by side (50/50 split) */}
        <div className='grid grid-cols-1 gap-4 lg:grid-cols-2'>
          <div className='flex h-full flex-col'>
            <div className='flex-1 overflow-hidden'>
              <AiProgramsWidget />
            </div>
          </div>
          <div className='flex h-full flex-col'>
            <div className='flex-1 overflow-hidden'>
              <ProgramsPerProduct />
            </div>
          </div>
        </div>

        <div className='grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-7'>
          {/* Row 1: New ChatsPerDayChart and ProgramsByCategoryCard */}
          <div className='col-span-full flex h-full flex-col lg:col-span-4'>
            <ChatsPerDayChart />
          </div>
          <div className='col-span-full flex h-full flex-col lg:col-span-3'>
            <ProgramsByCategoryCard />
          </div>
        </div>
      </div>
    </PageContainer>
  );
}
