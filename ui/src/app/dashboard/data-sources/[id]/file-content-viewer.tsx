import { useGetFileDetailFromSource } from '@/queries/source';
import { FileDetailResponse } from 'types';

interface FileContentViewerProps {
  sourceId: number;
  fileId: number;
}

export function FileContentViewer({
  sourceId,
  fileId
}: FileContentViewerProps) {
  const { data: fileDetail } = useGetFileDetailFromSource(sourceId, fileId);

  if (!fileDetail) {
    return (
      <div className='flex h-full items-center justify-center'>
        <p className='text-muted-foreground'>
          Select a file to view its content
        </p>
      </div>
    );
  }

  return (
    <div className='space-y-4'>
      <div className='border-b pb-4'>
        <h3 className='text-xl font-semibold'>Extracted Content</h3>
      </div>
      <div className='space-y-6'>
        {fileDetail.map((content: FileDetailResponse, index: number) => (
          <div key={index} className='rounded-lg border bg-card p-4'>
            <div className='mb-2 flex justify-between text-sm text-muted-foreground'>
              <span>Page {content.metadata.page_label}</span>
              <span>{content.metadata.file_name}</span>
            </div>
            <p className='whitespace-pre-wrap'>{content.page_content}</p>
          </div>
        ))}
      </div>
    </div>
  );
}
