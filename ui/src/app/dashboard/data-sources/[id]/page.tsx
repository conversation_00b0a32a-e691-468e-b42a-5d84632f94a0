'use client';

import {
  Drawer,
  DrawerContent,
  DrawerTitle,
  DrawerDescription
} from '@/components/ui/drawer';

import PageContainer from '@/components/layout/page-container';
import { useAuthStore } from '@/stores/auth.store';
import { redirect, useParams } from 'next/navigation';
import { ROUTES } from '@/constants/data';
import { useState, useCallback, useEffect } from 'react';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { LucideFileText } from 'lucide-react';
import { FileUploader } from '@/components/file-uploader';
import {
  useDeleteFileFromSource,
  useGetFilesFromSource,
  useUpdateFileToSource,
  useUpdateSource
} from '@/queries/source';
import { toast } from 'sonner';
import { FileContentViewer } from './file-content-viewer';
import { <PERSON><PERSON><PERSON><PERSON> } from '@/components/ui/scroll-area';
import { FileCard } from '@/components/file-card';

export default function DataSourceDetailPage() {
  const user = useAuthStore((state) => state.user);
  const [files, setFiles] = useState<File[]>([]);
  const [name, setName] = useState<string>('');
  const [description, setDescription] = useState<string>('');
  const [selectedFile, setSelectedFile] = useState<{ id: number } | null>(null);
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);
  const [saveLoading, setSaveLoading] = useState(false);

  const params = useParams();
  const sourceId = Number(params?.id) || 0;

  if (!user?.isAdmin) {
    redirect(ROUTES.OVERVIEW);
  }

  const { mutateAsync: updateSource, isPending: isSourceUpdating } =
    useUpdateSource();
  const { mutateAsync: updateFile, isPending: isFileUploading } =
    useUpdateFileToSource();
  const { mutate: deleteFileFromSource } = useDeleteFileFromSource();

  const [hasPendingFiles, setHasPendingFiles] = useState(false);

  const { data: sourceData, refetch: filesRefetch } = useGetFilesFromSource(
    sourceId,
    {
      refetchInterval: () => (hasPendingFiles ? 5000 : false)
    }
  );

  // Update hasPendingFiles when sourceData changes
  useEffect(() => {
    const pending =
      sourceData?.files?.some((file) => file.status === 'pending') ?? false;
    setHasPendingFiles(pending);
  }, [sourceData]);

  const handleUploadFiles = useCallback(
    async (sourceId: number) => {
      if (!files.length) return;

      const fileData = files.map((file) => ({
        name: file.name,
        file_type: file.type,
        content: file
      }));

      await updateFile({
        id: sourceId,
        file: {
          files: fileData
        }
      });

      setFiles([]); // Clear files after successful upload
      toast.success('Files uploaded successfully');
    },
    [files, updateFile]
  );

  const handleUpdateSource = useCallback(async () => {
    try {
      await updateSource({
        id: sourceId,
        payload: {
          name: name || sourceData?.source.name || '',
          description: description || sourceData?.source.description || '',
          s3_bucket_url: ''
        }
      });

      toast.success('Data source updated successfully');

      if (files.length > 0) {
        await handleUploadFiles(sourceId);
      }
    } catch (error) {
      toast.error(
        `Error: ${error instanceof Error ? error.message : 'Unknown error occurred'}`
      );
    }
  }, [
    sourceId,
    name,
    description,
    sourceData,
    files,
    updateSource,
    handleUploadFiles
  ]);

  const handleDeleteFile = useCallback(
    async (fileId: number) => {
      return deleteFileFromSource(
        {
          sourceId,
          fileId
        },
        {
          onSuccess: () => {
            toast.success('File deleted successfully');
            filesRefetch();
          },
          onError: (error: Error) => {
            toast.error(`Error deleting file: ${error}`);
          }
        }
      );
    },
    [deleteFileFromSource, filesRefetch, sourceId]
  );

  return (
    <>
      <PageContainer>
        <div className='space-y-6'>
          <div className='flex items-center justify-between border-b pb-4'>
            <h2 className='bg-gradient-to-r from-primary to-primary/60 bg-clip-text text-3xl font-bold tracking-tight'>
              {sourceData?.source.name}
            </h2>
          </div>

          <div className='w-full space-y-6 rounded-lg border bg-card p-6 shadow-sm'>
            <div className='space-y-3'>
              <Label htmlFor='name' className='text-lg font-medium'>
                Name
              </Label>
              <Input
                id='name'
                placeholder={sourceData?.source.name}
                onChange={(e) => setName(e.target.value)}
                className='transition-all hover:border-primary/50 focus:border-primary'
              />
            </div>

            <div className='space-y-3'>
              <Label htmlFor='description' className='text-lg font-medium'>
                Description
              </Label>
              <Textarea
                id='description'
                placeholder={sourceData?.source.description}
                onChange={(e) => setDescription(e.target.value)}
                className='transition-all hover:border-primary/50 focus:border-primary'
              />
            </div>

            <div className='space-y-3'>
              <Label className='text-lg font-medium'>Current Files</Label>
              <div className='text-sm text-muted-foreground'>
                {sourceData?.files?.length ? (
                  <div className='grid grid-cols-2 gap-4'>
                    {sourceData.files.map((file) => (
                      <FileCard
                        key={file.id}
                        file={file}
                        onFileClick={(file) => {
                          setSelectedFile(file);
                          setIsDrawerOpen(true);
                        }}
                        onDeleteFile={handleDeleteFile}
                        isSelected={selectedFile?.id === file.id}
                      />
                    ))}
                  </div>
                ) : (
                  <div className='flex flex-col items-center justify-center rounded-lg border bg-muted/30 py-12 text-center'>
                    <div className='mb-4 rounded-full bg-primary/10 p-4'>
                      <LucideFileText className='h-8 w-8 text-primary' />
                    </div>
                    <p className='text-base font-medium'>No files found</p>
                    <p className='mt-1 text-sm text-muted-foreground'>
                      Upload files using the section below
                    </p>
                  </div>
                )}
              </div>
            </div>

            <div className='space-y-3'>
              <Label className='text-lg font-medium'>Add More Files</Label>
              <FileUploader
                onFilesSelected={setFiles}
                maxFiles={10}
                value={files}
                onValueChange={setFiles}
              />
            </div>

            <div className='pt-4'>
              <Button
                disabled={isSourceUpdating || isFileUploading}
                variant='default'
                onClick={handleUpdateSource}
                className='w-full px-8 py-2.5 transition-all hover:shadow-md sm:w-auto'
              >
                Save Changes
              </Button>
            </div>
          </div>
        </div>
      </PageContainer>

      <Drawer open={isDrawerOpen} onOpenChange={setIsDrawerOpen}>
        <DrawerContent className='fixed inset-x-[unset] right-0 top-0 mt-0 h-full w-1/2 !rounded-r-none border-l bg-background'>
          <DrawerTitle className='text-lg font-medium'></DrawerTitle>
          <DrawerDescription className='text-sm text-muted-foreground'></DrawerDescription>
          <div className='h-full p-6'>
            <ScrollArea className='h-full w-full'>
              <FileContentViewer
                sourceId={sourceId}
                fileId={selectedFile?.id ?? 0}
              />
            </ScrollArea>
          </div>
        </DrawerContent>
      </Drawer>
    </>
  );
}
