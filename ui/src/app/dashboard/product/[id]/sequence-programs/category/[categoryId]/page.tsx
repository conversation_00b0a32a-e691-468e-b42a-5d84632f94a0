import PageContainer from '@/components/layout/page-container';
import ProductViewPage from '@/features/product/product-view-page';

export default async function CategoryPage({
  params
}: {
  params: Promise<{ id: string; categoryId: string }>
}) {
  const { categoryId } = await params;
  return (
    <PageContainer>
      <div className='flex w-full flex-col gap-3'>
        <ProductViewPage
          activeTab="sequence-programs"
          selectedCategoryId={parseInt(categoryId, 10)}
        />
      </div>
    </PageContainer>
  );
}
