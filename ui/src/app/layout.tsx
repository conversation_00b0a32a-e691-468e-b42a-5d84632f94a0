import { Toaster } from '@/components/ui/sonner';
import type { Metadata } from 'next';
import { NuqsAdapter } from 'nuqs/adapters/next/app';
import { Lato } from 'next/font/google';
import NextTopLoader from 'nextjs-toploader';
import { Inter as FontSans } from 'next/font/google';
import './globals.css';
import { AppProvider } from '@/providers/app-provider';
import { cn } from '@/lib/utils';
import { StagewiseDevToolbar } from '@/components/stagewise-toolbar';

export const metadata: Metadata = {
  title: '01-digital',
  description: 'Omniwave digital workspace'
};

const lato = Lato({
  subsets: ['latin'],
  weight: ['400', '700', '900'],
  display: 'swap'
});

const fontSans = FontSans({
  subsets: ['latin'],
  variable: '--font-sans'
});

export default async function RootLayout({
  children
}: {
  children: React.ReactNode;
}) {
  return (
    <html
      lang='en'
      className={cn(
        'min-h-screen bg-background font-sans antialiased',
        fontSans.variable
        // Add other font variables if you have them
        // fontUrban.variable
      )}
      suppressHydrationWarning
    >
      <body className={'overflow-hidden'}>
        <NextTopLoader showSpinner={false} />
        <NuqsAdapter>
          <Toaster />
          <AppProvider>{children}</AppProvider>
          <StagewiseDevToolbar />
        </NuqsAdapter>
      </body>
    </html>
  );
}
