import { create } from 'zustand';
import { ProgramDescription, ProgramVersion } from 'types'; // Assuming 'types' is an alias or correct path
import { StepType } from './config'; // Assuming './config' is the correct path for StepType

// Define the main interface for a program step, allowing for dynamic properties
export interface ProgramStep {
  step_number: string;        // Common identifier for the step
  type: StepType | string;    // Type of the step, using StepType enum for known types
  name?: string;               // Optional common name for the step
  [key: string]: any;         // Index signature for dynamic properties
}

// Type for the 'value' parameter in updateStep
export type StepFieldValue = any;


// Type for data coming from API or being saved
export interface SavedProgramData {
  id?: number; // programId
  name?: string;
  steps?: ProgramStep[];
  column_mapping_used?: Record<string, string>;
  current_version_number?: number;
  versions?: ProgramVersion[];
  logic_technique?: string;
  program_description?: ProgramDescription;
  user_id?: number; // If relevant from API
  // Include other fields that the API might return upon save/fetch
  program?: { // Nesting observed in handleSuccessfulSave
    name?: string;
    steps?: ProgramStep[];
    logic_technique?: string;
    program_description?: ProgramDescription;
    // Potentially other fields from the nested program object
  };
}


// Helper for deep cloning, can be replaced with a more robust library if needed
const deepClone = <T>(obj: T): T => JSON.parse(JSON.stringify(obj));

// Deep equality check for objects
// Note: Uses JSON.stringify which has limitations (doesn't handle circular references,
// doesn't properly compare undefined values, and depends on key order)
export const objectsAreEqual = (obj1: any, obj2: any): boolean => {
  if (obj1 === obj2) return true;
  if (obj1 === null || obj2 === null) return obj1 === obj2;
  if (obj1 === undefined || obj2 === undefined) return obj1 === obj2;
  
  return JSON.stringify(obj1) === JSON.stringify(obj2);
};

// Factory function for creating default step objects
const createDefaultStep = (stepType: StepType, stepNumber: string): ProgramStep => {
  const newStep: ProgramStep = {
    step_number: stepNumber,
    type: stepType,
    name: `${stepType.charAt(0).toUpperCase() + stepType.slice(1)} ${stepNumber}`, // Default name
  };

  // Initialize a few common dynamic properties for specific step types.
  // This ensures that newly created steps of these types have these fields present,
  // even if they are dynamic and not part of the core ProgramStep interface.
  switch (stepType) {
    case 'action':
      newStep.roller_action_description = '';
      // Initialize other common dynamic fields for 'action' if necessary e.g.
      // newStep.air_action_description = null;
      // newStep.kneading_speed = null;
      break;
    case 'subroutine':
      newStep.subroutine_id = null;
      newStep.roller_action_description = ''; // Also common for subroutines
      break;
    case 'program_note':
      newStep.description = '';
      newStep.roller_action_description = '';
      break;
    case 'comment':
      newStep.roller_action_description = '';
      // Initialize other common dynamic fields for 'comment' if necessary
      break;
    // No default case needed as stepType is from StepType enum.
    // If stepType could be an arbitrary string in the future, a default might be useful.
  }

  return newStep;
};


interface ProgramEditorState {
  // Program Core Data
  programId: number | null;
  programName: string;
  steps: ProgramStep[];
  programInfo: {
    logic_technique?: string;
    program_description?: ProgramDescription;
  };
  columnMapping: Record<string, string>;

  // Initial State for Diffing
  initialProgramName: string;
  initialSteps: ProgramStep[];
  initialProgramInfo: {
    logic_technique?: string;
    program_description?: ProgramDescription;
  };

  // UI State & Flags
  isDrawerOpen: boolean;
  isModified: boolean;

  // Undo/Redo
  history: ProgramStep[][];
  historyIndex: number;
  isUndoRedoAction: boolean;

  // Version Control
  currentVersionNumber: number;
  selectedVersionNumber: number | null;
  versions: ProgramVersion[];
  viewingOlderVersion: boolean;
  saveDialogOpen: boolean;
  versionNotes: string;
  isSavingVersion: boolean; // Specifically for the dialog flow of saving an older version

  // User Info
  currentUserId: number | undefined;

  // Actions
  setProgramId: (id: number | null) => void;
  setCurrentUserId: (id: number | undefined) => void;

  // Initialization
  initializeProgramData: (data: SavedProgramData) => void;
  initializeVersionData: (data: { steps?: ProgramStep[] }) => void; // Assuming version data only brings steps
  resetToCurrentVersion: (currentProgramSteps: ProgramStep[]) => void;

  // Program Name
  setProgramName: (name: string) => void;

  // Program Info
  setProgramInfo: (info: {
    logic_technique?: string;
    program_description?: ProgramDescription;
  }) => void;
  updateInitialProgramInfoAndNameFromSave: (savedData: SavedProgramData) => void;


  // Steps
  addStep: (stepType?: StepType) => void;
  addStepAtPosition: (stepType: StepType, position: number, insertBefore?: boolean) => void;
  removeStep: (index: number) => void;
  updateStep: (index: number, field: string, value: StepFieldValue) => void;
  reorderSteps: (newSteps: ProgramStep[]) => void;
  _updateStepsInternal: (newSteps: ProgramStep[], modified?: boolean) => void; // Internal helper

  // Undo/Redo
  handleUndo: () => void;
  handleRedo: () => void;
  _addCurrentStepsToHistory: () => void; // Internal helper for history management

  // Modification Status
  setIsModified: (modified: boolean) => void;
  checkAndSetModified: () => void;


  // Version Control UI
  setIsDrawerOpen: (open: boolean) => void;
  openSaveDialog: () => void;
  closeSaveDialog: () => void;
  setVersionNotes: (notes: string) => void;
  setIsSavingVersion: (isSaving: boolean) => void;
  setSelectedVersionNumber: (version: number | null) => void;
  setVersions: (versions: ProgramVersion[]) => void; // Usually set during init or after save/delete
  setCurrentVersionNumber: (versionNumber: number) => void; // Usually set during init

  // Post-save actions
  handleSuccessfulSave: (savedProgramData: SavedProgramData) => void;
  handleFailedSave: () => void;
  
  resetVersionSelection: () => void;
  resetProgramStateForIdChange: () => void;
}

export const useProgramEditorStore = create<ProgramEditorState>((set, get) => ({
  // Initial State
  programId: null,
  programName: '',
  steps: [],
  programInfo: {},
  columnMapping: {},
  initialProgramName: '',
  initialSteps: [],
  initialProgramInfo: {},
  isDrawerOpen: true,
  isModified: false,
  history: [],
  historyIndex: -1,
  isUndoRedoAction: false,
  currentVersionNumber: 1,
  selectedVersionNumber: null,
  versions: [],
  viewingOlderVersion: false,
  saveDialogOpen: false,
  versionNotes: '',
  isSavingVersion: false,
  currentUserId: undefined,

  // Actions
  setProgramId: (id) => set({ programId: id }),
  setCurrentUserId: (id) => set({ currentUserId: id }),

  initializeProgramData: (data: SavedProgramData) => {
    const newSteps = deepClone(data.steps || []) as ProgramStep[];
    const newProgramInfo = {
      logic_technique: data.logic_technique,
      program_description: data.program_description ?? undefined,
    };
    set({
      programName: data.name || '',
      initialProgramName: data.name || '',
      steps: newSteps,
      initialSteps: deepClone(newSteps),
      columnMapping: data.column_mapping_used || {},
      currentVersionNumber: data.current_version_number || 1,
      versions: data.versions || [],
      programInfo: newProgramInfo,
      initialProgramInfo: deepClone(newProgramInfo),
      history: [deepClone(newSteps)] as ProgramStep[][],
      historyIndex: 0,
      isModified: false,
      selectedVersionNumber: null, // Reset version selection
      viewingOlderVersion: false,
      programId: data.id ?? null, // Initialize programId if available
    });
  },

  initializeVersionData: (data: { steps?: ProgramStep[] }) => {
    const newSteps = deepClone(data.steps || []) as ProgramStep[];
    set((state) => ({
      steps: newSteps,
      viewingOlderVersion: state.selectedVersionNumber !== state.currentVersionNumber,
      history: [deepClone(newSteps)] as ProgramStep[][],
      historyIndex: 0,
      isModified: false, // Viewing an old version starts unmodified
    }));
  },
  
  resetToCurrentVersion: (currentProgramSteps: ProgramStep[]) => {
    const newSteps = deepClone(currentProgramSteps || []) as ProgramStep[];
    set({
      steps: newSteps,
      selectedVersionNumber: null,
      viewingOlderVersion: false,
      history: [deepClone(newSteps)] as ProgramStep[][],
      historyIndex: 0,
      isModified: false,
    });
  },

  setProgramName: (name: string) => {
    set({ programName: name });
    get().checkAndSetModified();
  },

  setProgramInfo: (info: {
    logic_technique?: string;
    program_description?: ProgramDescription;
  }) => {
    set({ programInfo: info });
    get().checkAndSetModified();
  },
  
  updateInitialProgramInfoAndNameFromSave: (savedData: SavedProgramData) => {
    // Extract program info from API response
    const programInfo = {
      logic_technique: savedData.logic_technique ?? savedData.program?.logic_technique,
      program_description: savedData.program_description 
        ? deepClone(savedData.program_description) 
        : (savedData.program?.program_description 
          ? deepClone(savedData.program.program_description) 
          : undefined),
    };
    
    // Extract program name from API response
    const programName = savedData.name ?? savedData.program?.name ?? get().programName;
    
    // Extract steps from API response
    const steps = savedData.steps 
      ? deepClone(savedData.steps) 
      : (savedData.program?.steps 
        ? deepClone(savedData.program.steps) 
        : get().initialSteps);
    
    set({
      initialProgramName: programName,
      initialProgramInfo: programInfo,
      initialSteps: steps as ProgramStep[]
    });
  },

  _updateStepsInternal: (newSteps: ProgramStep[], modified = true) => {
    set({ steps: newSteps, isModified: modified });
    if (modified && !get().isUndoRedoAction) {
      get()._addCurrentStepsToHistory();
    }
  },

  addStep: (stepType: StepType = 'action') => {
    const { steps } = get();
    const newStepNumber =
      steps.length > 0 && steps[steps.length - 1]?.step_number
        ? (parseInt(steps[steps.length - 1].step_number, 10) + 1).toString()
        : '1';

    const newStep = createDefaultStep(stepType, newStepNumber);
    get()._updateStepsInternal([...steps, newStep]);
  },

  addStepAtPosition: (stepType: StepType = 'action', position: number, insertBefore: boolean = false) => {
    const { steps } = get();
    const insertIndex = insertBefore ? position : position + 1;

    // Create new step with temporary step number (will be corrected below)
    const newStep = createDefaultStep(stepType, '1');

    // Create new array with inserted step
    const newSteps = [...steps];
    newSteps.splice(insertIndex, 0, newStep);

    // Renumber ALL steps to ensure proper sequential numbering (1, 2, 3, ...)
    const renumberedSteps = newSteps.map((step, index) => ({
      ...step,
      step_number: (index + 1).toString()
    }));

    get()._updateStepsInternal(renumberedSteps);
  },

  removeStep: (index: number) => {
    const newSteps = [...get().steps] as ProgramStep[];
    newSteps.splice(index, 1);
    get()._updateStepsInternal(newSteps);
  },

  updateStep: (index: number, field: string, value: StepFieldValue) => {
    const currentSteps = get().steps;
    // Ensure the step exists before trying to update it
    if (currentSteps[index]) {
        // Only clone the array and the specific step being updated for better performance
        const newSteps = [...currentSteps];
        newSteps[index] = { ...currentSteps[index], [field]: value } as ProgramStep;
        get()._updateStepsInternal(newSteps);
    } else {
        console.warn(`Attempted to update non-existent step at index ${index}`);
    }
  },

  reorderSteps: (newStepsFromArgs: ProgramStep[]) => {
    // Create a shallow copy of the array with shallow copies of each step
    // This is sufficient for reordering and much faster than deep cloning
    const newSteps = newStepsFromArgs.map(step => ({ ...step }));
    get()._updateStepsInternal(newSteps);
  },
  
  _addCurrentStepsToHistory: () => {
    set((state) => {
      // Use shallow copy for history - deep clone is expensive and not necessary for undo/redo
      const stepsSnapshot = state.steps.map(step => ({ ...step }));
      const newHistory = [...state.history.slice(0, state.historyIndex + 1), stepsSnapshot] as ProgramStep[][];
      const maxHistorySize = 50; // Define or import this constant
      // Ensure history doesn't grow indefinitely
      if (newHistory.length > maxHistorySize) {
        newHistory.shift(); // Remove the oldest entry
      }
      return { history: newHistory, historyIndex: newHistory.length - 1 };
    });
  },

  handleUndo: () => {
    set((state) => {
      if (state.historyIndex > 0) {
        const newIndex = state.historyIndex - 1;
        // Use shallow copy instead of deep clone for better performance
        const newSteps = state.history[newIndex].map(step => ({ ...step })) as ProgramStep[];
        return {
          historyIndex: newIndex,
          steps: newSteps,
          isModified: true, // Or re-evaluate based on comparison with initialSteps if needed
          isUndoRedoAction: true,
        };
      }
      return {};
    });
    // Check modification status after undo
    get().checkAndSetModified();
    set({ isUndoRedoAction: false }); // Reset after the update
  },

  handleRedo: () => {
    set((state) => {
      if (state.historyIndex < state.history.length - 1) {
        const newIndex = state.historyIndex + 1;
        // Use shallow copy instead of deep clone for better performance
        const newSteps = state.history[newIndex].map(step => ({ ...step })) as ProgramStep[];
        return {
          historyIndex: newIndex,
          steps: newSteps,
          isModified: true, // Or re-evaluate
          isUndoRedoAction: true,
        };
      }
      return {};
    });
    // Check modification status after redo
    get().checkAndSetModified();
    set({ isUndoRedoAction: false }); // Reset after the update
  },

  setIsModified: (modified: boolean) => set({ isModified: modified }),
  
  checkAndSetModified: () => {
    const { 
      programName, initialProgramName, 
      steps, initialSteps, 
      programInfo, initialProgramInfo, 
      isModified 
    } = get();
    
    // Check if any part of the program has changed
    const nameChanged = programName !== initialProgramName;
    const stepsChanged = !objectsAreEqual(steps, initialSteps);
    const logicTechniqueChanged = initialProgramInfo.logic_technique !== programInfo.logic_technique;
    const descriptionChanged = !objectsAreEqual(initialProgramInfo.program_description, programInfo.program_description);
    
    const hasChanges = nameChanged || stepsChanged || logicTechniqueChanged || descriptionChanged;

    // Only update isModified if the value would change
    if (hasChanges !== isModified) {
      set({ isModified: hasChanges });
    }
  },

  setIsDrawerOpen: (open: boolean) => set({ isDrawerOpen: open }),
  openSaveDialog: () => set({ saveDialogOpen: true }),
  closeSaveDialog: () => set({ saveDialogOpen: false, isSavingVersion: false, versionNotes: '' }), // also reset related
  setVersionNotes: (notes) => set({ versionNotes: notes }),
  setIsSavingVersion: (isSaving) => set({ isSavingVersion: isSaving }),
  
  setSelectedVersionNumber: (version) => {
    const { currentVersionNumber, isModified } = get();
    if (isModified && version !== null && version !== get().selectedVersionNumber) {
      // Prevent switching if modified, caller (UI) should handle toast.
      // toast.warning('Please save or discard your changes before switching versions.');
      return;
    }
    set({ selectedVersionNumber: version });
    // Fetching logic for version data will be in the component that calls this.
    // The component will then call initializeVersionData.
  },
  setVersions: (versions) => set({ versions: versions }),
  setCurrentVersionNumber: (versionNumber) => set({ currentVersionNumber: versionNumber }),
  
  resetVersionSelection: () => {
    set({
      selectedVersionNumber: null,
      viewingOlderVersion: false,
      // Potentially reload current steps if they were overwritten by version view
      // This logic is better handled in the component using programData
    });
  },

  handleSuccessfulSave: (savedProgramData: SavedProgramData) => {
    const { programName, steps, programInfo } = get(); // Current state

    // Update initial state from API response data
    const dataForInitialUpdate: SavedProgramData = {
      name: savedProgramData.program?.name ?? savedProgramData.name ?? programName,
      logic_technique: savedProgramData.program?.logic_technique ?? savedProgramData.logic_technique ?? programInfo.logic_technique,
      program_description: savedProgramData.program?.program_description ?? savedProgramData.program_description ?? programInfo.program_description,
      steps: (savedProgramData.program?.steps ?? savedProgramData.steps ?? steps) as ProgramStep[],
      current_version_number: savedProgramData.current_version_number,
      versions: savedProgramData.versions,
      column_mapping_used: savedProgramData.column_mapping_used,
      id: savedProgramData.id,
    };
    
    // Update the initial state (for change detection)
    get().updateInitialProgramInfoAndNameFromSave(dataForInitialUpdate);

    // Reset UI state
    set({
      isModified: false,
      saveDialogOpen: false,
      versionNotes: '',
      selectedVersionNumber: null,
      viewingOlderVersion: false,
      isSavingVersion: false,
    });
    
    // Update version info if available in API response
    if (dataForInitialUpdate.current_version_number) {
      set({ currentVersionNumber: dataForInitialUpdate.current_version_number });
    }
    
    if (dataForInitialUpdate.versions) {
      set({ versions: dataForInitialUpdate.versions });
    }
  },
  
  handleFailedSave: () => {
    set({ isSavingVersion: false }); // Reset saving flag
  },

  resetProgramStateForIdChange: () => set({
    // Reset all program-specific state when changing programs
    programName: '',
    steps: [],
    programInfo: {},
    columnMapping: {}, 
    initialProgramName: '',
    initialSteps: [],
    initialProgramInfo: {},
    isModified: false,
    history: [],
    historyIndex: -1,
    currentVersionNumber: 1,
    selectedVersionNumber: null,
    versions: [],
    viewingOlderVersion: false,
    saveDialogOpen: false,
    versionNotes: '',
    isSavingVersion: false,
  }),

}));

// Selector to check if there are actual changes to save
// This can be used in the component to enable/disable save button, complementing isModified
export const selectHasActualChangesToSave = (state: ProgramEditorState): boolean => {
  const nameActuallyChanged = state.programName !== state.initialProgramName;
  const stepsActuallyChanged = !objectsAreEqual(state.steps, state.initialSteps);
  const logicTechniqueActuallyChanged = state.initialProgramInfo.logic_technique !== state.programInfo.logic_technique;
  const descriptionActuallyChanged = !objectsAreEqual(state.initialProgramInfo.program_description, state.programInfo.program_description);
  return nameActuallyChanged || stepsActuallyChanged || logicTechniqueActuallyChanged || descriptionActuallyChanged;
};

// New Selectors for Undo/Redo
export const selectCanUndo = (state: ProgramEditorState): boolean => state.historyIndex > 0;
export const selectCanRedo = (state: ProgramEditorState): boolean => state.historyIndex < state.history.length - 1 && state.history.length > 0;