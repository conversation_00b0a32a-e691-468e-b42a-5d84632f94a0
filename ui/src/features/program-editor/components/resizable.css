/* Custom styles for react-resizable */
.react-resizable {
  position: relative;
}

.react-resizable-handle {
  position: absolute;
  right: 0;
  bottom: 0;
  width: 12px; /* Slightly wider for easier targeting */
  height: 100%;
  cursor: col-resize;
  z-index: 10;
}

.react-resizable-handle-e {
  right: 0;
  top: 0;
  width: 12px; /* Slightly wider for easier targeting */
  height: 100%;
  cursor: col-resize;
}

/* Make the resize handle more visible on hover */
.react-resizable-handle:hover::after,
.react-resizable-handle::after {
  content: "";
  position: absolute;
  right: 5px;
  top: 50%;
  transform: translateY(-50%);
  width: 3px; /* Slightly thicker for better visibility */
  height: 70%; /* Taller for better visibility */
  background-color: rgba(0, 0, 0, 0.15); /* Slightly darker for better visibility */
  border-radius: 2px;
  transition: background-color 0.2s ease, width 0.2s ease;
}

.react-resizable-handle:hover::after {
  background-color: rgba(0, 0, 0, 0.4); /* Darker on hover for better visibility */
  width: 4px; /* Thicker on hover */
}

/* Dark mode styles */
@media (prefers-color-scheme: dark) {
  .react-resizable-handle::after {
    background-color: rgba(255, 255, 255, 0.15); /* Slightly brighter for dark mode */
  }
  
  .react-resizable-handle:hover::after {
    background-color: rgba(255, 255, 255, 0.4); /* Brighter on hover for dark mode */
  }
}

/* Adjust the cell content padding for better resize handle access */
.react-resizable-wrapper {
  width: 100%;
  height: 100%;
  padding-right: 10px;
} 