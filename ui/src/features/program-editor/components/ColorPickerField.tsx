'use client';
import React, { useState, useEffect } from 'react';
import { RgbColorPicker, RgbColor } from 'react-colorful';
import { Palette } from 'lucide-react';
import {
  Popover,
  PopoverContent,
  PopoverTrigger
} from '@/components/ui/popover';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';

interface ColorPickerFieldProps {
  value: string | null;
  onChange: (value: string | null) => void;
}

// Helper to parse RGB string like "51, 255, 180" into RgbColor object
const parseRgbString = (value: string | null): RgbColor => {
  if (!value) return { r: 255, g: 255, b: 255 };

  const rgbMatch = value.match(/(\d+),\s*(\d+),\s*(\d+)/);
  if (rgbMatch) {
    return {
      r: parseInt(rgbMatch[1], 10),
      g: parseInt(rgbMatch[2], 10),
      b: parseInt(rgbMatch[3], 10)
    };
  }

  return { r: 255, g: 255, b: 255 };
};

// Helper to convert RgbColor to string format
const rgbToString = (color: RgbColor): string => {
  return `${color.r}, ${color.g}, ${color.b}`;
};

const ColorPickerField: React.FC<ColorPickerFieldProps> = ({
  value,
  onChange
}) => {
  // Initialize color from value, defaulting to white if invalid
  const [color, setColor] = useState<RgbColor>(() => parseRgbString(value));
  const [isValidRgb, setIsValidRgb] = useState(true);
  const [inputValue, setInputValue] = useState(value || '');

  // Update local state when external value changes, but only if it's different
  // This prevents unnecessary re-renders
  useEffect(() => {
    if (value !== inputValue) {
      setInputValue(value || '');
    }

    // Only update the color picker if the value is valid
    if (value && value.match(/^\d+,\s*\d+,\s*\d+$/)) {
      const newColor = parseRgbString(value);
      setColor(newColor);
    }

    // Check if the value is a valid RGB format
    const isValid = !value || !!value.match(/^\d+,\s*\d+,\s*\d+$/);
    setIsValidRgb(isValid);
  }, [value]);

  // Handle color picker change - validate before calling onChange
  const handleColorChange = (newColor: RgbColor) => {
    setColor(newColor);
    const rgbString = rgbToString(newColor);
    setInputValue(rgbString);
    // Color picker always produces valid RGB values
    onChange(rgbString);
  };

  // Handle input field change with validation
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setInputValue(newValue);

    // Only update parent if the value is valid or empty
    if (!newValue) {
      onChange(null);
    } else if (newValue.match(/^\d+,\s*\d+,\s*\d+$/)) {
      onChange(newValue);
    }
    // If invalid, don't call onChange - this prevents the infinite loop
  };

  return (
    <div className='flex w-full items-center space-x-2'>
      <Popover>
        <PopoverTrigger asChild>
          <Button
            variant='outline'
            size='sm'
            className='flex h-9 w-9 items-center justify-center p-0'
          >
            {value ? (
              <div
                className='color-preview h-6 w-6 rounded-sm border border-gray-300'
                style={{
                  backgroundColor: `rgb(${color.r}, ${color.g}, ${color.b})`
                }}
                aria-label='Selected color'
              />
            ) : (
              <Palette className='h-5 w-5 text-muted-foreground' />
            )}
          </Button>
        </PopoverTrigger>
        <PopoverContent className='w-auto p-3'>
          <RgbColorPicker color={color} onChange={handleColorChange} />
          <div className='mt-2 text-center text-xs text-muted-foreground'>
            RGB: {color.r}, {color.g}, {color.b}
          </div>
        </PopoverContent>
      </Popover>

      <Input
        type='text'
        value={inputValue}
        onChange={handleInputChange}
        onBlur={() => {
          // On blur, try to fix invalid values
          if (inputValue && !inputValue.match(/^\d+,\s*\d+,\s*\d+$/)) {
            // Try to extract numbers
            const numbers = inputValue.match(/\d+/g);
            if (numbers && numbers.length >= 3) {
              const r = Math.min(255, Math.max(0, parseInt(numbers[0])));
              const g = Math.min(255, Math.max(0, parseInt(numbers[1])));
              const b = Math.min(255, Math.max(0, parseInt(numbers[2])));
              const validRgb = `${r}, ${g}, ${b}`;
              setInputValue(validRgb);
              onChange(validRgb);
            } else if (numbers && numbers.length > 0) {
              // If we have at least one number, use it for all channels
              const val = Math.min(255, Math.max(0, parseInt(numbers[0])));
              const validRgb = `${val}, ${val}, ${val}`;
              setInputValue(validRgb);
              onChange(validRgb);
            } else {
              // If no valid numbers, clear the field
              setInputValue('');
              onChange(null);
            }
          }
        }}
        placeholder='R, G, B'
        className={`w-full ${!isValidRgb ? 'border-red-500' : ''}`}
      />
    </div>
  );
};

export default ColorPickerField;
