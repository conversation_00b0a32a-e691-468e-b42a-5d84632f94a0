import React, { useState, useEffect } from 'react';
import {
  Drawer,
  DrawerContent,
  DrawerTitle,
  DrawerDescription
} from '@/components/ui/drawer';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Button } from '@/components/ui/button';
import { X, Save, Edit, Eye, Plus, Trash2, FileDown, Loader2 } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { ProgramDetail, ProgramDescription } from 'types';
import { useExportProgramInfo } from '@/queries/program';
import { toast } from 'sonner';

interface ProgramInfoDrawerProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  programData: ProgramDetail | undefined;
  onSave?: (updatedData: {
    logic_technique?: string;
    program_description?: ProgramDescription;
  }) => void;
  isSaving?: boolean;
}

const ProgramInfoDrawer: React.FC<ProgramInfoDrawerProps> = ({
  open,
  onOpenChange,
  programData,
  onSave,
  isSaving = false
}) => {
  const [isEditMode, setIsEditMode] = useState(false);
  const [logicTechnique, setLogicTechnique] = useState<string>('');
  const [programDescription, setProgramDescription] =
    useState<ProgramDescription>({});

  // Use the export mutation hook
  const exportProgramInfo = useExportProgramInfo({
    onSuccess: (presignedUrl, filename) => {
      toast.success(
        'Program info export started. Your download will begin shortly.'
      );
      // Open the presigned URL. The Content-Disposition header set by S3 should trigger the download.
      window.open(presignedUrl, '_blank');
    },
    onError: (error) => {
      console.error('S3 Export error in component:', error);
      toast.error(
        `Export failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  });

  // Initialize form data when programData changes
  useEffect(() => {
    if (programData) {
      setLogicTechnique(programData.logic_technique || '');
      setProgramDescription(
        (programData.program_description as ProgramDescription) || {}
      );
    }
  }, [programData]);

  // Handle save button click
  const handleSave = () => {
    if (onSave) {
      onSave({
        logic_technique: logicTechnique,
        program_description: programDescription
      });
    }
    setIsEditMode(false);
  };

  // Handle export button click
  const onExportClick = () => {
    if (!programData?.id) {
      toast.error('Program ID is missing. Please save the program first.');
      return;
    }

    // The mutation will handle opening the presigned URL on success by default
    exportProgramInfo.mutate({
      programId: programData.id,
      programName: programData.name || programData.program_title
    });
  };

  // Handle field change for program description
  const handleFieldChange = (field: keyof ProgramDescription, value: any) => {
    setProgramDescription((prev) => ({
      ...prev,
      [field]: value
    }));
  };

  // Handle change for programme_sequence field
  const handleSequenceChange = (key: string, value: string) => {
    setProgramDescription((prev) => ({
      ...prev,
      programme_sequence: {
        ...(prev.programme_sequence || {}),
        [key]: value
      }
    }));
  };

  // Handle removing a sequence entry
  const handleRemoveSequence = (keyToRemove: string) => {
    setProgramDescription((prev) => {
      if (!prev.programme_sequence) return prev;

      const newSequence = { ...prev.programme_sequence };
      delete newSequence[keyToRemove];

      return {
        ...prev,
        programme_sequence: newSequence
      };
    });
  };

  // Sequence type options
  const sequenceOptions = [
    'Front Sequence',
    'Main Sequence',
    'Mid Sequence',
    'Cooling Sequence',
    'Ending Sequence',
    'Sequence'
  ];

  // Handle adding a new sequence entry
  const handleAddSequence = (sequenceType: string = 'Sequence') => {
    setProgramDescription((prev) => {
      // Check if this sequence type already exists
      if (prev.programme_sequence && prev.programme_sequence[sequenceType]) {
        // Find an available name by appending a number
        let counter = 1;
        let newName = sequenceType;
        while (prev.programme_sequence[newName]) {
          counter++;
          newName = `${sequenceType} ${counter}`;
        }
        sequenceType = newName;
      }

      return {
        ...prev,
        programme_sequence: {
          ...(prev.programme_sequence || {}),
          [sequenceType]: ''
        }
      };
    });
  };

  // Handle array field changes
  const handleArrayChange = (
    field: 'targeted_acupressure_points' | 'signature_moves',
    index: number,
    value: string
  ) => {
    setProgramDescription((prev) => {
      const array = [...(prev[field] || [])];
      array[index] = value;
      return {
        ...prev,
        [field]: array
      };
    });
  };

  // Handle adding item to array
  const handleAddArrayItem = (
    field: 'targeted_acupressure_points' | 'signature_moves'
  ) => {
    setProgramDescription((prev) => ({
      ...prev,
      [field]: [...(prev[field] || []), '']
    }));
  };

  // Handle removing item from array
  const handleRemoveArrayItem = (
    field: 'targeted_acupressure_points' | 'signature_moves',
    index: number
  ) => {
    setProgramDescription((prev) => {
      const array = [...(prev[field] || [])];
      array.splice(index, 1);
      return {
        ...prev,
        [field]: array
      };
    });
  };

  // Render edit form for program description
  const renderProgramDescriptionForm = () => {
    return (
      <div className='space-y-6'>
        {/* Basic fields */}
        <div className='grid grid-cols-2 gap-4'>
          <div className='mb-4'>
            <Label
              htmlFor='intensity'
              className='mb-1 block text-xs font-medium'
            >
              Intensity
            </Label>
            <Input
              id='intensity'
              type='number'
              value={programDescription.intensity || ''}
              onChange={(e) =>
                handleFieldChange(
                  'intensity',
                  e.target.value ? Number(e.target.value) : undefined
                )
              }
              className='text-sm'
              placeholder='Enter intensity level (1-10)'
            />
          </div>

          <div className='mb-4'>
            <Label
              htmlFor='program_name'
              className='mb-1 block text-xs font-medium'
            >
              Program Name
            </Label>
            <Input
              id='program_name'
              value={programDescription.program_name || ''}
              onChange={(e) =>
                handleFieldChange('program_name', e.target.value)
              }
              className='text-sm'
              placeholder='Enter program name'
            />
          </div>
        </div>

        <div className='mb-4'>
          <Label
            htmlFor='recommended_position'
            className='mb-1 block text-xs font-medium'
          >
            Recommended Position
          </Label>
          <Input
            id='recommended_position'
            value={programDescription.recommended_position || ''}
            onChange={(e) =>
              handleFieldChange('recommended_position', e.target.value)
            }
            className='text-sm'
            placeholder='Enter recommended position'
          />
        </div>

        <div className='mb-4'>
          <Label
            htmlFor='target_group_objective'
            className='mb-1 block text-xs font-medium'
          >
            Target Group Objective
          </Label>
          <Textarea
            id='target_group_objective'
            value={programDescription.target_group_objective || ''}
            onChange={(e) =>
              handleFieldChange('target_group_objective', e.target.value)
            }
            className='min-h-[100px] text-sm'
            placeholder='Enter target group objective'
          />
        </div>

        {/* Programme Sequence (object with string keys and values) */}
        <div className='mb-6'>
          <div className='mb-2 flex items-center justify-between'>
            <Label className='text-xs font-medium'>Programme Sequence</Label>
            <Select onValueChange={handleAddSequence} defaultValue='Sequence'>
              <SelectTrigger className='h-8 w-[180px]'>
                <SelectValue placeholder='Add Sequence' />
              </SelectTrigger>
              <SelectContent>
                {sequenceOptions.map((option) => (
                  <SelectItem key={option} value={option}>
                    {option}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className='space-y-3'>
            {programDescription.programme_sequence &&
              Object.entries(programDescription.programme_sequence).map(
                ([key, value]) => (
                  <div key={key} className='flex gap-2'>
                    <div className='flex-1 space-y-1'>
                      <div className='flex items-center justify-between'>
                        <Label className='text-xs font-medium'>{key}</Label>
                        <Button
                          variant='ghost'
                          size='sm'
                          onClick={() => handleRemoveSequence(key)}
                          className='h-6 px-2'
                        >
                          <Trash2 className='h-3 w-3' />
                        </Button>
                      </div>
                      <Textarea
                        value={value}
                        onChange={(e) =>
                          handleSequenceChange(key, e.target.value)
                        }
                        className='flex-1 text-sm'
                        placeholder='Enter sequence description'
                      />
                    </div>
                  </div>
                )
              )}

            {(!programDescription.programme_sequence ||
              Object.keys(programDescription.programme_sequence).length ===
                0) && (
              <div className='rounded-md border border-dashed border-gray-300 bg-background p-4 text-center text-sm text-gray-500'>
                No sequences added. Click &quot;Add Sequence&quot; to create
                one.
              </div>
            )}
          </div>
        </div>

        {/* Targeted Acupressure Points (string array) */}
        <div className='mb-6'>
          <div className='mb-2 flex items-center justify-between'>
            <Label className='text-xs font-medium'>
              Targeted Acupressure Points
            </Label>
            <Button
              variant='outline'
              size='sm'
              onClick={() => handleAddArrayItem('targeted_acupressure_points')}
              className='h-8'
            >
              <Plus className='mr-1 h-3 w-3' /> Add Point
            </Button>
          </div>

          <div className='space-y-3'>
            {programDescription.targeted_acupressure_points?.map(
              (point, index) => (
                <div key={index} className='flex gap-2'>
                  <Textarea
                    value={point}
                    onChange={(e) =>
                      handleArrayChange(
                        'targeted_acupressure_points',
                        index,
                        e.target.value
                      )
                    }
                    className='flex-1 text-sm'
                    placeholder='Enter acupressure point description'
                  />
                  <Button
                    variant='outline'
                    size='icon'
                    onClick={() =>
                      handleRemoveArrayItem(
                        'targeted_acupressure_points',
                        index
                      )
                    }
                    className='h-10 self-start'
                  >
                    <Trash2 className='h-4 w-4' />
                  </Button>
                </div>
              )
            )}

            {(!programDescription.targeted_acupressure_points ||
              programDescription.targeted_acupressure_points.length === 0) && (
              <div className='rounded-md border border-dashed border-gray-300 bg-background p-4 text-center text-sm text-gray-500'>
                No acupressure points added. Click &quot;Add Point&quot; to
                create one.
              </div>
            )}
          </div>
        </div>

        {/* Signature Moves (string array) */}
        <div className='mb-6'>
          <div className='mb-2 flex items-center justify-between'>
            <Label className='text-xs font-medium'>Signature Moves</Label>
            <Button
              variant='outline'
              size='sm'
              onClick={() => handleAddArrayItem('signature_moves')}
              className='h-8'
            >
              <Plus className='mr-1 h-3 w-3' /> Add Move
            </Button>
          </div>

          <div className='space-y-3'>
            {programDescription.signature_moves?.map((move, index) => (
              <div key={index} className='flex gap-2'>
                <Textarea
                  value={move}
                  onChange={(e) =>
                    handleArrayChange('signature_moves', index, e.target.value)
                  }
                  className='flex-1 text-sm'
                  placeholder='Enter signature move description'
                />
                <Button
                  variant='outline'
                  size='icon'
                  onClick={() =>
                    handleRemoveArrayItem('signature_moves', index)
                  }
                  className='h-10 self-start'
                >
                  <Trash2 className='h-4 w-4' />
                </Button>
              </div>
            ))}

            {(!programDescription.signature_moves ||
              programDescription.signature_moves.length === 0) && (
              <div className='rounded-md border border-dashed border-gray-300 bg-background p-4 text-center text-sm text-gray-500'>
                No signature moves added. Click &quot;Add Move&quot; to create
                one.
              </div>
            )}
          </div>
        </div>

        {/* Limitations */}
        <div className='mb-4'>
          <Label
            htmlFor='limitations'
            className='mb-1 block text-xs font-medium'
          >
            Limitations
          </Label>
          <Textarea
            id='limitations'
            value={programDescription.limitations || ''}
            onChange={(e) => handleFieldChange('limitations', e.target.value)}
            className='min-h-[100px] text-sm'
            placeholder='Enter any limitations'
          />
        </div>
      </div>
    );
  };

  // Render view mode for program description
  const renderProgramDescriptionView = (
    description: ProgramDescription | undefined | null
  ) => {
    if (!description) return null;

    return (
      <div className='space-y-6'>
        {/* Basic Information Card */}
        <Card>
          <CardHeader>
            <CardTitle className='text-sm'>Basic Information</CardTitle>
          </CardHeader>
          <CardContent className='grid grid-cols-2 gap-4 pt-0'>
            {description.intensity !== undefined && (
              <div>
                <h5 className='text-xs font-medium text-gray-500'>Intensity</h5>
                <Badge variant='outline' className='mt-1'>
                  {description.intensity}
                </Badge>
              </div>
            )}

            {description.program_name && (
              <div>
                <h5 className='text-xs font-medium text-gray-500'>
                  Program Name
                </h5>
                <div className='mt-1 text-sm'>{description.program_name}</div>
              </div>
            )}

            {description.recommended_position && (
              <div className='col-span-2'>
                <h5 className='text-xs font-medium text-gray-500'>
                  Recommended Position
                </h5>
                <div className='mt-1 text-sm'>
                  {description.recommended_position}
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Target Group Objective */}
        {description.target_group_objective && (
          <Card>
            <CardHeader>
              <CardTitle className='text-sm'>Target Group Objective</CardTitle>
            </CardHeader>
            <CardContent className='pt-0'>
              <div className='rounded-md bg-muted p-3 text-sm'>
                {description.target_group_objective}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Programme Sequence */}
        {description.programme_sequence &&
          Object.keys(description.programme_sequence).length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className='text-sm'>Programme Sequence</CardTitle>
              </CardHeader>
              <CardContent className='space-y-3 pt-0'>
                {Object.entries(description.programme_sequence).map(
                  ([key, value]) => (
                    <div key={key}>
                      <h5 className='text-xs font-medium text-gray-700'>
                        {key}
                      </h5>
                      <div className='mt-1 rounded-md bg-muted p-2 text-sm'>
                        {value}
                      </div>
                    </div>
                  )
                )}
              </CardContent>
            </Card>
          )}

        {/* Targeted Acupressure Points */}
        {description.targeted_acupressure_points &&
          description.targeted_acupressure_points.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className='text-sm'>
                  Targeted Acupressure Points
                </CardTitle>
              </CardHeader>
              <CardContent className='space-y-2 pt-0'>
                {description.targeted_acupressure_points.map((point, index) => (
                  <div key={index} className='rounded-md bg-muted p-2 text-sm'>
                    {point}
                  </div>
                ))}
              </CardContent>
            </Card>
          )}

        {/* Signature Moves */}
        {description.signature_moves &&
          description.signature_moves.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className='text-sm'>Signature Moves</CardTitle>
              </CardHeader>
              <CardContent className='space-y-2 pt-0'>
                {description.signature_moves.map((move, index) => (
                  <div key={index} className='rounded-md bg-muted p-2 text-sm'>
                    {move}
                  </div>
                ))}
              </CardContent>
            </Card>
          )}

        {/* Limitations */}
        {description.limitations && (
          <Card>
            <CardHeader>
              <CardTitle className='text-sm'>Limitations</CardTitle>
            </CardHeader>
            <CardContent className='pt-0'>
              <div className='rounded-md bg-muted p-3 text-sm'>
                {description.limitations}
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    );
  };

  return (
    <Drawer open={open} onOpenChange={onOpenChange}>
      <DrawerContent className='fixed inset-x-[unset] right-0 top-0 mt-0 h-full w-[600px] max-w-[90vw] !rounded-r-none border-l bg-background'>
        <div className='flex items-center justify-between px-4 pt-4'>
          <DrawerTitle className='text-lg font-medium'>
            Program Info
          </DrawerTitle>
          <div className='flex items-center gap-2'>
            <div className='mr-2 flex items-center space-x-2'>
              <Switch
                id='edit-mode'
                checked={isEditMode}
                onCheckedChange={setIsEditMode}
              />
              <Label htmlFor='edit-mode' className='text-xs'>
                {isEditMode ? (
                  <span className='flex items-center gap-1'>
                    <Edit className='h-3 w-3' /> Edit Mode
                  </span>
                ) : (
                  <span className='flex items-center gap-1'>
                    <Eye className='h-3 w-3' /> View Mode
                  </span>
                )}
              </Label>
            </div>
            <Button
              variant='outline'
              size='sm'
              onClick={onExportClick}
              disabled={exportProgramInfo.isPending || !programData?.id}
              className='bg-white/80 dark:bg-slate-800/80'
            >
              {exportProgramInfo.isPending ? (
                <>
                  <Loader2 className='h-4 w-4 mr-2 animate-spin' />
                  Exporting...
                </>
              ) : (
                <>
                  <FileDown className='h-4 w-4 mr-2' />
                  Export Word
                </>
              )}
            </Button>
            <Button
              variant='ghost'
              size='icon'
              aria-label='Close Info'
              onClick={() => onOpenChange(false)}
            >
              <X className='h-5 w-5' />
            </Button>
          </div>
        </div>
        <DrawerDescription className='px-4 text-xs text-gray-500'>
          {isEditMode
            ? 'Edit program information and click Save when done'
            : 'View detailed program information'}
        </DrawerDescription>
        <div className='h-full p-4 pt-2'>
          <ScrollArea className='h-[calc(100vh-120px)] w-full pr-4'>
            {isEditMode ? (
              // Edit Mode
              <div className='space-y-4'>
                <div className='mb-4'>
                  <Label
                    htmlFor='logic_technique'
                    className='mb-1 block text-xs font-medium'
                  >
                    Logic Technique
                  </Label>
                  <Textarea
                    id='logic_technique'
                    value={logicTechnique}
                    onChange={(e) => setLogicTechnique(e.target.value)}
                    className='min-h-[100px] text-sm'
                    placeholder='Enter logic technique'
                  />
                </div>

                <Separator className='my-4' />

                <div>
                  <h3 className='mb-3 text-sm font-semibold'>
                    Program Description
                  </h3>
                  {renderProgramDescriptionForm()}
                </div>

                <div className='sticky bottom-0 mt-6 border-t bg-background pb-2 pt-4'>
                  <Button
                    onClick={handleSave}
                    className='w-full'
                    disabled={isSaving}
                  >
                    {isSaving ? (
                      <>
                        <svg
                          className='mr-2 h-4 w-4 animate-spin'
                          xmlns='http://www.w3.org/2000/svg'
                          fill='none'
                          viewBox='0 0 24 24'
                        >
                          <circle
                            className='opacity-25'
                            cx='12'
                            cy='12'
                            r='10'
                            stroke='currentColor'
                            strokeWidth='4'
                          ></circle>
                          <path
                            className='opacity-75'
                            fill='currentColor'
                            d='M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z'
                          ></path>
                        </svg>
                        Saving...
                      </>
                    ) : (
                      <>
                        <Save className='mr-2 h-4 w-4' /> Save Changes
                      </>
                    )}
                  </Button>
                </div>
              </div>
            ) : (
              // View Mode
              <div>
                <Card className='mb-6'>
                  <CardHeader>
                    <CardTitle className='text-sm'>Logic Technique</CardTitle>
                  </CardHeader>
                  <CardContent className='pt-0'>
                    {programData?.logic_technique ? (
                      <div className='rounded-md bg-muted p-3 text-sm'>
                        {programData.logic_technique}
                      </div>
                    ) : (
                      <div className='text-sm text-gray-500'>Not specified</div>
                    )}
                  </CardContent>
                </Card>

                <Separator className='my-4' />

                <h3 className='mb-3 text-sm font-semibold'>
                  Program Description
                </h3>

                {programData?.program_description ? (
                  renderProgramDescriptionView(
                    programData.program_description as ProgramDescription
                  )
                ) : (
                  <div className='text-sm text-gray-500'>
                    No description available
                  </div>
                )}
              </div>
            )}
          </ScrollArea>
        </div>
      </DrawerContent>
    </Drawer>
  );
};

export default ProgramInfoDrawer;
