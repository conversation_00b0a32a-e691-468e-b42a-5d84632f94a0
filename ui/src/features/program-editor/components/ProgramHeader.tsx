import React, { useState } from 'react';
import { CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Save,
  Play,
  Info,
  FileDown,
  Trash2,
  ChevronDown,
  Loader2,
  Package,
  Tag,
  Calendar,
  User,
  MoreHorizontal
} from 'lucide-react';
import { toast } from 'sonner';
import { ProgramVersion } from 'types';
import { useExportProgram } from '@/queries/program';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { format } from 'date-fns';
import DeleteConfirmationDialog from '@/components/ui/delete-confirmation-dialog';
import { getStatusDotColor, ProgramStatus, statusOptions } from '@/lib/utils';

interface ProgramHeaderProps {
  programName: string;
  handleNameChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  saveProgram: () => void;
  isModified: boolean;
  isSaving: boolean;
  productName: string;
  productModel: string;
  categoryName: string;
  isDrawerOpen: boolean;
  setIsDrawerOpen: (open: boolean) => void;
  // Version control props
  currentVersionNumber: number;
  versions: ProgramVersion[];
  selectedVersionNumber: number | null;
  onVersionChange: (versionNumber: number) => void;
  viewingOlderVersion: boolean;
  // Props for delete functionality
  programId: number;
  currentUserId: number | undefined;
  onDeleteVersion: (versionNumber: number) => void;
  isDeletingVersion: boolean;
  // Status props
  status: 'Draft' | 'In Progress' | 'Ready for Testing' | 'Tested' | 'Verified' | null | undefined;
  onStatusChange: (status: string) => void;
}

const ProgramHeader: React.FC<ProgramHeaderProps> = ({
  programName,
  handleNameChange,
  saveProgram,
  isModified,
  isSaving,
  productName,
  productModel,
  categoryName,
  isDrawerOpen,
  setIsDrawerOpen,
  // Version control props
  currentVersionNumber,
  versions,
  selectedVersionNumber,
  onVersionChange,
  viewingOlderVersion,
  // Destructure new props
  programId,
  currentUserId,
  onDeleteVersion,
  isDeletingVersion,
  // Status props
  status,
  onStatusChange
}) => {
  const renderStatusOption = (statusValue: ProgramStatus) => (
    <div className='flex items-center gap-2'>
      <div className={`w-2 h-2 rounded-full ${getStatusDotColor(statusValue)}`} />
      {statusValue}
    </div>
  );
  // Define placeholder for visualize feature
  const onVisualizeClick = () => toast.info('Visualize feature coming soon');

  // Use the export mutation hook
  const exportProgram = useExportProgram({
    onSuccess: (presignedUrl, filename) => {
      toast.success(
        'Program export started. Your download will begin shortly.'
      );
      // Open the presigned URL. The Content-Disposition header set by S3 should trigger the download.
      window.open(presignedUrl, '_blank');
    },
    onError: (error) => {
      console.error('S3 Export error in component:', error);
      toast.error(
        `Export failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  });

  // Handle export button click
  const onExportClick = () => {
    if (!programId) {
      toast.error('Program ID is missing. Please save the program first.');
      return;
    }


    // The mutation will handle opening the presigned URL on success by default
    exportProgram.mutate({
      programId,
      programName // Pass programName for filename generation fallback if needed
    });
  };

  // Helper to format date from ISO string
  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), 'MMM d, yyyy h:mm a');
    } catch (e) {
      return dateString;
    }
  };

  const displayedVersionNumber = selectedVersionNumber || currentVersionNumber;
  const versionToDisplayMeta = versions.find(
    (v) => v.version_number === displayedVersionNumber
  );

  // State for managing which version's delete confirmation is open
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [versionToDelete, setVersionToDelete] = useState<ProgramVersion | null>(
    null
  );

  const handleDeleteClick = (version: ProgramVersion) => {
    setVersionToDelete(version);
    setDeleteDialogOpen(true);
  };

  const handleConfirmDelete = () => {
    if (versionToDelete) {
      onDeleteVersion(versionToDelete.version_number);
    }
    setDeleteDialogOpen(false);
    setVersionToDelete(null);
  };

  return (
    <div className='border-b bg-gradient-to-r from-slate-50 to-slate-100/50 dark:from-slate-900/50 dark:to-slate-800/30'>
      <div className='py-3'>
        {/* Top Row: Title and Actions */}
        <div className='flex items-start justify-between mb-3'>
          {/* Program Title Section */}
          <div className='flex-1 min-w-0'>
            <CardTitle className='mb-3'>
              <Input
                value={programName}
                onChange={handleNameChange}
                className='border-none px-0 text-2xl font-bold bg-transparent focus-visible:ring-0 focus-visible:ring-offset-0 placeholder:text-muted-foreground/60'
                placeholder='Enter program name...'
              />
            </CardTitle>

            {/* Product Information Cards */}
            <div className='flex flex-wrap gap-3'>
              {productName && (
                <div className='flex items-center gap-2 px-3 py-1.5 bg-white/60 dark:bg-slate-800/60 rounded-lg border border-slate-200/60 dark:border-slate-700/60'>
                  <Package className='h-4 w-4 text-blue-600 dark:text-blue-400' />
                  <span className='text-sm font-medium text-slate-700 dark:text-slate-300'>
                    {productName}
                  </span>
                  {productModel && (
                    <>
                      <Separator orientation="vertical" className="h-4" />
                      <span className='text-sm text-slate-600 dark:text-slate-400'>
                        {productModel}
                      </span>
                    </>
                  )}
                </div>
              )}

              {categoryName && (
                <div className='flex items-center gap-2 px-3 py-1.5 bg-white/60 dark:bg-slate-800/60 rounded-lg border border-slate-200/60 dark:border-slate-700/60'>
                  <Tag className='h-4 w-4 text-green-600 dark:text-green-400' />
                  <span className='text-sm font-medium text-slate-700 dark:text-slate-300'>
                    {categoryName}
                  </span>
                </div>
              )}
            </div>
          </div>

          {/* Action Buttons */}
          <div className='flex items-center gap-2 ml-6'>
            <Button
              variant='outline'
              size='sm'
              onClick={() => setIsDrawerOpen(true)}
              className='bg-white/80 dark:bg-slate-800/80'
            >
              <Info className='h-4 w-4 mr-2' />
              Details
            </Button>

            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant='outline' size='sm' className='bg-white/80 dark:bg-slate-800/80'>
                  <MoreHorizontal className='h-4 w-4' />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align='end' className='w-48'>
                <DropdownMenuItem onClick={onVisualizeClick}>
                  <Play className='h-4 w-4 mr-2' />
                  Run Program
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={onExportClick}
                  disabled={exportProgram.isPending || !programId}
                >
                  {exportProgram.isPending ? (
                    <>
                      <Loader2 className='h-4 w-4 mr-2 animate-spin' />
                      Exporting...
                    </>
                  ) : (
                    <>
                      <FileDown className='h-4 w-4 mr-2' />
                      Export Excel
                    </>
                  )}
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>

            <Button
              onClick={saveProgram}
              disabled={!isModified || isSaving}
              size='sm'
              className='bg-primary hover:bg-primary/90'
            >
              {isSaving ? (
                <>
                  <Loader2 className='h-4 w-4 mr-2 animate-spin' />
                  Saving...
                </>
              ) : (
                <>
                  <Save className='h-4 w-4 mr-2' />
                  {viewingOlderVersion ? 'Save as New' : 'Save'}
                </>
              )}
            </Button>
          </div>
        </div>

        {/* Bottom Row: Version and Status */}
        <div className='flex items-center justify-between'>
          {/* Version Section */}
          <div className='flex items-center gap-4'>
            <div className='flex items-center gap-3'>
              <div className='flex items-center gap-2 px-3 py-2 bg-white/80 dark:bg-slate-800/80 rounded-lg border border-slate-200/60 dark:border-slate-700/60'>
                <Calendar className='h-4 w-4 text-purple-600 dark:text-purple-400' />
                <span className='text-sm font-medium text-slate-700 dark:text-slate-300'>
                  Version
                </span>
                <Badge
                  variant={viewingOlderVersion ? 'outline' : 'secondary'}
                  className={
                    viewingOlderVersion
                      ? 'border-amber-300 bg-amber-50 text-amber-700 dark:border-amber-600 dark:bg-amber-900/20 dark:text-amber-400'
                      : 'bg-slate-100 text-slate-700 dark:bg-slate-700 dark:text-slate-300'
                  }
                >
                  v{displayedVersionNumber}
                  {viewingOlderVersion && ' (Viewing older)'}
                </Badge>
              </div>

              {versions.length > 0 && (
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant='outline' size='sm' className='bg-white/80 dark:bg-slate-800/80'>
                      <ChevronDown className='h-4 w-4 mr-1' />
                      Switch Version
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align='start' className='w-80'>
                    {versions.map((version) => {
                      const isOwner =
                        currentUserId &&
                        version.created_by_user_id === currentUserId;
                      const isActiveVersion =
                        version.version_number === currentVersionNumber;
                      const canDelete = isOwner && !isActiveVersion;

                      return (
                        <DropdownMenuItem
                          key={version.version_number}
                          onSelect={(e) => {
                            if (
                              (e.target as HTMLElement).closest(
                                '.delete-version-button-trigger'
                              )
                            ) {
                              e.preventDefault();
                            } else {
                              onVersionChange(version.version_number);
                            }
                          }}
                          className='flex items-center justify-between p-3'
                        >
                          <div className='flex items-center gap-3'>
                            <Badge
                              variant={isActiveVersion ? 'default' : 'outline'}
                              className='text-xs'
                            >
                              v{version.version_number}
                            </Badge>
                            <div className='flex flex-col'>
                              <div className='flex items-center gap-2'>
                                {isActiveVersion && (
                                  <Badge variant='secondary' className='text-xs'>
                                    Current
                                  </Badge>
                                )}
                                <span className='text-xs text-muted-foreground'>
                                  {formatDate(version.created_at)}
                                </span>
                              </div>
                              {version.created_by_username && (
                                <div className='flex items-center gap-1 mt-1'>
                                  <User className='h-3 w-3 text-muted-foreground' />
                                  <span className='text-xs text-muted-foreground'>
                                    {version.created_by_username}
                                  </span>
                                </div>
                              )}
                            </div>
                          </div>
                          {canDelete && (
                            <Button
                              variant='ghost'
                              size='sm'
                              className='delete-version-button-trigger h-8 w-8 p-0 text-destructive hover:bg-destructive/10 hover:text-destructive'
                              onClick={(e) => {
                                e.stopPropagation();
                                handleDeleteClick(version);
                              }}
                              disabled={isDeletingVersion}
                              aria-label={`Delete version ${version.version_number}`}
                            >
                              <Trash2 className='h-4 w-4' />
                            </Button>
                          )}
                        </DropdownMenuItem>
                      );
                    })}
                  </DropdownMenuContent>
                </DropdownMenu>
              )}
            </div>

            {/* Version Author Info */}
            {versionToDisplayMeta?.created_by_username && (
              <div className='flex items-center gap-2 text-sm text-muted-foreground'>
                <User className='h-4 w-4' />
                <span>by {versionToDisplayMeta.created_by_username}</span>
              </div>
            )}
          </div>

          {/* Status Section */}
          <div className='flex items-center gap-2'>
            <Select value={status || undefined} onValueChange={onStatusChange}>
              <SelectTrigger className='w-48 bg-white/80 dark:bg-slate-800/80'>
                <SelectValue placeholder='Select status' />
              </SelectTrigger>
              <SelectContent>
                {statusOptions.map((statusValue) => (
                  <SelectItem key={statusValue} value={statusValue}>
                    {renderStatusOption(statusValue)}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Version Notes Section */}
        {versionToDisplayMeta?.version_notes && (
          <div className='mt-2 p-3 bg-white/60 dark:bg-slate-800/60 rounded-lg border border-slate-200/60 dark:border-slate-700/60'>
            <div className='flex items-center gap-2 mb-2'>
              <Info className='h-4 w-4 text-blue-600 dark:text-blue-400' />
              <span className='text-sm font-medium text-slate-700 dark:text-slate-300'>
                Version Notes
              </span>
            </div>
            <p className='text-sm text-slate-600 dark:text-slate-400 leading-relaxed'>
              {versionToDisplayMeta.version_notes}
            </p>
          </div>
        )}
      </div>

      {/* Render the DeleteConfirmationDialog once, controlled by state */}
      {versionToDelete && (
        <DeleteConfirmationDialog
          open={deleteDialogOpen}
          onOpenChange={setDeleteDialogOpen}
          onConfirm={handleConfirmDelete}
          itemName={`version ${versionToDelete.version_number}`}
          triggerComponent={null}
          title='Delete Program Version?'
          confirmButtonVariant='destructive'
        />
      )}
    </div>
  );
};

export default ProgramHeader;
