import React, { useState, useEffect, useCallback } from 'react';
import { Input } from '@/components/ui/input';
import { useDebounce } from '@/hooks/useDebounce';

interface DebouncedInputProps {
  value: string;
  onChange: (value: string) => void;
  delay?: number;
  className?: string;
  placeholder?: string;
  type?: string;
}

/**
 * Debounced input component that delays onChange calls to reduce update frequency
 */
const DebouncedInput: React.FC<DebouncedInputProps> = ({
  value,
  onChange,
  delay = 300,
  className = 'w-full',
  placeholder,
  type = 'text'
}) => {
  const [localValue, setLocalValue] = useState(value);

  // Update local value when prop value changes (e.g., from external updates)
  useEffect(() => {
    setLocalValue(value);
  }, [value]);

  // Debounced onChange handler
  const debouncedOnChange = useDebounce(onChange, delay);

  // Handle input change
  const handleChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setLocalValue(newValue);
    debouncedOnChange(newValue);
  }, [debouncedOnChange]);

  return (
    <Input
      type={type}
      value={localValue}
      onChange={handleChange}
      className={className}
      placeholder={placeholder}
    />
  );
};

export default React.memo(DebouncedInput);
