import React, { ReactNode } from 'react';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { ColumnDefinition } from '../config';
import SubroutineSelector from './SubroutineSelector';
import <PERSON><PERSON>icker<PERSON>ield from './ColorPickerField';
import DebouncedInput from './DebouncedInput';
import DebouncedTextarea from './DebouncedTextarea';

interface StepCellProps {
  column: ColumnDefinition;
  step: any;
  index: number;
  subroutines: any[];
  onUpdateStep: (index: number, field: string, value: any) => void;
}

const StepCell: React.FC<StepCellProps> = ({
  column,
  step,
  index,
  subroutines,
  onUpdateStep
}) => {
  const { key, type: renderType, options, showFor } = column;

  // Special case for program_note: Use description field for roller_action_description column
  if (step.type === 'program_note' && key === 'roller_action_description') {
    return (
      <DebouncedTextarea
        value={step.description || ''}
        onChange={(value) => onUpdateStep(index, 'description', value || '')}
        placeholder='Program note description'
      />
    );
  }

  // Hide field if not applicable for this step type
  if (showFor && !showFor.includes(step.type)) {
    return <span className='text-muted-foreground'>N/A</span>;
  }

  // Hide description field as it's already handled through roller_action_description for program_notes
  if (key === 'description' && step.type === 'program_note') {
    return null;
  }

  switch (renderType) {
    case 'stepNumber':
      return (
        <span className='font-mono text-sm'>{step[key] || index + 1}</span>
      );

    case 'type':
      const triggerClass =
        step[key] === 'subroutine'
          ? 'bg-blue-50 border-blue-200 hover:bg-blue-100 dark:bg-blue-950/40 dark:border-blue-800 dark:hover:bg-blue-900/50 dark:text-blue-300'
          : step[key] === 'program_note'
            ? 'bg-yellow-50 border-yellow-200 hover:bg-yellow-100 dark:bg-yellow-950/40 dark:border-yellow-800 dark:hover:bg-yellow-900/50 dark:text-yellow-300'
            : step[key] === 'comment'
              ? 'bg-purple-50 border-purple-200 hover:bg-purple-100 dark:bg-purple-950/40 dark:border-purple-800 dark:hover:bg-purple-900/50 dark:text-purple-300'
              : 'bg-green-50 border-green-200 hover:bg-green-100 dark:bg-green-950/40 dark:border-green-800 dark:hover:bg-green-900/50 dark:text-green-300';
      return (
        <Select
          value={step[key] || 'action'}
          onValueChange={(value) => onUpdateStep(index, key, value)}
        >
          <SelectTrigger className={`w-full ${triggerClass}`}>
            <SelectValue>{step[key] || 'action'}</SelectValue>
          </SelectTrigger>
          <SelectContent>
            <SelectItem value='action'>action</SelectItem>
            <SelectItem value='subroutine'>subroutine</SelectItem>
            <SelectItem value='program_note'>program_note</SelectItem>
            <SelectItem value='comment'>comment</SelectItem>
          </SelectContent>
        </Select>
      );

    case 'subroutine':
      return (
        <SubroutineSelector
          value={step[key] || ''}
          subroutines={subroutines}
          onChange={(value) => onUpdateStep(index, key, value)}
        />
      );

    case 'color':
      return (
        <ColorPickerField
          value={step[key] || null}
          onChange={(value) => onUpdateStep(index, key, value)}
        />
      );

    case 'text':
      return (
        <DebouncedInput
          value={step[key] || ''}
          onChange={(value) => onUpdateStep(index, key, value || null)}
          className='w-full'
        />
      );

    case 'number':
      return (
        <Input
          type='number'
          value={step[key] ?? ''}
          onChange={(e) =>
            onUpdateStep(
              index,
              key,
              e.target.value ? Number(e.target.value) : null
            )
          }
          className='w-full'
          placeholder='0'
        />
      );

    case 'textarea':
      // Customize placeholder based on step type and field
      let placeholder = '';
      if (key === 'roller_action_description') {
        placeholder =
          step.type === 'action'
            ? 'Roller action description'
            : step.type === 'subroutine'
              ? 'Subroutine description'
              : 'Note description';
      }

      return (
        <DebouncedTextarea
          value={step[key] || ''}
          onChange={(value) => onUpdateStep(index, key, value || '')}
          placeholder={placeholder}
        />
      );

    case 'dropdown':
      return (
        <Select
          value={step[key] ?? options?.[0]?.value ?? ''}
          onValueChange={(value) =>
            onUpdateStep(index, key, value === 'none' ? null : value)
          }
        >
          <SelectTrigger className='w-full'>
            <SelectValue>
              {step[key] ? step[key] : <span className='opacity-0'>·</span>}
            </SelectValue>
          </SelectTrigger>
          <SelectContent>
            {options?.map(
              (opt: { value: any; label: string }): ReactNode => (
                <SelectItem key={opt.value} value={opt.value}>
                  {opt.label}
                </SelectItem>
              )
            )}
          </SelectContent>
        </Select>
      );

    case 'position':
      const position = Array.isArray(step[key]) ? step[key] : [null, null];
      return (
        <div className='flex gap-2'>
          <Input
            type='text'
            value={position[0] || ''}
            onChange={(e) => {
              // Sanitize input to only allow numeric values (including decimals)
              const sanitizedValue = e.target.value.replace(/[^0-9.]/g, '');
              const newPos = [...position];
              newPos[0] = sanitizedValue || null;
              onUpdateStep(index, key, newPos);
            }}
            className='w-20'
          />
          <Input
            type='text'
            value={position[1] || ''}
            onChange={(e) => {
              // Sanitize input to only allow numeric values (including decimals)
              const sanitizedValue = e.target.value.replace(/[^0-9.]/g, '');
              const newPos = [...position];
              newPos[1] = sanitizedValue || null;
              onUpdateStep(index, key, newPos);
            }}
            className='w-20'
          />
        </div>
      );

    default:
      return <span>{step[key]}</span>;
  }
};

export default React.memo(StepCell);
