'use client';
import React, { useMemo, useState } from 'react';
import { convertJsonToCpp } from 'lib/convertToCpp';
import { Button } from '@/components/ui/button';
import { Copy, Download, Check, Languages, Loader2 } from 'lucide-react';
import { toast } from 'sonner';
import Editor from 'react-simple-code-editor';
import { translationService } from '@/services/translation.service';
import Prism from 'prismjs';
import 'prismjs/components/prism-clike';
import 'prismjs/components/prism-c';
import 'prismjs/components/prism-cpp';
import 'prismjs/themes/prism.css';

interface ProgramSubroutine {
  id: number;
  name: string;
  subroutineIdJson: string;
  steps?: any[];
  product_id: number;
  created_at: string;
  updated_at: string;
}

interface CppPreviewProps {
  steps: any[];
  programName: string;
  programId: number;
  overallSettings?: any;
  subRoutines?: ProgramSubroutine[];
}

const CppPreview: React.FC<CppPreviewProps> = ({
  steps,
  programName,
  programId,
  overallSettings,
  subRoutines = []
}) => {
  const initialCode = useMemo(() => {
    try {
      // Map program subRoutines to ensure it matches the Subroutine interface from convertToCpp.ts
      const mappedSubRoutines = (subRoutines || []).map((sr) => {
        // Program subroutines have the full structure including steps
        const idFromJson = sr.subroutineIdJson;
        const nameFromJson = sr.name;
        const stepsFromJson = sr.steps || [];
        
        return {
          name: nameFromJson || `Unnamed Subroutine ${idFromJson || ''}`,
          subroutineIdJson: idFromJson, // This will be used as the key in the map
          steps: Array.isArray(stepsFromJson) ? stepsFromJson : []
        };
      }).filter(sr => sr.subroutineIdJson); // Only include subroutines that have an ID

      // Create the JSON structure expected by convertJsonToCpp
      const programData = {
        id: programId,
        name: programName,
        program_title: programName,
        steps: steps,
        overallSettings: overallSettings || {
          strength: 'MEDIUM',
          position: 'AUTO',
          leg_program: 'AUTO',
          default_ranges: {
            kneading_speed: '1-5',
            tapping_speed: '1-5',
            position_3d: '0-10'
          }
        },
        subRoutines: mappedSubRoutines, // Use the mapped and validated subroutines
        source_folder: '',
        source_filename: '',
        logic_technique: '',
        source_notes: '',
        program_description: null,
        column_mapping_used: null,
        productId: 1,
        categoryId: 1,
        current_version_number: 1
      };

      return convertJsonToCpp(JSON.stringify(programData), programName, programId);
    } catch (error) {
      console.error('Error generating C++ code:', error);
      return `// Error generating C++ code: ${error instanceof Error ? error.message : 'Unknown error'}`;
    }
  }, [steps, programName, programId, overallSettings, subRoutines]);

  const [code, setCode] = useState<string>(initialCode);
  const [copied, setCopied] = useState(false);
  const [isTranslating, setIsTranslating] = useState(false);

  const handleTranslateComments = async () => {
    if (!code || code.trim() === '' || isTranslating) {
      if (!code || code.trim() === '') toast.info('No code to translate.');
      return;
    }

    setIsTranslating(true);
    try {
      const translatedCode = await translationService.translateCppComments(code);
      setCode(translatedCode);
      toast.success('Comments translated successfully!');
    } catch (error) {
      toast.error(`Translation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsTranslating(false);
    }
  };

  React.useEffect(() => {
    setCode(initialCode);
  }, [initialCode]);

  const highlightCode = (code: string) => {
    return Prism.highlight(code, Prism.languages.cpp, 'cpp');
  };

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(code);
      setCopied(true);
      toast.success('C++ code copied to clipboard');
      setTimeout(() => setCopied(false), 2000);
    } catch (error) {
      toast.error('Failed to copy to clipboard');
    }
  };

  const handleDownload = () => {
    const blob = new Blob([code], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${programName.replace(/[^a-zA-Z0-9]/g, '_')}_program.cpp`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    toast.success('C++ file downloaded');
  };

  return (
    <div className="flex flex-col h-full">
      <div className="flex items-center justify-between mb-4">
        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={handleCopy}
            className="flex items-center gap-2"
          >
            {copied ? <Check className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
            {copied ? 'Copied' : 'Copy'}
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={handleDownload}
            className="flex items-center gap-2"
          >
            <Download className="h-4 w-4" />
            Download
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={handleTranslateComments}
            disabled={isTranslating || !code || code.trim() === ''}
            className="flex items-center gap-2"
          >
            {isTranslating ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <Languages className="h-4 w-4" />
            )}
            Translate Comments
          </Button>
        </div>
      </div>
      <div className="rounded-md border bg-muted/30 h-full overflow-hidden">
        <div className="min-h-full w-full overflow-auto flex">
          {/* Line numbers column */}
          <div className="bg-muted/30 text-right pr-2 py-4 text-sm text-muted-foreground select-none">
            {code.split('\n').map((_, i) => (
              <div key={i} className="h-5 leading-5">
                {i + 1}
              </div>
            ))}
          </div>
          
          {/* Editor */}
          <div className="flex-1 min-w-0">
            <Editor
              value={code}
              onValueChange={code => setCode(code)}
              highlight={highlightCode}
              padding={16}
              style={{
                fontFamily: '"Fira code", "Fira Mono", monospace',
                fontSize: 14,
                minWidth: '100%',
                minHeight: '100%'
              }}
              textareaClassName="outline-none min-h-full"
              preClassName="min-h-full"
              className="min-w-full"
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default CppPreview;
