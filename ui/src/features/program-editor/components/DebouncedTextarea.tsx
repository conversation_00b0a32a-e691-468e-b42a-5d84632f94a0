import React, { useState, useEffect, useCallback } from 'react';
import { useDebounce } from '@/hooks/useDebounce';

interface DebouncedTextareaProps {
  value: string;
  onChange: (value: string) => void;
  delay?: number;
  className?: string;
  placeholder?: string;
}

/**
 * Debounced textarea component that delays onChange calls to reduce update frequency
 */
const DebouncedTextarea: React.FC<DebouncedTextareaProps> = ({
  value,
  onChange,
  delay = 300,
  className = 'min-h-[60px] w-full rounded-md border p-1 text-sm',
  placeholder
}) => {
  const [localValue, setLocalValue] = useState(value);

  // Update local value when prop value changes (e.g., from external updates)
  useEffect(() => {
    setLocalValue(value);
  }, [value]);

  // Debounced onChange handler
  const debouncedOnChange = useDebounce(onChange, delay);

  // Handle textarea change
  const handleChange = useCallback((e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newValue = e.target.value;
    setLocalValue(newValue);
    debouncedOnChange(newValue);
  }, [debouncedOnChange]);

  return (
    <textarea
      value={localValue}
      onChange={handleChange}
      className={className}
      placeholder={placeholder}
    />
  );
};

export default React.memo(DebouncedTextarea);
