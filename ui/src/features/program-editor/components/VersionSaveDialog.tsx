import React from 'react';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';

interface VersionSaveDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  versionNotes: string;
  onVersionNotesChange: (notes: string) => void;
  onSaveDecision: (createNewVersion: boolean) => void;
  isSavingVersion: boolean;
  selectedVersionNumber: number | null;
  currentVersionNumber: number;
}

const VersionSaveDialog: React.FC<VersionSaveDialogProps> = ({
  open,
  onOpenChange,
  versionNotes,
  onVersionNotesChange,
  onSaveDecision,
  isSavingVersion,
  selectedVersionNumber,
  currentVersionNumber
}) => {
  return (
    <Dialog
      open={open}
      onOpenChange={(open) => {
        if (!isSavingVersion) {
          onOpenChange(open);
        }
      }}
    >
      <DialogContent className='sm:max-w-md'>
        <DialogHeader>
          <DialogTitle>Save Version</DialogTitle>
          <DialogDescription>
            You are editing version {selectedVersionNumber} while the current
            version is {currentVersionNumber}. How would you like to save your
            changes?
          </DialogDescription>
        </DialogHeader>
        <div className='grid gap-4 py-4'>
          <div className='grid gap-2'>
            <Label htmlFor='version-notes'>Version Notes</Label>
            <Textarea
              id='version-notes'
              placeholder='What changes did you make in this version? (Optional)'
              value={versionNotes}
              onChange={(e) => onVersionNotesChange(e.target.value)}
              rows={3}
              className='resize-none'
            />
          </div>
        </div>
        <DialogFooter className='gap-2 sm:justify-start'>
          <Button
            type='button'
            variant='default'
            onClick={() => onSaveDecision(true)}
            disabled={isSavingVersion}
          >
            {isSavingVersion ? (
              <>
                <div className='mr-2 h-4 w-4 animate-spin rounded-full border-2 border-background border-t-transparent'></div>
                Creating...
              </>
            ) : (
              'Create New Version'
            )}
          </Button>
          <Button
            type='button'
            variant='secondary'
            onClick={() => onOpenChange(false)}
            disabled={isSavingVersion}
          >
            Cancel
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default VersionSaveDialog;
