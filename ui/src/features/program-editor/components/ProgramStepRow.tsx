'use client';
import React, { useState, useCallback, useMemo } from 'react';
import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { TableRow, TableCell } from '@/components/ui/table';
import { But<PERSON> } from '@/components/ui/button';
import { GripVertical, Trash2, StickyNote, Plus } from 'lucide-react';
import { ColumnDefinition, StepType } from '../config';
import StepCell from './StepCell';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter
} from '@/components/ui/dialog';
import { Textarea } from '@/components/ui/textarea';

interface Subroutine {
  id: string | number;
  name: string;
  subroutine_id?: string;
  subroutineIdJson?: string;
}

interface ProgramStepRowProps {
  step: any;
  columns: ColumnDefinition[];
  index: number;
  subroutines: Subroutine[];
  onRemoveStep: (index: number) => void;
  onUpdateStep: (index: number, field: string, value: any) => void;
  onAddStepAtPosition?: (stepType: StepType, position: number, insertBefore?: boolean) => void;
  columnWidths?: Record<string, number>;
}

const ProgramStepRow: React.FC<ProgramStepRowProps> = ({
  step,
  columns,
  index,
  subroutines,
  onRemoveStep,
  onUpdateStep,
  onAddStepAtPosition,
  columnWidths
}) => {
  // State for notes dialog
  const [isNoteDialogOpen, setIsNoteDialogOpen] = useState(false);
  const [noteText, setNoteText] = useState(step.notes || '');

  // Simplified stepId generation - use index as primary key for better performance
  const stepId = `step-${index}`;

  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging
  } = useSortable({ id: stepId });

  // Memoize drag style to prevent object recreation
  const style = useMemo(() => ({
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
    zIndex: isDragging ? 1 : 'auto'
  }), [transform, transition, isDragging]);

  // Memoized callbacks to prevent unnecessary re-renders
  const handleSaveNote = useCallback(() => {
    onUpdateStep(index, 'notes', noteText);
    setIsNoteDialogOpen(false);
  }, [onUpdateStep, index, noteText]);

  const handleRemoveStep = useCallback(() => {
    onRemoveStep(index);
  }, [onRemoveStep, index]);

  const handleOpenNoteDialog = useCallback(() => {
    setNoteText(step.notes || '');
    setIsNoteDialogOpen(true);
  }, [step.notes]);
  // Handle add step at position
  const handleAddStep = useCallback((event: React.MouseEvent) => {
    if (!onAddStepAtPosition) return;

    const insertBefore = event.altKey;
    onAddStepAtPosition('action', index, insertBefore);
  }, [onAddStepAtPosition, index]);





  // Memoize row className to prevent recalculation
  const rowClassName = useMemo(() =>
    `group ${isDragging ? 'shadow-lg' : ''} ${
      step.type === 'program_note'
        ? 'bg-yellow-100/50 hover:bg-yellow-100/70 dark:bg-yellow-950/30 dark:hover:bg-yellow-900/40'
        : step.type === 'subroutine'
          ? 'bg-blue-100/50 hover:bg-blue-100/70 dark:bg-blue-950/30 dark:hover:bg-blue-900/40'
          : step.type === 'comment'
            ? 'bg-purple-100/50 hover:bg-purple-100/70 dark:bg-purple-950/30 dark:hover:bg-purple-900/40'
            : 'hover:bg-muted/30'
    }`,
    [isDragging, step.type]
  );

  return (
    <>
      <TableRow
        ref={setNodeRef}
        style={style}
        className={rowClassName}
      >
        {/* Plus Button and Drag Handle Cell */}
        <TableCell className='w-16 text-center'>
          <div className='flex items-center justify-center gap-1'>
            <button
              onClick={handleAddStep}
              className='flex items-center justify-center text-gray-400 hover:text-gray-600 opacity-0 group-hover:opacity-100 transition-opacity'
              title='Click to add step after, Alt+Click to add step before'
              aria-label='Add step'
            >
              <Plus className='h-4 w-4 stroke-[2.5]' />
            </button>
            <button
              {...attributes}
              {...listeners}
              className='flex items-center justify-center text-gray-400 hover:text-gray-600 cursor-grab opacity-0 group-hover:opacity-100 transition-opacity'
              aria-label='Drag to reorder'
            >
              <GripVertical className='h-4 w-4 stroke-[2.5]' />
            </button>
          </div>
        </TableCell>

        {/* Data Cells */}
        {columns.map((column) => (
          <TableCell
            key={`${stepId}-${column.key}`}
            className='text-center'
            style={{
              width: columnWidths?.[column.key] || column.width || 120,
              maxWidth: columnWidths?.[column.key] || column.width || 120
            }}
          >
            {column.type === 'actions' ? (
              <div className='flex justify-center space-x-1'>
                <Button
                  variant='ghost'
                  size='icon'
                  onClick={handleOpenNoteDialog}
                  className={`${!!step.notes ? 'text-amber-500' : 'opacity-0 group-hover:opacity-100'} transition-opacity hover:text-amber-600`}
                  title={!!step.notes ? 'Edit note' : 'Add note'}
                >
                  <StickyNote className='h-4 w-4' />
                </Button>
                <Button
                  variant='ghost'
                  size='icon'
                  onClick={handleRemoveStep}
                  className='text-destructive opacity-0 transition-opacity hover:text-destructive/90 group-hover:opacity-100'
                >
                  <Trash2 className='h-4 w-4' />
                </Button>
              </div>
            ) : (
              <StepCell
                column={column}
                step={step}
                index={index}
                subroutines={subroutines}
                onUpdateStep={onUpdateStep}
              />
            )}
          </TableCell>
        ))}
      </TableRow>

      {/* Notes Dialog */}
      <Dialog open={isNoteDialogOpen} onOpenChange={setIsNoteDialogOpen}>
        <DialogContent className='sm:max-w-md'>
          <DialogHeader>
            <DialogTitle>
              {step.notes ? 'Edit Step Note' : 'Add Step Note'}
            </DialogTitle>
          </DialogHeader>
          <div className='py-4'>
            <Textarea
              placeholder='Enter notes for this step...'
              value={noteText}
              onChange={(e) => setNoteText(e.target.value)}
              rows={6}
              className='w-full resize-none'
            />
          </div>
          <DialogFooter>
            <Button
              variant='outline'
              onClick={() => setIsNoteDialogOpen(false)}
            >
              Cancel
            </Button>
            <Button onClick={handleSaveNote}>Save</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
};

// Memoize the component to prevent unnecessary re-renders
// Custom comparison function to optimize re-renders
const arePropsEqual = (prevProps: ProgramStepRowProps, nextProps: ProgramStepRowProps) => {
  // Check if step object reference changed or key properties changed
  if (prevProps.step !== nextProps.step) {
    // If step reference changed, check if the actual content changed
    const prevStep = prevProps.step;
    const nextStep = nextProps.step;

    // Compare key properties that affect rendering
    if (prevStep.step_number !== nextStep.step_number ||
        prevStep.type !== nextStep.type ||
        prevStep.notes !== nextStep.notes ||
        JSON.stringify(prevStep) !== JSON.stringify(nextStep)) {
      return false;
    }
  }

  // Check other props
  return (
    prevProps.index === nextProps.index &&
    prevProps.columns === nextProps.columns &&
    prevProps.subroutines === nextProps.subroutines &&
    prevProps.onRemoveStep === nextProps.onRemoveStep &&
    prevProps.onUpdateStep === nextProps.onUpdateStep &&
    prevProps.onAddStepAtPosition === nextProps.onAddStepAtPosition &&
    prevProps.columnWidths === nextProps.columnWidths
  );
};

export default React.memo(ProgramStepRow, arePropsEqual);
