'use client';
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import {
  Popover,
  PopoverContent,
  PopoverTrigger
} from '@/components/ui/popover';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem
} from '@/components/ui/command';
import { ChevronDown } from 'lucide-react';

interface Subroutine {
  id: string | number;
  name: string;
  subroutine_id?: string;
  subroutineIdJson?: string;
}

interface SubroutineSelectorProps {
  value: string;
  subroutines: Subroutine[];
  onChange: (value: string) => void;
}

const SubroutineSelector: React.FC<SubroutineSelectorProps> = ({
  value,
  subroutines,
  onChange
}) => {
  const [open, setOpen] = useState(false);
  const [search, setSearch] = useState('');

  const matchingSubroutine = subroutines.find(
    (sub) =>
      String(sub.subroutineIdJson) === String(value) ||
      String(sub.id) === String(value)
  );

  // Filter subroutines by search
  const filteredSubroutines = search
    ? subroutines.filter(
        (sub) =>
          (sub.name && sub.name.toLowerCase().includes(search.toLowerCase())) ||
          (sub.subroutineIdJson &&
            sub.subroutineIdJson
              .toString()
              .toLowerCase()
              .includes(search.toLowerCase())) ||
          (sub.id &&
            sub.id.toString().toLowerCase().includes(search.toLowerCase()))
      )
    : subroutines;

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant='outline'
          role='combobox'
          aria-expanded={open}
          className='w-full justify-between'
        >
          {matchingSubroutine ? (
            <span className='font-mono text-sm'>
              {matchingSubroutine.subroutineIdJson || matchingSubroutine.id}
            </span>
          ) : (
            <span className='text-muted-foreground'>Select subroutine</span>
          )}
          <ChevronDown className='ml-2 h-4 w-4 shrink-0 opacity-50' />
        </Button>
      </PopoverTrigger>
      <PopoverContent className='w-[300px] p-0' align='start'>
        <Command>
          <CommandInput
            placeholder='Search subroutine...'
            value={search}
            onValueChange={setSearch}
            autoFocus
          />
          <CommandEmpty>No subroutine found.</CommandEmpty>
          <CommandGroup className='slim-scrollbar max-h-[300px] overflow-auto'>
            {filteredSubroutines.map((sub) => (
              <CommandItem
                key={sub.id}
                value={sub.subroutineIdJson || sub.id.toString()}
                onSelect={() => {
                  setOpen(false);
                  setSearch('');
                  onChange(sub.subroutineIdJson || sub.id.toString());
                }}
              >
                <div className='flex flex-col'>
                  <span className='font-mono text-sm'>
                    {sub.subroutineIdJson || sub.id}
                  </span>
                  <span className='max-w-[200px] truncate text-xs text-muted-foreground'>
                    {sub.name}
                  </span>
                </div>
              </CommandItem>
            ))}
          </CommandGroup>
        </Command>
      </PopoverContent>
    </Popover>
  );
};

export default SubroutineSelector;
