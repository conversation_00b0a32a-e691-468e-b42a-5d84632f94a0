'use client';
import React, { useState, useMemo, useCallback } from 'react';
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent
} from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy
} from '@dnd-kit/sortable';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Plus, ChevronDown, Undo, Redo } from 'lucide-react';
import ProgramStepRow from './ProgramStepRow';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
import { fieldConfigMap, ColumnDefinition, StepType } from '../config';
import { Resizable } from 'react-resizable';
import 'react-resizable/css/styles.css';
import './resizable.css';

interface Subroutine {
  id: string | number;
  name: string;
  subroutineIdJson?: string;
}

interface ProgramStepsTableProps {
  steps: any[];
  columnMapping: Record<string, string>;
  subroutines: Subroutine[];
  onAddStep: (stepType?: StepType) => void;
  onAddStepAtPosition?: (stepType: StepType, position: number, insertBefore?: boolean) => void;
  onRemoveStep: (index: number) => void;
  onUpdateStep: (index: number, field: string, value: any) => void;
  onReorderSteps?: (newSteps: any[]) => void;
  onUndo?: () => void;
  onRedo?: () => void;
  canUndo?: boolean;
  canRedo?: boolean;
}

const ProgramStepsTable: React.FC<ProgramStepsTableProps> = ({
  steps,
  columnMapping,
  subroutines,
  onAddStep,
  onAddStepAtPosition,
  onRemoveStep,
  onUpdateStep,
  onReorderSteps,
  onUndo,
  onRedo,
  canUndo = false,
  canRedo = false
}) => {
  // Build columns dynamically using configuration
  const defaultLabels = React.useMemo(() => ({
    step_number: '#',
    type: 'Type',
    subroutine_id: 'Subroutine',
    actions: 'Actions'
  }), []);

  // Determine dynamic fields from backend mapping
  const dynamicFieldKeys = React.useMemo(
    () =>
      Object.keys(columnMapping).filter(
        (key) => !['step_number', 'type', 'subroutine_id'].includes(key)
      ),
    [columnMapping]
  );

  // Compose the complete ordered list of column keys
  const columnKeys: string[] = React.useMemo(
    () => [
      'step_number',
      'type',
      'subroutine_id',
      ...dynamicFieldKeys,
      'actions'
    ],
    [dynamicFieldKeys]
  );

  // Generate ColumnDefinition entries with width properties

  const columns: ColumnDefinition[] = React.useMemo(() => {
    return (
      columnKeys
        .map((key) => {
          const label = columnMapping[key] || (defaultLabels as Record<string, string>)[key] || key;
          const config = fieldConfigMap[key];

          // Always apply a robust fallback config for unknown columns
          if (!config) {
            return {
              key,
              label,
              type: 'text' as ColumnDefinition['type'],
              width: 120,
              minWidth: 60,
              resizable: true
            };
          }

          return {
            key,
            label,
            type: config.type || 'text',
            options: config.options,
            showFor: config.showFor,
            width: typeof config.width === 'number' ? config.width : 120,
            minWidth:
              typeof config.minWidth === 'number' ? config.minWidth : 60,
            resizable: config.resizable !== false
          };
        })
        // Filter out columns with empty showFor arrays
        // FIX: Only filter out columns if showFor is defined and empty (do NOT filter if showFor is undefined)
        .filter((column) => {
          const config = fieldConfigMap[column.key];
          if (config && Array.isArray(config.showFor)) {
            return config.showFor.length !== 0;
          }
          // If showFor is undefined, keep the column (for dynamic columns)
          return true;
        })
    );
  }, [columnKeys, columnMapping, defaultLabels]);

  // Memoize initial column widths to prevent recalculation
  const initialColumnWidths = useMemo(() => {
    const widths: Record<string, number> = {};
    columns.forEach((col) => {
      widths[col.key] = col.width || 120;
    });
    return widths;
  }, [columns]);

  // State for column widths, initialized from the memoized config
  const [columnWidths, setColumnWidths] = useState<Record<string, number>>(initialColumnWidths);

  // Re-initialize columnWidths when columns change (e.g., when columnMapping loads)
  React.useEffect(() => {
    setColumnWidths((prev) => {
      const newWidths: Record<string, number> = { ...prev };
      columns.forEach((col) => {
        if (typeof newWidths[col.key] !== 'number') {
          newWidths[col.key] = col.width || 120;
        }
      });
      // Remove widths for columns that no longer exist
      Object.keys(newWidths).forEach((key) => {
        if (!columns.find((col) => col.key === key)) {
          delete newWidths[key];
        }
      });
      return newWidths;
    });
  }, [columns]);

  // Handle resize for react-resizable
  const onResize =
    (key: string) =>
    (
      e: React.SyntheticEvent,
      { size }: { size: { width: number; height: number } }
    ) => {
      e.preventDefault();

      const column = columns.find((col) => col.key === key);
      if (!column) return;

      // Ensure width is valid and respects minimum width
      if (!isNaN(size.width) && size.width > 0 && isFinite(size.width)) {
        const newWidth = Math.max(column.minWidth, Math.round(size.width));

        setColumnWidths((prev) => ({
          ...prev,
          [key]: newWidth
        }));
      }
    };

  // Set up DnD sensors
  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates
    })
  );

  // Memoize drag end handler to prevent recreation on every render
  const handleDragEnd = useCallback((event: DragEndEvent) => {
    const { active, over } = event;

    if (over && active.id !== over.id) {
      // Extract index from simplified key format (step-{index})
      const extractIndex = (id: string): number => {
        const parts = id.split('-');
        if (parts[0] === 'step' && parts.length === 2) {
          return parseInt(parts[1], 10);
        }
        return -1;
      };

      const oldIndex = extractIndex(active.id.toString());
      const newIndex = extractIndex(over.id.toString());

      if (oldIndex !== -1 && newIndex !== -1 && oldIndex < steps.length && newIndex < steps.length) {
        const newSteps = arrayMove(steps, oldIndex, newIndex);
        if (onReorderSteps) {
          onReorderSteps(newSteps);
        }
      }
    }
  }, [steps, onReorderSteps]);

  // Memoize callback functions to prevent unnecessary re-renders
  const memoizedOnRemoveStep = useCallback((index: number) => {
    onRemoveStep(index);
  }, [onRemoveStep]);

  const memoizedOnAddStepAtPosition = useCallback((stepType: StepType, position: number, insertBefore?: boolean) => {
    if (onAddStepAtPosition) {
      onAddStepAtPosition(stepType, position, insertBefore);
    }
  }, [onAddStepAtPosition]);

  // Memoize stable IDs for sortable context - simplified for better performance
  const stepIds = useMemo(() => {
    return steps.map((_, index) => `step-${index}`);
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [steps.length]); // Only depend on length, not the entire steps array for performance

  // Memoize update step handler to prevent recreation on every render
  const handleUpdateStep = useCallback((index: number, field: string, value: any) => {
    // If changing to program_note type, automatically copy roller_action_description to description
    if (field === 'type' && value === 'program_note') {
      const step = steps[index];

      // Set initial description value from roller_action_description if description is empty
      if (!step.description && step.roller_action_description) {
        onUpdateStep(index, field, value);
        onUpdateStep(index, 'description', step.roller_action_description);
      } else {
        onUpdateStep(index, field, value);
        if (!step.description) {
          onUpdateStep(index, 'description', '');
        }
      }
    }
    // Validate light field to ensure it contains proper RGB values
    else if (field === 'light' && value !== null && typeof value === 'string') {
      // Check if it matches the expected RGB format (e.g., "51, 255, 180")
      const isValidRgb = !value || value.match(/^\d+,\s*\d+,\s*\d+$/);

      if (isValidRgb) {
        // Value is valid, update it directly
        onUpdateStep(index, field, value);
      } else {
        // If invalid format, try to correct it
        const parsed = value
          .replace(/[^\d,\s]/g, '')
          .replace(/,+/g, ',')
          .trim();

        // Make sure we have exactly two commas for RGB format
        const parts = parsed.split(',');
        if (parts.length === 3 && parts.every((part) => part.trim() !== '')) {
          // We have a valid RGB format after parsing
          onUpdateStep(index, field, parsed);
        } else if (parts.length > 0 && parts[0].trim() !== '') {
          // Try to construct a valid RGB value from what we have
          const r = parseInt(parts[0]) || 0;
          const g = parseInt(parts[1]) || 0;
          const b = parseInt(parts[2]) || 0;

          // Ensure values are in valid range (0-255)
          const validR = Math.min(255, Math.max(0, r));
          const validG = Math.min(255, Math.max(0, g));
          const validB = Math.min(255, Math.max(0, b));

          onUpdateStep(index, field, `${validR}, ${validG}, ${validB}`);
        } else {
          // If we can't parse anything useful, set to null
          onUpdateStep(index, field, null);
        }
      }
    } else {
      // Regular update
      onUpdateStep(index, field, value);
    }
  }, [steps, onUpdateStep]);

  // We've removed the useEffect that was causing the infinite update loop
  // The validation now happens only in the handleUpdateStep function

  return (
    <div className='space-y-4'>
      <div className='flex items-center justify-between'>
        <div className='flex gap-2'>
          <Button
            size='sm'
            variant='outline'
            onClick={onUndo}
            disabled={!canUndo}
            title='Undo'
          >
            <Undo className='h-4 w-4' />
          </Button>
          <Button
            size='sm'
            variant='outline'
            onClick={onRedo}
            disabled={!canRedo}
            title='Redo'
          >
            <Redo className='h-4 w-4' />
          </Button>
        </div>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button size='sm' className='gap-1'>
              <Plus className='h-4 w-4' />
              Add Step
              <ChevronDown className='ml-1 h-4 w-4' />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align='end' className='flex flex-col gap-1 p-1'>
            <DropdownMenuItem
              onClick={() => onAddStep('action')}
              className='bg-green-50 text-green-700 hover:bg-green-100 dark:bg-green-950/40 dark:text-green-300 dark:hover:bg-green-900/50'
            >
              action
            </DropdownMenuItem>
            <DropdownMenuItem
              onClick={() => onAddStep('subroutine')}
              className='bg-blue-50 text-blue-700 hover:bg-blue-100 dark:bg-blue-950/40 dark:text-blue-300 dark:hover:bg-blue-900/50'
            >
              subroutine
            </DropdownMenuItem>
            <DropdownMenuItem
              onClick={() => onAddStep('program_note')}
              className='bg-yellow-50 text-yellow-700 hover:bg-yellow-100 dark:bg-yellow-950/40 dark:text-yellow-300 dark:hover:bg-yellow-900/50'
            >
              program_note
            </DropdownMenuItem>
            <DropdownMenuItem
              onClick={() => onAddStep('comment')}
              className='bg-purple-50 text-purple-700 hover:bg-purple-100 dark:bg-purple-950/40 dark:text-purple-300 dark:hover:bg-purple-900/50'
            >
              comment
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      <div className='overflow-x-auto'>
        <div className='rounded-md border'>
          <DndContext
            sensors={sensors}
            collisionDetection={closestCenter}
            onDragEnd={handleDragEnd}
          >
            <Table className='table-fixed'>
              <TableHeader>
                <TableRow className='bg-muted/50'>
                  {/* Add empty header for plus button and drag handle */}
                  <TableHead className='w-16'></TableHead>
                  {columns.map((column) => (
                    <TableHead
                      key={column.key}
                      className='relative border-l px-0 text-center'
                      style={{ width: columnWidths[column.key] }}
                    >
                      {column.resizable ? (
                        <Resizable
                          width={columnWidths[column.key]}
                          height={40}
                          onResize={onResize(column.key)}
                          resizeHandles={['e']}
                          minConstraints={[column.minWidth, 40]}
                          handle={
                            <div className='react-resizable-handle react-resizable-handle-e' />
                          }
                        >
                          <div
                            className='flex h-full w-full items-center justify-center px-2'
                            style={{
                              minHeight: 40,
                              minWidth: columnWidths[column.key],
                              height: '100%',
                              width: '100%',
                              boxSizing: 'border-box'
                            }}
                          >
                            {column.label}
                          </div>
                        </Resizable>
                      ) : (
                        <div className='px-2'>{column.label}</div>
                      )}
                    </TableHead>
                  ))}
                </TableRow>
              </TableHeader>
              <SortableContext
                items={stepIds}
                strategy={verticalListSortingStrategy}
              >
                <TableBody>
                  {steps.length === 0 ? (
                    <TableRow>
                      <TableCell
                        colSpan={columns.length} // +1 for combined plus button and drag handle column
                        className='py-6 text-center text-muted-foreground'
                      >
                        No steps found. Click &quot;Add Step&quot; to create a
                        new step.
                      </TableCell>
                    </TableRow>
                  ) : (
                    steps.map((step, index) => {
                      // Use simplified key for better performance
                      const uniqueKey = `step-${index}`;
                      return (
                        <ProgramStepRow
                          key={uniqueKey}
                          step={step}
                          columns={columns}
                          index={index}
                          subroutines={subroutines}
                          onRemoveStep={memoizedOnRemoveStep}
                          onUpdateStep={handleUpdateStep}
                          onAddStepAtPosition={memoizedOnAddStepAtPosition}
                          columnWidths={columnWidths}
                        />
                      );
                    })
                  )}
                </TableBody>
              </SortableContext>
            </Table>
          </DndContext>
        </div>
      </div>
    </div>
  );
};

export default ProgramStepsTable;
