// Configuration for dynamic program editor columns

// Define the possible step types
export type StepType = 'action' | 'subroutine' | 'program_note' | 'comment';

// Configuration for each field/column
export interface FieldConfig {
  // Determines how the field is rendered
  type:
    | 'stepNumber'   // renders step number badge
    | 'type'         // renders step type selector
    | 'subroutine'   // renders subroutine selector
    | 'actions'      // renders action buttons (delete)
    | 'text'         // simple text input
    | 'number'       // numeric input
    | 'textarea'     // multiline text
    | 'dropdown'     // select from options
    | 'position'     // dual inputs for X/Y
    | 'color';       // color picker for RGB values
  // For dropdown type, list of options
  options?: { value: any; label: string }[];
  // Which step types this field applies to (if omitted, applies to all)
  showFor?: StepType[];
  // Default width in pixels
  width: number;
  // Minimum width for resizing
  minWidth?: number;
  // Whether the column is resizable
  resizable?: boolean;
}

// Column definition passed to the table
export interface ColumnDefinition {
  key: string;
  label: string;
  type: FieldConfig['type'];
  options?: FieldConfig['options'];
  showFor?: FieldConfig['showFor'];
  width: number;
  minWidth: number;
  resizable: boolean;
}

// Default field configurations for known keys
// This map provides rendering hints and per-step-type visibility
export const fieldConfigMap: Record<string, FieldConfig> = {
  // Core columns
  step_number: { type: 'stepNumber', width: 60, minWidth: 40, resizable: true },
  type: { type: 'type', width: 120, minWidth: 80, resizable: true },
  subroutine_id: { type: 'subroutine', showFor: ['subroutine'], width: 110, minWidth: 100, resizable: true },
  actions: { type: 'actions', width: 60, minWidth: 40, resizable: false }, // Actions column is not resizable

  // Action & program_note fields
  roller_action_description: {
    type: 'text',
    showFor: ['action', 'subroutine', 'program_note', 'comment'],
    width: 240,
    minWidth: 120,
    resizable: true
  },
  air_action_description: {
    type: 'text',
    showFor: ['action', 'comment'],
    width: 240,
    minWidth: 120,
    resizable: true
  },
  kneading_speed: {
    type: 'number',
    showFor: ['action', 'comment'],
    width: 80,
    minWidth: 80,
    resizable: true
  },
  tapping_speed: {
    type: 'number',
    showFor: ['action', 'comment'],
    width: 80,
    minWidth: 80,
    resizable: true
  },
  position_3d: {
    type: 'position',
    showFor: ['action', 'comment'],
    width: 180,
    minWidth: 120,
    resizable: true
  },
  width: {
    type: 'dropdown',
    options: [
      { value: 'N', label: 'N (Narrow)' },
      { value: 'M', label: 'M (Medium)' },
      { value: 'W', label: 'W (Wide)' }
    ],
    showFor: ['action'],
    width: 100,
    minWidth: 60,
    resizable: true
  },
  seat_program: {
    type: 'text',
    showFor: ['action', 'subroutine'],
    width: 120,
    minWidth: 80,
    resizable: true
  },
  notes: {
    type: 'text',
    showFor: ['action', 'comment'],
    width: 120,
    minWidth: 80,
    resizable: true
  },
  scent: {
    type: 'dropdown',
    options: [
      { value: 'ON', label: 'ON' },
      { value: 'OFF', label: 'OFF' }
    ],
    showFor: ['action'],
    width: 80,
    minWidth: 60,
    resizable: true
  },
  light: {
    type: 'color',
    showFor: ['action', 'subroutine'],
    width: 120,
    minWidth: 80,
    resizable: true
  },

  // Program note specific field - don't show as separate column
  description: {
    type: 'textarea',
    showFor: [],
    width: 240,
    minWidth: 120,
    resizable: true
  }
};
