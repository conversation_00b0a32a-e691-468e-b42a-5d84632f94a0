'use client';
import React, { useEffect, useCallback } from 'react';
import { useSearchParams } from 'next/navigation';
import ProgramInfoDrawer from './components/ProgramInfoDrawer';
import {
  useGetProgramById,
  useUpdateProgram,
  useListSubroutines,
  useGetProgramVersion,
  useDeleteProgramVersion,
  useUpdateProgramStatus
} from '@/queries/program';
import { toast } from 'sonner';
import { Loader2 } from 'lucide-react';
import ProgramStepsTable from './components/ProgramStepsTable';
import ProgramHeader from './components/ProgramHeader';
import CppPreview from './components/CppPreview';
import { ProgramDescription } from 'types';
import VersionSaveDialog from './components/VersionSaveDialog';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  useProgramEditorStore,
  selectHasActualChangesToSave,
  selectCanUndo,
  selectCanRedo,
  objectsAreEqual
} from './programEditorStore';
import { Table2, Code2 } from 'lucide-react';

const ProgramEditor: React.FC = () => {
  const searchParams = useSearchParams();
  const programIdParam = searchParams.get('id')
    ? parseInt(searchParams.get('id')!, 10)
    : null;

  const {
    programId,
    programName,
    steps,
    columnMapping,
    isDrawerOpen,
    isModified,
    currentVersionNumber,
    selectedVersionNumber,
    versions,
    viewingOlderVersion,
    saveDialogOpen,
    versionNotes,
    isSavingVersion,
    currentUserId,
    setProgramId,
    setCurrentUserId,
    initializeProgramData,
    initializeVersionData,
    resetToCurrentVersion,
    setProgramName: setStoreProgramName,
    setProgramInfo: setStoreProgramInfo,
    addStep,
    addStepAtPosition,
    removeStep,
    updateStep,
    reorderSteps,
    handleUndo,
    handleRedo,
    setIsModified,
    setIsDrawerOpen,
    openSaveDialog,
    closeSaveDialog,
    setVersionNotes,
    setIsSavingVersion,
    setSelectedVersionNumber: setStoreSelectedVersionNumber,
    handleSuccessfulSave,
    handleFailedSave,
    resetProgramStateForIdChange
  } = useProgramEditorStore();

  const hasActualChanges = useProgramEditorStore(selectHasActualChangesToSave);
  const canUndo = useProgramEditorStore(selectCanUndo);
  const canRedo = useProgramEditorStore(selectCanRedo);

  useEffect(() => {
    if (programIdParam !== programId) {
      // Reset state when program ID changes
      if (typeof resetProgramStateForIdChange === 'function') {
        resetProgramStateForIdChange();
      }
    }
  }, [programIdParam, programId, resetProgramStateForIdChange]);

  useEffect(() => {
    setCurrentUserId(1);
  }, [setCurrentUserId]);

  const {
    data: programData,
    isLoading: isProgramLoading,
    error: programError,
    refetch: refetchProgramData
  } = useGetProgramById(programIdParam || 0, {
    enabled: !!programIdParam
  });

  // Trigger refetch when URL parameter changes
  useEffect(() => {
    if (programIdParam) {
      refetchProgramData();
    }
  }, [programIdParam, refetchProgramData]);

  useEffect(() => {
    if (
      programData &&
      programData.id === programIdParam &&
      programIdParam !== null
    ) {
      // Set program ID and initialize data when API response matches URL parameter
      setProgramId(programIdParam);

      const fetchedProgramDataWithTypedDescription = {
        ...programData,
        program_description:
          programData.program_description === null
            ? undefined
            : programData.program_description
      };
      initializeProgramData(fetchedProgramDataWithTypedDescription);
    }
  }, [programData, programIdParam, setProgramId, initializeProgramData]);

  const {
    data: versionData,
    isLoading: isVersionLoading,
    error: versionError
  } = useGetProgramVersion(programId || 0, selectedVersionNumber || 0, {
    enabled:
      !!programId &&
      !!selectedVersionNumber &&
      selectedVersionNumber !== currentVersionNumber
  });

  useEffect(() => {
    if (
      versionData &&
      selectedVersionNumber &&
      programId === versionData.program_id
    ) {
      initializeVersionData(versionData);
    }
  }, [versionData, selectedVersionNumber, programId, initializeVersionData]);

  const {
    data: subroutineResponse,
    isLoading: isSubroutineLoading,
    error: subroutineError
  } = useListSubroutines({ limit: 500 });

  const updateProgramMutation = useUpdateProgram({
    onSuccess: (data) => {
      toast.success('Program saved successfully');
      handleSuccessfulSave(data);
      refetchProgramData();
    },
    onError: (error) => {
      console.error('Save error:', error);
      toast.error(`Failed to save program: ${error.message}`);
      handleFailedSave();
    }
  });

  const deleteProgramVersionMutation = useDeleteProgramVersion({
    onSuccess: () => {
      toast.success('Program version deleted successfully');
      refetchProgramData();
    },
    onError: (error) => {
      toast.error(`Failed to delete program version: ${error.message}`);
    }
  });

  const updateProgramStatusMutation = useUpdateProgramStatus({
    onSuccess: () => {
      toast.success('Program status updated successfully');
      refetchProgramData();
    },
    onError: (error) => {
      toast.error(`Failed to update program status: ${error.message}`);
    }
  });

  const handleVersionChange = useCallback(
    (versionNumber: number) => {
      if (isModified && hasActualChanges) {
        toast.warning(
          'Please save or discard your changes before switching versions.'
        );
        return;
      }

      if (versionNumber === currentVersionNumber) {
        setStoreSelectedVersionNumber(null);
        if (programData?.steps) {
          resetToCurrentVersion(programData.steps);
        }
      } else {
        setStoreSelectedVersionNumber(versionNumber);
      }
    },
    [
      isModified,
      hasActualChanges,
      currentVersionNumber,
      programData,
      resetToCurrentVersion,
      setStoreSelectedVersionNumber
    ]
  );

  const saveProgram = useCallback(
    (
      _?: any,
      infoFromDrawer?: {
        logic_technique?: string;
        program_description?: ProgramDescription;
      }
    ) => {
      const currentStoreState = useProgramEditorStore.getState();
      if (!currentStoreState.programId) {
        toast.error('No program ID provided');
        return;
      }

      if (
        currentStoreState.viewingOlderVersion &&
        !currentStoreState.isSavingVersion
      ) {
        openSaveDialog();
        return;
      }

      const effectiveProgramInfo =
        infoFromDrawer || currentStoreState.programInfo;

      const nameActuallyChanged =
        currentStoreState.programName !== currentStoreState.initialProgramName;
      const stepsActuallyChanged = !objectsAreEqual(
        currentStoreState.steps,
        currentStoreState.initialSteps
      );
      const logicTechniqueActuallyChanged =
        currentStoreState.initialProgramInfo.logic_technique !==
        effectiveProgramInfo.logic_technique;
      const descriptionActuallyChanged = !objectsAreEqual(
        currentStoreState.initialProgramInfo.program_description,
        effectiveProgramInfo.program_description
      );

      const anythingActuallyChanged =
        nameActuallyChanged ||
        stepsActuallyChanged ||
        logicTechniqueActuallyChanged ||
        descriptionActuallyChanged;

      if (!anythingActuallyChanged && !infoFromDrawer) {
        toast.info('No changes to save.');
        if (currentStoreState.isModified) setIsModified(false);
        return;
      }

      if (anythingActuallyChanged && !currentStoreState.isModified) {
        setIsModified(true);
      }

      const payload: any = {
        name: currentStoreState.programName,
        logic_technique: effectiveProgramInfo.logic_technique,
        program_description: effectiveProgramInfo.program_description
      };

      let saveActionToBackend: 'update_only' | 'new';

      if (
        nameActuallyChanged &&
        !stepsActuallyChanged &&
        !logicTechniqueActuallyChanged &&
        !descriptionActuallyChanged
      ) {
        saveActionToBackend = 'update_only';
      } else {
        saveActionToBackend = 'new';
        payload.steps = currentStoreState.steps;
        payload.version_notes = currentStoreState.versionNotes;
      }

      payload.save_action = saveActionToBackend;

      updateProgramMutation.mutate({
        programId: currentStoreState.programId,
        programData: payload
      });
    },
    [openSaveDialog, setIsModified, updateProgramMutation]
  );

  const handleSaveDecision = useCallback(
    (createNewVersion: boolean) => {
      const currentStoreState = useProgramEditorStore.getState();
      if (!currentStoreState.programId) return;

      setIsSavingVersion(true);

      updateProgramMutation.mutate({
        programId: currentStoreState.programId,
        programData: {
          steps: currentStoreState.steps,
          name: currentStoreState.programName,
          ...(currentStoreState.programInfo.logic_technique !== undefined && {
            logic_technique: currentStoreState.programInfo.logic_technique
          }),
          ...(currentStoreState.programInfo.program_description !==
            undefined && {
            program_description:
              currentStoreState.programInfo.program_description
          }),
          version_notes: currentStoreState.versionNotes,
          save_action: createNewVersion ? 'new' : 'overwrite'
        }
      });
    },
    [updateProgramMutation, setIsSavingVersion]
  );

  const handleProgramInfoUpdate = useCallback(
    (updatedInfo: {
      logic_technique?: string;
      program_description?: ProgramDescription;
    }) => {
      setStoreProgramInfo(updatedInfo);
      saveProgram(undefined, updatedInfo);
    },
    [setStoreProgramInfo, saveProgram]
  );

  const handleNameChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      setStoreProgramName(e.target.value);
    },
    [setStoreProgramName]
  );

  const handleDeleteVersion = useCallback(
    (versionNumber: number) => {
      if (!programId) {
        toast.error('Program ID is missing.');
        return;
      }
      deleteProgramVersionMutation.mutate({ programId, versionNumber });
    },
    [programId, deleteProgramVersionMutation]
  );

  const handleStatusChange = useCallback(
    (status: string) => {
      if (!programId) {
        toast.error('Program ID is missing.');
        return;
      }
      updateProgramStatusMutation.mutate({ programId, status });
    },
    [programId, updateProgramStatusMutation]
  );

  if (
    isProgramLoading ||
    (programId && !programData && !programError) ||
    isSubroutineLoading ||
    (isVersionLoading && selectedVersionNumber !== null)
  ) {
    return (
      <div className='flex h-64 items-center justify-center'>
        <Loader2 className='h-8 w-8 animate-spin text-primary' />
        <span className='ml-2'>Loading program data...</span>
      </div>
    );
  }

  if (programError && programId) {
    return <div>Error loading program: {programError.message}</div>;
  }
  if (subroutineError) {
    return <div>Error loading subroutines: {subroutineError.message}</div>;
  }
  if (versionError && selectedVersionNumber !== null) {
    return <div>Error loading program version: {versionError.message}</div>;
  }

  if (!programId && !isProgramLoading) {
    return (
      <div className='flex h-64 items-center justify-center'>
        <span className='text-muted-foreground'>Select a program to edit.</span>
      </div>
    );
  }

  return (
    <>
      <div className='flex w-full flex-col'>
        <ProgramHeader
          programName={programName}
          handleNameChange={handleNameChange}
          saveProgram={saveProgram}
          isModified={isModified && hasActualChanges}
          isSaving={updateProgramMutation.isPending || isSavingVersion}
          productName={programData?.product_name || ''}
          productModel={programData?.product_model || ''}
          categoryName={programData?.category_name || ''}
          isDrawerOpen={isDrawerOpen}
          setIsDrawerOpen={setIsDrawerOpen}
          currentVersionNumber={currentVersionNumber}
          versions={versions}
          selectedVersionNumber={selectedVersionNumber}
          onVersionChange={handleVersionChange}
          viewingOlderVersion={viewingOlderVersion}
          programId={programId ?? 0}
          currentUserId={currentUserId}
          onDeleteVersion={handleDeleteVersion}
          isDeletingVersion={deleteProgramVersionMutation.isPending}
          status={programData?.status || null}
          onStatusChange={handleStatusChange}
        />
        <div className='mt-6'>
          {viewingOlderVersion && (
            <div className='mb-4 rounded-md border border-amber-200 bg-amber-50/30 p-3 text-amber-800 dark:border-amber-800/30 dark:bg-amber-900/10 dark:text-amber-400'>
              <p className='text-sm font-medium'>
                You are viewing version {selectedVersionNumber}. The current
                active version is {currentVersionNumber}.{' '}
                {isModified && hasActualChanges ? (
                  <span className='font-bold'>
                    You have made changes to this older version. When you save,
                    you will be prompted to create a new version.
                  </span>
                ) : (
                  <span>
                    Any changes you make will create a new version when saved.
                  </span>
                )}
              </p>
            </div>
          )}
          <Tabs defaultValue="table" className="w-full">
            <TabsList className="inline-flex h-10 items-center justify-start rounded-md bg-transparent p-0 mb-4">
              <TabsTrigger className="rounded-l-md data-[state=active]:bg-primary data-[state=active]:text-primary-foreground" value="table">
                <Table2 className="h-4 w-4 mr-2" />
                Table View
              </TabsTrigger>
              <TabsTrigger className="rounded-r-md data-[state=active]:bg-primary data-[state=active]:text-primary-foreground" value="cpp">
                <Code2 className="h-4 w-4 mr-2" />
                C++ Preview
              </TabsTrigger>
            </TabsList>
            <TabsContent value="table" className="mt-2">
              <div className='thin-scrollbar overflow-x-auto'>
                <ProgramStepsTable
                  steps={steps}
                  columnMapping={columnMapping}
                  subroutines={subroutineResponse?.subroutines || []} // Full list for dropdown selection
                  onAddStep={addStep}
                  onAddStepAtPosition={addStepAtPosition}
                  onRemoveStep={removeStep}
                  onUpdateStep={updateStep}
                  onReorderSteps={reorderSteps}
                  onUndo={handleUndo}
                  onRedo={handleRedo}
                  canUndo={canUndo}
                  canRedo={canRedo}
                />
              </div>
            </TabsContent>
            <TabsContent value="cpp" className="mt-2">
              <CppPreview
                steps={steps}
                programName={programName}
                programId={programId ?? 0}
                overallSettings={programData?.overall_settings}
                subRoutines={programData?.subRoutines || []} // Program's associated subroutines with steps
              />
            </TabsContent>
          </Tabs>
        </div>
        <ProgramInfoDrawer
          open={isDrawerOpen}
          onOpenChange={setIsDrawerOpen}
          programData={programData}
          onSave={handleProgramInfoUpdate}
          isSaving={updateProgramMutation.isPending || isSavingVersion}
        />
        <VersionSaveDialog
          open={saveDialogOpen}
          onOpenChange={(open) => {
            if (!isSavingVersion) {
              if (open) openSaveDialog();
              else closeSaveDialog();
            }
          }}
          versionNotes={versionNotes}
          onVersionNotesChange={setVersionNotes}
          onSaveDecision={handleSaveDecision}
          isSavingVersion={isSavingVersion}
          selectedVersionNumber={selectedVersionNumber}
          currentVersionNumber={currentVersionNumber}
        />
      </div>
    </>
  );
};

export default ProgramEditor;
