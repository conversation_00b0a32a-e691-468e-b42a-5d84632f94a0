'use client';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  LucideMessageCircleReply,
  Search,
  X,
  ArrowUpDown,
  ArrowUp,
  ArrowDown,
  Star
} from 'lucide-react';
import { DataTable } from '@/components/ui/table/data-table';
import { ColumnDef } from '@tanstack/react-table';
import { Thread } from 'types';
import { useRouter } from 'next/navigation';
import { Row } from '@tanstack/react-table';
import { useListThreads, useStarChat } from '@/queries/chat';
import { useEffect, useState } from 'react';
import moment from 'moment';
import { useDebounce } from '@/hooks/use-debounce';
import { motion, AnimatePresence } from 'framer-motion';
import { toast } from 'sonner';

export default function HistoryTable() {
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(20);
  const [searchTerm, setSearchTerm] = useState('');
  const [isSearching, setIsSearching] = useState(false);
  const [sortField, setSortField] = useState<string>('updated_at');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');
  const [starredOnly, setStarredOnly] = useState(false);

  const debouncedSearchTerm = useDebounce(searchTerm, 300);

  const { data, isLoading, isFetching, refetch } = useListThreads({
    skip: (page - 1) * pageSize,
    limit: pageSize,
    search: debouncedSearchTerm,
    sortField,
    sortDirection,
    starredOnly
  });

  const { mutate: starChat } = useStarChat({
    onError: () => {
      toast.error('Failed to update star status');
    }
  });

  useEffect(() => {
    setPage(1);
  }, [debouncedSearchTerm, sortField, sortDirection, starredOnly]);

  useEffect(() => {
    setIsSearching(isFetching);
  }, [isFetching]);

  useEffect(() => {
    refetch();
  }, [refetch]); // Consider if refetch is needed here or if query key changes are enough

  const threads = data?.threads || [];
  const totalPages = data?.total ? Math.ceil(data.total / pageSize) : 0;

  const handlePageSizeChange = (newPageSize: number) => {
    setPageSize(newPageSize);
    setPage(1);
  };

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
  };

  const clearSearch = () => {
    setSearchTerm('');
  };

  const handleSort = (field: string) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  const renderSortIndicator = (field: string) => {
    if (sortField !== field) {
      return <ArrowUpDown className='ml-2 h-4 w-4' />;
    }
    return sortDirection === 'asc' ? (
      <ArrowUp className='ml-2 h-4 w-4' />
    ) : (
      <ArrowDown className='ml-2 h-4 w-4' />
    );
  };

  const router = useRouter();

  const columns: ColumnDef<Thread>[] = [
    {
      id: 'star',
      header: '',
      cell: ({ row }: { row: Row<Thread> }) => (
        <Button
          variant="ghost"
          size="sm"
          onClick={(e) => {
            e.stopPropagation();
            starChat(row.original.id);
          }}
          className="h-8 w-8 p-0"
        >
          {row.original.is_starred ? (
            <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
          ) : (
            <Star className="h-4 w-4 text-gray-400 hover:text-yellow-400" />
          )}
        </Button>
      ),
      enableSorting: false,
      enableHiding: false
    },
    {
      accessorKey: 'config_name',
      header: () => (
        <div
          className='flex cursor-pointer items-center'
          onClick={() => handleSort('config_name')}
        >
          Config Name
          {renderSortIndicator('config_name')}
        </div>
      ),
      cell: ({ row }: { row: Row<Thread> }) => (
        <div>{row.getValue('config_name')}</div>
      )
    },
    {
      accessorKey: 'first_message',
      header: () => (
        <div
          className='flex cursor-pointer items-center'
          onClick={() => handleSort('first_message')}
        >
          First Message
          {renderSortIndicator('first_message')}
        </div>
      ),
      cell: ({ row }: { row: Row<Thread> }) => (
        <div className='max-w-[350px] truncate'>
          {row.getValue('first_message')}
        </div>
      )
    },
    {
      accessorKey: 'created_at',
      header: () => (
        <div
          className='flex cursor-pointer items-center'
          onClick={() => handleSort('created_at')}
        >
          Created
          {renderSortIndicator('created_at')}
        </div>
      ),
      cell: ({ row }: { row: Row<Thread> }) => (
        <div
          title={moment.utc(row.getValue('created_at')).local().format('LLLL')}
        >
          {moment.utc(row.getValue('created_at')).local().fromNow()}
        </div>
      )
    },
    {
      accessorKey: 'updated_at',
      header: () => (
        <div
          className='flex cursor-pointer items-center'
          onClick={() => handleSort('updated_at')}
        >
          Last Updated
          {renderSortIndicator('updated_at')}
        </div>
      ),
      cell: ({ row }: { row: Row<Thread> }) => (
        <div
          title={moment.utc(row.getValue('updated_at')).local().format('LLLL')}
        >
          {moment.utc(row.getValue('updated_at')).local().fromNow()}
        </div>
      )
    },
    {
      id: 'actions',
      header: 'Actions',
      cell: ({ row }: { row: Row<Thread> }) => (
        <Button
          onClick={() =>
            router.push(
              `/dashboard/chat?config_id=${row.original.config_id}&thread_id=${row.original.slug}`
            )
          }
        >
          <span className='mr-2 text-sm'>Chat</span>
          <LucideMessageCircleReply className='h-4 w-4' />
        </Button>
      ),
      enableSorting: false,
      enableHiding: false
    }
  ];

  return (
    <>
      <div className='mb-2 flex flex-wrap items-center justify-between gap-x-4 space-y-2'>
        <div>
          <h2 className='text-2xl font-bold tracking-tight'>Chat History</h2>
          <p className='text-muted-foreground'>
            Here&apos;s a list of your chat history
          </p>
        </div>
        <div className='flex items-center space-x-2'>
          <div className='relative w-full max-w-sm'>
            <div className='pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3'>
              <Search className='h-4 w-4 text-gray-400' />
            </div>
            <Input
              type='text'
              placeholder='Search in messages...'
              value={searchTerm}
              onChange={handleSearchChange}
              className='pl-10 pr-10'
            />
            <AnimatePresence>
              {searchTerm && (
                <motion.button
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  exit={{ opacity: 0 }}
                  onClick={clearSearch}
                  className='absolute inset-y-0 right-0 flex items-center pr-3'
                >
                  <X className='h-4 w-4 text-gray-400 hover:text-gray-600' />
                </motion.button>
              )}
            </AnimatePresence>
          </div>

          <Button
            variant={starredOnly ? "default" : "outline"}
            size="sm"
            onClick={() => {
              setStarredOnly(!starredOnly);
              setPage(1); // Reset to first page when filtering
            }}
            className="flex items-center space-x-1"
          >
            <Star className={`h-4 w-4 ${starredOnly ? 'fill-current' : ''}`} />
            <span>Starred</span>
          </Button>
        </div>
      </div>

      <AnimatePresence>
        {isSearching && !isLoading && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className='mb-2 text-sm text-gray-500'
          >
            Loading...
          </motion.div>
        )}
      </AnimatePresence>

      <div className='-mx-4 flex-1 overflow-auto px-4 py-1 lg:flex-row lg:space-x-12 lg:space-y-0'>
        <DataTable
          data={threads}
          columns={columns}
          isLoading={isLoading}
          pagination={{
            page,
            pageCount: totalPages,
            pageSize,
            onPageChange: setPage,
            onPageSizeChange: handlePageSizeChange
          }}
        />

        <AnimatePresence>
          {threads.length === 0 && !isLoading && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className='mt-4 text-center text-gray-500'
            >
              {searchTerm
                ? 'No chat history found matching your search.'
                : starredOnly
                ? 'No starred chats found.'
                : 'No chat history available.'}
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </>
  );
}
