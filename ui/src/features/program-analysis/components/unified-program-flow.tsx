'use client';

import { useState } from 'react';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import {
  ChevronDown,
  ChevronRight,
  Activity,
  Play,
  ArrowUp,
  ArrowDown,
  MoveRight
} from 'lucide-react';

import { Badge } from '@/components/ui/badge';
import { groupStepsByType, STEP_TYPE_CONFIG } from '@/utils/step-type-classifier';
import { ProgramFlowView } from './program-flow-view';
import type { ProgramSegmentationResult, ProgramSegment } from '@/utils/program-expander';

interface UnifiedProgramFlowProps {
  segmentationResult: ProgramSegmentationResult;
}

export function UnifiedProgramFlow({ segmentationResult }: UnifiedProgramFlowProps) {
  const [expandedSegments, setExpandedSegments] = useState<Set<string>>(new Set());

  const toggleSegmentExpansion = (segmentId: string) => {
    const newExpanded = new Set(expandedSegments);
    if (newExpanded.has(segmentId)) {
      newExpanded.delete(segmentId);
    } else {
      newExpanded.add(segmentId);
    }
    setExpandedSegments(newExpanded);
  };

  const renderBodyPartSequence = (segment: ProgramSegment) => {
    const isExpanded = expandedSegments.has(segment.id);
    const bodyPartGroups = segment.bodyPartAnalysis?.bodyPartGroups || [];
    
    return (
      <Card key={segment.id} className="border-l-4 border-l-blue-400">
        <Collapsible open={isExpanded} onOpenChange={() => toggleSegmentExpansion(segment.id)}>
          <CollapsibleTrigger asChild>
            <CardHeader className="cursor-pointer hover:bg-muted/30 transition-colors py-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  {isExpanded ? (
                    <ChevronDown className="h-4 w-4 text-muted-foreground" />
                  ) : (
                    <ChevronRight className="h-4 w-4 text-muted-foreground" />
                  )}
                  <Activity className="h-4 w-4 text-blue-600" />
                  <div>
                    <div className="text-sm font-medium">
                      Body Part Sequence
                    </div>
                    <div className="text-xs text-muted-foreground">
                      Steps {segment.startStepNumber}-{segment.endStepNumber} • {segment.stepCount} steps
                    </div>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <Badge variant="outline" className="text-xs bg-blue-50 text-blue-700 border-blue-200">
                    {bodyPartGroups.length} body parts
                  </Badge>
                </div>
              </div>
            </CardHeader>
          </CollapsibleTrigger>
          <CollapsibleContent>
            <CardContent className="pt-0 pb-4">
              <div className="border-t pt-4">
                {segment.bodyPartAnalysis && (
                  <ProgramFlowView classificationResult={segment.bodyPartAnalysis} />
                )}
              </div>
            </CardContent>
          </CollapsibleContent>
        </Collapsible>
      </Card>
    );
  };

  const renderSubroutine = (segment: ProgramSegment) => {
    const isExpanded = expandedSegments.has(segment.id);
    const bodyPartGroups = segment.bodyPartAnalysis?.bodyPartGroups || [];
    const stepTypeResult = groupStepsByType(segment.steps);
    
    // Get top techniques for preview
    const topTechniques = Object.entries(stepTypeResult.techniqueDistribution)
      .filter(([type, count]) => count > 0 && type !== 'unknown')
      .sort(([,a], [,b]) => b - a)
      .slice(0, 3);
    
    return (
      <Card key={segment.id} className="border-l-4 border-l-purple-400 bg-purple-50/30">
        <Collapsible open={isExpanded} onOpenChange={() => toggleSegmentExpansion(segment.id)}>
          <CollapsibleTrigger asChild>
            <CardHeader className="cursor-pointer hover:bg-muted/30 transition-colors py-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  {isExpanded ? (
                    <ChevronDown className="h-4 w-4 text-muted-foreground" />
                  ) : (
                    <ChevronRight className="h-4 w-4 text-muted-foreground" />
                  )}
                  <Play className="h-4 w-4 text-purple-600" />
                  <div className="flex-1">
                    <div className="text-sm font-medium text-purple-800">
                      {segment.subroutineName}
                    </div>
                    <div className="text-xs text-muted-foreground mb-2">
                      Step {segment.startStepNumber} • {segment.stepCount} steps
                    </div>
                    {/* Technique Preview */}
                    <div className="flex flex-wrap gap-1">
                      {topTechniques.map(([type, count]) => {
                        const config = STEP_TYPE_CONFIG[type as keyof typeof STEP_TYPE_CONFIG];
                        return (
                          <Badge 
                            key={type} 
                            variant="secondary" 
                            className="text-xs px-1.5 py-0.5 bg-purple-100 text-purple-700 border-purple-200" 
                            title={`${config.displayName}: ${count} steps`}
                          >
                            <span className="mr-0.5">{config.icon}</span>
                            {count}
                          </Badge>
                        );
                      })}
                    </div>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <Badge variant="outline" className="text-xs bg-purple-100 text-purple-700 border-purple-300">
                    {segment.subroutineId}
                  </Badge>
                  <Badge variant="outline" className="text-xs bg-purple-50 text-purple-600 border-purple-200">
                    {bodyPartGroups.length} body parts
                  </Badge>
                </div>
              </div>
            </CardHeader>
          </CollapsibleTrigger>
          <CollapsibleContent>
            <CardContent className="pt-0 pb-4">
              <div className="border-t pt-4">
                {segment.bodyPartAnalysis && (
                  <ProgramFlowView classificationResult={segment.bodyPartAnalysis} />
                )}
              </div>
            </CardContent>
          </CollapsibleContent>
        </Collapsible>
      </Card>
    );
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Activity className="h-5 w-5 text-blue-600" />
            <h3 className="text-lg font-semibold">Program Flow</h3>
          </div>
          <div className="flex items-center gap-2">
            <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
              {segmentationResult.bodyPartCount} body part sequences
            </Badge>
            <Badge variant="outline" className="bg-purple-50 text-purple-700 border-purple-200">
              {segmentationResult.subroutineCount} subroutines
            </Badge>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          {segmentationResult.segments.map((segment) => {
            if (segment.type === 'body_part_sequence') {
              return renderBodyPartSequence(segment);
            } else {
              return renderSubroutine(segment);
            }
          })}
        </div>
      </CardContent>
    </Card>
  );
}
