'use client';

import { useState } from 'react';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import {
  ChevronDown,
  ChevronRight,
  Activity,
  ArrowUp,
  ArrowDown,
  MoveRight,
  Play,
  Square
} from 'lucide-react';

import { ProgramStepDetails } from './program-step-details';
import { Badge } from '@/components/ui/badge';
import { groupStepsByType, STEP_TYPE_CONFIG } from '@/utils/step-type-classifier';
import { getSubroutineInfo, isSubroutineBoundary } from '@/utils/program-expander';
import type { ClassificationResult, FlowGroup } from '@/utils/body-part-classifier';

interface ProgramFlowViewProps {
  classificationResult: ClassificationResult;
}

const FlowIndicator = ({
  direction,
  isFirst,
  isLast
}: {
  direction: FlowGroup['direction'];
  isFirst: boolean;
  isLast: boolean;
}) => {
  if (direction === 'end') return null;

  const getDirectionConfig = () => {
    switch (direction) {
      case 'down':
        return {
          icon: <ArrowDown className="h-3 w-3" />,
          color: 'text-teal-600',
          lineColor: 'border-teal-300'
        };
      case 'up':
        return {
          icon: <ArrowUp className="h-3 w-3" />,
          color: 'text-amber-600',
          lineColor: 'border-amber-300'
        };
      case 'same':
        return {
          icon: <MoveRight className="h-3 w-3" />,
          color: 'text-blue-600',
          lineColor: 'border-blue-300'
        };
      default:
        return {
          icon: null,
          color: 'text-muted-foreground',
          lineColor: 'border-muted'
        };
    }
  };

  const config = getDirectionConfig();

  return (
    <div className="absolute left-3 top-0 bottom-0 flex flex-col items-center">
      {/* Vertical line spanning the entire flow group */}
      <div className={`w-0.5 flex-1 border-l-2 ${config.lineColor}`} />
      
      {/* Direction indicator positioned in the middle of the flow group */}
      {!isLast && (
        <div className="absolute top-1/2 transform -translate-y-1/2 bg-background rounded-full p-1.5 border shadow-sm">
          <div className={config.color}>
            {config.icon}
          </div>
        </div>
      )}
    </div>
  );
};

export function ProgramFlowView({ classificationResult }: ProgramFlowViewProps) {
  const [expandedGroups, setExpandedGroups] = useState<Set<number>>(new Set());

  const toggleGroupExpansion = (groupIndex: number) => {
    const newExpanded = new Set(expandedGroups);
    if (newExpanded.has(groupIndex)) {
      newExpanded.delete(groupIndex);
    } else {
      newExpanded.add(groupIndex);
    }
    setExpandedGroups(newExpanded);
  };

  const { flowGroups } = classificationResult;

  if (flowGroups.length === 0) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-12">
          <div className="text-center">
            <Activity className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">No Program Groups</h3>
            <p className="text-muted-foreground">
              No program groups could be identified from the program steps.
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader className="pb-3">
        <div className="flex items-center gap-2">
          <div className="p-1 bg-emerald-50 rounded border border-emerald-200">
            <Activity className="h-3 w-3 text-emerald-600" />
          </div>
          <div>
            <h3 className="text-sm font-medium">Program Flow</h3>
            <p className="text-xs text-muted-foreground">
              Visual timeline showing focus on different body parts
            </p>
          </div>
        </div>
      </CardHeader>
      <CardContent>

        <div className="space-y-4">
          {flowGroups.map((flowGroup, flowIndex) => (
            <div key={flowIndex} className="relative pl-8">
              {/* Flow indicator that spans the entire group */}
              <FlowIndicator
                direction={flowGroup.direction}
                isFirst={flowIndex === 0}
                isLast={flowIndex === flowGroups.length - 1}
              />
              
              {/* Flow group header */}
              {flowGroup.direction !== 'end' && (
                <div className="mb-3 pl-2">
                  <div className="flex items-center gap-2 text-xs">
                    <div className={`w-1.5 h-1.5 rounded-full ${
                      flowGroup.direction === 'down' ? 'bg-teal-500' :
                      flowGroup.direction === 'up' ? 'bg-amber-500' : 'bg-blue-500'
                    }`} />
                    <span className={`font-medium ${
                      flowGroup.direction === 'down' ? 'text-teal-700' :
                      flowGroup.direction === 'up' ? 'text-amber-700' : 'text-blue-700'
                    }`}>
                      {flowGroup.direction === 'down' ? 'Moving Down' :
                       flowGroup.direction === 'up' ? 'Moving Up' : 'Same Level'}
                    </span>
                    <Badge variant="outline" className="text-xs">
                      {flowGroup.bodyPartGroups.length} group{flowGroup.bodyPartGroups.length !== 1 ? 's' : ''}
                    </Badge>
                  </div>
                </div>
              )}
            
              <div className="space-y-2">
                {flowGroup.bodyPartGroups.map((group, groupIndex) => {
                  const globalIndex = classificationResult.bodyPartGroups.indexOf(group);
                  const isExpanded = expandedGroups.has(globalIndex);

                  // Check for subroutine boundaries in this group
                  const subroutineSteps = group.steps.filter(step => isSubroutineBoundary(step));
                  const hasSubroutines = subroutineSteps.length > 0;

                  return (
                    <div key={globalIndex} className="space-y-1">
                      {/* Render subroutine start markers */}
                      {subroutineSteps
                        .filter(step => getSubroutineInfo(step)?.isStart)
                        .map((step, idx) => {
                          const subInfo = getSubroutineInfo(step);
                          return (
                            <Card key={`sub-start-${idx}`} className="border-l-4 border-l-green-400 bg-green-50">
                              <CardHeader className="py-2">
                                <div className="flex items-center gap-2">
                                  <Play className="h-3 w-3 text-green-600" />
                                  <div className="text-sm font-medium text-green-800">
                                    Subroutine Start: {subInfo?.name}
                                  </div>
                                  <Badge variant="outline" className="text-xs bg-green-100 text-green-700 border-green-300">
                                    {subInfo?.id}
                                  </Badge>
                                </div>
                              </CardHeader>
                            </Card>
                          );
                        })}

                      <Card className={`border-l-4 ${hasSubroutines ? 'border-l-purple-200 bg-purple-50/30' : 'border-l-blue-200'} hover:border-l-blue-300 transition-colors`}>
                      <Collapsible open={isExpanded} onOpenChange={() => toggleGroupExpansion(globalIndex)}>
                        <CollapsibleTrigger asChild>
                          <CardHeader className="cursor-pointer hover:bg-muted/30 transition-colors py-2">
                            <div className="flex items-center justify-between">
                              <div className="flex items-center gap-2 flex-1">
                                {isExpanded ? (
                                  <ChevronDown className="h-3 w-3 text-muted-foreground" />
                                ) : (
                                  <ChevronRight className="h-3 w-3 text-muted-foreground" />
                                )}
                                <div className="flex-1">
                                  <div className="text-sm font-medium">
                                    {group.displayName}
                                  </div>
                                  <div className="text-xs text-muted-foreground mb-2">
                                    Steps {group.startStepNumber}-{group.endStepNumber} • {group.stepCount} steps
                                  </div>
                                  {/* Technique Summary */}
                                  {(() => {
                                    const stepTypeResult = groupStepsByType(group.steps);
                                    const uniqueTechniques = Object.entries(stepTypeResult.techniqueDistribution)
                                      .filter(([type, count]) => count > 0 && type !== 'unknown')
                                      .sort(([,a], [,b]) => b - a)
                                      .slice(0, 4); // Show top 4 techniques
                                    
                                    return (
                                      <div className="flex flex-wrap gap-1">
                                        {uniqueTechniques.map(([type, count]) => {
                                          const config = STEP_TYPE_CONFIG[type as keyof typeof STEP_TYPE_CONFIG];
                                          return (
                                            <Badge 
                                              key={type} 
                                              variant="secondary" 
                                              className="text-xs px-1.5 py-0.5 bg-indigo-50 text-indigo-700 border-indigo-200" 
                                              title={`${config.displayName}: ${count} steps`}
                                            >
                                              <span className="mr-0.5">{config.icon}</span>
                                              {count}
                                            </Badge>
                                          );
                                        })}
                                        {stepTypeResult.techniqueDistribution.unknown > 0 && (
                                          <Badge variant="outline" className="text-xs px-1.5 py-0.5 bg-amber-50 text-amber-700 border-amber-200">
                                            ❓ {stepTypeResult.techniqueDistribution.unknown}
                                          </Badge>
                                        )}
                                      </div>
                                    );
                                  })()}
                                </div>
                              </div>
                            </div>
                          </CardHeader>
                        </CollapsibleTrigger>
                        <CollapsibleContent>
                          <CardContent className="pt-0 pb-2">
                            <div className="border-t pt-2 overflow-hidden">
                              <ProgramStepDetails group={group} />
                            </div>
                          </CardContent>
                        </CollapsibleContent>
                      </Collapsible>
                    </Card>

                    {/* Render subroutine end markers */}
                    {subroutineSteps
                      .filter(step => getSubroutineInfo(step)?.isEnd)
                      .map((step, idx) => {
                        const subInfo = getSubroutineInfo(step);
                        return (
                          <Card key={`sub-end-${idx}`} className="border-l-4 border-l-red-400 bg-red-50">
                            <CardHeader className="py-2">
                              <div className="flex items-center gap-2">
                                <Square className="h-3 w-3 text-red-600" />
                                <div className="text-sm font-medium text-red-800">
                                  Subroutine End: {subInfo?.name}
                                </div>
                                <Badge variant="outline" className="text-xs bg-red-100 text-red-700 border-red-300">
                                  {subInfo?.id}
                                </Badge>
                              </div>
                            </CardHeader>
                          </Card>
                        );
                      })}
                  </div>
                  );
                })}
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}
