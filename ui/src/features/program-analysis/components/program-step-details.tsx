'use client';

import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { ScrollArea } from '@/components/ui/scroll-area';

import type { BodyPartGroup } from '@/utils/body-part-classifier';
import { classifyStepType, STEP_TYPE_CONFIG } from '@/utils/step-type-classifier';
import { getSubroutineInfo, isSubroutineStep } from '@/utils/program-expander';

interface ProgramStepDetailsProps {
  group: BodyPartGroup;
}

export function ProgramStepDetails({ group }: ProgramStepDetailsProps) {
  const formatValue = (value: any) => {
    if (value === null || value === undefined || value === '') {
      return '-';
    }
    if (Array.isArray(value)) {
      return value.join(', ');
    }
    return String(value);
  };


  return (
    <div className="space-y-2">
      <div className="border rounded overflow-hidden">
        <div className="overflow-x-auto">
          <ScrollArea className={`${group.steps.length > 8 ? 'h-64' : 'h-auto max-h-64'}`}>
            <Table className="min-w-full">
              <TableHeader>
                <TableRow className="border-b">
                  <TableHead className="w-10 text-xs font-medium">Step</TableHead>
                  <TableHead className="w-10 text-xs font-medium" title="Massage Technique Type">Type</TableHead>
                  <TableHead className="w-16 text-xs font-medium">Source</TableHead>
                  <TableHead className="w-72 text-xs font-medium">Description</TableHead>
                  <TableHead className="w-16 text-xs font-medium">Position</TableHead>
                  <TableHead className="w-32 text-xs font-medium">Air Action</TableHead>
                  <TableHead className="w-16 text-xs font-medium">Speed</TableHead>
                  <TableHead className="w-12 text-xs font-medium">Width</TableHead>
                  <TableHead className="w-16 text-xs font-medium">Seat Prog</TableHead>
                  <TableHead className="w-12 text-xs font-medium">Scent</TableHead>
                  <TableHead className="w-12 text-xs font-medium">Light</TableHead>
                </TableRow>
              </TableHeader>
            <TableBody>
              {group.steps.map((step, index) => {
                const stepType = classifyStepType(step);
                const stepTypeConfig = STEP_TYPE_CONFIG[stepType];
                const subroutineInfo = getSubroutineInfo(step);
                const isFromSubroutine = isSubroutineStep(step);

                return (
                  <TableRow key={index} className={`hover:bg-muted/20 ${isFromSubroutine ? 'bg-purple-50/50' : ''}`}>
                    <TableCell className="text-xs py-2 font-medium">
                      {step.step_number}
                    </TableCell>
                    <TableCell className="py-2">
                      <div className="flex items-center justify-center" title={`${stepTypeConfig.displayName} - ${stepTypeConfig.description}`}>
                        <span className="text-sm">{stepTypeConfig.icon}</span>
                      </div>
                    </TableCell>
                    <TableCell className="py-2">
                      {isFromSubroutine ? (
                        <Badge variant="outline" className="text-xs bg-purple-100 text-purple-700 border-purple-300" title={subroutineInfo?.name}>
                          SUB
                        </Badge>
                      ) : (
                        <Badge variant="outline" className="text-xs bg-blue-100 text-blue-700 border-blue-300">
                          MAIN
                        </Badge>
                      )}
                    </TableCell>
                    <TableCell className="py-2 max-w-0">
                      <div className="space-y-1">
                        <div className="text-xs font-medium truncate" title={formatValue(step.en_roller_action_description)}>
                          {formatValue(step.en_roller_action_description)}
                        </div>
                        {step.roller_action_description && (
                          <div className="text-xs text-muted-foreground truncate" title={formatValue(step.roller_action_description)}>
                            {formatValue(step.roller_action_description)}
                          </div>
                        )}
                      </div>
                    </TableCell>
                    <TableCell className="py-2">
                      <div className="text-xs">
                        {step.position_3d && Array.isArray(step.position_3d) ? (
                          <Badge variant="secondary" className="text-xs px-1">
                            {step.position_3d.join(', ')}
                          </Badge>
                        ) : (
                          <span className="text-muted-foreground">{formatValue(step.position_3d)}</span>
                        )}
                      </div>
                    </TableCell>
                    <TableCell className="py-2">
                      <div className="text-xs text-muted-foreground truncate" title={formatValue(step.en_air_action_description)}>
                        {formatValue(step.en_air_action_description)}
                      </div>
                    </TableCell>
                    <TableCell className="py-2">
                      <div className="flex flex-wrap gap-0.5">
                        {step.kneading_speed && (
                          <Badge variant="secondary" className="text-xs px-1">
                            K: {step.kneading_speed}
                          </Badge>
                        )}
                        {step.tapping_speed && (
                          <Badge variant="secondary" className="text-xs px-1">
                            T: {step.tapping_speed}
                          </Badge>
                        )}
                      </div>
                    </TableCell>
                    <TableCell className="py-2">
                      <div className="text-xs">
                        {step.width ? (
                          <span className="font-mono text-muted-foreground">{step.width}</span>
                        ) : (
                          <span className="text-muted-foreground">-</span>
                        )}
                      </div>
                    </TableCell>
                    <TableCell className="py-2">
                      <div className="text-xs">
                        {step.seat_program ? (
                          <Badge variant="outline" className="text-xs px-1 font-mono">
                            {step.seat_program}
                          </Badge>
                        ) : (
                          <span className="text-muted-foreground">-</span>
                        )}
                      </div>
                    </TableCell>
                    <TableCell className="py-2">
                      <div className="text-xs">
                        {step.scent ? (
                          <Badge variant="outline" className="text-xs px-1 bg-amber-50 text-amber-700 border-amber-200">
                            {step.scent}
                          </Badge>
                        ) : (
                          <span className="text-muted-foreground">-</span>
                        )}
                      </div>
                    </TableCell>
                    <TableCell className="py-2">
                      <div className="text-xs">
                        {step.light ? (
                          <Badge variant="outline" className="text-xs px-1 bg-yellow-50 text-yellow-700 border-yellow-200">
                            {step.light}
                          </Badge>
                        ) : (
                          <span className="text-muted-foreground">-</span>
                        )}
                      </div>
                    </TableCell>
                  </TableRow>
                );
              })}
            </TableBody>
            </Table>
          </ScrollArea>
        </div>
      </div>
    </div>
  );
}
