'use client';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { BarChart3, Target, AlertCircle, CheckCircle, ArrowUp, ArrowDown, MoveRight, Zap } from 'lucide-react';
import { groupStepsByType, getStepTypeStats, STEP_TYPE_CONFIG } from '@/utils/step-type-classifier';

import type { ClassificationResult, BodyPart } from '@/utils/body-part-classifier';

interface ProgramStatsSummaryProps {
  stats: Record<string, any>;
  classificationResult: ClassificationResult;
}

export function ProgramStatsSummary({ stats, classificationResult }: ProgramStatsSummaryProps) {
  const { bodyPartGroups, totalSteps, totalGroups, unclassifiedSteps } = classificationResult;

  // Calculate step type analysis for all steps
  const allSteps = bodyPartGroups.flatMap(group => group.steps);
  const stepTypeResult = groupStepsByType(allSteps);
  const stepTypeStats = getStepTypeStats(stepTypeResult);

  // Calculate body part distribution
  const bodyPartCounts = bodyPartGroups.reduce((acc, group) => {
    acc[group.bodyPart] = (acc[group.bodyPart] || 0) + group.stepCount;
    return acc;
  }, {} as Record<BodyPart, number>);

  // Sort body parts by step count
  const sortedBodyParts = Object.entries(bodyPartCounts)
    .sort(([, a], [, b]) => b - a); // Show all body parts

  // Sort step types by usage
  const sortedStepTypes = Object.entries(stepTypeResult.techniqueDistribution)
    .filter(([type]) => type !== 'unknown')
    .sort(([, a], [, b]) => b - a)

  const classificationRate = totalSteps > 0 
    ? ((totalSteps - unclassifiedSteps) / totalSteps * 100)
    : 0;

  // Calculate flow changes
  const flowChanges = classificationResult.flowGroups.reduce((acc, group) => {
    if (group.direction !== 'end') {
      acc[group.direction] = (acc[group.direction] || 0) + 1;
    }
    return acc;
  }, {} as Record<string, number>);

  // Calculate technique complexity score
  const techniqueComplexity = stepTypeStats.techniqueVariety;
  const hasAdvancedTechniques = Object.keys(stepTypeResult.techniqueDistribution)
    .some(type => ['combination', '3d_movement', 'stretching'].includes(type));
  
  // Calculate program balance (how evenly distributed techniques are)
  const totalTechniqueSteps = Object.values(stepTypeResult.techniqueDistribution)
    .reduce((sum, count) => sum + count, 0);
  const programBalance = totalTechniqueSteps > 0 ? 
    (1 - (Math.max(...Object.values(stepTypeResult.techniqueDistribution)) / totalTechniqueSteps)) * 100 : 0;

  return (
    <div className="space-y-3">
      {/* Overview Stats */}
      <Card>
        <CardHeader className="pb-2">
          <div className="flex items-center gap-2">
            <div className="p-1 bg-purple-50 rounded border border-purple-200">
              <BarChart3 className="h-3 w-3 text-purple-600" />
            </div>
            <CardTitle className="text-sm font-medium">Analysis Overview</CardTitle>
          </div>
        </CardHeader>
        <CardContent className="space-y-3 pt-0">
          <div className="grid grid-cols-2 gap-2">
            <div className="text-center p-2 border rounded bg-blue-50 border-blue-200">
              <div className="text-xl font-bold text-blue-700">{totalSteps}</div>
              <div className="text-xs text-blue-600">Total Steps</div>
            </div>
            <div className="text-center p-2 border rounded bg-emerald-50 border-emerald-200">
              <div className="text-xl font-bold text-emerald-700">{totalGroups}</div>
              <div className="text-xs text-emerald-600">Groups</div>
            </div>
          </div>

          <div className="space-y-2">
            <div className="flex items-center justify-between text-sm">
              <span className="text-muted-foreground">Classification Rate</span>
              <span className="font-medium">{classificationRate.toFixed(1)}%</span>
            </div>
            <Progress value={classificationRate} className="h-2" />
            
            {unclassifiedSteps > 0 ? (
              <div className="flex items-center gap-2 text-xs text-amber-600 pt-1">
                <AlertCircle className="h-3 w-3" />
                <span>{unclassifiedSteps} steps unclassified</span>
              </div>
            ) : totalSteps > 0 ? (
              <div className="flex items-center gap-2 text-xs text-emerald-600 pt-1">
                <CheckCircle className="h-3 w-3" />
                <span>All steps classified</span>
              </div>
            ) : null}
          </div>
        </CardContent>
      </Card>

      {/* Flow Change Stats */}
      <Card>
        <CardHeader className="pb-2">
          <div className="flex items-center gap-2">
            <div className="p-1 bg-orange-50 rounded border border-orange-200">
              <MoveRight className="h-3 w-3 text-orange-600" />
            </div>
            <CardTitle className="text-sm font-medium">Flow Dynamics</CardTitle>
          </div>
        </CardHeader>
        <CardContent className="space-y-2 pt-0">
          <div className="grid grid-cols-3 gap-2">
            <div className="text-center p-1.5 border rounded bg-amber-50 border-amber-200">
              <ArrowUp className="h-3 w-3 mx-auto mb-0.5 text-amber-600" />
              <div className="font-semibold text-amber-700">{flowChanges.up || 0}</div>
              <div className="text-xs text-amber-600">Up</div>
            </div>
            <div className="text-center p-1.5 border rounded bg-teal-50 border-teal-200">
              <ArrowDown className="h-3 w-3 mx-auto mb-0.5 text-teal-600" />
              <div className="font-semibold text-teal-700">{flowChanges.down || 0}</div>
              <div className="text-xs text-teal-600">Down</div>
            </div>
            <div className="text-center p-1.5 border rounded bg-blue-50 border-blue-200">
              <MoveRight className="h-3 w-3 mx-auto mb-0.5 text-blue-600" />
              <div className="font-semibold text-blue-700">{flowChanges.same || 0}</div>
              <div className="text-xs text-blue-600">Same</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Body Part Distribution */}
      <Card>
        <CardHeader className="pb-2">
          <div className="flex items-center gap-2">
            <div className="p-1 bg-rose-50 rounded border border-rose-200">
              <Target className="h-3 w-3 text-rose-600" />
            </div>
            <CardTitle className="text-sm font-medium">Body Parts</CardTitle>
          </div>
          <CardDescription className="text-xs">
            Step distribution by body part
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-2 pt-0">

          <div className="space-y-2">
            {sortedBodyParts.map(([bodyPart, count]) => {
              const percentage = totalSteps > 0 ? (count / totalSteps * 100) : 0;
              const group = bodyPartGroups.find(g => g.bodyPart === bodyPart);

              return (
                <div key={bodyPart} className="space-y-1">
                  <div className="flex items-center justify-between text-xs">
                    <Badge variant="outline" className="text-xs">
                      {group?.displayName || bodyPart}
                    </Badge>
                    <div className="flex items-center gap-1">
                      <span className="font-medium">{count}</span>
                      <span className="text-muted-foreground">({percentage.toFixed(1)}%)</span>
                    </div>
                  </div>
                  <Progress value={percentage} className="h-1" />
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {/* Step Type Analysis */}
      <Card>
        <CardHeader className="pb-2">
          <div className="flex items-center gap-2">
            <div className="p-1 bg-indigo-50 rounded border border-indigo-200">
              <Zap className="h-3 w-3 text-indigo-600" />
            </div>
            <CardTitle className="text-sm font-medium">Techniques</CardTitle>
          </div>
        </CardHeader>
        <CardContent className="space-y-3 pt-0">
          {/* Technique Quality Metrics */}
          <div className="grid grid-cols-2 gap-2">
            <div className="text-center p-1.5 border rounded bg-purple-50 border-purple-200">
              <div className="text-base font-bold text-purple-700">{techniqueComplexity}</div>
              <div className="text-xs text-purple-600">Variety</div>
            </div>
            <div className="text-center p-1.5 border rounded bg-cyan-50 border-cyan-200">
              <div className="text-base font-bold text-cyan-700">{programBalance.toFixed(0)}%</div>
              <div className="text-xs text-cyan-600">Balance</div>
            </div>
          </div>

          {/* Advanced Techniques Indicator */}
          {hasAdvancedTechniques && (
            <div className="flex items-center gap-2 text-xs text-emerald-600">
              <CheckCircle className="h-3 w-3" />
              <span>Contains advanced techniques</span>
            </div>
          )}

          {/* Top Techniques Distribution */}
          <div className="space-y-1">
            <div className="text-xs text-muted-foreground">
              Usage distribution
            </div>
            <div className="space-y-2">
              {sortedStepTypes.map(([stepType, count]) => {
                const percentage = totalTechniqueSteps > 0 ? (count / totalTechniqueSteps * 100) : 0;
                const config = STEP_TYPE_CONFIG[stepType as keyof typeof STEP_TYPE_CONFIG];

                return (
                  <div key={stepType} className="space-y-1">
                    <div className="flex items-center justify-between text-xs">
                      <div className="flex items-center gap-1">
                        <span>{config.icon}</span>
                        <Badge variant="outline" className="text-xs">
                          {config.displayName}
                        </Badge>
                      </div>
                      <div className="flex items-center gap-1">
                        <span className="font-medium">{count}</span>
                        <span className="text-muted-foreground">({percentage.toFixed(1)}%)</span>
                      </div>
                    </div>
                    <Progress value={percentage} className="h-1" />
                  </div>
                );
              })}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Group Summary */}
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-sm font-medium">Group Summary</CardTitle>
        </CardHeader>
        <CardContent className="space-y-2 pt-0">
          <div className="space-y-1.5 text-xs">
            <div className="flex justify-between items-center">
              <span className="text-muted-foreground">Largest Group:</span>
              <span className="font-medium">
                {Math.max(...bodyPartGroups.map(g => g.stepCount))} steps
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-muted-foreground">Smallest Group:</span>
              <span className="font-medium">
                {Math.min(...bodyPartGroups.map(g => g.stepCount))} steps
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-muted-foreground">Average Size:</span>
              <span className="font-medium">
                {totalGroups > 0 ? (totalSteps / totalGroups).toFixed(1) : 0} steps
              </span>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
