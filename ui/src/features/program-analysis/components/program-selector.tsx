'use client';

import { useState, useEffect, useCallback, useRef } from 'react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { useListProducts } from '@/queries/product';
import { useGetProductProgramCategories, useListProgramsByCategory } from '@/queries/program';
import { Program } from 'types';

interface ProgramSelectorProps {
  selectedProgram: Program | null;
  onProgramSelect: (program: Program | null) => void;
  // Optional controlled state props to prevent reset on unmount/remount
  selectedProductId?: number | undefined;
  onProductIdChange?: (productId: number | undefined) => void;
  selectedCategoryId?: number | undefined;
  onCategoryIdChange?: (categoryId: number | undefined) => void;
}

export function ProgramSelector({
  selectedProgram,
  onProgramSelect,
  selectedProductId: controlledProductId,
  onProductIdChange,
  selectedCategoryId: controlledCategoryId,
  onCategoryIdChange
}: ProgramSelectorProps) {
  // Use controlled state if provided, otherwise use internal state
  const [internalProductId, setInternalProductId] = useState<number | undefined>();
  const [internalCategoryId, setInternalCategoryId] = useState<number | undefined>();


  // Track previous values to detect actual changes vs initial mount
  const prevProductIdRef = useRef<number | undefined>(undefined);
  const prevCategoryIdRef = useRef<number | undefined>(undefined);

  const selectedProductId = controlledProductId !== undefined ? controlledProductId : internalProductId;
  const selectedCategoryId = controlledCategoryId !== undefined ? controlledCategoryId : internalCategoryId;

  const setSelectedProductId = useCallback((value: number | undefined) => {
    if (onProductIdChange) {
      onProductIdChange(value);
    } else {
      setInternalProductId(value);
    }
  }, [onProductIdChange]);

  const setSelectedCategoryId = useCallback((value: number | undefined) => {
    if (onCategoryIdChange) {
      onCategoryIdChange(value);
    } else {
      setInternalCategoryId(value);
    }
  }, [onCategoryIdChange]);


  // Fetch products
  const { data: productsResponse } = useListProducts({ limit: 100 });
  const products = productsResponse?.products || [];

  // Fetch categories for selected product
  const { data: categoriesData } = useGetProductProgramCategories(selectedProductId);
  const categories = categoriesData || [];

  // Fetch programs for selected category
  const { data: programsResponse, isLoading: programsLoading } = useListProgramsByCategory({
    categoryId: selectedCategoryId || 0,
    productId: selectedProductId,
    limit: 50,
    sortField: 'program_title',
    sortDirection: 'asc'
  });

  const programs = programsResponse?.programs || [];

  // Reset dependent selections when parent changes (but not on initial mount)
  useEffect(() => {
    // Only reset if this is an actual change, not initial mount
    if (prevProductIdRef.current !== undefined && prevProductIdRef.current !== selectedProductId) {
      setSelectedCategoryId(undefined);
      onProgramSelect(null);
    }
    prevProductIdRef.current = selectedProductId;
  }, [selectedProductId, onProgramSelect, setSelectedCategoryId]);

  useEffect(() => {
    // Only reset if this is an actual change, not initial mount
    if (prevCategoryIdRef.current !== undefined && prevCategoryIdRef.current !== selectedCategoryId) {
      onProgramSelect(null);
    }
    prevCategoryIdRef.current = selectedCategoryId;
  }, [selectedCategoryId, onProgramSelect]);

  const handleProgramSelect = (programId: string) => {
    const program = programs.find(p => p.id === parseInt(programId));
    onProgramSelect(program || null);
  };


  return (
    <div className="space-y-3">
      {/* Horizontal Layout for Dropdowns */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-3">
        {/* Product Selection */}
        <div className="space-y-1.5">
          <label className="text-sm font-medium">Product</label>
          <Select
            value={selectedProductId?.toString() || ''}
            onValueChange={(value) => setSelectedProductId(value ? parseInt(value) : undefined)}
          >
            <SelectTrigger className="h-8">
              <SelectValue placeholder="Select product" />
            </SelectTrigger>
            <SelectContent>
              {products.map((product) => (
                <SelectItem key={product.id} value={product.id.toString()}>
                  <div className="flex items-center justify-between w-full">
                    <span className="font-medium">{product.name}</span>
                    <Badge variant="secondary" className="text-xs ml-2 bg-blue-50 text-blue-700 border-blue-200">
                      {product.model}
                    </Badge>
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Category Selection */}
        <div className="space-y-1.5">
          <label className="text-sm font-medium">Category</label>
          <Select
            value={selectedCategoryId?.toString() || ''}
            onValueChange={(value) => setSelectedCategoryId(value ? parseInt(value) : undefined)}
            disabled={!selectedProductId}
          >
            <SelectTrigger className="h-8">
              <SelectValue placeholder={!selectedProductId ? "Select product first" : "Select category"} />
            </SelectTrigger>
            <SelectContent>
              {categories.map((category) => (
                <SelectItem key={category.id} value={category.id.toString()}>
                  <div className="flex items-center justify-between w-full">
                    <span className="font-medium">{category.name}</span>
                    <Badge variant="secondary" className="text-xs ml-2 bg-emerald-50 text-emerald-700 border-emerald-200">
                      {category.programCount}
                    </Badge>
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Program Selection */}
        <div className="space-y-1.5">
          <label className="text-sm font-medium">Program</label>
          <Select
            value={selectedProgram?.id?.toString() || ''}
            onValueChange={handleProgramSelect}
            disabled={!selectedCategoryId || programsLoading}
          >
            <SelectTrigger className="h-8">
              <SelectValue placeholder={
                programsLoading ? "Loading..." : 
                !selectedCategoryId ? "Select category first" : 
                "Select program"
              } />
            </SelectTrigger>
            <SelectContent className="max-h-60">
              {programs.map((program) => (
                <SelectItem key={program.id} value={program.id.toString()}>
                  <div className="flex items-center justify-between w-full">
                    <span className="font-medium">{program.program_title || program.name}</span>
                    <Badge variant="outline" className="text-xs ml-2 bg-purple-50 text-purple-700 border-purple-200">
                      #{program.id}
                    </Badge>
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>
    </div>
  );
}
