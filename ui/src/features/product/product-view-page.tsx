'use client';

import { useParams, useRouter } from 'next/navigation';
import { useGetProduct } from '@/queries/product';
import { useGetProductProgramCategories } from '@/queries/program';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { List, FileText, Layers, ChevronDown, ChevronUp } from 'lucide-react';
import React, { useState, useRef } from 'react';
import { Button } from '@/components/ui/button';

// Import the new components
import { ProductHeader } from './components/ProductHeader';
import { ProductSkeleton } from './components/LoadingStates';
import {
  ErrorState,
  InvalidProductIdError,
  ProductNotFoundError
} from './components/ErrorState';
import { SequenceProgramsTab } from './components/SequenceProgramsTab';
import { SubRoutinesTab } from './components/SubRoutinesTab';
import { SupportDocsTab } from './components/SupportDocsTab';

// Main Component
interface ProductViewPageProps {
  activeTab?: string;
  selectedCategoryId?: number;
}

export default function ProductViewPage({
  activeTab = 'sequence-programs',
  selectedCategoryId
}: ProductViewPageProps) {
  const params = useParams();
  const router = useRouter();
  const productId = params?.id ? parseInt(params.id as string, 10) : null;
  const [showFullHeader, setShowFullHeader] = useState(false);
  const tabsRef = useRef<HTMLDivElement>(null);

  const {
    data: product,
    isLoading: isLoadingProduct,
    isError: isErrorProduct,
    error: errorProduct
  } = useGetProduct(productId, {
    enabled: !!productId
  });

  const {
    data: programCategories,
    isLoading: isLoadingCategories,
    isError: isErrorCategories,
    error: errorCategories
  } = useGetProductProgramCategories(productId);

  // We're not loading subroutines at this level anymore - the component handles it
  const isLoadingSubRoutines = false;

  // Scroll to tabs when tab is changed or when showFullHeader changes
  const scrollToTabs = () => {
    if (tabsRef.current) {
      tabsRef.current.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }
  };

  const handleTabChange = (value: string) => {
    const baseUrl = `/dashboard/product/${productId}`;
    switch (value) {
      case 'sequence-programs':
        router.push(`${baseUrl}/sequence-programs`);
        break;
      case 'subroutines':
        router.push(`${baseUrl}/subroutines`);
        break;
      case 'support-docs':
        router.push(`${baseUrl}/docs`);
        break;
      default:
        router.push(`${baseUrl}/sequence-programs`);
    }
  };

  const toggleFullHeader = () => {
    setShowFullHeader(!showFullHeader);
    // Delay scroll to let the header expand/collapse
    setTimeout(scrollToTabs, 100);
  };

  if (!productId) {
    return <InvalidProductIdError />;
  }

  if (isLoadingProduct) {
    return <ProductSkeleton />;
  }

  if (isErrorProduct || isErrorCategories) {
    const errorMessages = [
      errorProduct?.message,
      errorCategories?.message
    ].filter(Boolean) as string[];

    return <ErrorState messages={errorMessages} />;
  }

  if (!product) {
    console.log('Product is falsy, showing Not Found');
    return <ProductNotFoundError />;
  }

  return (
    <div className='h-full overflow-auto bg-gray-50 dark:bg-gray-900/50'>
      <div className='mx-auto max-w-6xl px-4 py-6 sm:px-6 lg:px-8'>
        <div className='space-y-4'>
          {/* Product Info Card with toggle for detailed view */}
          <div className='relative'>
            {showFullHeader && <ProductHeader product={product} />}

            {!showFullHeader && (
              <div className='mb-4'>
                <ProductHeader product={product} condensed={true} />
                <div className='-mt-1 flex justify-center'>
                  <Button
                    variant='ghost'
                    size='sm'
                    onClick={toggleFullHeader}
                    className='rounded-b-md rounded-t-none border-t-0 border-slate-200 text-xs text-slate-600 hover:text-slate-900 dark:border-slate-700'
                  >
                    <ChevronDown className='mr-1 h-3 w-3' />
                    Show full details
                  </Button>
                </div>
              </div>
            )}

            {showFullHeader && (
              <div className='-mt-1 flex justify-center'>
                <Button
                  variant='ghost'
                  size='sm'
                  onClick={toggleFullHeader}
                  className='rounded-b-md rounded-t-none border-t-0 border-slate-200 text-xs text-slate-600 hover:text-slate-900 dark:border-slate-700'
                >
                  <ChevronUp className='mr-1 h-3 w-3' />
                  Show less
                </Button>
              </div>
            )}
          </div>

          {/* Sticky Tabs Navigation */}
          <div
            className='sticky top-0 z-10 -mx-4 bg-gray-50 px-4 pb-1 pt-2 shadow-sm backdrop-blur-sm dark:bg-gray-900/90'
            ref={tabsRef}
          >
            <Tabs
              value={activeTab}
              className='w-full'
              onValueChange={handleTabChange}
            >
              <TabsList className='grid h-10 w-full grid-cols-3 overflow-hidden rounded-lg border border-transparent bg-muted/20 p-0'>
                <TabsTrigger
                  value='sequence-programs'
                  className='flex items-center justify-center gap-1.5 rounded-none border-b-2 border-transparent py-2.5 data-[state=active]:border-b-primary data-[state=active]:bg-background'
                >
                  <List className='h-4 w-4' />
                  <span className='text-sm font-medium'>Sequence Programs</span>
                </TabsTrigger>
                <TabsTrigger
                  value='subroutines'
                  className='flex items-center justify-center gap-1.5 rounded-none border-b-2 border-transparent py-2.5 data-[state=active]:border-b-primary data-[state=active]:bg-background'
                >
                  <Layers className='h-4 w-4' />
                  <span className='text-sm font-medium'>Subroutines</span>
                </TabsTrigger>
                <TabsTrigger
                  value='support-docs'
                  className='flex items-center justify-center gap-1.5 rounded-none border-b-2 border-transparent py-2.5 data-[state=active]:border-b-primary data-[state=active]:bg-background'
                >
                  <FileText className='h-4 w-4' />
                  <span className='text-sm font-medium'>Support & Docs</span>
                </TabsTrigger>
              </TabsList>

              {/* Tab Contents */}
              <TabsContent value='sequence-programs' className='mt-6'>
                <SequenceProgramsTab
                  isLoading={isLoadingCategories}
                  programCategories={programCategories}
                  productId={productId}
                  selectedCategoryId={selectedCategoryId}
                />
              </TabsContent>

              <TabsContent value='subroutines' className='mt-6'>
                <SubRoutinesTab
                  isLoading={isLoadingSubRoutines}
                  productId={productId}
                />
              </TabsContent>

              <TabsContent value='support-docs' className='mt-6'>
                <SupportDocsTab />
              </TabsContent>
            </Tabs>
          </div>
        </div>
      </div>
    </div>
  );
}
