import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { PlusCircle } from 'lucide-react';
import { SubroutinesSkeleton } from './LoadingStates';
import SubroutinesTable from './SubroutinesTable';

interface SubRoutinesTabProps {
  isLoading: boolean;
  productId?: number;
}

export function SubRoutinesTab({ isLoading, productId }: SubRoutinesTabProps) {
  return (
    <div>
      <div className='flex items-center justify-between pb-4'>
        <h2 className='text-xl font-semibold'>Subroutines</h2>
        <Button size='sm' className='h-9 gap-1'>
          <PlusCircle className='h-4 w-4' />
          <span>Create Subroutine</span>
        </Button>
      </div>
      {isLoading ? (
        <SubroutinesSkeleton />
      ) : (
        <SubroutinesTable productId={productId} />
      )}
    </div>
  );
}
