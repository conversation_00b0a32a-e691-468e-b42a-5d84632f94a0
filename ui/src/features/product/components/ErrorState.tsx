import React from 'react';
import { AlertCircle } from 'lucide-react';

interface ErrorStateProps {
  messages?: string[];
  defaultMessage?: string;
}

export function ErrorState({
  messages,
  defaultMessage = 'Unknown error loading product data'
}: ErrorStateProps) {
  const displayMessage =
    messages && messages.length > 0 ? messages.join('; ') : defaultMessage;

  return (
    <div className='flex items-center justify-center text-red-500'>
      <AlertCircle className='mr-2 h-5 w-5' /> Error: {displayMessage}
    </div>
  );
}

export function InvalidProductIdError() {
  return (
    <div className='flex items-center justify-center text-red-500'>
      <AlertCircle className='mr-2 h-5 w-5' /> Invalid Product ID
    </div>
  );
}

export function ProductNotFoundError() {
  return (
    <div className='flex items-center justify-center text-gray-500'>
      Product not found.
    </div>
  );
}
