import React from 'react';
import {
  BookOpen,
  Download,
  Play,
  HeartPulse,
  BrainCircuit,
  Brush,
  Bookmark,
  Package
} from 'lucide-react';

// Icon Mapping
const categoryIconMap: Record<string, React.ReactElement> = {
  'auto programs': <BookOpen className='h-6 w-6 text-blue-500' />,
  'download programs': <Download className='h-6 w-6 text-blue-500' />,
  'onetouch programs': <Play className='h-6 w-6 text-blue-500' />,
  'tension programs': <HeartPulse className='h-6 w-6 text-blue-500' />,
  'ai programs': <BrainCircuit className='h-6 w-6 text-blue-500' />,
  'customised programs': <Brush className='h-6 w-6 text-blue-500' />,
  'saved programs': <Bookmark className='h-6 w-6 text-blue-500' />,
  default: <Package className='h-6 w-6 text-gray-500' />
};

export const normalizeNameForMap = (name: string): string => {
  if (!name) return '';
  return name.toLowerCase().replace(/_/g, ' ').trim();
};

export const getCategoryIcon = (categoryName: string): React.ReactElement => {
  const normalizedName = normalizeNameForMap(categoryName);
  return categoryIconMap[normalizedName] || categoryIconMap.default;
};

export const capitalize = (s: string) => {
  if (!s) return '';
  return s.charAt(0).toUpperCase() + s.slice(1);
};
