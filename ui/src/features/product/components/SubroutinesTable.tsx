'use client';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Eye, Search, X, ArrowUpDown, ArrowUp, ArrowDown } from 'lucide-react';
import { DataTable } from '@/components/ui/table/data-table';
import { useRouter } from 'next/navigation';
import { ColumnDef } from '@tanstack/react-table';
import { SubRoutine } from 'types';
import { Row } from '@tanstack/react-table';
import { useListSubroutines } from '@/queries/program';
import { useState, useEffect } from 'react';
import moment from 'moment';
import { useDebounce } from '@/hooks/use-debounce';
import { motion, AnimatePresence } from 'framer-motion';

export default function SubroutinesTable({
  productId
}: {
  productId?: number;
}) {
  const router = useRouter();
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [searchTerm, setSearchTerm] = useState('');
  const [isSearching, setIsSearching] = useState(false);
  const [sortField, setSortField] = useState<string>('created_at');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');

  const debouncedSearchTerm = useDebounce(searchTerm, 300);

  const { data, isLoading, isFetching } = useListSubroutines({
    skip: (page - 1) * pageSize,
    limit: pageSize,
    productId,
    search: debouncedSearchTerm,
    sortField,
    sortDirection
  });

  useEffect(() => {
    setPage(1);
  }, [debouncedSearchTerm, sortField, sortDirection]);

  useEffect(() => {
    setIsSearching(isFetching);
  }, [isFetching]);

  const subroutines = data?.subroutines || [];
  const totalPages = data?.total ? Math.ceil(data.total / pageSize) : 0;

  const handlePageSizeChange = (newPageSize: number) => {
    setPageSize(newPageSize);
    setPage(1);
  };

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
  };

  const clearSearch = () => {
    setSearchTerm('');
  };

  const handleSort = (field: string) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  const renderSortIndicator = (field: string) => {
    if (sortField !== field) {
      return <ArrowUpDown className='ml-2 h-4 w-4' />;
    }
    return sortDirection === 'asc' ? (
      <ArrowUp className='ml-2 h-4 w-4' />
    ) : (
      <ArrowDown className='ml-2 h-4 w-4' />
    );
  };

  const columns: ColumnDef<SubRoutine>[] = [
    {
      accessorKey: 'name',
      header: () => (
        <div
          className='flex cursor-pointer items-center'
          onClick={() => handleSort('name')}
        >
          Name
          {renderSortIndicator('name')}
        </div>
      ),
      cell: ({ row }: { row: Row<SubRoutine> }) => (
        <div className='font-medium'>{row.getValue('name')}</div>
      )
    },
    {
      accessorKey: 'subroutineIdJson',
      header: () => (
        <div
          className='flex cursor-pointer items-center'
          onClick={() => handleSort('subroutineIdJson')}
        >
          Subroutine ID
          {renderSortIndicator('subroutineIdJson')}
        </div>
      ),
      cell: ({ row }: { row: Row<SubRoutine> }) => (
        <div className='max-w-[150px] truncate'>
          {row.getValue('subroutineIdJson')}
        </div>
      )
    },
    {
      accessorKey: 'product_name',
      header: () => (
        <div
          className='flex cursor-pointer items-center'
          onClick={() => handleSort('product_name')}
        >
          Product
          {renderSortIndicator('product_name')}
        </div>
      ),
      cell: ({ row }: { row: Row<SubRoutine> }) => (
        <div className='max-w-[120px] truncate'>
          {row.getValue('product_name') || '-'}
        </div>
      )
    },
    {
      accessorKey: 'actionsCount',
      header: () => (
        <div
          className='flex cursor-pointer items-center'
          onClick={() => handleSort('actionsCount')}
        >
          Actions
          {renderSortIndicator('actionsCount')}
        </div>
      ),
      cell: ({ row }: { row: Row<SubRoutine> }) => (
        <div className='text-center'>{row.getValue('actionsCount') || '0'}</div>
      )
    },
    {
      accessorKey: 'created_at',
      header: () => (
        <div
          className='flex cursor-pointer items-center'
          onClick={() => handleSort('created_at')}
        >
          Created
          {renderSortIndicator('created_at')}
        </div>
      ),
      cell: ({ row }: { row: Row<SubRoutine> }) => (
        <div
          title={moment.utc(row.getValue('created_at')).local().format('LLLL')}
          className='whitespace-nowrap'
        >
          {moment.utc(row.getValue('created_at')).local().fromNow()}
        </div>
      )
    },
    {
      id: 'actions',
      header: '',
      cell: ({ row }: { row: Row<SubRoutine> }) => (
        <div
          className='flex items-center justify-center space-x-2'
          onClick={(e) => e.stopPropagation()}
        >
          <Button
            variant='ghost'
            size='icon'
            title='View Subroutine'
            onClick={() =>
              router.push(`/dashboard/subroutine-editor?id=${row.original.id}`)
            }
            className='h-8 w-8'
          >
            <Eye className='h-4 w-4' />
          </Button>
        </div>
      ),
      enableSorting: false,
      enableHiding: false
    }
  ];

  // If viewing within a product context, hide the product column
  const columnsToUse = productId
    ? [...columns.slice(0, 2), ...columns.slice(3)]
    : columns;

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        duration: 0.1,
        when: 'beforeChildren',
        staggerChildren: 0.05
      }
    },
    exit: {
      opacity: 0,
      transition: { duration: 0.2 }
    }
  };

  const searchVariants = {
    hidden: { opacity: 0, y: -10 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.2 } }
  };

  return (
    <motion.div
      className='rounded-lg bg-white p-4 shadow-sm dark:bg-gray-800'
      variants={containerVariants}
      initial='hidden'
      animate='visible'
      exit='exit'
    >
      <motion.div className='mb-4 flex items-center' variants={searchVariants}>
        <div className='relative flex-1'>
          <div className='pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3'>
            <Search className='h-4 w-4 text-gray-400' />
          </div>
          <Input
            type='text'
            placeholder='Search subroutines by name or ID...'
            value={searchTerm}
            onChange={handleSearchChange}
            className='pl-10 pr-10'
          />
          <AnimatePresence>
            {searchTerm && (
              <motion.button
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                onClick={clearSearch}
                className='absolute inset-y-0 right-0 flex items-center pr-3'
              >
                <X className='h-4 w-4 text-gray-400 hover:text-gray-600' />
              </motion.button>
            )}
          </AnimatePresence>
        </div>
      </motion.div>

      <AnimatePresence>
        {isSearching && !isLoading && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className='mb-2 text-sm text-gray-500'
          >
            Searching...
          </motion.div>
        )}
      </AnimatePresence>

      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.1, delay: 0.1 }}
      >
        <DataTable
          data={subroutines}
          columns={columnsToUse}
          isLoading={isLoading}
          pagination={{
            page,
            pageCount: totalPages,
            pageSize,
            onPageChange: setPage,
            onPageSizeChange: handlePageSizeChange
          }}
          onRowClick={(row) =>
            router.push(`/dashboard/subroutine-editor?id=${row.original.id}`)
          }
        />
      </motion.div>

      <AnimatePresence>
        {subroutines.length === 0 && !isLoading && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className='mt-4 text-center text-gray-500'
          >
            {searchTerm
              ? 'No subroutines found matching your search.'
              : 'No subroutines available.'}
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  );
}
