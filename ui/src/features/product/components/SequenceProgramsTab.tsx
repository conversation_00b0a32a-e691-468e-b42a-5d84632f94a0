import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { PlusCircle, ArrowLeft, Trash2 } from 'lucide-react';
import { ProgramCategoryWithCount } from 'types';
import { CategorySkeleton } from './LoadingStates';
import {
  capitalize,
  getCategoryIcon,
  normalizeNameForMap
} from './CategoryIcon';
import ProgramsTable from './ProgramsTable';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
  DialogClose
} from '@/components/ui/dialog';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle
} from '@/components/ui/alert-dialog';
import { useDeleteProgramCategory } from '@/queries/program';
import { toast } from 'sonner';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { useCreateProgramCategory } from '@/queries/program';

interface SequenceProgramsTabProps {
  isLoading: boolean;
  programCategories: ProgramCategoryWithCount[] | undefined;
  productId: number | null; // Add productId prop
  selectedCategoryId?: number; // Add selectedCategoryId prop
}

export function SequenceProgramsTab({
  isLoading,
  programCategories,
  productId, // Destructure productId
  selectedCategoryId // Destructure selectedCategoryId
}: SequenceProgramsTabProps) {
  const router = useRouter();
  const [selectedCategory, setSelectedCategory] =
    useState<ProgramCategoryWithCount | null>(null);

  // Modal state
  const [open, setOpen] = useState(false);
  const [name, setName] = useState('');
  const [description, setDescription] = useState('');
  const [error, setError] = useState<string | null>(null);

  const createCategoryMutation = useCreateProgramCategory({
    onSuccess: () => {
      setOpen(false);
      setName('');
      setDescription('');
      setError(null);
    },
    onError: (err) => {
      setError(err?.message || 'Failed to create category');
    }
  });

  // Effect to set selected category based on URL parameter
  useEffect(() => {
    if (selectedCategoryId && programCategories) {
      const category = programCategories.find(cat => cat.id === selectedCategoryId);
      if (category) {
        setSelectedCategory(category);
      }
    } else {
      setSelectedCategory(null);
    }
  }, [selectedCategoryId, programCategories]);

  // State for delete dialog
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [categoryToDelete, setCategoryToDelete] =
    useState<ProgramCategoryWithCount | null>(null);
  const [deleteError, setDeleteError] = useState<string | null>(null);

  const deleteCategoryMutation = useDeleteProgramCategory({
    onSuccess: () => {
      setDeleteDialogOpen(false);
      setCategoryToDelete(null);
      setDeleteError(null);
      setSelectedCategory(null);
      toast('Category deleted');
    },
    onError: (err) => {
      setDeleteError(err?.message || 'Failed to delete category');
    }
  });

  const handleDeleteClick = (
    e: React.MouseEvent,
    category: ProgramCategoryWithCount
  ) => {
    e.stopPropagation();
    setCategoryToDelete(category);
    setDeleteDialogOpen(true);
    setDeleteError(null);
  };

  const handleDeleteConfirm = () => {
    if (categoryToDelete) {
      deleteCategoryMutation.mutate(categoryToDelete.id);
    }
  };

  const handleCategoryClick = (category: ProgramCategoryWithCount) => {
    router.push(`/dashboard/product/${productId}/sequence-programs/category/${category.id}`);
  };

  const handleBackClick = () => {
    router.push(`/dashboard/product/${productId}/sequence-programs`);
  };

  const handleOpenModal = () => {
    setOpen(true);
    setName('');
    setDescription('');
    setError(null);
  };

  const handleCloseModal = () => {
    setOpen(false);
    setName('');
    setDescription('');
    setError(null);
  };

  const handleCreateCategory = (e: React.FormEvent) => {
    e.preventDefault();
    if (!name.trim()) {
      setError('Name is required');
      return;
    }
    createCategoryMutation.mutate({
      name: name.trim(),
      description: description.trim()
    });
  };

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        duration: 0.1,
        when: 'beforeChildren',
        staggerChildren: 0.05
      }
    },
    exit: {
      opacity: 0,
      transition: {
        duration: 0.1,
        when: 'afterChildren',
        staggerChildren: 0.05,
        staggerDirection: -1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.1 } },
    exit: { opacity: 0, y: -10, transition: { duration: 0.1 } }
  };

  const tableVariants = {
    hidden: { opacity: 0, x: 20 },
    visible: { opacity: 1, x: 0, transition: { duration: 0.1 } },
    exit: { opacity: 0, x: -20, transition: { duration: 0.1 } }
  };

  const headerVariants = {
    hidden: { opacity: 0, y: -10 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.1 } },
    exit: { opacity: 0, y: -10, transition: { duration: 0.1 } }
  };

  return (
    <div className='pb-4'>
      <AnimatePresence mode='wait'>
        <motion.div
          key={selectedCategory ? 'category-header' : 'main-header'}
          initial='hidden'
          animate='visible'
          exit='exit'
          variants={headerVariants}
          className='flex items-center justify-between pb-4'
        >
          {selectedCategory ? (
            <>
              <div className='flex items-center gap-2'>
                <Button
                  variant='ghost'
                  size='icon'
                  onClick={handleBackClick}
                  className='h-8 w-8'
                >
                  <ArrowLeft className='h-4 w-4' />
                </Button>
                <h2 className='text-xl font-semibold'>
                  {capitalize(normalizeNameForMap(selectedCategory.name))}
                </h2>
                <Button
                  variant='ghost'
                  size='icon'
                  className='h-8 w-8 text-red-500'
                  title='Delete category'
                  onClick={(e) => handleDeleteClick(e, selectedCategory)}
                >
                  <Trash2 className='h-4 w-4' />
                </Button>
              </div>
            </>
          ) : (
            <>
              <h2 className='text-xl font-semibold'>Sequence Programs</h2>
              <Button size='sm' className='h-9 gap-1' onClick={handleOpenModal}>
                <PlusCircle className='h-4 w-4' />
                <span>Create Folder</span>
              </Button>
              <Dialog open={open} onOpenChange={setOpen}>
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle>Create Program Category</DialogTitle>
                    <DialogDescription>
                      Enter a name and (optionally) a description for the new
                      program category.
                    </DialogDescription>
                  </DialogHeader>
                  <form onSubmit={handleCreateCategory} className='space-y-4'>
                    <div>
                      <label
                        className='mb-1 block text-sm font-medium'
                        htmlFor='category-name'
                      >
                        Name <span className='text-red-500'>*</span>
                      </label>
                      <Input
                        id='category-name'
                        value={name}
                        onChange={(e) => setName(e.target.value)}
                        required
                        autoFocus
                        placeholder='Category name'
                        disabled={createCategoryMutation.isPending}
                      />
                    </div>
                    <div>
                      <label
                        className='mb-1 block text-sm font-medium'
                        htmlFor='category-description'
                      >
                        Description
                      </label>
                      <Textarea
                        id='category-description'
                        value={description}
                        onChange={(e) => setDescription(e.target.value)}
                        placeholder='Optional description'
                        disabled={createCategoryMutation.isPending}
                      />
                    </div>
                    {error && (
                      <div className='text-sm text-red-500'>{error}</div>
                    )}
                    <DialogFooter>
                      <Button
                        type='submit'
                        disabled={createCategoryMutation.isPending}
                      >
                        {createCategoryMutation.isPending
                          ? 'Creating...'
                          : 'Create'}
                      </Button>
                      <DialogClose asChild>
                        <Button
                          type='button'
                          variant='outline'
                          onClick={handleCloseModal}
                          disabled={createCategoryMutation.isPending}
                        >
                          Cancel
                        </Button>
                      </DialogClose>
                    </DialogFooter>
                  </form>
                </DialogContent>
              </Dialog>
            </>
          )}
        </motion.div>
      </AnimatePresence>

      <AnimatePresence mode='wait'>
        {selectedCategory ? (
          <motion.div
            key='programs-table'
            initial='hidden'
            animate='visible'
            exit='exit'
            variants={tableVariants}
          >
            <ProgramsTable
              categoryId={selectedCategory.id}
              productId={productId} // Pass productId down
              categoryName={selectedCategory.name} // Pass category name
            />
          </motion.div>
        ) : isLoading ? (
          <motion.div
            key='loading-skeleton'
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
          >
            <CategorySkeleton />
          </motion.div>
        ) : programCategories && programCategories.length > 0 ? (
          <motion.div
            key='category-grid'
            className='grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4'
            variants={containerVariants}
            initial='hidden'
            animate='visible'
            exit='exit'
          >
            {programCategories.map((category: ProgramCategoryWithCount) => (
              <motion.div
                key={category.id}
                className='group relative cursor-pointer'
                onClick={() => handleCategoryClick(category)}
                variants={itemVariants}
                whileHover={{ scale: 1.02, transition: { duration: 0.1 } }}
              >
                <div className='relative rounded-lg bg-white p-6 shadow-sm transition-all hover:shadow-md dark:bg-gray-800 dark:hover:bg-gray-800/80'>
                  <div className='flex flex-col items-center justify-center gap-3 text-center'>
                    <div className='rounded-md bg-blue-50 p-3 dark:bg-blue-900/20'>
                      {getCategoryIcon(category.name)}
                    </div>
                    <h3 className='font-medium'>
                      {capitalize(normalizeNameForMap(category.name))}
                    </h3>
                    <p className='text-xs text-muted-foreground'>
                      {category.programCount} program
                      {category.programCount !== 1 ? 's' : ''}
                    </p>
                  </div>
                </div>
              </motion.div>
            ))}
          </motion.div>
        ) : (
          <motion.p
            key='no-categories'
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className='text-center text-muted-foreground'
          >
            No program categories found for this product.
          </motion.p>
        )}
      </AnimatePresence>
      {/* Delete Category AlertDialog */}
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Program Category</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete{' '}
              <span className='font-semibold'>{categoryToDelete?.name}</span>?
              This action cannot be undone and will remove the category and all
              its programs.
            </AlertDialogDescription>
          </AlertDialogHeader>
          {deleteError && (
            <div className='text-sm text-red-500'>{deleteError}</div>
          )}
          <AlertDialogFooter>
            <AlertDialogCancel disabled={deleteCategoryMutation.isPending}>
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteConfirm}
              disabled={deleteCategoryMutation.isPending}
              className='bg-red-600 hover:bg-red-700'
            >
              {deleteCategoryMutation.isPending ? 'Deleting...' : 'Delete'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
