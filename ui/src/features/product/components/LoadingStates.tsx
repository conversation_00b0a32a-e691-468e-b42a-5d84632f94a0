import React from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';

export function ProductSkeleton() {
  return (
    <div className='mx-auto max-w-6xl space-y-6 px-4 py-6 sm:px-6 lg:px-8'>
      {/* Product loading Skeleton */}
      <Card>
        <CardHeader>
          <div className='flex items-center justify-between'>
            <Skeleton className='h-8 w-1/4' />
            <Skeleton className='h-6 w-16' />
          </div>
          <Skeleton className='mt-1 h-4 w-1/3' />
        </CardHeader>
        <CardContent className='grid grid-cols-1 gap-6 md:grid-cols-3'>
          <Skeleton className='flex h-64 items-center justify-center rounded-md bg-gray-100 dark:bg-gray-800 md:col-span-1' />
          <div className='space-y-4 md:col-span-2'>
            <div className='mb-4 grid grid-cols-2 gap-x-4 gap-y-2'>
              <Skeleton className='h-4 w-full' />
              <Skeleton className='h-4 w-full' />
              <Skeleton className='h-4 w-full' />
              <Skeleton className='h-4 w-full' />
            </div>
            <Skeleton className='mb-2 mt-6 h-6 w-1/3' />
            <Skeleton className='h-4 w-full' />
            <Skeleton className='h-4 w-3/4' />
          </div>
        </CardContent>
      </Card>
      <Skeleton className='h-10 w-full' />
    </div>
  );
}

export function CategorySkeleton() {
  return (
    <div className='grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4'>
      {[...Array(4)].map((_, i) => (
        <Skeleton key={i} className='h-36 w-full rounded-lg' />
      ))}
    </div>
  );
}

export function SubroutinesSkeleton() {
  return (
    <div className='space-y-2'>
      <Skeleton className='h-4 w-3/4' />
      <Skeleton className='h-4 w-1/2' />
      <Skeleton className='h-4 w-2/3' />
    </div>
  );
}
