'use client';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Eye, Search, X, FileSpreadsheet, Plus } from 'lucide-react';
import { DataTable } from '@/components/ui/table/data-table';
import { useRouter } from 'next/navigation';
import { ColumnDef } from '@tanstack/react-table';
import { Row } from '@tanstack/react-table';
import { useState, useEffect } from 'react';
import moment from 'moment';
import { useListProgramsByCategory } from '@/queries/program';
import { useDebounce } from '@/hooks/use-debounce';
import { ArrowUpDown, ArrowUp, ArrowDown } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { Program } from 'types';
import { toast } from 'sonner';
import { getStatusDotColor } from '@/lib/utils';

// Define props interface
interface ProgramsTableProps {
  categoryId: number;
  productId?: number | null; // Add optional productId
  categoryName?: string; // Add optional categoryName
  condensed?: boolean; // Add optional condensed parameter
}

export default function ProgramsTable({
  categoryId,
  productId, // Destructure productId
  categoryName, // Destructure categoryName
  condensed = false // Destructure condensed with default value
}: ProgramsTableProps) {
  const router = useRouter();
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(condensed ? 5 : 10);
  const [searchTerm, setSearchTerm] = useState('');
  const [isSearching, setIsSearching] = useState(false);
  const [sortField, setSortField] = useState<string>('created_at');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');

  // Debounce search term to avoid too many API calls
  const debouncedSearchTerm = useDebounce(searchTerm, 300);

  // Query hook for fetching programs by category
  const { data, isLoading, isFetching } = useListProgramsByCategory({
    categoryId,
    productId, // Pass productId to the hook
    skip: (page - 1) * pageSize,
    limit: pageSize,
    search: debouncedSearchTerm,
    sortField,
    sortDirection
  });

  // Reset to first page when search term or sort changes
  useEffect(() => {
    setPage(1);
  }, [debouncedSearchTerm, sortField, sortDirection]);

  // Set searching state based on fetching status
  useEffect(() => {
    setIsSearching(isFetching);
  }, [isFetching]);

  const programs = data?.programs || [];
  const totalPages = data?.total ? Math.ceil(data.total / pageSize) : 0;

  // Mock handlers for the Import and Create buttons
  const handleImport = () => {
    toast.info('Excel import feature coming soon');
  };

  const handleCreate = () => {
    toast.info('Create program feature coming soon');
  };

  const handlePageSizeChange = (newPageSize: number) => {
    setPageSize(newPageSize);
    setPage(1);
  };

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
  };

  const clearSearch = () => {
    setSearchTerm('');
  };

  // Handle sorting
  const handleSort = (field: string) => {
    if (sortField === field) {
      // Toggle direction if clicking the same field
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      // Set new field and default to ascending
      setSortField(field);
      setSortDirection('asc');
    }
  };

  // Helper to render sort indicator
  const renderSortIndicator = (field: string) => {
    if (sortField !== field) {
      return <ArrowUpDown className='ml-2 h-4 w-4' />;
    }
    return sortDirection === 'asc' ? (
      <ArrowUp className='ml-2 h-4 w-4' />
    ) : (
      <ArrowDown className='ml-2 h-4 w-4' />
    );
  };

  const normalizedCategoryName = categoryName?.toLowerCase() || '';
  const isAiCategory = ['ai programs', 'generated programs'].includes(
    normalizedCategoryName
  );

  const columns: ColumnDef<Program>[] = isAiCategory
    ? [
        {
          accessorKey: 'name',
          header: ({ column }) => (
            <div
              className='flex cursor-pointer items-center'
              onClick={() => handleSort('name')}
            >
              Name
              {renderSortIndicator('name')}
            </div>
          ),
          cell: ({ row }: { row: Row<Program> }) => (
            <div className='max-w-[250px] truncate font-medium'>
              {row.getValue('name')}
            </div>
          )
        },
        {
          accessorKey: 'user_name',
          header: 'Created By',
          cell: ({ row }: { row: Row<Program> }) => (
            <div>{row.getValue('user_name') || 'N/A'}</div>
          )
        },
        {
          accessorKey: 'status',
          header: 'Status',
          cell: ({ row }: { row: Row<Program> }) => {
            const status = row.getValue('status') as string;
            const displayStatus = status || 'Draft';
            return (
              <div className='flex items-center gap-2'>
                <div className={`h-2 w-2 rounded-full ${getStatusDotColor(displayStatus)}`} />
                <span className='text-sm'>{displayStatus}</span>
              </div>
            );
          }
        },
        {
          accessorKey: 'created_at',
          header: ({ column }) => (
            <div
              className='flex cursor-pointer items-center'
              onClick={() => handleSort('created_at')}
            >
              Created At
              {renderSortIndicator('created_at')}
            </div>
          ),
          cell: ({ row }: { row: Row<Program> }) => (
            <div
              title={moment
                .utc(row.getValue('created_at'))
                .local()
                .format('LLLL')}
              className='whitespace-nowrap'
            >
              {moment.utc(row.getValue('created_at')).local().fromNow()}
            </div>
          )
        },
        {
          id: 'actions',
          header: '',
          cell: ({ row }: { row: Row<Program> }) => (
            <div
              className='flex items-center justify-center space-x-2'
              onClick={(e) => e.stopPropagation()}
            >
              <Button
                variant='ghost'
                size='icon'
                title='Edit Program'
                onClick={() =>
                  router.push(`/dashboard/program-editor?id=${row.original.id}`)
                }
                className='h-8 w-8'
              >
                <Eye className='h-4 w-4' />
              </Button>
            </div>
          ),
          enableSorting: false,
          enableHiding: false
        }
      ]
    : [
        {
          accessorKey: 'program_title',
          header: ({ column }) => (
            <div
              className='flex cursor-pointer items-center'
              onClick={() => handleSort('program_title')}
            >
              Title
              {renderSortIndicator('program_title')}
            </div>
          ),
          cell: ({ row }: { row: Row<Program> }) => (
            <div className='font-medium'>{row.getValue('program_title')}</div>
          )
        },
        {
          accessorKey: 'name',
          header: ({ column }) => (
            <div
              className='flex cursor-pointer items-center'
              onClick={() => handleSort('name')}
            >
              Name
              {renderSortIndicator('name')}
            </div>
          ),
          cell: ({ row }: { row: Row<Program> }) => (
            <div className='max-w-[150px] truncate'>{row.getValue('name')}</div>
          )
        },
        {
          accessorKey: 'source_filename',
          header: ({ column }) => (
            <div
              className='flex cursor-pointer items-center'
              onClick={() => handleSort('source_filename')}
            >
              Source File
              {renderSortIndicator('source_filename')}
            </div>
          ),
          cell: ({ row }: { row: Row<Program> }) => (
            <div className='max-w-[150px] truncate'>
              {row.getValue('source_filename')}
            </div>
          )
        },
        {
          accessorKey: 'created_at',
          header: ({ column }) => (
            <div
              className='flex cursor-pointer items-center'
              onClick={() => handleSort('created_at')}
            >
              Created At
              {renderSortIndicator('created_at')}
            </div>
          ),
          cell: ({ row }: { row: Row<Program> }) => (
            <div
              title={moment
                .utc(row.getValue('created_at'))
                .local()
                .format('LLLL')}
              className='whitespace-nowrap'
            >
              {moment.utc(row.getValue('created_at')).local().fromNow()}
            </div>
          )
        },
        {
          id: 'actions',
          header: '',
          cell: ({ row }: { row: Row<Program> }) => (
            <div
              className='flex items-center justify-center space-x-2'
              onClick={(e) => e.stopPropagation()}
            >
              <Button
                variant='ghost'
                size='icon'
                title='Edit Program'
                onClick={() =>
                  router.push(`/dashboard/program-editor?id=${row.original.id}`)
                }
                className='h-8 w-8'
              >
                <Eye className='h-4 w-4' />
              </Button>
            </div>
          ),
          enableSorting: false,
          enableHiding: false
        }
      ];

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        duration: 0.1,
        when: 'beforeChildren',
        staggerChildren: 0.05
      }
    },
    exit: {
      opacity: 0,
      transition: { duration: 0.2 }
    }
  };

  const searchVariants = {
    hidden: { opacity: 0, y: -10 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.2 } }
  };

  return (
    <motion.div
      className={`rounded-lg bg-white p-4 shadow-sm dark:bg-gray-800 ${
        condensed ? 'text-sm' : ''
      }`}
      variants={containerVariants}
      initial='hidden'
      animate='visible'
      exit='exit'
    >
      <motion.div
        className={`${condensed ? 'mb-2' : 'mb-4'} flex items-center justify-between`}
        variants={searchVariants}
      >
        <div className='relative mr-4 flex-1'>
          <div className='pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3'>
            <Search className='h-4 w-4 text-gray-400' />
          </div>
          <Input
            type='text'
            placeholder='Search programs by title or name...'
            value={searchTerm}
            onChange={handleSearchChange}
            className={`pl-10 pr-10 ${condensed ? 'h-8 text-xs' : ''}`}
          />
          <AnimatePresence>
            {searchTerm && (
              <motion.button
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                onClick={clearSearch}
                className='absolute inset-y-0 right-0 flex items-center pr-3'
              >
                <X className='h-4 w-4 text-gray-400 hover:text-gray-600' />
              </motion.button>
            )}
          </AnimatePresence>
        </div>
        {!condensed && (
          <div className='flex space-x-2'>
            <Button
              onClick={handleImport}
              variant='outline'
              className={`flex items-center ${condensed ? 'h-8 px-2 py-1 text-xs' : ''}`}
              size={condensed ? 'sm' : 'default'}
            >
              <FileSpreadsheet
                className={`${condensed ? 'h-3 w-3' : 'h-4 w-4'} mr-2`}
              />
              Import Excel
            </Button>
            <Button
              onClick={handleCreate}
              variant='default'
              className={`flex items-center ${condensed ? 'h-8 px-2 py-1 text-xs' : ''}`}
              size={condensed ? 'sm' : 'default'}
            >
              <Plus className={`${condensed ? 'h-3 w-3' : 'h-4 w-4'} mr-2`} />
              Create
            </Button>
          </div>
        )}
      </motion.div>

      <AnimatePresence>
        {isSearching && !isLoading && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className='mb-2 text-sm text-gray-500'
          >
            Searching...
          </motion.div>
        )}
      </AnimatePresence>

      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.1, delay: 0.1 }}
        className={condensed ? 'scale-95 transform' : ''}
      >
        <DataTable
          data={programs}
          columns={columns}
          isLoading={isLoading}
          pagination={{
            page,
            pageCount: totalPages,
            pageSize,
            onPageChange: setPage,
            onPageSizeChange: handlePageSizeChange
          }}
          onRowClick={(row) =>
            router.push(`/dashboard/program-editor?id=${row.original.id}`)
          }
          condensed={condensed}
        />
      </motion.div>

      <AnimatePresence>
        {programs.length === 0 && !isLoading && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className='mt-4 text-center text-gray-500'
          >
            {searchTerm
              ? 'No programs found matching your search.'
              : 'No programs available.'}
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  );
}
