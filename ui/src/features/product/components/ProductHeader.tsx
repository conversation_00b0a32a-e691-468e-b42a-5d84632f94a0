import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Cog, Tag, ImageOff, Info, ChevronDown, ChevronUp } from 'lucide-react';
import { Product } from 'types';
import { Button } from '@/components/ui/button';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger
} from '@/components/ui/tooltip';
import { getImageUrl } from '@/lib/utils';

interface ProductHeaderProps {
  product: Product;
  condensed?: boolean;
}

export function ProductHeader({
  product,
  condensed = false
}: ProductHeaderProps) {
  const imageUrl = getImageUrl(product.image_url);
  const [expanded, setExpanded] = useState(false);
  const [imageLoaded, setImageLoaded] = useState(false);
  const [showFullDetails, setShowFullDetails] = useState(!condensed);

  const toggleExpand = () => setExpanded(!expanded);
  const toggleDetails = () => setShowFullDetails(!showFullDetails);

  // Safely handle status
  const statusColor = 'bg-gray-400';
  const statusText = 'Idle';

  return (
    <Card
      className={`overflow-hidden border-none transition-shadow duration-300 ${showFullDetails ? '' : 'shadow-sm'}`}
    >
      <CardHeader className='bg-gradient-to-r from-slate-50 to-white pb-3 dark:from-slate-900 dark:to-slate-800'>
        <div className='flex items-start justify-between'>
          <div className='flex items-center gap-4'>
            {/* Compact Image Preview for Condensed Mode */}
            {condensed && imageUrl && (
              <div className='hidden h-16 w-16 overflow-hidden rounded-md border border-slate-200 shadow-sm dark:border-slate-700 sm:block'>
                <img
                  src={imageUrl}
                  alt={product.name}
                  className='h-full w-full object-cover'
                  onError={(e) => {
                    (e.target as HTMLImageElement).style.display = 'none';
                  }}
                />
              </div>
            )}
            <CardTitle
              className={`bg-gradient-to-r from-slate-900 to-slate-700 bg-clip-text ${condensed ? 'text-2xl' : 'text-3xl'} font-bold text-transparent dark:from-slate-100 dark:to-slate-300`}
            >
              {product.name}
            </CardTitle>
          </div>
          <div className='flex items-center gap-2'>
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Badge
                    variant='outline'
                    className='flex h-6 cursor-help items-center gap-1 px-2 hover:bg-slate-100 dark:hover:bg-slate-700'
                  >
                    <span
                      className={`h-2 w-2 rounded-full ${statusColor} animate-pulse`}
                    ></span>
                    <span className='text-xs font-medium'>{statusText}</span>
                  </Badge>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Current device status</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>

            {condensed && (
              <Button
                variant='outline'
                size='sm'
                onClick={toggleDetails}
                className='h-6 px-2 text-xs'
              >
                {showFullDetails ? 'Less' : 'More'}
              </Button>
            )}
          </div>
        </div>

        {/* Quick Info Row for Condensed Mode */}
        {condensed && (
          <div className='mt-2 flex flex-wrap items-center gap-x-6 gap-y-1 text-sm text-slate-600 dark:text-slate-400'>
            {product.model && (
              <div className='flex items-center gap-1'>
                <Cog className='h-3.5 w-3.5 shrink-0 text-blue-500 dark:text-blue-400' />
                <span className='font-medium'>{product.model}</span>
              </div>
            )}
            {product.series && (
              <div className='flex items-center gap-1'>
                <span className='font-medium'>{product.series}</span>
              </div>
            )}
            {product.type && (
              <div className='flex items-center gap-1'>
                <Tag className='h-3.5 w-3.5 shrink-0 text-blue-500 dark:text-blue-400' />
                <span className='font-medium'>{product.type}</span>
                {product.subtype && <span>({product.subtype})</span>}
              </div>
            )}
          </div>
        )}
      </CardHeader>

      {/* Show the content only if we're not in condensed mode or if the full details are requested */}
      {(!condensed || showFullDetails) && (
        <CardContent className='grid grid-cols-1 gap-x-6 gap-y-8 pt-6 md:grid-cols-4'>
          <div className='mx-auto flex aspect-square max-w-xs transform items-center justify-center overflow-hidden rounded-lg border border-slate-200 bg-card shadow-sm transition-transform duration-300 hover:scale-[1.02] dark:border-slate-700 dark:bg-slate-800 md:col-span-1 md:mx-0'>
            {imageUrl ? (
              <div className='relative h-full w-full'>
                {!imageLoaded && (
                  <div className='absolute inset-0 flex items-center justify-center bg-slate-100 dark:bg-slate-800'>
                    <div className='h-8 w-8 animate-spin rounded-full border-4 border-b-blue-700 border-l-transparent border-r-transparent border-t-blue-500'></div>
                  </div>
                )}
                <img
                  src={imageUrl}
                  alt={product.name}
                  className={`h-full w-full object-cover transition-opacity duration-300 ${imageLoaded ? 'opacity-100' : 'opacity-0'}`}
                  onLoad={() => setImageLoaded(true)}
                  onError={(e) => {
                    console.error('Image failed to load:', imageUrl);
                    (e.target as HTMLImageElement).style.display = 'none';
                    const parent = (e.target as HTMLImageElement).parentElement;
                    if (parent) {
                      parent.innerHTML =
                        '<div class="flex h-full w-full items-center justify-center bg-slate-100 dark:bg-slate-800"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-image-off h-12 w-12 text-slate-400"><path d="M10.4 10.4 2.3 2.3"/><path d="m21.7 21.7-2.4-2.4"/><path d="M14.5 8.5a2 2 0 0 1 3 0v5.5"/><path d="M5 12V4a2 2 0 0 1 2-2h8.5"/><path d="m14 14 1-1"/><path d="M22 12v8a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2v-6"/><path d="M2 12a2 2 0 0 1 2-2h4.5"/><path d="m4.2 10.8 1.8-1.8"/><path d="M2.3 2.3 8 8"/></svg><p class="mt-4 text-sm text-slate-500">Image not available</p></div>';
                    }
                  }}
                />
              </div>
            ) : (
              <div className='flex h-full w-full flex-col items-center justify-center bg-slate-100 dark:bg-slate-800'>
                <ImageOff className='h-12 w-12 text-slate-400' />
                <p className='mt-4 text-sm text-slate-500'>
                  Image not available
                </p>
              </div>
            )}
          </div>
          <div className='space-y-3 md:col-span-3'>
            {!condensed && (
              <div className='grid grid-cols-1 gap-x-4 gap-y-2 rounded-lg bg-slate-50 p-2 dark:bg-slate-800/50 sm:grid-cols-2'>
                {product.model && (
                  <div className='flex items-center gap-2 rounded-md p-1 transition-colors'>
                    <Cog className='h-5 w-5 shrink-0 text-blue-500 dark:text-blue-400' />
                    <span className='text-sm font-medium text-slate-600 dark:text-slate-400'>
                      Model:
                    </span>
                    <span className='text-sm font-semibold'>
                      {product.model}
                    </span>
                  </div>
                )}
                {product.series && (
                  <div className='flex items-center gap-2 rounded-md p-1 transition-colors'>
                    <Cog className='h-5 w-5 shrink-0 text-blue-500 dark:text-blue-400' />
                    <span className='text-sm font-medium text-slate-600 dark:text-slate-400'>
                      Series:
                    </span>
                    <span className='text-sm font-semibold'>
                      {product.series}
                    </span>
                  </div>
                )}
                {product.type && (
                  <div className='flex items-center gap-2 rounded-md p-1 transition-colors'>
                    <Tag className='h-5 w-5 shrink-0 text-blue-500 dark:text-blue-400' />
                    <span className='text-sm font-medium text-slate-600 dark:text-slate-400'>
                      Type:
                    </span>
                    <span className='text-sm font-semibold'>
                      {product.type}
                    </span>
                  </div>
                )}
                {product.subtype && (
                  <div className='flex items-center gap-2 rounded-md p-1 transition-colors'>
                    <Tag className='h-5 w-5 shrink-0 text-blue-500 dark:text-blue-400' />
                    <span className='text-sm font-medium text-slate-600 dark:text-slate-400'>
                      Subtype:
                    </span>
                    <span className='text-sm font-semibold'>
                      {product.subtype}
                    </span>
                  </div>
                )}
              </div>
            )}

            {/* Description Section with expand/collapse */}
            {product.description && (
              <div className='rounded-lg border border-slate-100 bg-white p-4 shadow-sm dark:border-slate-700 dark:bg-slate-800'>
                <div className='mb-2 flex items-center justify-between'>
                  <h3 className='flex items-center text-lg font-semibold'>
                    <Info className='mr-2 h-5 w-5 text-blue-500' />
                    Description
                  </h3>
                  <Button
                    variant='ghost'
                    size='sm'
                    onClick={toggleExpand}
                    className='h-8 px-2 text-slate-500 hover:text-slate-900'
                  >
                    {expanded ? (
                      <ChevronUp className='mr-1 h-4 w-4' />
                    ) : (
                      <ChevronDown className='mr-1 h-4 w-4' />
                    )}
                    {expanded ? 'Less' : 'More'}
                  </Button>
                </div>
                <p
                  className={`whitespace-pre-wrap text-sm leading-relaxed text-slate-700 dark:text-slate-300 ${expanded ? '' : 'line-clamp-2'}`}
                >
                  {product.description}
                </p>
              </div>
            )}

            {/* Features List - Enhanced */}
            {product.features && product.features.length > 0 && (
              <div className='rounded-lg border border-slate-100 bg-white p-4 shadow-sm dark:border-slate-700 dark:bg-slate-800'>
                <h3 className='mb-2 text-lg font-semibold'>Key Features</h3>
                <ul className='grid grid-cols-1 gap-1 sm:grid-cols-2'>
                  {product.features.map((feature, index) => (
                    <li
                      key={index}
                      className='flex items-start gap-2 rounded-md p-1 transition-all'
                    >
                      <div className='mt-1 h-2 w-2 shrink-0 rounded-full bg-blue-500'></div>
                      <span className='text-sm'>{feature}</span>
                    </li>
                  ))}
                </ul>
              </div>
            )}
          </div>
        </CardContent>
      )}
    </Card>
  );
}
