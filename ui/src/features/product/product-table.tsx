'use client';
import { But<PERSON> } from '@/components/ui/button';
import { Edit, PlusCircle, Trash2, Eye } from 'lucide-react'; // Added Eye icon
import { DataTable } from '@/components/ui/table/data-table';
import { useRouter } from 'next/navigation'; // Added useRouter
import { ColumnDef } from '@tanstack/react-table';
import { Product } from 'types';
import { Row } from '@tanstack/react-table';
import { useListProducts, useDeleteProduct } from '@/queries/product';
import { useEffect, useState } from 'react';
import moment from 'moment';
import { ProductDialog } from './product-dialog';
import DeleteConfirmationDialog from '@/components/ui/delete-confirmation-dialog'; // Import reusable dialog
import { toast } from 'sonner';

export default function ProductTable() {
  const router = useRouter(); // Initialize router
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(20);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [editProduct, setEditProduct] = useState<Product | null>(null);
  const [productToDelete, setProductToDelete] = useState<Product | null>(null); // New state for the product object
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false); // Dialog visibility state

  const { data, isLoading, refetch } = useListProducts({
    skip: (page - 1) * pageSize,
    limit: pageSize
  });

  const deleteProductMutation = useDeleteProduct({
    onSuccess: () => {
      toast.success('Product deleted');
      refetch();
    },
    onError: (error) => {
      toast.error(`Failed to delete product: ${error.message}`);
    }
  });

  const products = data?.products || [];
  const totalPages = data?.total ? Math.ceil(data.total / pageSize) : 0;

  const handlePageSizeChange = (newPageSize: number) => {
    setPageSize(newPageSize);
    setPage(1);
  };

  useEffect(() => {
    refetch();
  }, [refetch]);

  const handleCreateSuccess = () => {
    setIsCreateDialogOpen(false);
    refetch();
  };

  const handleEditSuccess = () => {
    setEditProduct(null);
    refetch();
  };

  const openDeleteDialog = (product: Product) => {
    setProductToDelete(product);
    setIsDeleteDialogOpen(true);
  };

  const handleDeleteConfirm = () => {
    if (productToDelete) {
      deleteProductMutation.mutate(productToDelete.id);
      setProductToDelete(null); // Clear after initiating delete
    }
    setIsDeleteDialogOpen(false); // Close dialog
  };

  const navigateToProduct = (productId: number) => {
    router.push(`/dashboard/product/${productId}`);
  };

  const columns: ColumnDef<Product>[] = [
    {
      accessorKey: 'name',
      header: 'Name',
      cell: ({ row }: { row: Row<Product> }) => (
        <div className='font-medium'>{row.getValue('name')}</div>
      )
    },
    {
      accessorKey: 'model',
      header: 'Model',
      cell: ({ row }: { row: Row<Product> }) => (
        <div className='max-w-[100px] truncate'>{row.getValue('model')}</div>
      )
    },
    {
      accessorKey: 'series',
      header: 'Series',
      cell: ({ row }: { row: Row<Product> }) => (
        <div className='max-w-[100px] truncate'>
          {row.getValue('series') || '-'}
        </div>
      )
    },
    {
      accessorKey: 'type',
      header: 'Type',
      cell: ({ row }: { row: Row<Product> }) => (
        <div className='max-w-[100px] truncate'>
          {row.getValue('type') || '-'}
        </div>
      )
    },
    {
      accessorKey: 'description',
      header: 'Description',
      cell: ({ row }: { row: Row<Product> }) => (
        <div className='max-w-[200px] truncate'>
          {row.getValue('description')}
        </div>
      )
    },
    {
      accessorKey: 'created_at',
      header: 'Created',
      cell: ({ row }: { row: Row<Product> }) => (
        <div
          title={moment.utc(row.getValue('created_at')).local().format('LLLL')}
          className='whitespace-nowrap'
        >
          {moment.utc(row.getValue('created_at')).local().fromNow()}
        </div>
      )
    },
    {
      id: 'actions',
      header: 'Actions',
      cell: ({ row }: { row: Row<Product> }) => (
        <div
          className='flex items-center justify-center space-x-2'
          onClick={(e) => e.stopPropagation()}
        >
          <Button
            variant='ghost'
            size='icon'
            title='View Product'
            onClick={() => router.push(`/dashboard/product/${row.original.id}`)}
            className='h-8 w-8'
          >
            <Eye className='h-4 w-4' />
          </Button>
          <Button
            variant='ghost'
            size='icon'
            title='Edit Product'
            onClick={() => setEditProduct(row.original)}
            className='h-8 w-8'
          >
            <Edit className='h-4 w-4' />
          </Button>
          <Button
            variant='ghost'
            size='icon'
            title='Delete Product'
            onClick={() => openDeleteDialog(row.original)} // Open dialog for this product
            className='h-8 w-8 text-red-500 hover:bg-red-100 hover:text-red-600 dark:hover:bg-red-900/20'
          >
            <Trash2 className='h-4 w-4' />
          </Button>
        </div>
      ),
      enableSorting: false,
      enableHiding: false
    }
  ];

  return (
    <>
      <div className='mb-6'>
        <div className='flex items-center justify-between'>
          <div>
            <h2 className='text-2xl font-bold tracking-tight'>Products</h2>
            <p className='text-muted-foreground'>Manage your products here</p>
          </div>
          <Button onClick={() => setIsCreateDialogOpen(true)}>
            <PlusCircle className='mr-2 h-4 w-4' />
            Add Product
          </Button>
        </div>
      </div>
      <div className='overflow-hidden rounded-md border'>
        <style jsx global>{`
          table {
            table-layout: fixed;
            width: 100%;
          }
          th:nth-child(1),
          td:nth-child(1) {
            width: 15%;
          }
          th:nth-child(2),
          td:nth-child(2) {
            width: 10%;
          }
          th:nth-child(3),
          td:nth-child(3) {
            width: 10%;
          }
          th:nth-child(4),
          td:nth-child(4) {
            width: 10%;
          }
          th:nth-child(5),
          td:nth-child(5) {
            width: 25%;
          }
          th:nth-child(6),
          td:nth-child(6) {
            width: 15%;
          }
          th:nth-child(7),
          td:nth-child(7) {
            width: 15%;
          }
        `}</style>
        <DataTable
          data={products}
          columns={columns}
          isLoading={isLoading}
          pagination={{
            page,
            pageCount: totalPages,
            pageSize,
            onPageChange: setPage,
            onPageSizeChange: handlePageSizeChange
          }}
          onRowClick={(row) => navigateToProduct(row.original.id)}
        />
      </div>

      <ProductDialog
        open={isCreateDialogOpen}
        onOpenChange={setIsCreateDialogOpen}
        onSuccess={handleCreateSuccess}
      />

      {editProduct && (
        <ProductDialog
          open={!!editProduct}
          onOpenChange={(open) => !open && setEditProduct(null)}
          product={editProduct}
          onSuccess={handleEditSuccess}
        />
      )}

      {productToDelete && (
        <DeleteConfirmationDialog
          open={isDeleteDialogOpen}
          onOpenChange={(open) => {
            setIsDeleteDialogOpen(open);
            if (!open) {
              setProductToDelete(null); // Clear product if dialog is closed
            }
          }}
          onConfirm={handleDeleteConfirm}
          itemName={`product "${productToDelete.name}"`}
          triggerComponent={null} // Trigger is handled by the button in the row calling openDeleteDialog
          title='Delete Product?'
          description='This action cannot be undone. This will permanently delete the product.'
          confirmButtonVariant='destructive'
        />
      )}
    </>
  );
}
