'use client';
import { <PERSON><PERSON> } from '@/components/ui/button';
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { toast } from 'sonner';
// Import the correct mutation payload type
import { useCreateProduct, useUpdateProduct } from '@/queries/product';
import { ProductMutationPayload } from '@/services/product.service'; // Import the payload type
import { useState, useEffect, useRef } from 'react'; // Added useEffect, useRef
import { Product } from 'types';
import { X, Upload, Trash2, Plus, ScanLine } from 'lucide-react'; // Added Upload, Trash2, Plus, ScanLine
import { Badge } from '@/components/ui/badge';
import { AxiosError } from 'axios';
import Image from 'next/image'; // Use Next.js Image for optimization
import { getImageUrl } from '@/lib/utils';
import { Card, CardContent } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';

interface ProductDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  product?: Product; // Product type should include image_url if defined in 'types'
  onSuccess?: () => void;
}

export function ProductDialog({
  open,
  onOpenChange,
  product,
  onSuccess
}: ProductDialogProps) {
  const [name, setName] = useState('');
  const [description, setDescription] = useState('');
  const [model, setModel] = useState('');
  const [series, setSeries] = useState('');
  const [type, setType] = useState('');
  const [subtype, setSubtype] = useState('');
  const [featureInput, setFeatureInput] = useState('');
  const [features, setFeatures] = useState<string[]>([]);
  const [imageFile, setImageFile] = useState<File | null>(null);
  const [imagePreviewUrl, setImagePreviewUrl] = useState<string | null>(null);
  // Track if the user explicitly requested image removal
  const [removeImageFlag, setRemoveImageFlag] = useState<boolean>(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null); // Ref for file input

  // Effect to initialize form state when product changes or dialog opens
  useEffect(() => {
    if (open && product) {
      setName(product.name || '');
      setDescription(product.description || '');
      setModel(product.model || '');
      setSeries(product.series || '');
      setType(product.type || '');
      setSubtype(product.subtype || '');
      setFeatures(product.features || []);
      // Set initial image preview from existing product URL
      const fullUrl = getImageUrl(product.image_url);
      setImagePreviewUrl(fullUrl);
      setImageFile(null); // Reset file input
      setRemoveImageFlag(false); // Reset removal flag
    } else if (open) {
      // Reset form for 'create' mode when dialog opens
      resetForm();
    }
    // Intentionally not resetting form on close, only on open
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [product, open]); // Rerun when product or open state changes

  // Effect for cleaning up object URLs
  useEffect(() => {
    let objectUrl: string | null = null;
    if (imageFile) {
      objectUrl = URL.createObjectURL(imageFile);
      setImagePreviewUrl(objectUrl);
      setRemoveImageFlag(false); // Selecting a new file cancels removal intent
    }

    // Cleanup function
    return () => {
      if (objectUrl) {
        URL.revokeObjectURL(objectUrl);
        // console.log("Revoked Object URL:", objectUrl); // For debugging
      }
    };
  }, [imageFile]); // Rerun only when imageFile changes

  const createProductMutation = useCreateProduct({
    onSuccess: (response) => {
      toast.success(`Product "${response.name}" created`);
      resetForm();
      onSuccess?.();
      onOpenChange(false); // Close dialog on success
    },
    onError: (error) => {
      setIsSubmitting(false);
      let errorMessage = 'An unknown error occurred';
      if (error instanceof AxiosError && error.response?.data?.detail) {
        errorMessage = error.response.data.detail;
      } else if (error.message) {
        errorMessage = error.message;
      }
      toast.error(`Failed to create product: ${errorMessage}`);
    }
  });

  const updateProductMutation = useUpdateProduct({
    onSuccess: (response) => {
      toast.success(`Product "${response.name}" updated`);
      resetForm();
      onSuccess?.();
      onOpenChange(false); // Close dialog on success
    },
    onError: (error) => {
      setIsSubmitting(false);
      let errorMessage = 'An unknown error occurred';
      if (error instanceof AxiosError && error.response?.data?.detail) {
        errorMessage = error.response.data.detail;
      } else if (error.message) {
        errorMessage = error.message;
      }
      toast.error(`Failed to update product: ${errorMessage}`);
    }
  });

  const resetForm = () => {
    setName('');
    setDescription('');
    setModel('');
    setSeries('');
    setType('');
    setSubtype('');
    setFeatureInput('');
    setFeatures([]);
    setImageFile(null);
    setImagePreviewUrl(null);
    setRemoveImageFlag(false);
    setIsSubmitting(false);
    // Reset file input visually
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const handleClose = () => {
    if (!isSubmitting) {
      // Don't reset form here, useEffect handles it based on `open` and `product`
      onOpenChange(false);
    }
  };

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      // Basic validation (optional: add size check)
      if (!file.type.startsWith('image/')) {
        toast.error('Please select a valid image file.');
        return;
      }
      setImageFile(file);
    } else {
      // If selection is cancelled, reset
      setImageFile(null);
      // Revert preview to original product image if editing, or null if creating
      setImagePreviewUrl(product ? getImageUrl(product.image_url) : null);
    }
  };

  const handleRemoveImage = () => {
    setImageFile(null);
    setImagePreviewUrl(null);
    setRemoveImageFlag(true); // Signal intent to remove existing image
    // Reset file input visually
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const handleAddFeature = () => {
    if (featureInput.trim() !== '' && !features.includes(featureInput.trim())) {
      setFeatures([...features, featureInput.trim()]);
      setFeatureInput('');
    }
  };

  const handleRemoveFeature = (featureToRemove: string) => {
    setFeatures(features.filter((feature) => feature !== featureToRemove));
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleAddFeature();
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (!name.trim()) {
      toast.error('Product name is required');
      return;
    }
    if (!model.trim()) {
      toast.error('Product model is required');
      return;
    }

    setIsSubmitting(true);

    // Use the correct payload type
    const payload: ProductMutationPayload = {
      name,
      description,
      model,
      // Send empty strings as null/undefined if desired by backend, or omit if not required
      series: series || undefined,
      type: type || undefined,
      subtype: subtype || undefined,
      // Send features array (service layer converts to JSON string)
      features: features.length > 0 ? features : undefined,
      // Conditionally include image or signal removal
      image: imageFile, // Include the file object if present
      // If updating and removeImageFlag is true, signal removal by setting image_url to null
      image_url: product && removeImageFlag ? null : undefined
    };

    // Clean up undefined fields before sending? Depends on backend strictness.
    // For FormData, undefined fields are typically just omitted.

    if (product) {
      updateProductMutation.mutate({ id: product.id, payload });
    } else {
      createProductMutation.mutate(payload);
    }
  };

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className='max-h-[95vh] w-[75vw] min-w-[350px] max-w-[1200px] overflow-y-auto p-0'>
        <DialogHeader className='p-6 pb-2'>
          <DialogTitle className='text-xl'>
            {product ? 'Edit Product' : 'Create New Product'}
          </DialogTitle>
          <DialogDescription>
            {product
              ? 'Update the product details and image below.'
              : 'Fill in the details and optionally upload an image.'}
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className='space-y-6 px-6'>
          <div className='grid grid-cols-1 gap-6 md:grid-cols-2'>
            {/* Left Column - Basic Info */}
            <div className='space-y-4'>
              <div className='space-y-4'>
                <h3 className='text-sm font-medium text-slate-500'>
                  Basic Information
                </h3>

                <div className='space-y-3'>
                  <div className='space-y-2'>
                    <Label htmlFor='name' className='text-sm'>
                      Name <span className='text-red-500'>*</span>
                    </Label>
                    <Input
                      id='name'
                      value={name}
                      onChange={(e) => setName(e.target.value)}
                      placeholder='Enter product name'
                      required
                      disabled={isSubmitting}
                      className='border-slate-300'
                    />
                  </div>

                  <div className='space-y-2'>
                    <Label htmlFor='model' className='text-sm'>
                      Model <span className='text-red-500'>*</span>
                    </Label>
                    <Input
                      id='model'
                      value={model}
                      onChange={(e) => setModel(e.target.value)}
                      placeholder='Enter product model'
                      required
                      disabled={isSubmitting}
                      className='border-slate-300'
                    />
                  </div>
                </div>
              </div>

              <Separator className='my-4' />

              <div className='space-y-4'>
                <h3 className='text-sm font-medium text-slate-500'>
                  Product Classification
                </h3>

                <div className='grid grid-cols-2 gap-3'>
                  <div className='space-y-2'>
                    <Label htmlFor='series' className='text-sm'>
                      Series
                    </Label>
                    <Input
                      id='series'
                      value={series}
                      onChange={(e) => setSeries(e.target.value)}
                      placeholder='Enter series'
                      disabled={isSubmitting}
                      className='border-slate-300'
                    />
                  </div>

                  <div className='space-y-2'>
                    <Label htmlFor='type' className='text-sm'>
                      Type
                    </Label>
                    <Input
                      id='type'
                      value={type}
                      onChange={(e) => setType(e.target.value)}
                      placeholder='Enter type'
                      disabled={isSubmitting}
                      className='border-slate-300'
                    />
                  </div>

                  <div className='col-span-2 space-y-2'>
                    <Label htmlFor='subtype' className='text-sm'>
                      Subtype
                    </Label>
                    <Input
                      id='subtype'
                      value={subtype}
                      onChange={(e) => setSubtype(e.target.value)}
                      placeholder='Enter subtype'
                      disabled={isSubmitting}
                      className='border-slate-300'
                    />
                  </div>
                </div>
              </div>
            </div>

            {/* Right Column - Image & Features */}
            <div className='space-y-4'>
              <div className='space-y-3'>
                <h3 className='text-sm font-medium text-slate-500'>
                  Product Image
                </h3>

                <Card className='border-dashed border-slate-300 bg-slate-50 dark:bg-slate-900/50'>
                  <CardContent className='flex flex-col items-center justify-center p-4 text-center'>
                    {imagePreviewUrl ? (
                      <div className='group relative h-40 w-full rounded-md border border-slate-200 bg-white dark:border-slate-700 dark:bg-slate-800'>
                        <div className='relative h-full w-full'>
                          <Image
                            src={imagePreviewUrl}
                            alt='Product Preview'
                            layout='fill'
                            objectFit='contain'
                            className='rounded-md p-2'
                            onError={() => {
                              console.warn(
                                'Failed to load image:',
                                imagePreviewUrl
                              );
                              setImagePreviewUrl(null);
                            }}
                          />
                          <Button
                            type='button'
                            variant='destructive'
                            size='icon'
                            className='absolute right-2 top-2 h-7 w-7 opacity-70 transition-opacity hover:opacity-100'
                            onClick={handleRemoveImage}
                            disabled={isSubmitting}
                            aria-label='Remove image'
                          >
                            <Trash2 className='h-4 w-4' />
                          </Button>
                        </div>
                      </div>
                    ) : (
                      <div className='flex w-full flex-col items-center justify-center gap-2 p-8 text-slate-500'>
                        <ScanLine className='h-10 w-10 text-slate-400' />
                        <p className='text-sm'>
                          Drag and drop or select an image
                        </p>
                        <Button
                          type='button'
                          variant='secondary'
                          size='sm'
                          className='mt-2'
                          onClick={() => fileInputRef.current?.click()}
                          disabled={isSubmitting}
                        >
                          <Upload className='mr-2 h-4 w-4' /> Select Image
                        </Button>
                      </div>
                    )}
                    <Input
                      id='product-image'
                      type='file'
                      accept='image/*'
                      onChange={handleFileChange}
                      className='hidden'
                      ref={fileInputRef}
                      disabled={isSubmitting}
                    />
                  </CardContent>
                </Card>
              </div>

              <Separator className='my-4' />

              <div className='space-y-3'>
                <h3 className='text-sm font-medium text-slate-500'>
                  Product Features
                </h3>

                <div className='flex gap-2'>
                  <Input
                    id='features'
                    value={featureInput}
                    onChange={(e) => setFeatureInput(e.target.value)}
                    onKeyPress={handleKeyPress}
                    placeholder='Add a feature and press Enter'
                    disabled={isSubmitting}
                    className='border-slate-300'
                  />
                  <Button
                    type='button'
                    onClick={handleAddFeature}
                    variant='outline'
                    size='icon'
                    disabled={isSubmitting || !featureInput.trim()}
                    className='h-10 w-10 flex-shrink-0'
                  >
                    <Plus className='h-4 w-4' />
                  </Button>
                </div>
                {features.length > 0 && (
                  <div className='max-h-32 overflow-y-auto rounded-md border border-slate-200 p-2 dark:border-slate-700'>
                    <div className='flex flex-wrap gap-2'>
                      {features.map((feature, index) => (
                        <Badge
                          key={index}
                          variant='secondary'
                          className='py-1.5 pl-2'
                        >
                          {feature}
                          <button
                            type='button'
                            onClick={() => handleRemoveFeature(feature)}
                            className='ml-1 rounded-full p-0.5 text-muted-foreground hover:bg-slate-200 hover:text-foreground disabled:opacity-50 dark:hover:bg-slate-700'
                            disabled={isSubmitting}
                            aria-label={`Remove feature ${feature}`}
                          >
                            <X className='h-3 w-3' />
                          </button>
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Description - Full Width */}
          <div className='space-y-3 pt-2'>
            <h3 className='text-sm font-medium text-slate-500'>
              Product Description
            </h3>

            <Textarea
              id='description'
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              placeholder='Enter product description'
              rows={4}
              disabled={isSubmitting}
              className='resize-y border-slate-300'
            />
          </div>

          {/* Footer Buttons */}
          <DialogFooter className='px-0 pb-6 pt-4'>
            <Button
              type='button'
              variant='outline'
              onClick={handleClose}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button
              type='submit'
              disabled={isSubmitting}
              className='min-w-[100px]'
            >
              {isSubmitting
                ? 'Saving...'
                : product
                  ? 'Update Product'
                  : 'Create Product'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
