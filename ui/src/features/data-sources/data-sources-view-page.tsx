'use client';
import { useState } from 'react';
import { useAuthStore } from '@/stores/auth.store';
import DataSourcesCreateNew from './data-sources-create-new';
import DataSourcesTable, {
  createSortableHeader,
  createTextCell
} from './data-sources-table';
import { redirect, useRouter } from 'next/navigation';
import { useDeleteSource, useListSources } from '@/queries/source';
import { ROUTES } from '@/constants/data';
import { ColumnDef } from '@tanstack/react-table';
import { SourceResponse } from 'types';
import { formatDateTime } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { LucideEdit, LucideTrash } from 'lucide-react';
import { toast } from 'sonner';
import DeleteConfirmationDialog from '@/components/ui/delete-confirmation-dialog';

import { useCallback, useMemo } from 'react';
import { useSourceStore } from '@/stores/source.store';

const useOriginalPageState = () => {
  const [originalPage, setOriginalPage] = useState(1);
  const [originalPageSize, setOriginalPageSize] = useState(20);
  return {
    originalPage,
    setOriginalPage,
    originalPageSize,
    setOriginalPageSize
  };
};

export default function DataSourcesViewPage() {
  const user = useAuthStore((state) => state.user);
  if (!user?.isAdmin) {
    redirect(ROUTES.OVERVIEW);
  }
  const router = useRouter();
  const {
    originalPage,
    setOriginalPage,
    originalPageSize,
    setOriginalPageSize
  } = useOriginalPageState();

  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [sourceToDelete, setSourceToDelete] = useState<SourceResponse | null>(
    null
  );

  const {
    data: sources,
    isLoading,
    refetch
  } = useListSources({
    skip: (originalPage - 1) * originalPageSize,
    limit: originalPageSize
  });

  const sourcesList = sources?.sources || [];
  const totalPages = sources?.total
    ? Math.ceil(sources.total / originalPageSize)
    : 0;

  const handlePageSizeChange = (newPageSize: number) => {
    setOriginalPageSize(newPageSize);
    setOriginalPage(1);
  };

  const { mutate: deleteSource } = useDeleteSource();
  const { setSelectedSource } = useSourceStore();

  const openDeleteDialog = (source: SourceResponse) => {
    setSourceToDelete(source);
    setIsDeleteDialogOpen(true);
  };

  const handleDeleteDataSource = useCallback(
    (sourceId: number) => {
      deleteSource(
        { sourceId },
        {
          onSuccess: () => {
            toast.success('Data source deleted successfully');
            refetch();
            setIsDeleteDialogOpen(false);
            setSourceToDelete(null);
          },
          onError: (error) => {
            toast.error(`Error deleting data source: ${error.message}`);
            setIsDeleteDialogOpen(false);
            setSourceToDelete(null);
          }
        }
      );
    },
    [deleteSource, refetch]
  );

  const handleNavigateDetailPage = useCallback(
    (source: SourceResponse) => {
      setSelectedSource(source);
      router.push(`/dashboard/data-sources/${source.id}`);
    },
    [router, setSelectedSource]
  );

  const columns: ColumnDef<SourceResponse>[] = useMemo(
    () => [
      {
        accessorKey: 'name',
        header: ({ column }) => createSortableHeader('Name', column),
        cell: ({ row }) => createTextCell(row.getValue('name'))
      },
      {
        accessorKey: 'created_at',
        header: ({ column }) => createSortableHeader('Created At', column),
        cell: ({ row }) =>
          createTextCell(formatDateTime(row.getValue('created_at')))
      },
      {
        id: 'actions',
        cell: ({ row }) => {
          return (
            <div className='flex items-center gap-1'>
              <DeleteConfirmationDialog
                open={
                  isDeleteDialogOpen && sourceToDelete?.id === row.original.id
                }
                onOpenChange={(open) => {
                  setIsDeleteDialogOpen(open);
                  if (!open) {
                    setSourceToDelete(null);
                  }
                }}
                onConfirm={() => {
                  if (sourceToDelete) {
                    handleDeleteDataSource(sourceToDelete.id);
                  }
                }}
                itemName={`data source "${row.original.name}"`}
                triggerComponent={
                  <Button
                    size={'icon'}
                    variant='ghost'
                    onClick={() => openDeleteDialog(row.original)}
                  >
                    <LucideTrash className='h-4 w-4' />
                  </Button>
                }
                title='Delete Data Source?'
                description='This action cannot be undone. This will permanently delete the data source.'
              />
              <Button
                size={'icon'}
                variant='ghost'
                onClick={() => handleNavigateDetailPage(row.original)}
              >
                <LucideEdit className='h-4 w-4' />
              </Button>
            </div>
          );
        },
        enableSorting: false,
        enableHiding: false
      }
    ],
    [
      handleDeleteDataSource,
      handleNavigateDetailPage,
      isDeleteDialogOpen,
      sourceToDelete
    ]
  );

  return (
    <div className='flex w-full flex-col gap-3'>
      <DataSourcesCreateNew onSuccess={refetch} />
      <DataSourcesTable
        dataTable={sourcesList}
        isLoading={isLoading}
        columns={columns}
        searchKey='name'
        filterableColumns={[{ id: 'name', title: 'Name', valueKey: 'name' }]}
        pagination={{
          page: originalPage,
          pageCount: totalPages,
          pageSize: originalPageSize,
          onPageChange: setOriginalPage,
          onPageSizeChange: handlePageSizeChange
        }}
      />
    </div>
  );
}
