'use client';
import { Button } from '@/components/ui/button';
import {
  Drawer,
  DrawerClose,
  DrawerContent,
  DrawerDescription,
  DrawerFooter,
  DrawerHeader,
  DrawerTitle,
  DrawerTrigger
} from '@/components/ui/drawer';
import { useState } from 'react';
import { FileUploader } from '@/components/file-uploader';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { useCreateSource, useUpdateFileToSource } from '@/queries/source';
import { toast } from 'sonner';
import { useRouter } from 'next/navigation';

export default function DataSourcesCreateNew({
  onSuccess
}: {
  onSuccess?: () => void;
}) {
  const router = useRouter();
  const [dataSourceType, setDataSourceType] = useState<string>('localdir');
  const [dataSourceName, setDataSourceName] = useState('');
  const [dataSourceDescription, setDataSourceDescription] =
    useState<string>('');
  const [url, setUrl] = useState('');
  const [files, setFiles] = useState<File[]>([]);
  const [openDrawer, setOpenDrawer] = useState<boolean>(false);

  const validateUrl = (url: string) => {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  };
  const { mutate: createSource } = useCreateSource();
  const { mutate: updateFile } = useUpdateFileToSource();

  const handleCreateSource = () => {
    createSource(
      {
        name: dataSourceName,
        description: dataSourceDescription,
        s3_bucket_url: url
      },
      {
        onSuccess: (response) => {
          toast.success(`Source created successfully`);
          if (response) {
            handleUploadFile(response.id).then(() => {
              router.push(`/dashboard/data-sources/${response.id}`);
            });
          }
          setOpenDrawer(!openDrawer);
          onSuccess?.();
        },
        onError: (error) => {
          toast.error(`Error: ${error}`);
        }
      }
    );
  };
  const handleUploadFile = async (id: number) => {
    if (!files || !files.length) return;

    const fileData = files.map((file) => ({
      name: file.name,
      file_type: file.type,
      content: file
    }));

    return updateFile(
      {
        id: id,
        file: {
          files: fileData
        }
      },
      {
        onSuccess: () => {
          toast.success('File uploaded successfully');
          setFiles([]);
        },
        onError: (error) => {
          toast.error(`Error uploading file: ${error}`);
        }
      }
    );
  };

  return (
    <div className='flex justify-end'>
      <Drawer
        direction='right'
        open={openDrawer}
        onClose={() => setOpenDrawer(!openDrawer)}
      >
        <DrawerTrigger asChild>
          <Button variant='default' onClick={() => setOpenDrawer(!openDrawer)}>
            New Data Sources
          </Button>
        </DrawerTrigger>
        <DrawerContent
          className="fixed inset-x-[unset] mt-0 right-0 top-0 h-full lg:w-1/3 border-l bg-background !rounded-r-none"
        >
          <div className="mx-auto w-full p-4">
            <DrawerHeader>
              <DrawerTitle>Create New Data Source</DrawerTitle>
              <DrawerDescription>
                Choose your data source type and configure it.
              </DrawerDescription>
            </DrawerHeader>

            <div className='space-y-6 px-2'>
              <div className='space-y-4'>
                <Label>Data Source Name</Label>
                <Input
                  placeholder='Enter data source name'
                  value={dataSourceName}
                  onChange={(e) => setDataSourceName(e.target.value)}
                />
              </div>

              <div className='space-y-4'>
                <Label>Description</Label>
                <Input
                  placeholder='Enter data source description'
                  value={dataSourceDescription}
                  onChange={(e) => setDataSourceDescription(e.target.value)}
                />
              </div>

              <RadioGroup
                value={dataSourceType}
                onValueChange={setDataSourceType}
                className='grid grid-cols-2 gap-4'
              >
                <div
                  onClick={() => setDataSourceType('localdir')}
                  className='flex w-full cursor-pointer items-center justify-start space-x-2 rounded-lg border p-4 hover:bg-accent'
                >
                  <RadioGroupItem value='localdir' id='localdir' />
                  <Label htmlFor='localdir' className='font-bold'>
                    Local Directory
                  </Label>
                </div>
                <div
                  onClick={() => setDataSourceType('url')}
                  className='flex w-full cursor-pointer items-center justify-start space-x-2 rounded-lg border p-4 hover:bg-accent'
                >
                  <RadioGroupItem value='url' id='url' />
                  <Label htmlFor='url' className='font-bold'>
                    URL
                  </Label>
                </div>
              </RadioGroup>

              {dataSourceType === 'localdir' ? (
                <div className='space-y-4'>
                  <Label>Upload Files</Label>
                  <FileUploader
                    onFilesSelected={setFiles}
                    maxFiles={10}
                    accept={{
                      'application/pdf': ['.pdf']
                    }}
                  />
                </div>
              ) : (
                <div className='space-y-4'>
                  <Label>URL</Label>
                  <Input
                    placeholder='Enter valid URL'
                    value={url}
                    onChange={(e) => setUrl(e.target.value)}
                    className={!validateUrl(url) && url ? 'border-red-500' : ''}
                  />
                  {!validateUrl(url) && url && (
                    <p className='text-sm text-red-500'>
                      Please enter a valid URL
                    </p>
                  )}
                </div>
              )}
            </div>

            <DrawerFooter>
              <Button
                disabled={!dataSourceName.trim()}
                onClick={handleCreateSource}
              >
                Submit
              </Button>
              <DrawerClose asChild>
                <Button variant='outline'>Cancel</Button>
              </DrawerClose>
            </DrawerFooter>
          </div>
        </DrawerContent>
      </Drawer>
    </div>
  );
}
