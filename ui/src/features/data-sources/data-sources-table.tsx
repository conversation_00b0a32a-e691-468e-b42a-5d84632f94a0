'use client';
import { Button } from '@/components/ui/button';
import { LucideSortDesc } from 'lucide-react';
import { DataTable } from '@/components/ui/table/data-table';
import { ColumnDef } from '@tanstack/react-table';
import { SourceResponse } from 'types';

interface DataSourcesTableProps {
  dataTable?: SourceResponse[];
  isLoading?: boolean;
  columns: ColumnDef<SourceResponse>[];
  searchKey?: string;
  filterableColumns?: {
    id: string;
    title: string;
    valueKey: string;
  }[];
  pagination?: {
    page: number;
    pageCount: number;
    onPageChange: (page: number) => void;
    onPageSizeChange: (pageSize: number) => void; // Add this
    pageSize: number; // Add this
  };
}

export default function DataSourcesTable({
  dataTable,
  isLoading,
  columns,
  searchKey,
  filterableColumns,
  pagination
}: DataSourcesTableProps) {
  return (
    <DataTable
      data={isLoading ? [] : (dataTable ?? [])}
      columns={columns}
      searchKey={searchKey || 'name'}
      isLoading={isLoading}
      filterableColumns={
        filterableColumns
          ? filterableColumns.map((col) =>
              createFilterableColumn(
                col.id,
                col.title,
                dataTable,
                isLoading ?? false,
                col.valueKey
              )
            )
          : []
      }
      pagination={pagination}
    />
  );
}

export const createSortableHeader = (label: string, column: any) => (
  <Button
    variant='ghost'
    onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
    className='flex items-center gap-1'
  >
    {label}
    <LucideSortDesc
      className={`w-3 ${column.getIsSorted() === 'desc' ? 'rotate-0' : 'rotate-180'} transition-all duration-300`}
    />
  </Button>
);

export const createTextCell = (value: any, maxWidth: number = 500) => (
  <div className={`max-w-[${maxWidth}px] truncate`}>{value}</div>
);

export const createFilterableColumn = (
  id: string,
  title: string,
  data: any[] | undefined,
  isLoading: boolean,
  valueKey: string
) => ({
  id,
  title,
  options: isLoading
    ? []
    : Array.from(new Set((data ?? []).map((item) => item[valueKey]))).map(
        (value) => ({
          label: value,
          value: value
        })
      )
});
