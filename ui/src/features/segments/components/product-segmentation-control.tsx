'use client';

import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select';
import { 
  Play, 
  Square, 
  Loader2, 
  CheckCircle, 
  XCircle, 
  AlertCircle,
  Clock,
  Package,
  Target
} from 'lucide-react';
import { useListProducts } from '@/queries/product';
import { 
  useStartProductSegmentation, 
  useCancelProductSegmentation,
  useCanStartProductSegmentation,
  useGetLatestProductSegmentationJob
} from '@/queries/segment';
import { SegmentationJobStatus } from '@/services/segment.service';

interface ProductSegmentationControlProps {
  selectedProductId?: number;
  onProductChange?: (productId?: number) => void;
  onSegmentationComplete?: () => void;
}

export function ProductSegmentationControl({ 
  selectedProductId, 
  onProductChange,
  onSegmentationComplete 
}: ProductSegmentationControlProps) {
  const [localSelectedProductId, setLocalSelectedProductId] = useState<number | undefined>(selectedProductId);
  const [showDetails, setShowDetails] = useState(false);

  const startProductSegmentation = useStartProductSegmentation();
  const cancelProductSegmentation = useCancelProductSegmentation();
  const { data: productsResponse } = useListProducts({ limit: 100 });
  const products = productsResponse?.products || [];

  const productId = localSelectedProductId || selectedProductId;
  const { canStart, latestJob, isRunning, isPending, isCompleted, isFailed, isCancelled } = useCanStartProductSegmentation(productId || 0);
  const { data: jobDetails } = useGetLatestProductSegmentationJob(productId || 0);

  const selectedProduct = products.find(p => p.id === productId);

  const handleProductSelect = (value: string) => {
    const newProductId = value ? parseInt(value) : undefined;
    setLocalSelectedProductId(newProductId);
    if (onProductChange) {
      onProductChange(newProductId);
    }
  };

  const handleStartSegmentation = () => {
    if (productId) {
      startProductSegmentation.mutate(productId, {
        onSuccess: () => {
          setShowDetails(true);
          onSegmentationComplete?.();
        }
      });
    }
  };

  const handleCancelSegmentation = () => {
    if (latestJob?.id) {
      cancelProductSegmentation.mutate(latestJob.id);
    }
  };

  const getStatusIcon = () => {
    if (isRunning) return <Loader2 className="h-4 w-4 animate-spin text-blue-500" />;
    if (isPending) return <Clock className="h-4 w-4 text-yellow-500" />;
    if (isCompleted) return <CheckCircle className="h-4 w-4 text-green-500" />;
    if (isFailed) return <XCircle className="h-4 w-4 text-red-500" />;
    if (isCancelled) return <AlertCircle className="h-4 w-4 text-gray-500" />;
    return null;
  };

  const getStatusText = () => {
    if (isRunning) return 'Running';
    if (isPending) return 'Pending';
    if (isCompleted) return 'Completed';
    if (isFailed) return 'Failed';
    if (isCancelled) return 'Cancelled';
    return 'Ready';
  };

  const getStatusColor = () => {
    if (isRunning) return 'bg-blue-500';
    if (isPending) return 'bg-yellow-500';
    if (isCompleted) return 'bg-green-500';
    if (isFailed) return 'bg-red-500';
    if (isCancelled) return 'bg-gray-500';
    return 'bg-gray-200';
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Package className="h-5 w-5" />
          Product Segmentation
        </CardTitle>
        <CardDescription>
          Start segmentation for all programs in a product
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Product Selection */}
        <div className="space-y-2">
          <label className="text-sm font-medium">Select Product</label>
          <Select 
            value={productId?.toString() || ''} 
            onValueChange={handleProductSelect}
          >
            <SelectTrigger>
              <SelectValue placeholder="Choose a product to segment" />
            </SelectTrigger>
            <SelectContent>
              {products.map((product) => (
                <SelectItem key={product.id} value={product.id.toString()}>
                  <div className="flex items-center gap-2">
                    <span>{product.name}</span>
                    <Badge variant="outline" className="text-xs">
                      {product.model}
                    </Badge>
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Status and Controls */}
        {productId && (
          <div className="space-y-4">
            {/* Current Status */}
            <div className="flex items-center justify-between p-3 bg-muted rounded-lg">
              <div className="flex items-center gap-2">
                {getStatusIcon()}
                <span className="font-medium">Status: {getStatusText()}</span>
              </div>
              <Badge variant="outline" className={getStatusColor()}>
                {latestJob?.status || 'Ready'}
              </Badge>
            </div>

            {/* Progress Information */}
            {(isRunning || isPending) && jobDetails && (
              <div className="space-y-3">
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Overall Progress</span>
                    <span>{jobDetails.progress_percentage}%</span>
                  </div>
                  <Progress value={jobDetails.progress_percentage} className="h-2" />
                </div>
                
                <div className="grid grid-cols-3 gap-4 text-sm">
                  <div className="text-center">
                    <div className="font-medium text-blue-600">{jobDetails.total_programs}</div>
                    <div className="text-muted-foreground">Total Programs</div>
                  </div>
                  <div className="text-center">
                    <div className="font-medium text-green-600">{jobDetails.completed_programs}</div>
                    <div className="text-muted-foreground">Completed</div>
                  </div>
                  <div className="text-center">
                    <div className="font-medium text-red-600">{jobDetails.failed_programs}</div>
                    <div className="text-muted-foreground">Failed</div>
                  </div>
                </div>

                {jobDetails.current_step && (
                  <div className="text-sm text-muted-foreground">
                    <strong>Current Step:</strong> {jobDetails.current_step}
                  </div>
                )}
              </div>
            )}

            {/* Error Message */}
            {isFailed && jobDetails?.error_message && (
              <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
                <div className="flex items-center gap-2 text-red-800">
                  <XCircle className="h-4 w-4" />
                  <span className="font-medium">Error</span>
                </div>
                <p className="text-sm text-red-700 mt-1">{jobDetails.error_message}</p>
              </div>
            )}

            {/* Action Buttons */}
            <div className="flex gap-2">
              {canStart && (
                <Button 
                  onClick={handleStartSegmentation}
                  disabled={startProductSegmentation.isPending}
                  className="flex-1"
                >
                  {startProductSegmentation.isPending ? (
                    <Loader2 className="h-4 w-4 animate-spin mr-2" />
                  ) : (
                    <Play className="h-4 w-4 mr-2" />
                  )}
                  Start Product Segmentation
                </Button>
              )}

              {(isRunning || isPending) && (
                <Button 
                  variant="destructive"
                  onClick={handleCancelSegmentation}
                  disabled={cancelProductSegmentation.isPending}
                >
                  {cancelProductSegmentation.isPending ? (
                    <Loader2 className="h-4 w-4 animate-spin mr-2" />
                  ) : (
                    <Square className="h-4 w-4 mr-2" />
                  )}
                  Cancel
                </Button>
              )}

              {(isCompleted || isFailed || isCancelled) && (
                <Button 
                  variant="outline"
                  onClick={() => setShowDetails(!showDetails)}
                >
                  <Target className="h-4 w-4 mr-2" />
                  {showDetails ? 'Hide' : 'Show'} Details
                </Button>
              )}
            </div>

            {/* Completion Summary */}
            {isCompleted && jobDetails && (
              <div className="p-3 bg-green-50 border border-green-200 rounded-lg">
                <div className="flex items-center gap-2 text-green-800">
                  <CheckCircle className="h-4 w-4" />
                  <span className="font-medium">Segmentation Completed</span>
                </div>
                <p className="text-sm text-green-700 mt-1">
                  Successfully processed {jobDetails.completed_programs} out of {jobDetails.total_programs} programs
                  {jobDetails.failed_programs > 0 && ` (${jobDetails.failed_programs} failed)`}
                </p>
              </div>
            )}
          </div>
        )}

        {!productId && (
          <div className="text-center py-8 text-muted-foreground">
            <Package className="h-12 w-12 mx-auto mb-2 opacity-50" />
            <p>Select a product to start segmentation</p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
