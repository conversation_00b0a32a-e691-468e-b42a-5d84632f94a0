'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { 
  AlertDialog, 
  AlertDialogAction, 
  AlertDialogCancel, 
  AlertDialogContent, 
  AlertDialogDescription, 
  AlertDialogFooter, 
  AlertDialogHeader, 
  AlertDialogTitle, 
  AlertDialogTrigger 
} from '@/components/ui/alert-dialog';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select';
import { Trash2, Loader2, AlertTriangle } from 'lucide-react';
import { useClearSegmentsAndJobs } from '@/queries/segment';
import { useListProducts } from '@/queries/product';

interface ProductCleanupButtonProps {
  selectedProductId?: number;
  onProductChange?: (productId?: number) => void;
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link';
  size?: 'default' | 'sm' | 'lg' | 'icon';
  className?: string;
}

export function ProductCleanupButton({ 
  selectedProductId, 
  onProductChange,
  variant = 'destructive',
  size = 'default',
  className 
}: ProductCleanupButtonProps) {
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [localSelectedProductId, setLocalSelectedProductId] = useState<number | undefined>(selectedProductId);
  
  const clearSegments = useClearSegmentsAndJobs();
  const { data: productsResponse } = useListProducts({ limit: 100 });
  const products = productsResponse?.products || [];

  const handleProductSelect = (value: string) => {
    const productId = value === 'all' ? undefined : parseInt(value);
    setLocalSelectedProductId(productId);
    if (onProductChange) {
      onProductChange(productId);
    }
  };

  const handleClearSegments = async () => {
    try {
      await clearSegments.mutateAsync({ 
        product_id: localSelectedProductId 
      });
      setIsDialogOpen(false);
    } catch (error) {
      // Error is handled by the mutation's onError callback
      console.error('Failed to clear segments:', error);
    }
  };

  const selectedProduct = products.find(p => p.id === localSelectedProductId);
  const isClearing = clearSegments.isPending;

  return (
    <AlertDialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
      <AlertDialogTrigger asChild>
        <Button 
          variant={variant} 
          size={size} 
          className={className}
          disabled={isClearing}
        >
          {isClearing ? (
            <Loader2 className="h-4 w-4 animate-spin" />
          ) : (
            <Trash2 className="h-4 w-4" />
          )}
          <span className="ml-2">Clear Segments</span>
        </Button>
      </AlertDialogTrigger>
      
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5 text-destructive" />
            Clear Program Segments
          </AlertDialogTitle>
          <AlertDialogDescription>
            This will permanently delete all program segments and segmentation jobs.
            This action cannot be undone.
          </AlertDialogDescription>
        </AlertDialogHeader>

        <div className="space-y-4">
          <div className="space-y-2">
            <label className="text-sm font-medium">Select scope:</label>
            <Select
              value={localSelectedProductId ? localSelectedProductId.toString() : 'all'}
              onValueChange={handleProductSelect}
            >
              <SelectTrigger>
                <SelectValue placeholder="Choose what to clear" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">
                  <span className="font-medium text-destructive">All Products</span>
                  <span className="text-xs text-muted-foreground ml-2">
                    (Clear everything)
                  </span>
                </SelectItem>
                {products.map((product) => (
                  <SelectItem key={product.id} value={product.id.toString()}>
                    <span>{product.name}</span>
                    <span className="text-xs text-muted-foreground ml-2">
                      ({product.model})
                    </span>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="bg-destructive/10 border border-destructive/20 rounded-md p-3">
            <p className="text-sm font-medium text-destructive">
              ⚠️ What will be deleted:
            </p>
            <ul className="text-sm text-muted-foreground mt-1 space-y-1">
              <li>• All macro phases and micro chunks</li>
              <li>• All segmentation jobs and progress</li>
              <li>• All vector embeddings from Pinecone</li>
              {localSelectedProductId ? (
                <li>• Only for product: <strong>{selectedProduct?.name}</strong></li>
              ) : (
                <li>• <strong>Across ALL products</strong></li>
              )}
            </ul>
          </div>
        </div>
        
        <AlertDialogFooter>
          <AlertDialogCancel disabled={isClearing}>
            Cancel
          </AlertDialogCancel>
          <AlertDialogAction
            onClick={handleClearSegments}
            disabled={isClearing}
            className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
          >
            {isClearing ? (
              <>
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
                Clearing...
              </>
            ) : (
              <>
                <Trash2 className="h-4 w-4 mr-2" />
                Clear Segments
              </>
            )}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
