'use client';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Target, Zap, Clock, BarChart3 } from 'lucide-react';
import { SegmentStats as SegmentStatsType, SegmentStatsParams } from '@/services/segment.service';
import { useGetSegmentStats } from '@/queries/segment';

interface SegmentStatsProps {
  filters?: SegmentStatsParams;
}

export function SegmentStats({ filters = {} }: SegmentStatsProps) {
  const { data: stats, isLoading, error } = useGetSegmentStats(filters);

  if (isLoading) {
    return (
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {[...Array(4)].map((_, i) => (
          <Card key={i}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <div className="h-4 w-20 bg-muted animate-pulse rounded" />
              <div className="h-4 w-4 bg-muted animate-pulse rounded" />
            </CardHeader>
            <CardContent>
              <div className="h-8 w-16 bg-muted animate-pulse rounded mb-1" />
              <div className="h-3 w-24 bg-muted animate-pulse rounded" />
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (error || !stats) {
    return (
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardContent className="pt-6">
            <div className="text-sm text-destructive">Error loading stats</div>
          </CardContent>
        </Card>
      </div>
    );
  }

  const formatDuration = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    if (hours > 0) {
      return `${hours}h ${minutes}m`;
    }
    return `${minutes}m`;
  };

  const getPhaseColor = (phase: string) => {
    switch (phase) {
      case 'front':
        return 'bg-blue-100 text-blue-800';
      case 'main':
        return 'bg-green-100 text-green-800';
      case 'cooling':
        return 'bg-purple-100 text-purple-800';
      case 'discovered':
        return 'bg-orange-100 text-orange-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      {/* Total Segments */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Total Segments</CardTitle>
          <BarChart3 className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{stats.total_segments}</div>
          <p className="text-xs text-muted-foreground">
            {stats.macro_phases} macro + {stats.micro_chunks} micro
          </p>
          <p className="text-xs text-muted-foreground mt-1">
            From {stats.analyzed_programs} analyzed programs
          </p>
        </CardContent>
      </Card>

      {/* Macro Phases */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Macro Phases</CardTitle>
          <Target className="h-4 w-4 text-blue-600" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{stats.macro_phases}</div>
          <div className="flex flex-wrap gap-1 mt-2">
            {Object.entries(stats.phase_distribution).map(([phase, count]) => (
              count > 0 && phase !== 'discovered' && (
                <Badge key={phase} variant="secondary" className={getPhaseColor(phase)}>
                  {phase}: {count}
                </Badge>
              )
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Micro Chunks */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Micro Chunks</CardTitle>
          <Zap className="h-4 w-4 text-orange-600" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{stats.micro_chunks}</div>
          <p className="text-xs text-muted-foreground">
            Avg intensity: {stats.avg_intensity}/10
          </p>
        </CardContent>
      </Card>

      {/* Total Duration */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Total Duration</CardTitle>
          <Clock className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{formatDuration(stats.total_duration)}</div>
          <p className="text-xs text-muted-foreground">
            Across all segments
          </p>
        </CardContent>
      </Card>
    </div>
  );
}
