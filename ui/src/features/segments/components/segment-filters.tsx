'use client';

import { Card, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Search, Filter } from 'lucide-react';
import { useListProducts } from '@/queries/product';

interface SegmentFiltersProps {
  searchTerm: string;
  onSearchChange: (value: string) => void;
  selectedPhase: string;
  onPhaseChange: (value: string) => void;
  selectedProductId?: number;
  onProductIdChange: (value?: number) => void;
  showMacroOnly: boolean;
  onShowMacroOnlyChange: (value: boolean) => void;
}

export function SegmentFilters({
  searchTerm,
  onSearchChange,
  selectedPhase,
  onPhaseChange,
  selectedProductId,
  onProductIdChange,
  showMacroOnly,
  onShowMacroOnlyChange,
}: SegmentFiltersProps) {
  const { data: productsResponse } = useListProducts({ limit: 100 });
  const products = productsResponse?.products || [];

  return (
    <Card>
      <CardContent className="py-4">
        <div className="flex flex-col gap-3 lg:flex-row lg:items-center lg:gap-4">
          {/* Search */}
          <div className="flex-1 min-w-0">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
              <Input
                placeholder="Search by purpose, technique, or body part tags..."
                value={searchTerm}
                onChange={(e) => onSearchChange(e.target.value)}
                className="pl-10 h-9"
              />
            </div>
          </div>

          {/* Filters Row */}
          <div className="flex flex-col gap-3 sm:flex-row sm:items-center sm:gap-3">
            {/* Phase Filter */}
            <div className="w-full sm:w-40">
              <Select value={selectedPhase || "all"} onValueChange={(value) => onPhaseChange(value === "all" ? "" : value)}>
                <SelectTrigger className="h-9">
                  <SelectValue placeholder="All phases" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All phases</SelectItem>
                  <SelectItem value="front">Front</SelectItem>
                  <SelectItem value="main">Main</SelectItem>
                  <SelectItem value="cooling">Cooling</SelectItem>
                  <SelectItem value="discovered">Discovered</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Product Filter */}
            <div className="w-full sm:w-40">
              <Select
                value={selectedProductId?.toString() || "all"}
                onValueChange={(value) => onProductIdChange(value === "all" ? undefined : parseInt(value))}
              >
                <SelectTrigger className="h-9">
                  <SelectValue placeholder="All products" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All products</SelectItem>
                  {products.map((product) => (
                    <SelectItem key={product.id} value={product.id.toString()}>
                      {product.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Macro Only Toggle */}
            <div className="flex items-center space-x-2 whitespace-nowrap">
              <Switch
                id="macro-only"
                checked={showMacroOnly}
                onCheckedChange={onShowMacroOnlyChange}
              />
              <Label htmlFor="macro-only" className="text-sm font-medium">
                Macro only
              </Label>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
