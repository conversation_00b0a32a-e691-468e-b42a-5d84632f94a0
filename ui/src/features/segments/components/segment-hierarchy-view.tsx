'use client';

import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';

import { Badge } from '@/components/ui/badge';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { ChevronDown, ChevronRight, Target, Zap } from 'lucide-react';
import { useGetProgramSegmentHierarchy } from '@/queries/segment';
import { Program } from 'types';
import { SegmentCard } from './segment-card';
import { SegmentationControl } from './segmentation-control';
import { ProgramSelector } from './program-selector';

export function SegmentHierarchyView() {
  const [selectedProgram, setSelectedProgram] = useState<Program | null>(null);
  const [expandedPhases, setExpandedPhases] = useState<Set<number>>(new Set());

  const { data: hierarchyData, isLoading, error } = useGetProgramSegmentHierarchy(
    selectedProgram?.id || 0,
    { enabled: !!selectedProgram?.id }
  );

  const togglePhaseExpansion = (phaseId: number) => {
    const newExpanded = new Set(expandedPhases);
    if (newExpanded.has(phaseId)) {
      newExpanded.delete(phaseId);
    } else {
      newExpanded.add(phaseId);
    }
    setExpandedPhases(newExpanded);
  };

  const getPhaseColor = (phase: string) => {
    switch (phase) {
      case 'front':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'main':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'cooling':
        return 'bg-purple-100 text-purple-800 border-purple-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const formatDuration = (seconds?: number) => {
    if (!seconds) return 'Unknown';
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  return (
    <div className="space-y-4 w-full">
      {/* Program Selection */}
      <ProgramSelector
        selectedProgramId={selectedProgram?.id}
        onProgramSelect={setSelectedProgram}
      />

      {/* Hierarchy Display */}
      {selectedProgram && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Target className="h-5 w-5" />
              Program #{selectedProgram.id} - {selectedProgram.program_title || selectedProgram.name}
            </CardTitle>
            {hierarchyData && (
              <CardDescription>
                {hierarchyData.summary.macro_phase_count} macro phases, {hierarchyData.summary.micro_chunk_count} micro chunks
              </CardDescription>
            )}
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="flex items-center justify-center py-8">
                <div className="text-sm text-muted-foreground">Loading hierarchy...</div>
              </div>
            ) : error ? (
              <div className="flex items-center justify-center py-8">
                <div className="text-sm text-destructive">Error loading hierarchy</div>
              </div>
            ) : !hierarchyData || hierarchyData.macro_phases.length === 0 ? (
              <div className="flex flex-col items-center justify-center py-12 space-y-4">
                <Target className="h-12 w-12 text-muted-foreground opacity-50" />
                <div className="text-center space-y-2">
                  <div className="text-sm font-medium">No segments found</div>
                  <div className="text-sm text-muted-foreground">
                    This program hasn&apos;t been segmented yet. Use the segmentation control below to start the process.
                  </div>
                </div>
              </div>
            ) : (
              <div className="space-y-4">
                {hierarchyData.macro_phases.map((macroPhase) => {
                  const isExpanded = expandedPhases.has(macroPhase.segment.id);
                  
                  return (
                    <Card key={macroPhase.segment.id} className="border-l-4 border-l-blue-500">
                      <Collapsible open={isExpanded} onOpenChange={() => togglePhaseExpansion(macroPhase.segment.id)}>
                        <CollapsibleTrigger asChild>
                          <CardHeader className="cursor-pointer hover:bg-muted/50 transition-colors">
                            <div className="flex items-center justify-between">
                              <div className="flex items-center gap-3">
                                {isExpanded ? (
                                  <ChevronDown className="h-4 w-4" />
                                ) : (
                                  <ChevronRight className="h-4 w-4" />
                                )}
                                <Target className="h-5 w-5 text-blue-600" />
                                <div>
                                  <CardTitle className="text-base">
                                    {macroPhase.segment.phase.charAt(0).toUpperCase() + macroPhase.segment.phase.slice(1)} Phase
                                  </CardTitle>
                                  <CardDescription>
                                    {macroPhase.micro_chunks.length} micro chunks • 
                                    {macroPhase.segment.steps.length} steps • 
                                    {formatDuration(macroPhase.segment.duration_seconds)}
                                  </CardDescription>
                                </div>
                              </div>
                              <div className="flex items-center gap-2">
                                <Badge className={getPhaseColor(macroPhase.segment.phase)}>
                                  {macroPhase.segment.phase}
                                </Badge>
                                {macroPhase.segment.intensity_score && (
                                  <Badge variant="outline">
                                    {macroPhase.segment.intensity_score}/10
                                  </Badge>
                                )}
                              </div>
                            </div>
                          </CardHeader>
                        </CollapsibleTrigger>

                        <CollapsibleContent>
                          <CardContent className="pt-0">
                            {/* Macro Phase Details */}
                            <div className="mb-4 p-4 bg-muted/30 rounded-lg">
                              <SegmentCard segment={macroPhase.segment} showDetails={false} />
                            </div>

                            {/* Micro Chunks */}
                            {macroPhase.micro_chunks.length > 0 ? (
                              <div className="space-y-3">
                                <div className="flex items-center gap-2 text-sm font-medium text-muted-foreground">
                                  <Zap className="h-4 w-4" />
                                  Micro Chunks ({macroPhase.micro_chunks.length})
                                </div>
                                <div className="grid gap-3 md:grid-cols-2">
                                  {macroPhase.micro_chunks.map((microChunk) => (
                                    <div key={microChunk.id} className="border-l-2 border-l-orange-300 pl-3">
                                      <SegmentCard segment={microChunk} showDetails={false} />
                                    </div>
                                  ))}
                                </div>
                              </div>
                            ) : (
                              <div className="text-center py-4 text-sm text-muted-foreground">
                                No micro chunks found in this phase
                              </div>
                            )}
                          </CardContent>
                        </CollapsibleContent>
                      </Collapsible>
                    </Card>
                  );
                })}
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Segmentation Control */}
      {selectedProgram && (
        <SegmentationControl
          programId={selectedProgram.id}
          productId={selectedProgram.id}
          onSegmentationComplete={() => {
            // Refresh the hierarchy data when segmentation completes
            // The query will automatically refetch due to its cache invalidation
          }}
        />
      )}
    </div>
  );
}
