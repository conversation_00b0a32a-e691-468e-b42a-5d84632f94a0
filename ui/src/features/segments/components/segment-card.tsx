'use client';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { ChevronDown, ChevronRight, Clock, Zap, Target, User } from 'lucide-react';
import { useState } from 'react';
import { ProgramSegment } from '@/services/segment.service';

interface SegmentCardProps {
  segment: ProgramSegment;
  showDetails?: boolean;
}

export function SegmentCard({ segment, showDetails = false }: SegmentCardProps) {
  const [isExpanded, setIsExpanded] = useState(showDetails);

  const getPhaseColor = (phase: string) => {
    switch (phase) {
      case 'front':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'main':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'cooling':
        return 'bg-purple-100 text-purple-800 border-purple-200';
      case 'discovered':
        return 'bg-orange-100 text-orange-800 border-orange-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getIntensityColor = (intensity?: number) => {
    if (!intensity) return 'bg-gray-100 text-gray-800';
    if (intensity <= 3) return 'bg-green-100 text-green-800';
    if (intensity <= 6) return 'bg-yellow-100 text-yellow-800';
    return 'bg-red-100 text-red-800';
  };

  const formatDuration = (seconds?: number) => {
    if (!seconds) return 'Unknown';
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const isMacroPhase = !segment.parent_segment_id;

  return (
    <Card className="hover:shadow-md transition-shadow">
      <CardHeader className="pb-3 p-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            {isMacroPhase ? (
              <Target className="h-4 w-4 text-blue-600" />
            ) : (
              <Zap className="h-4 w-4 text-orange-600" />
            )}
            <CardTitle className="text-sm font-medium">
              {isMacroPhase ? 'Macro Phase' : 'Micro Chunk'} #{segment.id}
            </CardTitle>
          </div>
          <Badge className={getPhaseColor(segment.phase)}>
            {segment.phase}
          </Badge>
        </div>
        <CardDescription className="text-xs">
          Program #{segment.original_program_id}
          {segment.parent_segment_id && ` • Parent: #${segment.parent_segment_id}`}
        </CardDescription>
      </CardHeader>

      <CardContent className="space-y-3 p-3">
        {/* Key Metrics */}
        <div className="flex items-center gap-4 text-xs text-muted-foreground">
          {segment.intensity_score && (
            <div className="flex items-center gap-1">
              <Zap className="h-3 w-3" />
              <Badge variant="outline" className={getIntensityColor(segment.intensity_score)}>
                {segment.intensity_score}/10
              </Badge>
            </div>
          )}
          {segment.duration_seconds && (
            <div className="flex items-center gap-1">
              <Clock className="h-3 w-3" />
              <span>{formatDuration(segment.duration_seconds)}</span>
            </div>
          )}
          <div className="flex items-center gap-1">
            <User className="h-3 w-3" />
            <span>{segment.steps.length} steps</span>
          </div>
        </div>

        {/* Tags */}
        <div className="space-y-2">
          {segment.purpose_tags && segment.purpose_tags.length > 0 && (
            <div className="space-y-1">
              <div className="text-xs font-medium text-muted-foreground">Purpose</div>
              <div className="flex flex-wrap gap-1">
                {segment.purpose_tags.map((tag, index) => (
                  <Badge key={index} variant="secondary" className="text-xs">
                    {tag}
                  </Badge>
                ))}
              </div>
            </div>
          )}

          {segment.technique_tags && segment.technique_tags.length > 0 && (
            <div className="space-y-1">
              <div className="text-xs font-medium text-muted-foreground">Techniques</div>
              <div className="flex flex-wrap gap-1">
                {segment.technique_tags.map((tag, index) => (
                  <Badge key={index} variant="outline" className="text-xs">
                    {tag}
                  </Badge>
                ))}
              </div>
            </div>
          )}

          {segment.body_part_tags && segment.body_part_tags.length > 0 && (
            <div className="space-y-1">
              <div className="text-xs font-medium text-muted-foreground">Body Parts</div>
              <div className="flex flex-wrap gap-1">
                {segment.body_part_tags.map((tag, index) => (
                  <Badge key={index} variant="outline" className="text-xs bg-blue-50 text-blue-700">
                    {tag}
                  </Badge>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Expandable Details */}
        <Collapsible open={isExpanded} onOpenChange={setIsExpanded}>
          <CollapsibleTrigger asChild>
            <Button variant="ghost" size="sm" className="w-full justify-between text-xs">
              {isExpanded ? 'Hide Details' : 'Show Details'}
              {isExpanded ? (
                <ChevronDown className="h-3 w-3" />
              ) : (
                <ChevronRight className="h-3 w-3" />
              )}
            </Button>
          </CollapsibleTrigger>
          <CollapsibleContent className="space-y-3 pt-2">
            {/* Entry/Exit States */}
            <div className="grid grid-cols-2 gap-2 text-xs">
              <div className="space-y-1">
                <div className="font-medium text-muted-foreground">Entry State</div>
                <div className="bg-muted p-2 rounded text-xs">
                  {Object.entries(segment.entry_state).map(([key, value]) => (
                    <div key={key} className="flex justify-between">
                      <span className="text-muted-foreground">{key}:</span>
                      <span>{String(value)}</span>
                    </div>
                  ))}
                </div>
              </div>
              <div className="space-y-1">
                <div className="font-medium text-muted-foreground">Exit State</div>
                <div className="bg-muted p-2 rounded text-xs">
                  {Object.entries(segment.exit_state).map(([key, value]) => (
                    <div key={key} className="flex justify-between">
                      <span className="text-muted-foreground">{key}:</span>
                      <span>{String(value)}</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Steps Preview */}
            <div className="space-y-1">
              <div className="font-medium text-muted-foreground">Steps Preview</div>
              <div className="bg-muted p-2 rounded text-xs max-h-32 overflow-y-auto">
                {segment.steps.slice(0, 3).map((step, index) => (
                  <div key={index} className="mb-1 last:mb-0">
                    <span className="text-muted-foreground">Step {index + 1}:</span>{' '}
                    {JSON.stringify(step).substring(0, 100)}...
                  </div>
                ))}
                {segment.steps.length > 3 && (
                  <div className="text-muted-foreground">
                    ... and {segment.steps.length - 3} more steps
                  </div>
                )}
              </div>
            </div>
          </CollapsibleContent>
        </Collapsible>
      </CardContent>
    </Card>
  );
}
