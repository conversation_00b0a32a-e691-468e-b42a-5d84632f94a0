'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Search, X, Target, Calendar, User } from 'lucide-react';
import { useListProducts } from '@/queries/product';
import { useGetProductProgramCategories, useListProgramsByCategory } from '@/queries/program';
import { useDebounce } from '@/hooks/use-debounce';
import { Program } from 'types';

interface ProgramSelectorProps {
  selectedProgramId?: number;
  onProgramSelect: (program: Program | null) => void;
  title?: string;
  description?: string;
}

export function ProgramSelector({ 
  selectedProgramId, 
  onProgramSelect, 
  title = "Select Program",
  description = "Choose a product and program to view its segment hierarchy"
}: ProgramSelectorProps) {
  const [selectedProductId, setSelectedProductId] = useState<number | undefined>();
  const [selectedCategoryId, setSelectedCategoryId] = useState<number | undefined>();
  const [searchTerm, setSearchTerm] = useState('');
  const [showSearch, setShowSearch] = useState(false);

  const debouncedSearchTerm = useDebounce(searchTerm, 300);

  // Fetch products
  const { data: productsResponse } = useListProducts({ limit: 100 });
  const products = productsResponse?.products || [];

  // Fetch categories for selected product
  const { data: categoriesData } = useGetProductProgramCategories(selectedProductId);
  const categories = categoriesData || [];

  // Fetch programs for selected category
  const { data: programsResponse, isLoading: programsLoading } = useListProgramsByCategory({
    categoryId: selectedCategoryId || 0,
    productId: selectedProductId,
    search: debouncedSearchTerm || undefined,
    limit: 50,
    sortField: 'program_title',
    sortDirection: 'asc'
  });

  const programs = programsResponse?.programs || [];
  const selectedProgram = programs.find(p => p.id === selectedProgramId);

  // Reset dependent selections when parent changes
  useEffect(() => {
    setSelectedCategoryId(undefined);
    onProgramSelect(null);
  }, [selectedProductId, onProgramSelect]);

  useEffect(() => {
    onProgramSelect(null);
  }, [selectedCategoryId, onProgramSelect]);

  const handleProgramSelect = (programId: string) => {
    const program = programs.find(p => p.id === parseInt(programId));
    onProgramSelect(program || null);
  };

  const clearSelection = () => {
    setSelectedProductId(undefined);
    setSelectedCategoryId(undefined);
    setSearchTerm('');
    onProgramSelect(null);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Target className="h-5 w-5" />
              {title}
            </CardTitle>
            <CardDescription>{description}</CardDescription>
          </div>
          {selectedProgramId && (
            <Button variant="outline" size="sm" onClick={clearSelection}>
              <X className="h-4 w-4 mr-1" />
              Clear
            </Button>
          )}
        </div>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Product Selection */}
        <div className="space-y-2">
          <label className="text-sm font-medium">Product</label>
          <Select 
            value={selectedProductId?.toString() || ''} 
            onValueChange={(value) => setSelectedProductId(value ? parseInt(value) : undefined)}
          >
            <SelectTrigger>
              <SelectValue placeholder="Select a product" />
            </SelectTrigger>
            <SelectContent>
              {products.map((product) => (
                <SelectItem key={product.id} value={product.id.toString()}>
                  <div className="flex items-center gap-2">
                    <span>{product.name}</span>
                    <Badge variant="outline" className="text-xs">
                      {product.model}
                    </Badge>
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Category Selection */}
        {selectedProductId && (
          <div className="space-y-2">
            <label className="text-sm font-medium">Category</label>
            <Select 
              value={selectedCategoryId?.toString() || ''} 
              onValueChange={(value) => setSelectedCategoryId(value ? parseInt(value) : undefined)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select a category" />
              </SelectTrigger>
              <SelectContent>
                {categories.map((category) => (
                  <SelectItem key={category.id} value={category.id.toString()}>
                    <div className="flex items-center justify-between w-full">
                      <span>{category.name}</span>
                      <Badge variant="secondary" className="text-xs ml-2">
                        {category.programCount} programs
                      </Badge>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        )}

        {/* Program Search and Selection */}
        {selectedCategoryId && (
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <label className="text-sm font-medium">Program</label>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowSearch(!showSearch)}
              >
                <Search className="h-4 w-4" />
              </Button>
            </div>
            
            {showSearch && (
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search programs..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            )}

            <Select 
              value={selectedProgramId?.toString() || ''} 
              onValueChange={handleProgramSelect}
              disabled={programsLoading}
            >
              <SelectTrigger>
                <SelectValue placeholder={programsLoading ? "Loading programs..." : "Select a program"} />
              </SelectTrigger>
              <SelectContent className="max-h-60">
                {programs.map((program) => (
                  <SelectItem key={program.id} value={program.id.toString()}>
                    <div className="flex flex-col gap-1 py-1">
                      <div className="flex items-center gap-2">
                        <span className="font-medium">{program.program_title || program.name}</span>
                        <Badge variant="outline" className="text-xs">
                          ID: {program.id}
                        </Badge>
                      </div>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        )}

        {/* Selected Program Info */}
        {selectedProgram && (
          <div className="p-3 bg-muted rounded-lg">
            <h4 className="font-medium mb-2">Selected Program</h4>
            <div className="grid grid-cols-2 gap-2 text-sm">
              <div><strong>ID:</strong> {selectedProgram.id}</div>
              <div><strong>Title:</strong> {selectedProgram.program_title || selectedProgram.name}</div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
