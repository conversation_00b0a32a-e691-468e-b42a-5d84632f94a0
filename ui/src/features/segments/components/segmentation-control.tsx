'use client';

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
import {
  Play,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle,
  RefreshCw,
  Loader2,
  Square,
  ChevronDown,
  Trash2
} from 'lucide-react';
import {
  useStartSegmentation,
  useCancelSegmentation,
  useCanStartSegmentation,
  useGetLatestSegmentationJob,
  useClearSegmentsAndJobs
} from '@/queries/segment';
import { SegmentationJobStatus } from '@/services/segment.service';

interface SegmentationControlProps {
  programId: number;
  productId?: number;
  onSegmentationComplete?: () => void;
}

export function SegmentationControl({ programId, productId, onSegmentationComplete }: SegmentationControlProps) {
  const [showDetails, setShowDetails] = useState(false);

  const startSegmentation = useStartSegmentation();
  const cancelSegmentation = useCancelSegmentation();
  const clearSegments = useClearSegmentsAndJobs();
  const { canStart, latestJob, isRunning, isPending, isCompleted, isFailed, isCancelled } = useCanStartSegmentation(programId);
  const { data: jobDetails } = useGetLatestSegmentationJob(programId);

  const handleStartSegmentation = () => {
    startSegmentation.mutate(programId, {
      onSuccess: () => {
        setShowDetails(true);
      }
    });
  };

  const handleCancelSegmentation = () => {
    if (latestJob?.id) {
      cancelSegmentation.mutate(latestJob.id);
    }
  };

  const handleClearProgramSegments = () => {
    clearSegments.mutate({ program_id: programId });
  };

  const handleClearProductSegments = () => {
    if (productId) {
      clearSegments.mutate({ product_id: productId });
    }
  };

  const getStatusIcon = (status: SegmentationJobStatus) => {
    switch (status) {
      case SegmentationJobStatus.PENDING:
        return <Clock className="h-4 w-4 text-yellow-600" />;
      case SegmentationJobStatus.RUNNING:
        return <Loader2 className="h-4 w-4 text-blue-600 animate-spin" />;
      case SegmentationJobStatus.COMPLETED:
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case SegmentationJobStatus.FAILED:
        return <XCircle className="h-4 w-4 text-red-600" />;
      case SegmentationJobStatus.CANCELLED:
        return <XCircle className="h-4 w-4 text-orange-600" />;
      default:
        return <AlertCircle className="h-4 w-4 text-gray-600" />;
    }
  };

  const getStatusColor = (status: SegmentationJobStatus) => {
    switch (status) {
      case SegmentationJobStatus.PENDING:
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case SegmentationJobStatus.RUNNING:
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case SegmentationJobStatus.COMPLETED:
        return 'bg-green-100 text-green-800 border-green-200';
      case SegmentationJobStatus.FAILED:
        return 'bg-red-100 text-red-800 border-red-200';
      case SegmentationJobStatus.CANCELLED:
        return 'bg-orange-100 text-orange-800 border-orange-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const formatDuration = (startTime?: string, endTime?: string) => {
    if (!startTime) return 'Not started';
    
    const start = new Date(startTime);
    const end = endTime ? new Date(endTime) : new Date();
    const duration = Math.floor((end.getTime() - start.getTime()) / 1000);
    
    if (duration < 60) return `${duration}s`;
    if (duration < 3600) return `${Math.floor(duration / 60)}m ${duration % 60}s`;
    return `${Math.floor(duration / 3600)}h ${Math.floor((duration % 3600) / 60)}m`;
  };

  // Trigger callback when segmentation completes
  if (isCompleted && onSegmentationComplete) {
    onSegmentationComplete();
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <RefreshCw className="h-5 w-5" />
              Program Segmentation
            </CardTitle>
            <CardDescription>
              Generate macro phases and micro chunks for this program
            </CardDescription>
          </div>
          {latestJob && (
            <Badge className={getStatusColor(latestJob.status)}>
              {getStatusIcon(latestJob.status)}
              <span className="ml-1 capitalize">{latestJob.status}</span>
            </Badge>
          )}
        </div>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Start Button */}
        {canStart && (
          <div className="flex items-center gap-2">
            <Button 
              onClick={handleStartSegmentation}
              disabled={startSegmentation.isPending}
              className="flex items-center gap-2"
            >
              {startSegmentation.isPending ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <Play className="h-4 w-4" />
              )}
              Start Segmentation
            </Button>
            <Button 
              variant="outline" 
              size="sm"
              onClick={() => setShowDetails(!showDetails)}
            >
              {showDetails ? 'Hide Details' : 'Show Details'}
            </Button>
          </div>
        )}

        {/* Progress Display */}
        {(isRunning || isPending) && jobDetails && (
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2 text-sm">
                <span className="font-medium">Progress</span>
                <span>{jobDetails.progress_percentage}%</span>
              </div>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="outline"
                    size="sm"
                    disabled={cancelSegmentation.isPending || clearSegments.isPending}
                    className="flex items-center gap-1 text-red-600 hover:text-red-700 hover:bg-red-50"
                  >
                    {(cancelSegmentation.isPending || clearSegments.isPending) ? (
                      <Loader2 className="h-3 w-3 animate-spin" />
                    ) : (
                      <Square className="h-3 w-3" />
                    )}
                    Stop
                    <ChevronDown className="h-3 w-3" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem
                    onClick={handleCancelSegmentation}
                    disabled={cancelSegmentation.isPending}
                  >
                    <Square className="h-4 w-4 mr-2" />
                    Cancel Current Job
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem
                    onClick={handleClearProgramSegments}
                    disabled={clearSegments.isPending}
                    className="text-destructive focus:text-destructive"
                  >
                    <Trash2 className="h-4 w-4 mr-2" />
                    Clear This Program&apos;s Segments
                  </DropdownMenuItem>
                  {productId && (
                    <DropdownMenuItem
                      onClick={handleClearProductSegments}
                      disabled={clearSegments.isPending}
                      className="text-destructive focus:text-destructive"
                    >
                      <Trash2 className="h-4 w-4 mr-2" />
                      Clear All Product Segments
                    </DropdownMenuItem>
                  )}
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
            <Progress value={jobDetails.progress_percentage} className="h-2" />
            
            {jobDetails.current_step && (
              <div className="text-sm text-muted-foreground">
                <strong>Current Step:</strong> {jobDetails.current_step}
              </div>
            )}
            
            {jobDetails.total_steps > 0 && (
              <div className="text-sm text-muted-foreground">
                <strong>Progress:</strong> {jobDetails.completed_steps} of {jobDetails.total_steps} steps completed
              </div>
            )}
            
            <div className="text-sm text-muted-foreground">
              <strong>Duration:</strong> {formatDuration(jobDetails.started_at)}
            </div>
          </div>
        )}

        {/* Completion Message */}
        {isCompleted && jobDetails && (
          <Alert>
            <CheckCircle className="h-4 w-4" />
            <AlertDescription>
              <div className="flex items-center justify-between">
                <div>
                  Segmentation completed successfully in {formatDuration(jobDetails.started_at, jobDetails.completed_at)}.
                  The program has been segmented into macro phases and micro chunks.
                </div>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button
                      variant="outline"
                      size="sm"
                      disabled={clearSegments.isPending}
                      className="flex items-center gap-1 text-red-600 hover:text-red-700 hover:bg-red-50"
                    >
                      {clearSegments.isPending ? (
                        <Loader2 className="h-3 w-3 animate-spin" />
                      ) : (
                        <Trash2 className="h-3 w-3" />
                      )}
                      Clear
                      <ChevronDown className="h-3 w-3" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem
                      onClick={handleClearProgramSegments}
                      disabled={clearSegments.isPending}
                      className="text-destructive focus:text-destructive"
                    >
                      <Trash2 className="h-4 w-4 mr-2" />
                      Clear This Program&apos;s Segments
                    </DropdownMenuItem>
                    {productId && (
                      <DropdownMenuItem
                        onClick={handleClearProductSegments}
                        disabled={clearSegments.isPending}
                        className="text-destructive focus:text-destructive"
                      >
                        <Trash2 className="h-4 w-4 mr-2" />
                        Clear All Product Segments
                      </DropdownMenuItem>
                    )}
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </AlertDescription>
          </Alert>
        )}

        {/* Error Message */}
        {isFailed && jobDetails && (
          <Alert variant="destructive">
            <XCircle className="h-4 w-4" />
            <AlertDescription>
              <div className="flex items-center justify-between">
                <div>
                  <div>
                    <strong>Segmentation failed:</strong> {jobDetails.error_message || 'Unknown error occurred'}
                  </div>
                  {jobDetails.started_at && (
                    <div className="mt-1 text-sm">
                      Duration: {formatDuration(jobDetails.started_at, jobDetails.completed_at)}
                    </div>
                  )}
                </div>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button
                      variant="outline"
                      size="sm"
                      disabled={clearSegments.isPending}
                      className="flex items-center gap-1 text-red-600 hover:text-red-700 hover:bg-red-50"
                    >
                      {clearSegments.isPending ? (
                        <Loader2 className="h-3 w-3 animate-spin" />
                      ) : (
                        <Trash2 className="h-3 w-3" />
                      )}
                      Clear
                      <ChevronDown className="h-3 w-3" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem
                      onClick={handleClearProgramSegments}
                      disabled={clearSegments.isPending}
                      className="text-destructive focus:text-destructive"
                    >
                      <Trash2 className="h-4 w-4 mr-2" />
                      Clear This Program&apos;s Segments
                    </DropdownMenuItem>
                    {productId && (
                      <DropdownMenuItem
                        onClick={handleClearProductSegments}
                        disabled={clearSegments.isPending}
                        className="text-destructive focus:text-destructive"
                      >
                        <Trash2 className="h-4 w-4 mr-2" />
                        Clear All Product Segments
                      </DropdownMenuItem>
                    )}
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </AlertDescription>
          </Alert>
        )}

        {/* Cancelled Message */}
        {isCancelled && jobDetails && (
          <Alert className="border-orange-200 bg-orange-50">
            <XCircle className="h-4 w-4 text-orange-600" />
            <AlertDescription>
              <div className="flex items-center justify-between">
                <div>
                  <div>
                    <strong>Segmentation cancelled:</strong> The segmentation process was stopped by user request.
                  </div>
                  {jobDetails.started_at && (
                    <div className="mt-1 text-sm">
                      Duration: {formatDuration(jobDetails.started_at, jobDetails.completed_at)}
                    </div>
                  )}
                </div>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button
                      variant="outline"
                      size="sm"
                      disabled={clearSegments.isPending}
                      className="flex items-center gap-1 text-red-600 hover:text-red-700 hover:bg-red-50"
                    >
                      {clearSegments.isPending ? (
                        <Loader2 className="h-3 w-3 animate-spin" />
                      ) : (
                        <Trash2 className="h-3 w-3" />
                      )}
                      Clear
                      <ChevronDown className="h-3 w-3" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem
                      onClick={handleClearProgramSegments}
                      disabled={clearSegments.isPending}
                      className="text-destructive focus:text-destructive"
                    >
                      <Trash2 className="h-4 w-4 mr-2" />
                      Clear This Program&apos;s Segments
                    </DropdownMenuItem>
                    {productId && (
                      <DropdownMenuItem
                        onClick={handleClearProductSegments}
                        disabled={clearSegments.isPending}
                        className="text-destructive focus:text-destructive"
                      >
                        <Trash2 className="h-4 w-4 mr-2" />
                        Clear All Product Segments
                      </DropdownMenuItem>
                    )}
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </AlertDescription>
          </Alert>
        )}

        {/* Job Details */}
        {showDetails && latestJob && (
          <div className="mt-4 p-3 bg-muted rounded-lg">
            <h4 className="font-medium mb-2">Job Details</h4>
            <div className="grid grid-cols-2 gap-2 text-sm">
              <div><strong>Job ID:</strong> {latestJob.id}</div>
              <div><strong>Status:</strong> {latestJob.status}</div>
              <div><strong>Created:</strong> {new Date(latestJob.created_at).toLocaleString()}</div>
              {latestJob.started_at && (
                <div><strong>Started:</strong> {new Date(latestJob.started_at).toLocaleString()}</div>
              )}
              {latestJob.completed_at && (
                <div><strong>Completed:</strong> {new Date(latestJob.completed_at).toLocaleString()}</div>
              )}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
