'use client';

import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, TabsContent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Setting<PERSON>, Filter } from 'lucide-react';
import { SegmentHierarchyView } from './components/segment-hierarchy-view';
import { ProductSegmentationControl } from './components/product-segmentation-control';
import { SegmentStats } from './components/segment-stats';
import { ProductCleanupButton } from './components/product-cleanup-button';

export function SegmentsViewPage() {
  const [activeTab, setActiveTab] = useState('hierarchy');
  const [selectedProductId, setSelectedProductId] = useState<number | undefined>();

  // Handle filter changes
  const handleFilterChange = () => {
    // Refresh data when filters change
  };

  return (
    <>
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Program Segments</h2>
          <p className="text-muted-foreground">
            View program hierarchy and manage segmentation processes
          </p>
        </div>
        <div className="flex items-center gap-2">
          <ProductCleanupButton
            variant="outline"
            size="default"
            className="border-destructive text-destructive hover:bg-destructive hover:text-destructive-foreground"
          />
        </div>
      </div>
      <div className="space-y-4 max-w-7xl mx-auto w-full">
        {/* Stats Overview */}
        <SegmentStats filters={{ product_id: selectedProductId }} />

        {/* Main Content */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
          <TabsList className="flex w-full">
            <TabsTrigger value="hierarchy" className="flex items-center gap-2 flex-1">
              <Filter className="h-4 w-4" />
              Hierarchy View
            </TabsTrigger>
            <TabsTrigger value="segmentation" className="flex items-center gap-2 flex-1">
              <Settings className="h-4 w-4" />
              Segmentation
            </TabsTrigger>
          </TabsList>

          <TabsContent value="hierarchy" className="space-y-4 w-full">
            <div className="max-w-7xl mx-auto">
              <SegmentHierarchyView />
            </div>
          </TabsContent>

          <TabsContent value="segmentation" className="space-y-4 w-full">
            <div className="max-w-7xl mx-auto">
              <div className="grid gap-6 w-full">
                {/* Product-Level Segmentation */}
                <ProductSegmentationControl
                  selectedProductId={selectedProductId}
                  onProductChange={setSelectedProductId}
                  onSegmentationComplete={handleFilterChange}
                />

                <Card>
                  <CardHeader>
                    <CardTitle>Program-Level Segmentation</CardTitle>
                    <CardDescription>
                      Start and monitor segmentation processes for individual massage programs
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="w-full">
                    <p className="text-sm text-muted-foreground mb-4">
                      Select a program from the hierarchy view to start individual program segmentation, or use the product-level control above to segment all programs in a product.
                    </p>
                    <div className="text-center py-8 text-muted-foreground">
                      <Settings className="h-12 w-12 mx-auto mb-2 opacity-50" />
                      <p>Select a program to view individual segmentation controls</p>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </>
  );
}
