'use client';

import { useState } from 'react';
import { Area, AreaChart, CartesianGrid, XA<PERSON>s, Legend } from 'recharts';

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import {
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent
} from '@/components/ui/chart';
import { useProductProgramGenerationActivity } from '@/queries/dashboard';
import { AreaGraphSkeleton } from './area-graph-skeleton';

export function AreaGraph() {
  // Default period and granularity; can be made dynamic later
  const {
    data: response,
    isLoading,
    isError
  } = useProductProgramGenerationActivity();
  const [hiddenProducts, setHiddenProducts] = useState<Set<string>>(new Set());

  // Prepare recharts data
  const products = response?.query_details.products_returned ?? [];
  const productNames = response?.data?.[0]?.product_names ?? {};

  // Create a mapping of product names to pids for the legend formatter
  const productLegendInfo = products.map((pid) => ({
    pid,
    name: productNames[pid] || pid
  }));

  // Build recharts data: [{ timestamp, [product_id]: count, ... }, ...]
  const chartData =
    response?.data?.map((point) => {
      const row: Record<string, any> = { timestamp: point.timestamp };
      products.forEach((pid) => {
        row[pid] = point.product_program_counts?.[pid] ?? 0;
      });
      return row;
    }) ?? [];

  // Color palette for product lines (fallback to hsl if > 10)
  const palette = [
    '#4F8CFF',
    '#FFB547',
    '#FF5C5C',
    '#6DD400',
    '#A259FF',
    '#00B8A9',
    '#FF7F50',
    '#FFD700',
    '#00CFFF',
    '#FF69B4'
  ];

  const inactiveColor = '#cccccc';
  const activeFillOpacity = 0.15;

  // Handler for legend item click
  const handleLegendClick = (data: any) => {
    const { dataKey } = data;
    if (dataKey) {
      setHiddenProducts((prevHidden) => {
        const newHidden = new Set(prevHidden);
        if (newHidden.has(dataKey)) {
          newHidden.delete(dataKey);
        } else {
          newHidden.add(dataKey);
        }
        return newHidden;
      });
    }
  };

  if (isLoading) {
    return <AreaGraphSkeleton />;
  }

  if (isError) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Program Generation per Product</CardTitle>
        </CardHeader>
        <CardContent>
          <div className='text-destructive'>
            Failed to load program generation activity data.
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Program Generation per Product</CardTitle>
        <CardDescription>
          Number of programs generated per product over time
        </CardDescription>
      </CardHeader>
      <CardContent>
        <ChartContainer config={{}} className='aspect-auto h-[310px] w-full'>
          <AreaChart
            accessibilityLayer
            data={chartData}
            margin={{
              left: 12,
              right: 12
            }}
          >
            <XAxis
              dataKey='timestamp'
              tickFormatter={(value) => value?.slice(0, 10)}
            />
            <CartesianGrid strokeDasharray='3 3' />
            {products.map((pid, idx) => {
              const isActive = !hiddenProducts.has(pid);
              const originalColor = palette[idx % palette.length];

              const strokeColor = isActive ? originalColor : inactiveColor;
              const fillColor = isActive ? originalColor : inactiveColor; // Match stroke for legend swatch consistency
              const currentStrokeWidth = isActive ? 1 : 0; // 0 width for inactive
              const currentFillOpacity = isActive ? activeFillOpacity : 0; // 0 opacity for inactive

              return (
                <Area
                  key={pid}
                  type='monotone'
                  dataKey={pid}
                  stroke={strokeColor}
                  fill={fillColor}
                  strokeWidth={currentStrokeWidth}
                  fillOpacity={currentFillOpacity}
                  name={productNames[pid] || pid}
                  isAnimationActive={false}
                />
              );
            })}
            <ChartTooltip
              content={({ active, payload, label }) => (
                <ChartTooltipContent
                  active={active}
                  payload={payload}
                  label={label}
                />
              )}
            />
            <Legend
              onClick={handleLegendClick}
              formatter={(value, entry) => {
                // value is the name of the series
                const product = productLegendInfo.find((p) => p.name === value);
                const color = entry.color; // entry.color should be reliable for styling

                if (product && hiddenProducts.has(product.pid)) {
                  return (
                    <span
                      style={{ textDecoration: 'line-through', color: '#999' }}
                    >
                      {value}
                    </span>
                  );
                }
                return <span style={{ color }}>{value}</span>;
              }}
            />
          </AreaChart>
        </ChartContainer>
      </CardContent>
    </Card>
  );
}
