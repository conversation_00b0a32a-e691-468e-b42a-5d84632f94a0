'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useListApplications } from '@/queries/application';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { PaperPlaneIcon } from '@radix-ui/react-icons';

type Application = {
  id: number;
  name: string;
};

export const QuickChatBar: React.FC = () => {
  const router = useRouter();
  const { data, isLoading, isError } = useListApplications();
  const [selectedAppId, setSelectedAppId] = useState<string>('');
  const [message, setMessage] = useState<string>('');

  const applications: Application[] = data?.chat_configs ?? [];

  const handleSend = () => {
    if (!selectedAppId || !message) return;
    const encodedMessage = encodeURIComponent(message);
    router.push(
      `/dashboard/chat?config_id=${selectedAppId}&initial_message=${encodedMessage}`
    );
  };

  const isSendDisabled =
    !selectedAppId ||
    !message ||
    isLoading ||
    isError ||
    applications.length === 0;

  return (
    <div className='rounded-lg border bg-white shadow-sm dark:border-border dark:bg-card'>
      <div className='flex items-center justify-between border-b p-3 dark:border-border'>
        <div className='flex items-center'>
          <h3 className='text-sm font-medium text-gray-800 dark:text-foreground'>
            Quick Chat
          </h3>
          <div className='ml-2 rounded-full bg-blue-100 px-2 py-0.5 text-xs font-medium text-blue-800 dark:bg-primary dark:text-primary-foreground'>
            Fast access
          </div>
        </div>
      </div>

      <div className='p-3'>
        <div className='flex flex-col gap-3 sm:flex-row sm:items-center'>
          <div className='sm:w-[220px]'>
            <label
              className='mb-1 block text-xs font-medium text-gray-700 dark:text-muted-foreground'
              htmlFor='quickchat-app'
            >
              Select Application
            </label>
            <Select value={selectedAppId} onValueChange={setSelectedAppId}>
              <SelectTrigger className='h-9 w-full bg-gray-50 text-sm transition-colors focus:bg-white dark:border-border dark:bg-input dark:text-foreground dark:focus:bg-muted overflow-hidden whitespace-nowrap'>
                <SelectValue
                  className='truncate overflow-hidden text-ellipsis whitespace-nowrap'
                  placeholder={
                    isLoading
                      ? 'Loading...'
                      : isError
                        ? 'Failed to load'
                        : 'Select application'
                  }
                />
              </SelectTrigger>
              <SelectContent>
                {applications.map((app) => (
                  <SelectItem key={app.id} value={String(app.id)}>
                    {app.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className='flex-1'>
            <label
              className='mb-1 block text-xs font-medium text-gray-700 dark:text-muted-foreground'
              htmlFor='quickchat-message'
            >
              Your Message
            </label>
            <div className='flex'>
              <Input
                id='quickchat-message'
                value={message}
                onChange={(e) => setMessage(e.target.value)}
                placeholder='Type your message here...'
                className='h-9 flex-1 rounded-r-none bg-gray-50 text-sm transition-colors focus:bg-white dark:border-border dark:bg-input dark:text-foreground dark:focus:bg-muted'
                onKeyDown={(e) => {
                  if (e.key === 'Enter' && !isSendDisabled) {
                    e.preventDefault();
                    handleSend();
                  }
                }}
              />
              <Button
                type='button'
                onClick={handleSend}
                disabled={isSendDisabled}
                className='h-9 rounded-l-none px-4 dark:bg-primary dark:text-primary-foreground dark:hover:bg-primary/80'
              >
                <PaperPlaneIcon className='h-4 w-4' />
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default QuickChatBar;
