'use client';

import { useChatsPerDay } from '@/queries/dashboard';
import {
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
  ChartLegend,
  ChartLegendContent
} from '@/components/ui/chart';
import { <PERSON>, <PERSON><PERSON><PERSON>, CartesianGrid, XAxis, YAxis } from 'recharts';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Terminal } from 'lucide-react';

const chartConfig = {
  chats: {
    label: 'Chats',
    color: 'hsl(var(--chart-1))'
  }
};

export function ChatsPerDayChart() {
  const { data: chatsPerDay, isLoading, isError, error } = useChatsPerDay(30); // Default to 30 days

  if (isLoading) {
    return (
      <Card className='flex h-full flex-col'>
        <CardHeader>
          <CardTitle>Chats per Day</CardTitle>
          <CardDescription>Last 30 days</CardDescription>
        </CardHeader>
        <CardContent>
          <Skeleton className='h-[300px] w-full' />
        </CardContent>
      </Card>
    );
  }

  if (isError) {
    return (
      <Card className='flex h-full flex-col'>
        <CardHeader>
          <CardTitle>Chats per Day</CardTitle>
          <CardDescription>Last 30 days</CardDescription>
        </CardHeader>
        <CardContent>
          <Alert variant='destructive'>
            <Terminal className='h-4 w-4' />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>
              {error instanceof Error
                ? error.message
                : 'Failed to load chat data.'}
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  if (!chatsPerDay || chatsPerDay.length === 0) {
    return (
      <Card className='flex h-full flex-col'>
        <CardHeader>
          <CardTitle>Chats per Day</CardTitle>
          <CardDescription>Last 30 days</CardDescription>
        </CardHeader>
        <CardContent>
          <p>No chat data available for the selected period.</p>
        </CardContent>
      </Card>
    );
  }

  // Format data for the chart
  const chartData = chatsPerDay.map((item) => ({
    date: item.date, // Store the full date string
    chats: item.count
  }));

  return (
    <Card className='flex h-full flex-col'>
      <CardHeader>
        <CardTitle>Chats per Day</CardTitle>
        <CardDescription>Last 30 days</CardDescription>
      </CardHeader>
      <CardContent className='flex-1 px-2 sm:p-6'>
        <ChartContainer
          config={chartConfig}
          className='aspect-auto h-[300px] w-full'
        >
          <BarChart
            accessibilityLayer
            data={chartData}
            margin={{
              left: 12,
              right: 12
            }}
          >
            <CartesianGrid vertical={false} />
            <XAxis
              dataKey='date'
              tickLine={false}
              tickMargin={8}
              axisLine={false}
              minTickGap={32}
              tickFormatter={(value) => {
                const date = new Date(value);
                return date.toLocaleDateString('en-US', {
                  month: 'short',
                  day: 'numeric'
                });
              }}
            />
            <YAxis
              tickLine={false}
              axisLine={false}
              tickMargin={10}
              allowDecimals={false}
            />
            <ChartTooltip
              cursor={false}
              content={
                <ChartTooltipContent
                  className='w-[150px]'
                  nameKey='chats'
                  labelFormatter={(value) => {
                    return new Date(value).toLocaleDateString('en-US', {
                      month: 'short',
                      day: 'numeric',
                      year: 'numeric'
                    });
                  }}
                />
              }
            />
            <ChartLegend content={<ChartLegendContent />} />
            <Bar dataKey='chats' fill='var(--color-chats)' radius={4} />
          </BarChart>
        </ChartContainer>
      </CardContent>
    </Card>
  );
}
