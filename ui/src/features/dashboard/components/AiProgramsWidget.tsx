'use client';

import React, { useState, useEffect } from 'react';
import { Card } from '@/components/ui/card';
import {
  Select,
  SelectTrigger,
  SelectValue,
  SelectContent,
  SelectItem
} from '@/components/ui/select';
import { Skeleton } from '@/components/ui/skeleton';
import { useListProducts } from '@/queries/product';
import { useGetProductProgramCategories } from '@/queries/program';
import ProgramsTable from '@/features/product/components/ProgramsTable';

type Product = { id: number; name: string };

export const AiProgramsWidget: React.FC = () => {
  const [selectedProductId, setSelectedProductId] = useState<number | null>(
    null
  );

  // Fetch all products
  const {
    data: productsResponse,
    isLoading: productsLoading,
    isError: productsError,
    error: productsErrorObj
  } = useListProducts();

  // Fetch categories for selected product
  const {
    data: categories,
    isLoading: categoriesLoading,
    isError: categoriesError,
    error: categoriesErrorObj
  } = useGetProductProgramCategories(selectedProductId);

  // Handle product selection
  const handleProductChange = (value: string) => {
    setSelectedProductId(value ? Number(value) : null);
  };

  // Auto-select the uDream product by default
  useEffect(() => {
    if (
      !selectedProductId &&
      !productsLoading &&
      productsResponse?.products &&
      productsResponse.products.length > 0
    ) {
      // Find product that starts with "udream" (case-insensitive)
      const uDreamProduct = productsResponse.products.find((product: Product) =>
        product.name.toLowerCase().startsWith('udream')
      );

      if (uDreamProduct) {
        setSelectedProductId(uDreamProduct.id);
      }
    }
  }, [productsResponse, productsLoading, selectedProductId]);

  return (
    <Card className='flex h-full flex-col p-2'>
      <h2 className='text-lg font-semibold'>AI Programs</h2>
      {/* Product Selector */}
      <div className='mb-4'>
        <label
          className='mb-1 block text-sm font-medium'
          htmlFor='product-select'
        >
          Select Product
        </label>
        {productsLoading ? (
          <Skeleton className='h-10 w-full' />
        ) : productsError ? (
          <div className='text-sm text-red-500'>Failed to load products.</div>
        ) : productsResponse?.products &&
          productsResponse.products.length > 0 ? (
          <Select
            value={selectedProductId?.toString() ?? ''}
            onValueChange={handleProductChange}
          >
            <SelectTrigger id='product-select' className='w-full'>
              <SelectValue placeholder='Choose a product...' />
            </SelectTrigger>
            <SelectContent>
              {productsResponse.products.map((product: Product) => (
                <SelectItem key={product.id} value={product.id.toString()}>
                  {product.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        ) : (
          <div className='text-sm text-muted-foreground'>
            No products found.
          </div>
        )}
      </div>

      {/* AI Programs Table or Message */}
      <div className='flex flex-1 flex-col'>
        <div className='max-h-[360px] flex-1 overflow-y-auto'>
          {categoriesLoading && selectedProductId ? (
            <div>
              <Skeleton className='mb-2 h-6 w-2/3' />
              <Skeleton className='mb-2 h-6 w-1/2' />
              <Skeleton className='h-6 w-1/4' />
            </div>
          ) : categoriesError && selectedProductId ? (
            <div className='text-sm text-red-500'>
              Failed to load categories.
              {categoriesErrorObj instanceof Error && (
                <div className='mt-1 text-xs'>{categoriesErrorObj.message}</div>
              )}
            </div>
          ) : selectedProductId && categories && categories.length > 0 ? (
            (() => {
              // Find the "generated programs" category (case-insensitive)
              const aiCategory = categories.find(
                (cat) =>
                  typeof cat.name === 'string' &&
                  cat.name.toLowerCase() === 'generated programs'
              );
              if (aiCategory) {
                return (
                  <ProgramsTable
                    categoryId={aiCategory.id}
                    productId={selectedProductId}
                    categoryName={aiCategory.name}
                    condensed={true}
                  />
                );
              } else {
                return (
                  <div className='text-sm text-muted-foreground'>
                    The &apos;ai programs&apos; category was not found for this
                    product.
                  </div>
                );
              }
            })()
          ) : selectedProductId && categories && categories.length === 0 ? (
            <div className='text-sm text-muted-foreground'>
              No categories found for this product.
            </div>
          ) : (
            <div className='text-sm text-muted-foreground'>
              Select a product to view AI programs.
            </div>
          )}
        </div>
      </div>
    </Card>
  );
};

export default AiProgramsWidget;
