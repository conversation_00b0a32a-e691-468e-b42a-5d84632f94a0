'use client';

import React from 'react';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
  CardFooter
} from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Terminal, TrendingUp } from 'lucide-react';
import { useDashboardStats } from '@/queries/dashboard';
import {
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
  type ChartConfig
} from '@/components/ui/chart';
import { Label, Pie, PieChart } from 'recharts';

const initialChartConfig = {
  programs: {
    label: 'Programs'
  }
  // Individual category configs will be generated dynamically
} satisfies ChartConfig;

// Add a color palette array with more diverse colors
const colorPalette = [
  'hsl(var(--chart-1))',
  'hsl(var(--chart-2))',
  'hsl(var(--chart-3))',
  'hsl(var(--chart-4))',
  'hsl(var(--chart-5))',
  'hsl(217, 91%, 60%)', // Blue
  'hsl(43, 96%, 56%)', // Yellow
  'hsl(284, 70%, 73%)', // Purple
  'hsl(162, 73%, 46%)', // Teal
  'hsl(14, 100%, 71%)', // Salmon
  'hsl(324, 77%, 58%)', // Pink
  'hsl(190, 90%, 50%)', // Cyan
  'hsl(150, 60%, 60%)', // Mint green
  'hsl(30, 100%, 65%)' // Orange
];

// Fisher-Yates shuffle algorithm to randomize colors
const shuffleArray = <T,>(array: T[]): T[] => {
  const shuffled = [...array];
  for (let i = shuffled.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
  }
  return shuffled;
};

export function ProgramsByCategoryCard() {
  const { data, isLoading, isError, error } = useDashboardStats();

  // Create a randomly shuffled version of the color palette
  const randomizedColors = React.useMemo(() => shuffleArray(colorPalette), []);

  // Use CSS variables for fill, matching the reference component style
  const chartData = React.useMemo(() => {
    if (data?.programsByCategory) {
      return Object.entries(data.programsByCategory).map(
        ([name, value], index) => ({
          name,
          value,
          fill: randomizedColors[index % randomizedColors.length]
        })
      );
    }
    return [];
  }, [data, randomizedColors]);

  // Generate chartConfig dynamically using more diverse colors
  const chartConfig = React.useMemo(() => {
    const config: ChartConfig = { ...initialChartConfig };
    if (data?.programsByCategory) {
      Object.keys(data.programsByCategory).forEach((category, index) => {
        config[category.toLowerCase().replace(/\s+/g, '-')] = {
          label: category,
          color: randomizedColors[index % randomizedColors.length]
        };
      });
    }
    return config;
  }, [data, randomizedColors]);

  const totalPrograms = data?.totalPrograms ?? 0;

  // Hardcoded trend value for mockup purposes
  const trend = 3.7;

  return (
    <Card className='col-span-4 flex h-full flex-col md:col-span-3'>
      <CardHeader className='items-center pb-0'>
        <CardTitle>Programs by Category</CardTitle>
        <CardDescription>Current quarter</CardDescription>
      </CardHeader>
      <CardContent className='flex-1 pb-0'>
        {isLoading && (
          <div className='flex h-full w-full flex-col items-center justify-center gap-4'>
            <Skeleton className='h-8 w-3/4' />
            <Skeleton className='h-[200px] w-[200px] rounded-full' />
            <Skeleton className='h-6 w-1/2' />
          </div>
        )}
        {isError && (
          <Alert variant='destructive' className='mt-4'>
            <Terminal className='h-4 w-4' />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>
              Could not load category data.{' '}
              {error instanceof Error ? error.message : ''}
            </AlertDescription>
          </Alert>
        )}
        {!isLoading && !isError && data && (
          <>
            {chartData.length > 0 ? (
              <ChartContainer
                config={chartConfig}
                className='mx-auto h-[300px] w-full'
              >
                <PieChart>
                  <ChartTooltip
                    cursor={false}
                    content={
                      <ChartTooltipContent
                        hideLabel
                        className='min-w-[150px]'
                        formatter={(value, name) => (
                          <div className='flex w-full items-center justify-between gap-2'>
                            <span className='text-muted-foreground'>
                              {name}
                            </span>
                            <span className='font-mono font-medium tabular-nums text-foreground'>
                              {typeof value === 'number'
                                ? value.toLocaleString()
                                : value}
                            </span>
                          </div>
                        )}
                      />
                    }
                  />
                  <Pie
                    data={chartData}
                    dataKey='value'
                    nameKey='name'
                    innerRadius={60}
                    strokeWidth={5}
                  >
                    <Label
                      content={({ viewBox }) => {
                        if (viewBox && 'cx' in viewBox && 'cy' in viewBox) {
                          return (
                            <text
                              x={viewBox.cx}
                              y={viewBox.cy}
                              textAnchor='middle'
                              dominantBaseline='middle'
                            >
                              <tspan
                                x={viewBox.cx}
                                y={viewBox.cy}
                                className='fill-foreground text-3xl font-bold'
                              >
                                {totalPrograms.toLocaleString()}
                              </tspan>
                              <tspan
                                x={viewBox.cx}
                                y={(viewBox.cy || 0) + 24}
                                className='fill-muted-foreground'
                              >
                                Programs
                              </tspan>
                            </text>
                          );
                        }
                      }}
                    />
                  </Pie>
                </PieChart>
              </ChartContainer>
            ) : (
              <p className='text-center text-sm text-muted-foreground'>
                No category data available.
              </p>
            )}
          </>
        )}
      </CardContent>
      <CardFooter className='flex-col gap-2 text-sm'>
        <div className='flex items-center gap-2 font-medium leading-none text-emerald-500'>
          Trending up by {trend.toFixed(1)}% this quarter{' '}
          <TrendingUp className='h-4 w-4' />
        </div>
        <div className='leading-none text-muted-foreground'>
          Showing program distribution by category
        </div>
      </CardFooter>
    </Card>
  );
}
