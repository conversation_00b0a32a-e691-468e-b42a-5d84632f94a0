'use client';

import React from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Terminal } from 'lucide-react'; // Using Terminal as a generic error icon
import { useDashboardStats } from '@/queries/dashboard';
import { Icons } from '@/components/icons'; // Assuming Icons component exists for specific icons

export function DashboardStatsCards() {
  const { data, isLoading, isError, error } = useDashboardStats();

  if (isLoading) {
    return (
      <>
        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>
              Total Programs
            </CardTitle>
            <Icons.post className='h-4 w-4 text-muted-foreground' />{' '}
            {/* Use Icons.post */}
          </CardHeader>
          <CardContent>
            <Skeleton className='h-8 w-20' />
            <Skeleton className='mt-1 h-4 w-32' />
          </CardContent>
        </Card>
        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>
              Total Products
            </CardTitle>
            <Icons.product className='h-4 w-4 text-muted-foreground' />{' '}
            {/* Placeholder Icon */}
          </CardHeader>
          <CardContent>
            <Skeleton className='h-8 w-20' />
            <Skeleton className='mt-1 h-4 w-32' />
          </CardContent>
        </Card>
        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>
              Total Chat Sessions
            </CardTitle>
            <Icons.message className='h-4 w-4 text-muted-foreground' />{' '}
            {/* Use Icons.message */}
          </CardHeader>
          <CardContent>
            <Skeleton className='h-8 w-20' />
            <Skeleton className='mt-1 h-4 w-32' />
          </CardContent>
        </Card>
        {/* Add a fourth skeleton if needed to match layout */}
        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>Loading...</CardTitle>
            <Skeleton className='h-4 w-4' />
          </CardHeader>
          <CardContent>
            <Skeleton className='h-8 w-20' />
            <Skeleton className='mt-1 h-4 w-32' />
          </CardContent>
        </Card>
      </>
    );
  }

  if (isError) {
    return (
      <div className='col-span-full'>
        {' '}
        {/* Ensure error spans full width */}
        <Alert variant='destructive'>
          <Terminal className='h-4 w-4' />
          <AlertTitle>Error Fetching Stats</AlertTitle>
          <AlertDescription>
            {error instanceof Error
              ? error.message
              : 'An unknown error occurred.'}
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  // Assuming data is successfully fetched
  return (
    <>
      <Card>
        <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
          <CardTitle className='text-sm font-medium'>Total Programs</CardTitle>
          <Icons.post className='h-4 w-4 text-muted-foreground' />{' '}
          {/* Use Icons.post */}
        </CardHeader>
        <CardContent>
          <div className='text-2xl font-bold'>{data?.totalPrograms ?? 0}</div>
          {/* Optional: Add comparison text if available */}
          {/* <p className='text-xs text-muted-foreground'>+X% from last month</p> */}
        </CardContent>
      </Card>
      <Card>
        <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
          <CardTitle className='text-sm font-medium'>Total Products</CardTitle>
          <Icons.product className='h-4 w-4 text-muted-foreground' />{' '}
          {/* Placeholder Icon */}
        </CardHeader>
        <CardContent>
          <div className='text-2xl font-bold'>{data?.totalProducts ?? 0}</div>
        </CardContent>
      </Card>
      <Card>
        <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
          <CardTitle className='text-sm font-medium'>
            Total Chat Sessions
          </CardTitle>
          <Icons.message className='h-4 w-4 text-muted-foreground' />{' '}
          {/* Use Icons.message */}
        </CardHeader>
        <CardContent>
          <div className='text-2xl font-bold'>
            {data?.totalChatSessions ?? 0}
          </div>
        </CardContent>
      </Card>
      {/* Keep a fourth card for layout consistency if needed, or remove */}
      <Card>
        <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
          <CardTitle className='text-sm font-medium'>Total Users</CardTitle>
          <Icons.user className='h-4 w-4 text-muted-foreground' />{' '}
        </CardHeader>
        <CardContent>
          <div className='text-2xl font-bold'>{data?.totalUsers ?? 0}</div>
        </CardContent>
      </Card>
    </>
  );
}
