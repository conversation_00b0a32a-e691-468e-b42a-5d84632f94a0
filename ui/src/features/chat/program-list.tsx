import { Program } from 'types';
import { Play, ChevronDown, ChevronUp } from 'lucide-react';
import { ProgramCard } from './program-card';
import { useState } from 'react';

interface ProgramListProps {
  programs: Program[];
  onViewDetails: (program: Program) => void;
}

export function ProgramList({ programs, onViewDetails }: ProgramListProps) {
  const [expanded, setExpanded] = useState(false);

  if (!programs || programs.length === 0) {
    return null;
  }

  return (
    <div className='space-y-4'>
      <div
        className='flex cursor-pointer items-center py-1'
        onClick={() => setExpanded(!expanded)}
      >
        <div className='mr-2.5 flex h-8 w-8 items-center justify-center rounded-full bg-slate-600 shadow-sm'>
          <Play className='h-4 w-4 text-white' strokeWidth={2.5} />
        </div>
        <div className='flex-1'>
          <h3 className='mb-0.5 text-sm font-medium leading-tight'>
            Analyzing relevant programs
          </h3>
          {!expanded && (
            <p className='text-xs leading-tight text-muted-foreground'>
              {programs.length} program{programs.length !== 1 ? 's' : ''} found
            </p>
          )}
        </div>
        <div className='ml-2 flex h-8 w-8 items-center justify-center'>
          {expanded ? (
            <ChevronUp className='h-4 w-4 text-slate-600' strokeWidth={2} />
          ) : (
            <ChevronDown className='h-4 w-4 text-slate-600' strokeWidth={2} />
          )}
        </div>
      </div>

      {expanded && (
        <div className='space-y-3'>
          {programs.map((program, index) => (
            <ProgramCard
              key={program.id || index}
              program={program}
              onViewDetails={onViewDetails}
            />
          ))}
        </div>
      )}
    </div>
  );
}
