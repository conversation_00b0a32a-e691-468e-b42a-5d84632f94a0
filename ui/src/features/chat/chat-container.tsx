'use client';

import { But<PERSON> } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { ArrowUp } from 'lucide-react';
import React, { useState, useRef, useEffect } from 'react';
import { useSendMessage, useThreadMessages } from '@/queries/chat';
import {
  ChatStreamResponse,
  Document,
  Message,
  Program,
  TargetSequence,
  HighEndProductStep,
  MassageProgramDetails,
  ProgramPlan,

} from 'types';
import { toast } from 'sonner';
import { useRouter, useSearchParams } from 'next/navigation';
import { marked } from 'marked';
import { ProgramDetailsModal } from './program-details-modal';
import { ProgramList } from './program-list';
import TargetSequenceCard from './target-sequence-card';
import { ProgramPlanCard } from './program-plan-card';

import DocumentAccordion from './document-accordion';
import SynthesizedStepsCard from './synthesized-steps-card'; // Added import
import MassageProgramDetailsCard from './MassageProgramDetailsCard'; // Added import

// Define the default initial message
const defaultInitialMessage: Message = {
  id: 0,
  role: 'assistant',
  content:
    'Hi! I am your expert massage program generator, how can I help you today?'
};

export default function ChatContainer() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const initialThreadId = searchParams.get('thread_id'); // Store initial threadId
  const initialMessageFromUrl = searchParams.get('initial_message');
  const configIdFromUrl = searchParams.get('config_id');
  const initialMessageProcessed = useRef(false);

  const [messages, setMessages] = useState<Message[]>([]);
  const [inputMessage, setInputMessage] = useState('');
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const { mutate: sendMessage, isPending } = useSendMessage();
  const [configId, setConfigId] = useState(searchParams.get('config_id'));
  const [threadId, setThreadId] = useState(initialThreadId); // Initialize with initialThreadId
  const [renderedContent, setRenderedContent] = useState<
    Record<string, string>
  >({});
  const [selectedProgram, setSelectedProgram] = useState<Program | null>(null);
  const [programModalOpen, setProgramModalOpen] = useState(false);
  const [isInitialLoadFetchComplete, setIsInitialLoadFetchComplete] =
    useState(!initialThreadId); // If no initial threadId, fetch is considered "complete"

  const hasRedirected = useRef(false);

  // Effect to clear messages ONLY when thread ID is removed from the URL (e.g., via back button)
  const prevUrlThreadIdRef = useRef<string | null>(
    searchParams.get('thread_id')
  );

  useEffect(() => {
    const urlThreadId = searchParams.get('thread_id');
    const prevUrlThreadId = prevUrlThreadIdRef.current;

    // Only clear if thread ID transitioned from set to unset (back navigation)
    if (prevUrlThreadId && !urlThreadId && messages.length > 0) {
      setMessages([]);
      setThreadId(null);
      setIsInitialLoadFetchComplete(true); // Prevents refetching
    }

    // Update the ref for next render
    prevUrlThreadIdRef.current = urlThreadId;
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [searchParams, messages.length]);

  // Effect to handle initial message from URL
  useEffect(() => {
    if (
      initialMessageFromUrl &&
      configIdFromUrl &&
      !initialMessageProcessed.current
    ) {
      initialMessageProcessed.current = true; // Set flag synchronously

      const decodedMessage = decodeURIComponent(initialMessageFromUrl);
      const newUserMessage: Message = {
        id: Date.now(), // Or a more robust ID generation
        content: decodedMessage,
        role: 'user'
      };
      setMessages((prev) => [...prev, newUserMessage]);

      const payload = {
        message: decodedMessage,
        slug: threadId || '',
        chat_config_id: Number(configIdFromUrl) || 1,
        onToken: handleMessageSuccess
      };

      // Defer sendMessage to next tick
      setTimeout(() => {
        sendMessage(payload);
      }, 0);

      // Defer URL update to ensure it runs after sendMessage has initiated
      setTimeout(() => {
        const newSearchParams = new URLSearchParams(searchParams.toString());
        newSearchParams.delete('initial_message');
        router.replace(
          `/dashboard/chat?${newSearchParams.toString()}`,
          undefined // Use undefined for shallow routing if supported and desired, or remove for full navigation
        );
      }, 0); // A small delay might be needed if 0 isn't enough, e.g., 50ms
    }
  }, [
    initialMessageFromUrl,
    configIdFromUrl,
    sendMessage,
    router,
    searchParams,
    threadId
  ]);

  // Fetch historical messages ONLY if there was an initial threadId and fetch isn't complete yet
  const { data: threadMessages, isLoading: isLoadingThreadMessages } =
    useThreadMessages(threadId || '', {
      enabled: !!initialThreadId && !isInitialLoadFetchComplete && !!threadId
    });

  // Effect to process HISTORICAL messages fetched on initial load
  useEffect(() => {
    // Only run if we had an initialThreadId, messages are fetched, and processing hasn't happened
    if (
      initialThreadId &&
      threadMessages &&
      !isLoadingThreadMessages &&
      !isInitialLoadFetchComplete
    ) {
      const processedMessages = threadMessages.messages.map((message) => {
        // Handle programs messages
        if (message.role === 'programs' && message.content) {
          try {
            // If role is 'programs', the content should be a JSON array of programs
            const programs = JSON.parse(message.content);

            // Return message with programs data extracted from content
            return {
              ...message,
              programs: Array.isArray(programs) ? programs : [],
              content: '' // Clear content since we're using programs data
            };
          } catch (e) {
            // If JSON parsing fails for a 'programs' role message, log error
            console.error(
              'Failed to parse program JSON from message content:',
              message,
              e
            );
            // Return message without modification if parsing fails
            return message;
          }
        }

        // Handle target_sequence messages
        if (message.role === 'target_sequence' && message.content) {
          try {
            // Parse the target sequence JSON from content
            const targetSequence = JSON.parse(message.content);
            return {
              ...message,
              target_sequence: targetSequence,
              content: '' // Clear content since we're using target_sequence data
            };
          } catch (e) {
            console.error(
              'Failed to parse target sequence JSON from message content:',
              message,
              e
            );
            return message;
          }
        }

        // Handle synthesized_steps messages
        if (message.role === 'synthesized_steps' && message.content) {
          try {
            const synthesizedStepsArray = JSON.parse(
              message.content
            ) as HighEndProductStep[];
            return {
              ...message,
              synthesized_steps: synthesizedStepsArray,
              program_id: message.program_id,
              program_liked: message.program_liked,
              program_disliked: message.program_disliked,
              program_saved: message.program_saved,
              content: ''
            };
          } catch (e) {
            console.error(
              'Failed to parse synthesized_steps JSON from message content:',
              message,
              e
            );
            return message;
          }
        }

        // Handle program_plan messages
        if (message.role === 'program_plan' && message.content) {
          try {
            const programPlan = JSON.parse(message.content) as ProgramPlan;
            return {
              ...message,
              program_plan: programPlan,
              content: ''
            };
          } catch (e) {
            console.error(
              'Failed to parse program_plan JSON from message content:',
              message,
              e
            );
            return message;
          }
        }



        // Handle massage_program_details messages
        if (message.role === 'massage_program_details' && message.content) {
          try {
            const programDetails = JSON.parse(message.content) as MassageProgramDetails;
            return {
              ...message,
              massageProgramDetails: programDetails,
              content: '' // Clear content since we're using massageProgramDetails data
            };
          } catch (e) {
            console.error(
              'Failed to parse massage_program_details JSON from message content:',
              message,
              e
            );
            return message;
          }
        }

        return message; // Return other messages as is
      });

      setMessages(processedMessages);
      setIsInitialLoadFetchComplete(true); // Mark initial fetch/processing as complete
    }
  }, [
    threadMessages,
    threadId,
    initialThreadId,
    isLoadingThreadMessages,
    isInitialLoadFetchComplete
  ]);

  // Effect to render markdown for messages (remains largely the same)
  useEffect(() => {
    const renderMarkdown = async () => {
      for (const message of messages) {
        if (message.role === 'assistant' && message.content) { // Re-render if content exists
          try {
            // Use marked synchronously if possible
            const html = marked.parse
              ? marked.parse(message.content)
              : await marked(message.content);
            setRenderedContent((prev) => ({
              ...prev,
              [message.id]: typeof html === 'string' ? html : ''
            }));
          } catch (error) {
            console.error('Error rendering markdown:', error);
          }
        }
      }
    };

    renderMarkdown();
  }, [messages]);

  const handleMessageSuccess = (response: ChatStreamResponse) => {
    if (!response) return;
    switch (response.type) {
      case 'token':
        // Update URL only once when starting a new chat
        if (!threadId && response.slug && !hasRedirected.current) {
          router.push(
            `/dashboard/chat?config_id=${configId}&thread_id=${response.slug}`
          );
          setThreadId(response.slug);
          hasRedirected.current = true;
        }

        // Simplified token handling: find and update, or create if not found
        setMessages((prev) => {
          const messageIndex = prev.findIndex(
            (msg) => msg.id === response.message_id
          );

          if (messageIndex !== -1) {
            // Message exists, update it
            const updatedMessages = [...prev];
            const existingMessage = updatedMessages[messageIndex];

            // Only append text content if the message role is not 'programs' or 'synthesized_steps'
            // (prevents overwriting structured data with stray tokens)
            if (
              existingMessage.role !== 'programs' &&
              existingMessage.role !== 'synthesized_steps'
            ) {
              updatedMessages[messageIndex] = {
                ...existingMessage,
                content: existingMessage.content + (response.content as string)
              };
            }
            return updatedMessages;
          } else {
            // Message doesn't exist, create it
            const newMessage: Message = {
              id: response.message_id,
              content: response.content as string, // Start with this token's content
              role: 'assistant' // Default role, can be overridden by specific types
            };
            return [...prev, newMessage];
          }
        });
        break;
      case 'programs':
        // Ensure programs logic correctly finds or creates messages
        if (Array.isArray(response.content)) {
          setMessages((prev) => {
            const messageIndex = prev.findIndex(
              (msg) => msg.id === response.message_id
            );

            if (messageIndex !== -1) {
              // Message exists, update it for programs
              const updatedMessages = [...prev];
              updatedMessages[messageIndex] = {
                ...updatedMessages[messageIndex], // Keep existing fields like documents
                role: 'programs', // Ensure role is set to 'programs'
                programs: response.content as Program[],
                content: '' // Clear text content when displaying programs
              };
              return updatedMessages;
            } else {
              // Message doesn't exist, create a new programs message
              const newMessage: Message = {
                id: response.message_id,
                content: '', // Programs messages shouldn't have text content initially
                role: 'programs',
                programs: response.content as Program[]
              };
              return [...prev, newMessage];
            }
          });
        }
        break;
      case 'target_sequence':
        // Handle target sequence messages
        setMessages((prev) => {
          const targetSequence = response.content as TargetSequence;
          const messageIndex = prev.findIndex(
            (msg) => msg.id === response.message_id
          );

          if (messageIndex !== -1) {
            // Update existing message
            const updatedMessages = [...prev];
            updatedMessages[messageIndex] = {
              ...updatedMessages[messageIndex],
              role: 'target_sequence',
              target_sequence: targetSequence,
              content: '' // Clear text content for target_sequence role
            };
            return updatedMessages;
          } else {
            // Create new message
            const newMessage: Message = {
              id: response.message_id,
              content: '',
              role: 'target_sequence',
              target_sequence: targetSequence
            };
            return [...prev, newMessage];
          }
        });
        break;
      case 'synthesized_steps':
        setMessages((prev) => {
          let parsedStepsArray: HighEndProductStep[];
          if (typeof response.content === 'string') {
            parsedStepsArray = JSON.parse(
              response.content
            ) as HighEndProductStep[];
          } else {
            parsedStepsArray = response.content as HighEndProductStep[];
          }
          const programId = response.program_id;
          const programLiked = false;
          const programDisliked = false;
          const programSaved = false;

          const messageIndex = prev.findIndex(
            (msg) => msg.id === response.message_id
          );

          if (messageIndex !== -1) {
            const updatedMessages = [...prev];
            updatedMessages[messageIndex] = {
              ...updatedMessages[messageIndex],
              role: 'synthesized_steps',
              synthesized_steps: parsedStepsArray,
              program_id: programId,
              program_liked: programLiked,
              program_disliked: programDisliked,
              program_saved: programSaved,
              content: ''
            };
            return updatedMessages;
          } else {
            const newMessage: Message = {
              id: response.message_id,
              content: '',
              role: 'synthesized_steps',
              synthesized_steps: parsedStepsArray,
              program_id: programId,
              program_liked: programLiked,
              program_disliked: programDisliked,
              program_saved: programSaved
            };
            return [...prev, newMessage];
          }
        });
        break;
      case 'error': // New case for handling errors from backend
        setMessages((prev) => {
          // Check if an identical error message (same ID) already exists to prevent duplicates
          if (
            prev.some(
              (msg) => msg.id === response.message_id && msg.role === 'error'
            )
          ) {
            return prev;
          }
          const newErrorMessage: Message = {
            id: response.message_id || Date.now(), // Prefer response.message_id from backend
            content: response.content as string,
            role: 'error' // Special role for styling
          };
          return [...prev, newErrorMessage];
        });
        break;
      case 'massage_program_details': // New case for handling tool_call from backend
        setMessages((prev) => {
          try {
            const programDetails = JSON.parse(
              response.content as string
            ) as MassageProgramDetails;
            const messageIndex = prev.findIndex(
              (msg) => msg.id === response.message_id
            );

            if (messageIndex !== -1) {
              // Message exists, update it
              const updatedMessages = [...prev];
              updatedMessages[messageIndex] = {
                ...updatedMessages[messageIndex],
                role: 'massage_program_details',
                massageProgramDetails: programDetails,
                content: '' // Clear text content when displaying program details
              };
              return updatedMessages;
            } else {
              // Message doesn't exist, create a new one
              const newMessage: Message = {
                id: response.message_id,
                content: '', // Tool call messages display structured data, not text
                role: 'massage_program_details',
                massageProgramDetails: programDetails
              };
              return [...prev, newMessage];
            }
          } catch (e) {
            console.error(
              'Failed to parse MassageProgramDetails JSON from massage_program_details content:',
              response.content,
              e
            );
            // Optionally, add an error message to the chat
            const newErrorMessage: Message = {
              id: response.message_id || Date.now(),
              content:
                'Failed to display program details due to a parsing error.',
              role: 'error'
            };
            // Add the error message without altering existing messages if parsing fails
            return prev.some((msg) => msg.id === newErrorMessage.id)
              ? prev
              : [...prev, newErrorMessage];
          }
        });
        break;
      case 'documents':
        if (Array.isArray(response.content)) {
          setMessages((prev) => {
            const botMessage = prev.find(
              (msg) => msg.id === response.message_id
            );
            if (botMessage) {
              return prev.map((message) =>
                message.id === response.message_id
                  ? { ...message, documents: response.content as Document[] }
                  : message
              );
            }
            // Add new message if there is no existing message with this ID
            return [
              ...prev,
              {
                id: response.message_id,
                content: '',
                role: 'assistant',
                documents: response.content as Document[]
              }
            ];
          });
        }
        break;
      case 'program_plan':
        // Handle program plan messages from segment-aware synthesis
        setMessages((prev) => {
          const programPlan = response.content as ProgramPlan;
          const messageIndex = prev.findIndex(
            (msg) => msg.id === response.message_id
          );

          if (messageIndex !== -1) {
            // Update existing message
            const updatedMessages = [...prev];
            updatedMessages[messageIndex] = {
              ...updatedMessages[messageIndex],
              role: 'program_plan',
              program_plan: programPlan,
              content: ''
            };
            return updatedMessages;
          } else {
            // Create new message
            const newMessage: Message = {
              id: response.message_id,
              content: '',
              role: 'program_plan',
              program_plan: programPlan
            };
            return [...prev, newMessage];
          }
        });
        break;

      default:
        // Ensure response.type is a string before using it in toast
        const responseTypeString =
          typeof response.type === 'string' ? response.type : 'unknown';
    }
  };

  const handleSubmit = async () => {
    if (!inputMessage.trim() || isPending) return;

    // Add user message immediately
    setMessages((prev) => [
      ...prev,
      {
        id: messages.length + 1,
        content: inputMessage,
        role: 'user'
      }
    ]);

    const payload = {
      message: inputMessage,
      slug: threadId || '', // Empty string for first message will create new thread
      chat_config_id: Number(configId) || 1,
      onToken: handleMessageSuccess
    };

    sendMessage(payload);
    setInputMessage('');
  };

  // Close the program details modal
  const handleCloseModal = () => {
    setProgramModalOpen(false);
    setSelectedProgram(null);
  };

  // Open the program details modal
  const handleOpenProgramDetails = (program: Program) => {
    setSelectedProgram(program);
    setProgramModalOpen(true);
  };

  // Clean up
  useEffect(() => {
    return () => {
      hasRedirected.current = false;
      // initialMessageProcessed.current = false; // Reset if component can be re-mounted with new URL params
    };
  }, []);

  return (
    <div className='flex h-full w-full flex-col bg-background'>
      {/* Messages Area */}
      <div className='slim-scrollbar flex-1 overflow-y-auto px-4 py-6'>
        <div className='mx-auto max-w-6xl space-y-6'>
          {/* Welcome message for new chats */}
          {!threadId && messages.length === 0 && !initialMessageFromUrl && (
            <div className='flex justify-start'>
              <div className='max-w-[85%] rounded-2xl bg-muted/50 px-4 py-3 text-sm text-muted-foreground'>
                <div className='whitespace-pre-wrap break-words'>
                  {defaultInitialMessage.content}
                </div>
              </div>
            </div>
          )}
          {/* Messages */}
          {messages.map((message) => (
            <div
              key={message.id}
              className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}
            >
              <div
                className={`max-w-[85%] rounded-2xl px-4 py-3 text-sm transition-colors ${
                  message.role === 'user'
                    ? 'bg-primary text-primary-foreground'
                    : message.role === 'error'
                      ? 'bg-destructive/10 text-destructive border border-destructive/20'
                      : 'bg-muted/50 text-foreground'
                }`}
              >
                <div className='whitespace-pre-wrap break-words'>
                  {message.role === 'programs' ? (
                    message.programs &&
                    message.programs.length > 0 && (
                      <ProgramList
                        programs={message.programs}
                        onViewDetails={handleOpenProgramDetails}
                      />
                    )
                  ) : message.role === 'target_sequence' &&
                    message.target_sequence ? (
                    <TargetSequenceCard
                      target_sequence={message.target_sequence}
                    />
                  ) : message.role === 'synthesized_steps' &&
                    message.synthesized_steps &&
                    message.program_id ? (
                    <SynthesizedStepsCard
                      steps={message.synthesized_steps}
                      programId={message.program_id}
                      liked={message.program_liked}
                      disliked={message.program_disliked}
                      saved={message.program_saved}
                    />
                  ) : message.role === 'error' ? (
                    <>
                      <span className='font-medium'>Error:</span>{' '}
                      {message.content}
                    </>
                  ) : message.role === 'massage_program_details' &&
                    message.massageProgramDetails ? (
                    <MassageProgramDetailsCard
                      details={message.massageProgramDetails}
                    />
                  ) : message.role === 'program_plan' &&
                    message.program_plan ? (
                    <ProgramPlanCard
                      program_plan={message.program_plan}
                    />
                  ) : (
                    (message.role === 'assistant' && renderedContent[message.id]) ? (
                      <div
                        className='prose prose-sm max-w-none dark:prose-invert prose-p:leading-relaxed prose-pre:bg-muted prose-pre:border prose-pre:border-border'
                        dangerouslySetInnerHTML={{ __html: renderedContent[message.id] }}
                      />
                    ) : (
                      message.content
                    )
                  )}
                </div>
                {message.documents &&
                  message.role !== 'error' && (
                    <div className='mt-3 pt-3 border-t border-border/50'>
                      <DocumentAccordion documents={message.documents} />
                    </div>
                  )}
              </div>
            </div>
          ))}
          {/* Loading indicator */}
          {isPending && (
            <div className='flex justify-start'>
              <div className='max-w-[85%] rounded-2xl bg-muted/50 px-4 py-3'>
                <div className='flex items-center space-x-2'>
                  <div className='flex space-x-1'>
                    <div className='h-2 w-2 animate-bounce rounded-full bg-muted-foreground/60' />
                    <div className='h-2 w-2 animate-bounce rounded-full bg-muted-foreground/60 [animation-delay:0.1s]' />
                    <div className='h-2 w-2 animate-bounce rounded-full bg-muted-foreground/60 [animation-delay:0.2s]' />
                  </div>
                  <span className='text-xs text-muted-foreground'>AI is thinking...</span>
                </div>
              </div>
            </div>
          )}
          <div ref={messagesEndRef} />
        </div>
      </div>

      {/* Input Area */}
      <div className='border-t bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60'>
        <div className='mx-auto max-w-3xl px-4 py-4'>
          <div className='relative flex items-end gap-3'>
            <div className='flex-1'>
              <Textarea
                placeholder='Type your message...'
                className='min-h-[44px] max-h-32 resize-none border-0 bg-muted/50 px-4 py-3 text-sm placeholder:text-muted-foreground focus-visible:ring-1 focus-visible:ring-ring'
                value={inputMessage}
                onChange={(e) => setInputMessage(e.target.value)}
                onKeyDown={(e) => {
                  if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    handleSubmit();
                  }
                }}
              />
            </div>
            <Button
              size='icon'
              className='h-11 w-11 shrink-0'
              onClick={handleSubmit}
              disabled={isPending || !inputMessage.trim()}
            >
              <ArrowUp className='h-4 w-4' />
            </Button>
          </div>
        </div>
      </div>

      {/* Program Details Modal */}
      <ProgramDetailsModal
        program={selectedProgram}
        isOpen={programModalOpen}
        onClose={handleCloseModal}
      />
    </div>
  );
}
