'use client';
import { Switch } from '@/components/ui/switch';
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger
} from '@/components/ui/collapsible';
import { useListSources } from '@/queries/source';
import { Button } from '@/components/ui/button';
import {
  Select,
  SelectGroup,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogClose
} from '@/components/ui/dialog';
import { Slider } from '@/components/ui/slider';
import { LucideChevronDown } from 'lucide-react';
import { useState, useEffect } from 'react';
import {
  useCreateConfig,
  useGetConfig,
  useListModels,
  useUpdateConfig
} from '@/queries/chat';
import { useListProducts } from '@/queries/product';
import { Textarea } from '@/components/ui/textarea';
import { toast } from 'sonner';
import { useSearchParams } from 'next/navigation';
import { useRouter } from 'next/navigation';

export default function ChatConfig({ className }: { className?: string }) {
  const router = useRouter();

  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(20);

  const { data: products, isLoading: isProductsLoading } = useListProducts({
    skip: (page - 1) * pageSize,
    limit: pageSize
  });
  const productsList = products?.products || [];

  const {
    data: sources,
    isLoading,
    refetch
  } = useListSources({
    skip: (page - 1) * pageSize,
    limit: pageSize
  });
  const sourcesList = sources?.sources || [];

  const [selectedProduct, setSelectedProduct] = useState<number | null>(null);
  const [selectedModel, setSelectedModel] = useState<number | null>(null);
  const [configName, setConfigName] = useState<string>('');
  const [sftModelName, setSftModelName] = useState<string>('');
  const [selectedConfig, setSelectedConfig] = useState<any>(null);
  const [synthesisStrategy, setSynthesisStrategy] = useState<string>('segment-aware-synthesis');
  const { data: models } = useListModels();

  const { mutate: createConfig } = useCreateConfig();
  const { mutate: updateConfig } = useUpdateConfig();

  const searchParams = useSearchParams();
  const mode = searchParams.get('mode');
  const configId = searchParams.get('config_id');
  const {
    data: config,
    isLoading: isConfigLoading,
    isError: isConfigError
  } = useGetConfig(configId ? Number(configId) : undefined, {
    // The enabled flag within useGetConfig (!!id) will handle this.
    // We pass undefined here so it doesn't try to fetch with a default ID like 1.
  });

  // Update state when config data is available
  if (config && !selectedConfig) {
    setSelectedProduct(config.product_id || null);
    setSelectedModel(config.model_id);
    setConfigName(config.name);
    setSftModelName(config.sft_model_name || '');
    setSynthesisStrategy(config.synthesis_strategy || 'segment-aware-synthesis');
    setSelectedConfig(config);
  }

  useEffect(() => {
    if (mode === 'create') {
      resetConfig();
    }
  }, [mode]);

  const resetConfig = () => {
    setSelectedProduct(null);
    setSelectedModel(null);
    setConfigName('');
    setSftModelName('');
    setSelectedConfig(null);
    setSynthesisStrategy('segment-aware-synthesis');
  };

  const handleSubmit = () => {
    if (
      !selectedModel || !selectedProduct || !configName
    ) {
      toast.error('Please fill in App name, Product and Model fields');
      return;
    }

    const payload = {
      name: configName,
      model_id: selectedModel,
      product_id: selectedProduct,
      sft_model_name: sftModelName || null,
      synthesis_strategy: synthesisStrategy || null
    };

    if (selectedConfig) {
      updateConfig(
        { id: selectedConfig.id, payload },
        {
          onSuccess: () => {
            toast.success('Chat configuration updated successfully');
          },
          onError: (error) => {
            toast.error(`Error updating config: ${error}`);
          }
        }
      );
    } else {
      createConfig(payload, {
        onSuccess: (response) => {
          setSelectedConfig(response);
          router.push(`/dashboard/chat?config_id=${response.id}`);
          toast.success('Chat configuration created successfully');
        },
        onError: (error) => {
          toast.error(`Error creating config: ${error}`);
        }
      });
    }
  };

  return (
    <div
      className={`mx-auto w-full max-w-md overflow-y-auto rounded-xl border bg-card p-6 text-card-foreground shadow-lg ${className}`}
    >
      <div className='flex flex-col gap-6'>
        <div className='space-y-4'>
          <div className='space-y-4'>
            <div className='flex items-center gap-4'>
              <span className='min-w-[120px] text-sm font-medium text-muted-foreground'>
                App name: <span className='text-red-500'>*</span>
              </span>
              <input
                type='text'
                value={configName}
                onChange={(e) => setConfigName(e.target.value)}
                className='flex h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50'
                placeholder='Enter configuration name...'
              />
            </div>

            {/* Product dropdown */}
            <div className='flex items-center gap-4'>
              <span className='min-w-[120px] text-sm font-medium text-muted-foreground'>
                Product: <span className='text-red-500'>*</span>
              </span>
              <Select
                value={selectedProduct?.toString() || 'none'}
                onValueChange={(value) =>
                  setSelectedProduct(value === 'none' ? null : Number(value))
                }
              >
                <SelectTrigger className='w-[280px]'>
                  <SelectValue placeholder='Select a product' />
                </SelectTrigger>
                <SelectContent>
                  <SelectGroup>
                    <SelectItem value='none'>None</SelectItem>
                    {productsList?.map((product) => (
                      <SelectItem
                        key={product.id}
                        value={product.id.toString()}
                      >
                        {product.name}
                      </SelectItem>
                    ))}
                  </SelectGroup>
                </SelectContent>
              </Select>
            </div>

            {/* Model dropdown */}
            <div className='flex items-center gap-4'>
              <span className='min-w-[120px] text-sm font-medium text-muted-foreground'>
                Model: <span className='text-red-500'>*</span>
              </span>
              <Select
                value={selectedModel?.toString()}
                onValueChange={(value) => setSelectedModel(Number(value))}
              >
                <SelectTrigger className='w-[280px]'>
                  <SelectValue placeholder='Select a model' />
                </SelectTrigger>
                <SelectContent>
                  <SelectGroup>
                    {models?.models.map((model) => (
                      <SelectItem key={model.id} value={model.id.toString()}>
                        {model.name}
                      </SelectItem>
                    ))}
                  </SelectGroup>
                </SelectContent>
              </Select>
            </div>

            {/* SFT Model Name input */}
            <div className='flex items-center gap-4'>
              <span className='min-w-[120px] text-sm font-medium text-muted-foreground'>
                SFT Model Name:
              </span>
              <input
                type='text'
                value={sftModelName}
                onChange={(e) => setSftModelName(e.target.value)}
                className='flex h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50'
                placeholder='Enter SFT model name (optional)...'
              />
            </div>

            {/* Synthesis Strategy selection - only show if no SFT model */}
            {!sftModelName && (
              <div className='flex items-start gap-4'>
                <span className='min-w-[120px] text-sm font-medium text-muted-foreground pt-2'>
                  Synthesis Strategy:
                </span>
                <div className='flex flex-col gap-3'>
                  <label className='flex items-center gap-2 cursor-pointer'>
                    <input
                      type='radio'
                      name='synthesis_strategy'
                      value='segment-aware-synthesis'
                      checked={synthesisStrategy === 'segment-aware-synthesis'}
                      onChange={(e) => setSynthesisStrategy(e.target.value)}
                      className='h-4 w-4 text-primary focus:ring-primary border-gray-300'
                    />
                    <span className='text-sm'>Segment aware synthesis</span>
                  </label>
                  <label className='flex items-center gap-2 cursor-pointer'>
                    <input
                      type='radio'
                      name='synthesis_strategy'
                      value='segment_and_stitch'
                      checked={synthesisStrategy === 'segment_and_stitch'}
                      onChange={(e) => setSynthesisStrategy(e.target.value)}
                      className='h-4 w-4 text-primary focus:ring-primary border-gray-300'
                    />
                    <span className='text-sm'>Segment and stitch</span>
                  </label>
                </div>
              </div>
            )}
          </div>
        </div>

        <Button onClick={handleSubmit} className='w-full' size='lg'>
          {selectedConfig ? 'Update' : 'Create'} Configuration
        </Button>
      </div>
    </div>
  );
}
