'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { HighEndProductStep } from 'types';
import {
  ChevronDown,
  ChevronUp,
  Code,
  ThumbsUp,
  ThumbsDown,
  Bookmark,
  BookmarkCheck
} from 'lucide-react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import { cn } from '@/lib/utils';
import { toast } from 'sonner';
import {
  useLikeProgram,
  useDislikeProgram,
  useSaveProgram
} from '@/queries/chat';

interface SynthesizedStepsCardProps {
  steps: HighEndProductStep[];
  programId: number;
  liked?: boolean;
  disliked?: boolean;
  saved?: boolean;
}

const SynthesizedStepsCard: React.FC<SynthesizedStepsCardProps> = ({
  steps,
  programId,
  liked: initialLiked = false,
  disliked: initialDisliked = false,
  saved: initialSaved = false
}) => {
  const router = useRouter();
  const [expanded, setExpanded] = useState(false);

  // Local state for optimistic UI updates
  const [localLiked, setLocalLiked] = useState(initialLiked);
  const [localDisliked, setLocalDisliked] = useState(initialDisliked);
  const [localSaved, setLocalSaved] = useState(initialSaved);

  // Update local state when props change (e.g., from a fetch)
  useEffect(() => {
    setLocalLiked(initialLiked);
    setLocalDisliked(initialDisliked);
    setLocalSaved(initialSaved);
  }, [initialLiked, initialDisliked, initialSaved]);

  const { mutate: likeProgram, isPending: isLikePending } = useLikeProgram({
    onSuccess: (data) => {
      // Update local state with server response
      setLocalLiked(data.liked);
      setLocalDisliked(data.disliked);
    },
    onError: () => {
      // Revert optimistic update on error
      setLocalLiked(initialLiked);
      setLocalDisliked(initialDisliked);
      toast.error('Could not like program. Please try again.');
    }
  });

  const { mutate: dislikeProgram, isPending: isDislikePending } =
    useDislikeProgram({
      onSuccess: (data) => {
        // Update local state with server response
        setLocalLiked(data.liked);
        setLocalDisliked(data.disliked);
      },
      onError: () => {
        // Revert optimistic update on error
        setLocalLiked(initialLiked);
        setLocalDisliked(initialDisliked);
        toast.error('Could not dislike program. Please try again.');
      }
    });

  const { mutate: saveProgram, isPending: isSavePending } = useSaveProgram({
    onSuccess: (data) => {
      // Update local state with server response
      setLocalSaved(data.saved);
    },
    onError: () => {
      // Revert optimistic update on error
      setLocalSaved(initialSaved);
      toast.error('Could not save program. Please try again.');
    }
  });

  const isLoading = isLikePending || isDislikePending || isSavePending;

  const handleLike = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (isLoading) return;

    // Optimistic update
    setLocalLiked(!localLiked);
    if (!localLiked && localDisliked) setLocalDisliked(false);

    likeProgram(programId);
  };

  const handleDislike = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (isLoading) return;

    // Optimistic update
    setLocalDisliked(!localDisliked);
    if (!localDisliked && localLiked) setLocalLiked(false);

    dislikeProgram(programId);
  };

  const handleSave = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (isLoading) return;

    // Optimistic update
    setLocalSaved(!localSaved);

    saveProgram(programId);
  };

  const handleViewProgram = () => {
    router.push(`/dashboard/program-editor?id=${programId}`);
  };

  // Get a summary of the first few steps
  const getStepSummary = (
    allSteps: HighEndProductStep[],
    count = 2
  ): string => {
    if (!allSteps || allSteps.length === 0) {
      return 'No steps available.';
    }
    return allSteps
      .slice(0, count)
      .map(
        (step) =>
          step.roller_action_description ||
          step.air_action_description ||
          `Step ${step.step_number || 'N/A'}`
      )
      .filter(Boolean)
      .join('; ')
      .concat(allSteps.length > count ? '...' : '');
  };

  // Determine if there are special features in the program
  const hasKneading = steps?.some(
    (step) => step.kneading_speed !== null && step.kneading_speed !== undefined
  );
  const hasTapping = steps?.some(
    (step) => step.tapping_speed !== null && step.tapping_speed !== undefined
  );
  const hasScent = steps?.some((step) => step.scent);
  const hasLight = steps?.some((step) => step.light);

  const summary = getStepSummary(steps, 2);

  return (
    <div className='group relative space-y-1'>
      <div
        className='flex cursor-pointer items-center py-1'
        onClick={() => setExpanded(!expanded)}
      >
        <div className='mr-2.5 flex h-8 w-8 items-center justify-center rounded-full bg-slate-600 shadow-sm'>
          <Code className='h-4 w-4 text-white' strokeWidth={2.5} />
        </div>
        <div className='flex-1'>
          <h3 className='text-sm font-medium leading-tight'>
            Synthesized Program Steps
          </h3>
          {!expanded && steps && steps.length > 0 && (
            <div className='mt-0.5 flex flex-col gap-0.5'>
              <div className='flex items-center gap-2 text-xs leading-tight text-muted-foreground'>
                <span>{steps.length} steps</span>
                {hasKneading && (
                  <Badge
                    variant='outline'
                    className='h-4 px-1.5 py-0 text-[10px]'
                  >
                    Kneading
                  </Badge>
                )}
                {hasTapping && (
                  <Badge
                    variant='outline'
                    className='h-4 px-1.5 py-0 text-[10px]'
                  >
                    Tapping
                  </Badge>
                )}
                {hasScent && (
                  <Badge
                    variant='outline'
                    className='h-4 px-1.5 py-0 text-[10px]'
                  >
                    Scent
                  </Badge>
                )}
                {hasLight && (
                  <Badge
                    variant='outline'
                    className='h-4 px-1.5 py-0 text-[10px]'
                  >
                    Light
                  </Badge>
                )}
              </div>
              <p className='line-clamp-1 text-[11px] text-muted-foreground'>
                {summary}
              </p>
            </div>
          )}
        </div>
        <div className='ml-2 flex h-6 w-6 items-center justify-center'>
          {expanded ? (
            <ChevronUp className='h-3.5 w-3.5 text-slate-600' strokeWidth={2} />
          ) : (
            <ChevronDown className='h-3.5 w-3.5 text-slate-600' strokeWidth={2} />
          )}
        </div>
      </div>

      {expanded && (
        <div className='mt-1 space-y-3 border-l border-primary/25 pb-5 pl-3'>
          {steps && steps.length > 0 ? (
            <>
              <div>
                <p className='mb-1 text-xs font-medium text-muted-foreground'>
                  Program Details
                </p>
                <div className='flex gap-1'>
                  {hasKneading && (
                    <Badge variant='secondary' className='text-xs'>
                      Kneading
                    </Badge>
                  )}
                  {hasTapping && (
                    <Badge variant='secondary' className='text-xs'>
                      Tapping
                    </Badge>
                  )}
                  {hasScent && (
                    <Badge variant='secondary' className='text-xs'>
                      Scent
                    </Badge>
                  )}
                  {hasLight && (
                    <Badge variant='secondary' className='text-xs'>
                      Light
                    </Badge>
                  )}
                </div>
              </div>

              <Separator className='my-2' />

              <div>
                <p className='mb-1 text-xs font-medium text-muted-foreground'>
                  Steps Overview
                </p>
                <div className='slim-scrollbar max-h-48 overflow-x-auto overflow-y-auto'>
                  <Table className='w-full'>
                    <TableHeader className='sticky top-0 bg-background'>
                      <TableRow>
                        <TableHead className='w-[40px] px-2 py-1'>
                          Step
                        </TableHead>
                        <TableHead className='min-w-[150px] px-2 py-1'>
                          Roller Action
                        </TableHead>
                        <TableHead className='min-w-[100px] px-2 py-1'>
                          Air Action
                        </TableHead>
                        <TableHead className='w-[70px] px-2 py-1'>
                          Kneading
                        </TableHead>
                        <TableHead className='w-[70px] px-2 py-1'>
                          Tapping
                        </TableHead>
                        <TableHead className='w-[60px] px-2 py-1'>
                          Width
                        </TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {steps.slice(0, 5).map((step, index) => (
                        <TableRow key={index}>
                          <TableCell className='px-2 py-1'>
                            <Badge variant='outline' className='text-xs'>
                              {step.step_number || index + 1}
                            </Badge>
                          </TableCell>
                          <TableCell className='px-2 py-1 text-xs'>
                            {step.roller_action_description || '-'}
                          </TableCell>
                          <TableCell className='px-2 py-1 text-xs'>
                            {step.air_action_description || '-'}
                          </TableCell>
                          <TableCell className='px-2 py-1 text-xs'>
                            {step.kneading_speed !== null &&
                            step.kneading_speed !== undefined
                              ? step.kneading_speed
                              : '-'}
                          </TableCell>
                          <TableCell className='px-2 py-1 text-xs'>
                            {step.tapping_speed !== null &&
                            step.tapping_speed !== undefined
                              ? step.tapping_speed
                              : '-'}
                          </TableCell>
                          <TableCell className='px-2 py-1 text-xs'>
                            {step.width || '-'}
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>

                <div className='mt-1 flex items-center justify-between'>
                  {steps.length > 5 ? (
                    <p className='text-xs text-muted-foreground'>
                      +{steps.length - 5} more steps
                    </p>
                  ) : (
                    <div></div>
                  )}
                  <Button
                    onClick={handleViewProgram}
                    size='sm'
                    variant='outline'
                    className='h-7 py-1 text-xs'
                  >
                    View Full Program
                  </Button>
                </div>
              </div>
            </>
          ) : (
            <p className='text-sm text-muted-foreground'>
              No synthesized steps to display.
            </p>
          )}
        </div>
      )}

      {/* Action buttons overlapping the card border */}
      <div className='absolute bottom-0 right-0 flex translate-y-3/4 transform items-center rounded-full border bg-background opacity-0 shadow-sm transition-opacity duration-200 group-hover:opacity-100'>
        <Button
          onClick={handleLike}
          size='sm'
          variant='ghost'
          className={cn(
            'h-7 w-7 rounded-full p-0',
            localLiked && 'text-green-500'
          )}
          title='Like'
          disabled={isLoading}
        >
          <ThumbsUp className='h-3.5 w-3.5' />
        </Button>
        <Button
          onClick={handleDislike}
          size='sm'
          variant='ghost'
          className={cn(
            'h-7 w-7 rounded-full p-0',
            localDisliked && 'text-red-500'
          )}
          title='Dislike'
          disabled={isLoading}
        >
          <ThumbsDown className='h-3.5 w-3.5' />
        </Button>
        <Button
          onClick={handleSave}
          size='sm'
          variant='ghost'
          className={cn(
            'h-7 w-7 rounded-full p-0',
            localSaved && 'text-blue-500'
          )}
          title='Save'
          disabled={isLoading}
        >
          {localSaved ? (
            <BookmarkCheck className='h-3.5 w-3.5' />
          ) : (
            <Bookmark className='h-3.5 w-3.5' />
          )}
        </Button>
      </div>
    </div>
  );
};

export default SynthesizedStepsCard;
