import { Program } from 'types';

interface ProgramCardProps {
  program: Program;
  onViewDetails: (program: Program) => void;
}

export function ProgramCard({ program, onViewDetails }: ProgramCardProps) {
  return (
    <div
      className='group relative cursor-pointer overflow-hidden rounded-lg border border-border/50 bg-card p-4 shadow-md transition-all hover:translate-y-[-2px] hover:border-primary/20 hover:shadow-lg'
      onClick={() => onViewDetails(program)}
    >
      <div className='absolute right-2 top-2 z-10'>
        <span className='inline-flex items-center rounded-full bg-primary/10 px-2 py-1 text-xs font-medium text-primary'>
          {program.relevance_score?.toFixed() || 'N/A'}
        </span>
      </div>

      <div className='relative z-10 space-y-3'>
        <h4 className='line-clamp-2 text-sm font-medium'>{program.name}</h4>

        {program.target_objective_text && (
          <p className='line-clamp-2 text-xs text-muted-foreground'>
            {program.target_objective_text}
          </p>
        )}
      </div>
    </div>
  );
}
