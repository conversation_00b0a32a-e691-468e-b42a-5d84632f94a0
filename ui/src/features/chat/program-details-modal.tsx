import { Program } from 'types';
import { PlayCircle } from 'lucide-react';
import { useRouter } from 'next/navigation';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';

interface ProgramDetailsModalProps {
  program: Program | null;
  isOpen: boolean;
  onClose: () => void;
}

export function ProgramDetailsModal({
  program,
  isOpen,
  onClose
}: ProgramDetailsModalProps) {
  const router = useRouter();

  const handleOpenInEditor = () => {
    if (program) {
      router.push(`/dashboard/program-editor/?id=${program.id}`);
      onClose();
    }
  };

  if (!program) return null;

  // Access programme_sequence directly from program object using type assertion
  const programmeSequence = (program as any).programme_sequence || {};

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className='slim-scrollbar max-h-[80vh] overflow-y-auto sm:max-w-4xl'>
        <DialogHeader className='pb-2'>
          <DialogTitle className='pr-10 text-xl'>{program.name}</DialogTitle>
          {program.program_title && program.program_title !== program.name && (
            <p className='text-sm text-muted-foreground'>
              {program.program_title}
            </p>
          )}
        </DialogHeader>

        <div className='space-y-5'>
          {/* Header with program info and match score */}
          <div className='flex items-center justify-between'>
            <div className='flex items-center gap-2'>
              <div className='flex h-10 w-10 items-center justify-center rounded-full bg-primary/10'>
                <PlayCircle className='h-5 w-5 text-primary' />
              </div>
              <div className='text-xs text-muted-foreground'>
                Program ID: {program.id}
              </div>
            </div>

            {program.relevance_score !== undefined && (
              <Badge className='bg-primary/10'>
                Match Score: {program.relevance_score}%
              </Badge>
            )}
          </div>

          {/* Target Objective for context */}
          {program.target_objective_text && (
            <div className='rounded-lg border border-dashed bg-muted/20 p-4'>
              <h4 className='mb-2 text-sm font-medium'>Target Objective</h4>
              <p className='text-sm italic text-muted-foreground'>
                {program.target_objective_text}
              </p>
            </div>
          )}

          {/* Programme Sequence Section - Direct from program object */}
          {Object.keys(programmeSequence).length > 0 && (
            <div className='rounded-lg border bg-card p-5'>
              <h3 className='mb-4 text-base font-medium'>Programme Sequence</h3>
              <div className='space-y-4'>
                {Object.entries(programmeSequence).map(([key, value]) => (
                  <div key={key} className='space-y-1.5'>
                    <h4 className='text-sm font-medium text-primary/80'>
                      {key}
                    </h4>
                    <div className='rounded-md bg-muted/50 p-3 text-sm leading-relaxed'>
                      {String(value)}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Acupressure Points Section */}
          {program.targeted_acupressure_points &&
            program.targeted_acupressure_points.length > 0 && (
              <div className='rounded-lg border bg-card p-5'>
                <h3 className='mb-4 text-base font-medium'>
                  Acupressure Points
                </h3>
                <div className='grid gap-3 md:grid-cols-3'>
                  {program.targeted_acupressure_points.map((point, index) => (
                    <div key={index} className='flex items-start gap-2.5'>
                      <span className='mt-0.5 inline-flex h-6 w-6 flex-shrink-0 items-center justify-center rounded-full bg-primary/10 text-xs font-medium text-primary'>
                        {index + 1}
                      </span>
                      <span className='text-sm leading-relaxed text-muted-foreground'>
                        {point}
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            )}

          {/* Signature Moves Section */}
          {program.signature_moves && program.signature_moves.length > 0 && (
            <div className='rounded-lg border bg-card p-5'>
              <h3 className='mb-4 text-base font-medium'>Signature Moves</h3>
              <div className='grid gap-3 md:grid-cols-2'>
                {program.signature_moves.map((move, index) => (
                  <div key={index} className='flex items-start gap-2.5'>
                    <span className='mt-0.5 inline-flex h-6 w-6 flex-shrink-0 items-center justify-center rounded-full bg-primary/10 text-xs font-medium text-primary'>
                      {index + 1}
                    </span>
                    <span className='text-sm leading-relaxed text-muted-foreground'>
                      {move}
                    </span>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>

        <DialogFooter className='mt-4 border-t pt-4'>
          <Button variant='outline' onClick={onClose} className='mr-2'>
            Close
          </Button>
          <Button onClick={handleOpenInEditor}>Open in Editor</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
