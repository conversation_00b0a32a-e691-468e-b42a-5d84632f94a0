import React from 'react';
import {
  Accordion,
  AccordionItem,
  AccordionTrigger,
  AccordionContent
} from '@/components/ui/accordion';
import { FileText } from 'lucide-react';
import { Document } from 'types';

interface DocumentAccordionProps {
  documents: Document[];
}

export const DocumentAccordion: React.FC<DocumentAccordionProps> = ({
  documents
}) => (
  <div className='mt-3 space-y-2 border-t pt-2 text-xs'>
    <Accordion type='single' collapsible>
      <AccordionItem value='references'>
        <AccordionTrigger className='p-1 text-sm text-secondary-foreground'>
          References
        </AccordionTrigger>
        <AccordionContent>
          <div className='space-y-2'>
            {documents.map((doc, index) => (
              <div
                key={index}
                className='rounded-lg border border-border/50 bg-card p-3 transition-colors'
              >
                <div className='mb-2 flex items-center gap-2'>
                  <FileText className='h-4 w-4 text-muted-foreground' />
                  <p className='text-sm font-medium'>
                    {doc.metadata.file_name}
                  </p>
                </div>
                <div className='border-l-2 border-border/50 pl-6'>
                  <p
                    className='break-words leading-relaxed text-muted-foreground [&>*:last-child]:mb-0 [&>*]:mb-1 [&_a]:text-primary [&_a]:underline [&_em]:italic [&_li]:ml-4 [&_ol]:list-decimal [&_strong]:font-semibold [&_ul]:list-disc'
                    dangerouslySetInnerHTML={{
                      __html: doc.page_content
                    }}
                  />
                </div>
              </div>
            ))}
          </div>
        </AccordionContent>
      </AccordionItem>
    </Accordion>
  </div>
);

export default DocumentAccordion;
