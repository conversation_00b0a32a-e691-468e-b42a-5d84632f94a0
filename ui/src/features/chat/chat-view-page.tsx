'use client';
import PageContainer from '@/components/layout/page-container';
import { Heading } from '@/components/ui/heading';
import ChatContainer from './chat-container';
import ChatConfig from './chat-config';
import { Drawer, DrawerContent, DrawerTrigger } from '@/components/ui/drawer';
import { Button } from '@/components/ui/button';
import { DialogTitle } from '@/components/ui/dialog';
import { LucideSettings, PanelLeftClose, PanelRightClose } from 'lucide-react';
import { useAuthStore } from '@/stores/auth.store';
import { useSearchParams } from 'next/navigation';
import React from 'react';
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger
} from '@/components/ui/collapsible';

export default function ChatViewPage() {
  const [isConfigOpen, setIsConfigOpen] = React.useState(false);
  const drawerWidth = 'w-[400px] max-w-[400px]';
  const user = useAuthStore((state) => state.user);
  const searchParams = useSearchParams();
  const configId = searchParams.get('config_id');

  return (
    <PageContainer>
      <div className='flex h-[calc(100vh-100px)] w-full flex-col gap-4'>
        <div className='flex items-start justify-between'>
          {/* <Heading title='Chat' description='Chat with AI' /> */}
          <div className='block lg:hidden'>
            <Drawer direction='right'>
              <DrawerTrigger asChild>
                <Button variant='outline' size='icon'>
                  <LucideSettings />
                </Button>
              </DrawerTrigger>
              <DrawerContent
                className={`fixed inset-x-[unset] right-0 top-0 mt-0 h-full ${drawerWidth} w-auto overflow-y-auto rounded-none border-l bg-background`}
              >
                <DialogTitle className='p-5'>Chat Configure</DialogTitle>
                <div className='max-h-[calc(100vh-80px)] overflow-y-auto'>
                  <ChatConfig />
                </div>
              </DrawerContent>
            </Drawer>
          </div>
        </div>

        <div className='h-full'>
          <div className='h-full gap-2 lg:flex'>
            {(user?.isAdmin || user?.isAIEngineer) && (
              <Collapsible
                open={isConfigOpen}
                onOpenChange={setIsConfigOpen}
                className='hidden lg:block'
              >
                <div className='flex items-center justify-end py-2'>
                  <CollapsibleTrigger asChild>
                    <Button
                      variant='outline'
                      size='icon'
                      className='shadow-sm transition-all hover:shadow'
                    >
                      {isConfigOpen ? (
                        <PanelLeftClose className='h-4 w-4' />
                      ) : (
                        <PanelRightClose className='h-4 w-4' />
                      )}
                      <span className='sr-only'>Toggle Chat Configuration</span>
                    </Button>
                  </CollapsibleTrigger>
                </div>
                <CollapsibleContent className='rounded-lg shadow-md'>
                  <ChatConfig />
                </CollapsibleContent>
              </Collapsible>
            )}
            <div
              className={`h-full ${
                isConfigOpen && (user?.isAdmin || user?.isAIEngineer)
                  ? 'lg:w-[calc(100%-400px-0.5rem)]' // Assuming ChatConfig is 400px wide + gap
                  : 'lg:w-full'
              } transition-all duration-300 ease-in-out`}
            >
              {configId ? (
                <ChatContainer />
              ) : (
                <div className='flex h-full items-center justify-center rounded-xl border bg-card p-6 text-card-foreground'>
                  <p className='text-muted-foreground'>
                    Please select or create a chat configuration to start
                    chatting
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </PageContainer>
  );
}
