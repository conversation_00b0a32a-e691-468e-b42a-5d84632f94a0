import React, { useState } from 'react';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { TargetSequence } from 'types';
import { ChevronDown, ChevronUp, Target } from 'lucide-react';

interface TargetSequenceCardProps {
  target_sequence: TargetSequence;
}

export const TargetSequenceCard: React.FC<TargetSequenceCardProps> = ({
  target_sequence
}) => {
  const [expanded, setExpanded] = useState(false);

  return (
    <div className='space-y-4'>
      <div
        className='flex cursor-pointer items-center py-1'
        onClick={() => setExpanded(!expanded)}
      >
        <div className='mr-2.5 flex h-8 w-8 items-center justify-center rounded-full bg-slate-600 shadow-sm'>
          <Target className='h-4 w-4 text-white' strokeWidth={2.5} />
        </div>
        <div className='flex-1'>
          <h3 className='mb-0.5 text-sm font-medium leading-tight'>
            Generated Massage Sequence
          </h3>
          {!expanded && (
            <p className='text-xs leading-tight text-muted-foreground'>
              {target_sequence.objective.substring(0, 60)}...
            </p>
          )}
        </div>
        <div className='ml-2 flex h-8 w-8 items-center justify-center'>
          {expanded ? (
            <ChevronUp className='h-4 w-4 text-slate-600' strokeWidth={2} />
          ) : (
            <ChevronDown className='h-4 w-4 text-slate-600' strokeWidth={2} />
          )}
        </div>
      </div>

      {expanded && (
        <div className='space-y-4 border-l border-primary/25 pl-4'>
          <div className='space-y-2'>
            <p className='text-xs font-medium text-muted-foreground'>
              Objective
            </p>
            <p className='text-sm'>{target_sequence.objective}</p>
          </div>

          <Separator className='my-3' />

          <div className='space-y-2'>
            <p className='text-xs font-medium text-muted-foreground'>
              Programme Sequence
            </p>
            <div className='space-y-2 text-sm'>
              <p>
                <span className='font-semibold'>Front:</span>{' '}
                {target_sequence.programme_sequence.front_sequence || 'N/A'}
              </p>
              <p>
                <span className='font-semibold'>Main:</span>{' '}
                {target_sequence.programme_sequence.main_sequence || 'N/A'}
              </p>
              <p>
                <span className='font-semibold'>Cooling:</span>{' '}
                {target_sequence.programme_sequence.cooling_sequence || 'N/A'}
              </p>
            </div>
          </div>

          {target_sequence.targeted_acupressure_points &&
            target_sequence.targeted_acupressure_points.length > 0 && (
              <>
                <Separator className='my-3' />
                <div className='space-y-2'>
                  <p className='text-xs font-medium text-muted-foreground'>
                    Targeted Acupressure Points
                  </p>
                  <div className='flex flex-wrap gap-1'>
                    {target_sequence.targeted_acupressure_points.map(
                      (point, i) => (
                        <Badge key={i} variant='secondary' className='text-xs'>
                          {point}
                        </Badge>
                      )
                    )}
                  </div>
                </div>
              </>
            )}

          <Separator className='my-3' />

          <div className='space-y-2'>
            <p className='text-xs font-medium text-muted-foreground'>
              Signature Moves
            </p>
            <div className='flex flex-wrap gap-1'>
              {target_sequence.signature_moves.map((move, i) => (
                <Badge key={i} variant='secondary' className='text-xs'>
                  {move}
                </Badge>
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default TargetSequenceCard;
