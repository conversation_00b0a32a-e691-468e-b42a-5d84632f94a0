import React, { useState } from 'react';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { MassageProgramDetails } from 'types';
import { ChevronDown, ChevronUp, Settings, Check, X } from 'lucide-react';

interface DetailItemProps {
  label: string;
  value: any;
  showStatus?: boolean;
  isBooleanField?: boolean;
  isEnumField?: boolean;
  fieldType?: 'duration' | 'intensity' | 'warm_air' | 'vibration' | 'air_bags' | 'default';
}

const DetailItem: React.FC<DetailItemProps> = ({
  label,
  value,
  showStatus = true,
  isBooleanField = false,
  isEnumField = false,
  fieldType = 'default',
}) => {
  const isGiven =
    value !== null &&
    value !== undefined &&
    value !== '' &&
    !(Array.isArray(value) && value.length === 0) &&
    !(typeof value === 'object' && Object.keys(value).length === 0);

  const renderValueContent = (): string | React.ReactNode => {
    // If DetailSection already determined the display (e.g., an <X/> icon for missing Program Flow item)
    if (React.isValidElement(value)) {
      return value;
    }

    // Handle enum fields with special formatting
    if (isEnumField && value) {
      switch (fieldType) {
        case 'duration':
          return `${value} minutes`;
        case 'intensity':
          return (
            <Badge
              variant={value === 'strong' ? 'destructive' : value === 'medium' ? 'default' : 'secondary'}
              className="text-xs"
            >
              {value}
            </Badge>
          );
        case 'warm_air':
          if (value === 'Off') {
            return <span className="text-muted-foreground">Off</span>;
          }
          return (
            <Badge variant="outline" className="text-xs">
              {value}
            </Badge>
          );
        case 'vibration':
          if (value === 'Off') {
            return <span className="text-muted-foreground">Off</span>;
          }
          return (
            <Badge variant="outline" className="text-xs">
              {value}
            </Badge>
          );
        case 'air_bags':
          return (
            <Badge variant="outline" className="text-xs">
              {value}
            </Badge>
          );
        default:
          return String(value);
      }
    }

    // For Massage Settings boolean items
    if (isBooleanField) {
      return value === true ? (
        <Check className='h-4 w-4 text-green-500' />
      ) : (
        <X className='h-4 w-4 text-red-500' /> // Handles false, null, undefined
      );
    }

    // For Basic Preferences, or Program Flow items where DetailSection passed actual data.
    const effectivelyGiven =
      value !== null &&
      value !== undefined &&
      value !== '' &&
      !(Array.isArray(value) && value.length === 0);

    if (effectivelyGiven) {
      if (Array.isArray(value)) return value.join(', ');
      return String(value);
    } else {
      // This branch is primarily for Basic Preferences when value is missing.
      // For Program Flow, DetailSection passes an <X/> icon if data is missing,
      // so 'effectivelyGiven' would be true for the icon itself if we didn't check React.isValidElement first.
      return '-';
    }
  };

  const valueOutput = renderValueContent();
  const isValueString = typeof valueOutput === 'string';
  const shouldShowTooltip = isValueString && (valueOutput as string).length > 30;

  return (
    <div className='flex items-start justify-between gap-1 py-0.5'>
      <span className='flex-shrink-0 text-xs font-medium text-muted-foreground min-w-0'>
        {label}
      </span>
      
      <div className='flex items-center gap-1 flex-shrink-0'>
        {isValueString ? (
          shouldShowTooltip ? (
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <span className='text-xs text-right max-w-[200px] truncate cursor-help'>
                    {valueOutput}
                  </span>
                </TooltipTrigger>
                <TooltipContent>
                  <p className='max-w-xs break-words'>{valueOutput}</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          ) : (
            <span className='text-xs text-right max-w-[200px] truncate'>
              {valueOutput}
            </span>
          )
        ) : (
          valueOutput // Render JSX.Element (icon) directly
        )}
        {showStatus && (
          isGiven ? (
            <Check className='h-3 w-3 text-green-500 flex-shrink-0' />
          ) : (
            <X className='h-3 w-3 text-red-500 flex-shrink-0' />
          )
        )}
      </div>
    </div>
  );
};

interface DetailSectionProps {
  title: string;
  data?: Record<string, any> | null;
}

const DetailSection: React.FC<DetailSectionProps> = ({ title, data }) => {
  // Define a more presentable order or mapping for labels if needed
  const labelMap: Record<string, string> = {
    highlights: 'Highlights',
    signature_moves: 'Signature Moves',
    logic_techniques: 'Logic/Techniques',
    intensity: 'Intensity',
    warm_air: 'Warm Air',
    vibration: 'Vibration',
    air_bags: 'Air Bags',
    scent: 'Scent',
    mood_light: 'Mood Light',
  };

  const programFlowKeys = [
    'highlights',
    'signature_moves',
    'logic_techniques',
    'intensity',
  ];

  // Updated to handle new enum types
  const massageSettingKeys = [
    'warm_air',
    'vibration',
    'air_bags',
    'scent',
    'mood_light',
  ];

  // Define which fields are boolean vs enum
  const booleanFields = ['scent', 'mood_light'];
  const enumFields = ['warm_air', 'vibration', 'air_bags'];

  let keysToRender: string[] = [];
  if (title === 'Program Flow') {
    keysToRender = programFlowKeys;
  } else if (title === 'Massage Settings') {
    keysToRender = massageSettingKeys;
  }

  return (
    <div className='space-y-1'>
      <div className='flex items-center gap-2'>
        <p className='text-xs font-medium text-muted-foreground flex-shrink-0'>
          {title}
        </p>
        <Badge variant="secondary" className="text-[10px] px-1.5 py-0">
          Optional
        </Badge>
      </div>

      <div className='space-y-1'>
        {keysToRender.map((key) => {
          const actualValue = data?.[key];
          let itemValue: any = actualValue;
          let isBooleanFieldForItem = false;
          let isEnumFieldForItem = false;
          let fieldType: 'duration' | 'intensity' | 'warm_air' | 'vibration' | 'air_bags' | 'default' = 'default';

          if (title === 'Massage Settings') {
            if (booleanFields.includes(key)) {
              isBooleanFieldForItem = true;
            } else if (enumFields.includes(key)) {
              isEnumFieldForItem = true;
              fieldType = key as 'warm_air' | 'vibration' | 'air_bags';
            }
          } else if (title === 'Program Flow') {
            if (key === 'intensity') {
              isEnumFieldForItem = true;
              fieldType = 'intensity';
            }

            const isProgramFlowItemGiven =
              actualValue !== null &&
              actualValue !== undefined &&
              actualValue !== '' &&
              !(Array.isArray(actualValue) && actualValue.length === 0);
            if (!isProgramFlowItemGiven && key !== 'intensity') {
              itemValue = <X className='h-4 w-4 text-red-500' />;
            }
          }

          return (
            <DetailItem
              key={key}
              label={labelMap[key] || key.replace(/_/g, ' ').replace(/\b\w/g, (l) => l.toUpperCase())}
              value={itemValue}
              showStatus={false}
              isBooleanField={isBooleanFieldForItem}
              isEnumField={isEnumFieldForItem}
              fieldType={fieldType}
            />
          );
        })}
      </div>
    </div>
  );
};

interface MassageProgramDetailsCardProps {
  details: MassageProgramDetails;
}

const MassageProgramDetailsCard: React.FC<MassageProgramDetailsCardProps> = ({
  details
}) => {
  const [expanded, setExpanded] = useState(false);

  // Count available preferences - count individual fields, not just categories
  const getAvailablePreferencesCount = () => {
    let count = 0;

    // Basic Preferences (4 fields) - all are now required with defaults
    count += 4; // target_market, duration, focus_area, problem_desired_outcome are always present

    // Program Flow (4 fields) - optional
    if (details.program_flow?.highlights) count++;
    if (details.program_flow?.signature_moves) count++;
    if (details.program_flow?.logic_techniques) count++;
    if (details.program_flow?.intensity) count++;

    // Massage Settings (5 fields) - count non-default values
    // Warm Air: count if not "Off" (default)
    if (details.massage_settings?.warm_air && details.massage_settings.warm_air !== 'Off') count++;
    // Vibration: count if not "Auto" (default)
    if (details.massage_settings?.vibration && details.massage_settings.vibration !== 'Auto') count++;
    // Air Bags: count if specified
    if (details.massage_settings?.air_bags) count++;
    // Scent: count if not true (default)
    if (details.massage_settings?.scent === false) count++;
    // Mood Light: count if not true (default)
    if (details.massage_settings?.mood_light === false) count++;

    return count;
  };

  const availableCount = getAvailablePreferencesCount();
  const totalCount = 13; // Total individual fields: 4 basic + 4 program flow + 5 massage settings

  // Get a preview of the key preferences
  const getPreferencesSummary = (): string => {
    const parts = [];

    // Duration is always present now
    parts.push(`${details.duration} min`);

    // Focus area - show first area if not default
    if (details.focus_area && details.focus_area.length > 0 &&
        !(details.focus_area.length === 1 && details.focus_area[0] === 'Full body')) {
      parts.push(details.focus_area[0]);
    }

    // Target market if not default
    if (details.target_market && details.target_market !== '18 to 65') {
      parts.push(details.target_market);
    }

    return parts.slice(0, 2).join(', ') + (parts.length > 2 ? '...' : '');
  };

  const summaryText = getPreferencesSummary();

  return (
    <div className='space-y-4'>
      <div
        className='flex cursor-pointer items-center py-0.5'
        onClick={() => setExpanded(!expanded)}
      >
        <div className='mr-2.5 flex h-8 w-8 items-center justify-center rounded-full bg-slate-600 shadow-sm'>
          <Settings className='h-4 w-4 text-white' strokeWidth={2.5} />
        </div>
        <div className='flex-1 min-w-0'>
          <h3 className='mb-0.5 text-sm font-medium leading-tight'>
            Extracted Massage Preferences
          </h3>
          {!expanded && (
            <div className='mt-0.5 flex flex-col gap-0.5'>
              <div className='flex items-center gap-2 text-xs leading-tight text-muted-foreground'>
                <span>{availableCount}/{totalCount} preferences extracted</span>
                <Badge variant='outline' className='h-4 px-1.5 py-0 text-[10px]'>
                  {Math.round((availableCount / totalCount) * 100)}% complete
                </Badge>
              </div>
              {summaryText && (
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <p className='line-clamp-1 text-[11px] text-muted-foreground cursor-help'>
                        {summaryText}
                      </p>
                    </TooltipTrigger>
                    <TooltipContent>
                      <div className='max-w-xs'>
                        <p className='font-medium mb-1'>Full Preferences Summary:</p>
                        <p>• Target: {details.target_market}</p>
                        <p>• Duration: {details.duration} minutes</p>
                        <p>• Focus: {details.focus_area.join(', ')}</p>
                        <p>• Goal: {details.problem_desired_outcome}</p>
                        {details.program_flow?.intensity && (
                          <p>• Intensity: {details.program_flow.intensity}</p>
                        )}
                      </div>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              )}
            </div>
          )}
        </div>
        <div className='ml-2 flex h-8 w-8 items-center justify-center flex-shrink-0'>
          {expanded ? (
            <ChevronUp className='h-4 w-4 text-slate-600' strokeWidth={2} />
          ) : (
            <ChevronDown className='h-4 w-4 text-slate-600' strokeWidth={2} />
          )}
        </div>
      </div>

      {expanded && (
        <div className='space-y-4 border-l border-primary/25 pl-4'>
          <div className='space-y-2'>
            <div className='flex items-center gap-2'>
              <p className='text-xs font-medium text-muted-foreground'>
                Basic Preferences
              </p>
              <Badge variant="outline" className="text-[10px] px-1.5 py-0">
                Required
              </Badge>
            </div>
            <div className='space-y-1'>
              <DetailItem
                label='Target Market'
                value={details.target_market}
                showStatus={false}
              />
              <DetailItem
                label='Duration'
                value={details.duration}
                isEnumField={true}
                fieldType='duration'
                showStatus={false}
              />
              <DetailItem
                label='Focus Area(s)'
                value={details.focus_area}
                showStatus={false}
              />
              <DetailItem
                label='Problem / Desired Outcome'
                value={details.problem_desired_outcome}
                showStatus={false}
              />
            </div>
          </div>

          <Separator className='my-3' />

          <DetailSection title='Program Flow' data={details.program_flow} />

          <Separator className='my-3' />

          <DetailSection title='Massage Settings' data={details.massage_settings} />
        </div>
      )}
    </div>
  );
};

export default MassageProgramDetailsCard;