import { ProgramPlan } from 'types';
import { ChevronDown, ChevronUp, FileText, Target, Zap } from 'lucide-react';
import { useState } from 'react';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';

interface ProgramPlanCardProps {
  program_plan: ProgramPlan;
}

export function ProgramPlanCard({ program_plan }: ProgramPlanCardProps) {
  const [expanded, setExpanded] = useState(false);

  // Defensive check for undefined data
  if (!program_plan) {
    return (
      <div className='text-center py-4 text-muted-foreground text-sm'>
        No program plan data available
      </div>
    );
  }

  const formatPhaseInstruction = (phase: any) => {
    if (!phase) return 'No instructions available';

    const parts = [];
    if (phase.body_parts?.length) {
      parts.push(`Body: ${phase.body_parts.join(', ')}`);
    }
    if (phase.techniques?.length) {
      parts.push(`Techniques: ${phase.techniques.join(', ')}`);
    }
    if (phase.purposes?.length) {
      parts.push(`Purpose: ${phase.purposes.join(', ')}`);
    }
    if (phase.intensity_range) {
      parts.push(`Intensity: ${phase.intensity_range}`);
    }
    return parts.length > 0 ? parts.join(' • ') : 'No specific instructions';
  };

  return (
    <div className='space-y-4'>
      <div
        className='flex cursor-pointer items-center py-1'
        onClick={() => setExpanded(!expanded)}
      >
        <div className='mr-2.5 flex h-8 w-8 items-center justify-center rounded-full bg-slate-600 shadow-sm'>
          <FileText className='h-4 w-4 text-white' strokeWidth={2.5} />
        </div>
        <div className='flex-1'>
          <h3 className='mb-0.5 text-sm font-medium leading-tight'>
            Program Strategy Plan
          </h3>
          {!expanded && (
            <p className='text-xs leading-tight text-muted-foreground'>
              {(program_plan.program_strategy || 'No strategy available').substring(0, 80)}...
            </p>
          )}
        </div>
        <div className='ml-2 flex h-8 w-8 items-center justify-center'>
          {expanded ? (
            <ChevronUp className='h-4 w-4 text-slate-600' strokeWidth={2} />
          ) : (
            <ChevronDown className='h-4 w-4 text-slate-600' strokeWidth={2} />
          )}
        </div>
      </div>

      {expanded && (
        <div className='space-y-4 border-l border-primary/25 pl-4'>
          {/* Overall Strategy */}
          <div className='space-y-2'>
            <div className='flex items-center gap-2'>
              <Target className='h-4 w-4 text-primary' />
              <p className='text-xs font-medium text-muted-foreground'>
                Overall Strategy
              </p>
            </div>
            <p className='text-sm text-foreground'>
              {program_plan.program_strategy || 'No strategy available'}
            </p>
          </div>

          {/* Overall Intensity */}
          {program_plan.overall_intensity && (
            <div className='space-y-2'>
              <div className='flex items-center gap-2'>
                <Zap className='h-4 w-4 text-primary' />
                <p className='text-xs font-medium text-muted-foreground'>
                  Overall Intensity
                </p>
              </div>
              <Badge variant='outline' className='text-xs'>
                {program_plan.overall_intensity}
              </Badge>
            </div>
          )}

          {/* Focus Priorities */}
          {program_plan.focus_priorities?.length && (
            <div className='space-y-2'>
              <p className='text-xs font-medium text-muted-foreground'>
                Focus Priorities
              </p>
              <div className='flex flex-wrap gap-1'>
                {program_plan.focus_priorities.map((priority, index) => (
                  <Badge
                    key={index}
                    variant='secondary'
                    className='text-[10px] px-1.5 py-0.5'
                  >
                    {priority}
                  </Badge>
                ))}
              </div>
            </div>
          )}

          <Separator className='my-3' />

          {/* Phase Instructions */}
          <div className='space-y-3'>
            <p className='text-xs font-medium text-muted-foreground'>
              Phase Search Instructions
            </p>
            
            {/* Front Phase */}
            <div className='space-y-1'>
              <p className='text-xs font-medium text-foreground'>Front Phase</p>
              <p className='text-xs text-muted-foreground'>
                {formatPhaseInstruction(program_plan.front_phase) || 'No specific instructions'}
              </p>
            </div>

            {/* Main Phase */}
            <div className='space-y-1'>
              <p className='text-xs font-medium text-foreground'>Main Phase</p>
              <p className='text-xs text-muted-foreground'>
                {formatPhaseInstruction(program_plan.main_phase) || 'No specific instructions'}
              </p>
            </div>

            {/* Cooling Phase */}
            <div className='space-y-1'>
              <p className='text-xs font-medium text-foreground'>Cooling Phase</p>
              <p className='text-xs text-muted-foreground'>
                {formatPhaseInstruction(program_plan.cooling_phase) || 'No specific instructions'}
              </p>
            </div>
          </div>

          {/* Avoid Characteristics */}
          {program_plan.avoid_characteristics?.length && (
            <>
              <Separator className='my-3' />
              <div className='space-y-2'>
                <p className='text-xs font-medium text-muted-foreground'>
                  Avoid Characteristics
                </p>
                <div className='flex flex-wrap gap-1'>
                  {program_plan.avoid_characteristics.map((characteristic, index) => (
                    <Badge
                      key={index}
                      variant='destructive'
                      className='text-[10px] px-1.5 py-0.5'
                    >
                      {characteristic}
                    </Badge>
                  ))}
                </div>
              </div>
            </>
          )}

          {/* Special Requirements */}
          {program_plan.special_requirements && (
            <>
              <Separator className='my-3' />
              <div className='space-y-2'>
                <p className='text-xs font-medium text-muted-foreground'>
                  Special Requirements
                </p>
                <p className='text-sm text-foreground'>
                  {program_plan.special_requirements}
                </p>
              </div>
            </>
          )}
        </div>
      )}
    </div>
  );
}
