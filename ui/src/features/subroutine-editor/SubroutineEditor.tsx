'use client';
import React from 'react';
import { useSearchParams } from 'next/navigation';
import { useGetSubroutineById, useListSubroutines } from '@/queries/program';
import ProgramStepsTable from '@/features/program-editor/components/ProgramStepsTable';
import { Card, CardContent } from '@/components/ui/card';
import { Loader2 } from 'lucide-react';

// Define a detailed subroutine type for editor use
interface DetailedSubroutine {
  id: number;
  name: string;
  subroutineIdJson: string;
  product_id: number;
  steps: any[];
  column_mapping_used?: Record<string, any>;
  // Add any other fields as needed
}

const SubroutineEditor: React.FC = () => {
  const searchParams = useSearchParams();
  const subroutineId = searchParams.get('id')
    ? parseInt(searchParams.get('id')!)
    : null;

  // Fetch subroutine data
  const {
    data: subroutine,
    isLoading: isSubroutineLoading,
    error: subroutineError
  } = useGetSubroutineById(subroutineId || 0, {
    enabled: !!subroutineId
  });

  // Fetch all subroutines (for dropdowns in steps table)
  const {
    data: subroutineListResponse,
    isLoading: isSubroutineListLoading,
    error: subroutineListError
  } = useListSubroutines({ limit: 500 });

  // Prepare subroutines array for ProgramStepsTable
  const subroutines = subroutineListResponse?.subroutines || [];

  if (!subroutineId) {
    return (
      <div className='p-6'>
        <h2 className='mb-2 text-xl font-semibold'>Subroutine Editor</h2>
        <div className='text-red-500'>
          No subroutine ID provided in the URL.
        </div>
      </div>
    );
  }

  if (isSubroutineLoading || isSubroutineListLoading) {
    return (
      <div className='flex h-64 flex-col items-center justify-center'>
        <Loader2 className='mb-2 h-8 w-8 animate-spin' />
        <span>Loading subroutine...</span>
      </div>
    );
  }

  if (subroutineError || subroutineListError) {
    return (
      <div className='p-6'>
        <h2 className='mb-2 text-xl font-semibold'>Subroutine Editor</h2>
        <div className='text-red-500'>
          {subroutineError?.message ||
            subroutineListError?.message ||
            'Failed to load subroutine.'}
        </div>
      </div>
    );
  }

  if (!subroutine) {
    return (
      <div className='p-6'>
        <h2 className='mb-2 text-xl font-semibold'>Subroutine Editor</h2>
        <div className='text-red-500'>Subroutine not found.</div>
      </div>
    );
  }

  // Cast to detailed type for TS
  const detailedSubroutine = subroutine as unknown as DetailedSubroutine;

  return (
    <div className='p-6'>
      <Card>
        <CardContent className='p-6'>
          <h2 className='mb-4 text-2xl font-bold'>
            Subroutine: {detailedSubroutine.name}
          </h2>
          <ProgramStepsTable
            steps={detailedSubroutine.steps || []}
            columnMapping={detailedSubroutine.column_mapping_used || {}}
            subroutines={subroutines}
            onAddStep={() => {}}
            onRemoveStep={() => {}}
            onUpdateStep={() => {}}
            // Optionally, add onReorderSteps={() => {}} if needed
          />
        </CardContent>
      </Card>
    </div>
  );
};

export default SubroutineEditor;
