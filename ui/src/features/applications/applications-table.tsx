'use client';
import { But<PERSON> } from '@/components/ui/button';
import {
  LucideMessageCircleReply,
  PlusCircle,
  LucideTrash
} from 'lucide-react';
import { DataTable } from '@/components/ui/table/data-table';
import { ColumnDef } from '@tanstack/react-table';
import {
  useListApplications,
  useDeleteApplication
} from '@/queries/application';
import { Application } from 'types';
import { useRouter } from 'next/navigation';
import { useAuthStore } from '@/stores/auth.store';
import { Row } from '@tanstack/react-table';
import { useEffect, useState } from 'react';
import { toast } from 'sonner';
import DeleteConfirmationDialog from '@/components/ui/delete-confirmation-dialog';

export default function ApplicationsTable() {
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(20);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [applicationToDelete, setApplicationToDelete] =
    useState<Application | null>(null);

  const { data, isLoading, refetch } = useListApplications({
    skip: (page - 1) * pageSize,
    limit: pageSize
  });

  const { mutate: deleteApplication } = useDeleteApplication();

  const handleDeleteApplication = (appId: number) => {
    deleteApplication(
      { id: appId },
      {
        onSuccess: () => {
          toast.success('Application deleted successfully');
          refetch();
        },
        onError: (error) => {
          toast.error(`Error deleting application: ${error.message}`);
        }
      }
    );
  };

  const openDeleteDialog = (application: Application) => {
    setApplicationToDelete(application);
    setIsDeleteDialogOpen(true);
  };

  const applications = data?.chat_configs || [];
  const totalPages = data?.total ? Math.ceil(data.total / pageSize) : 0;

  const handlePageSizeChange = (newPageSize: number) => {
    setPageSize(newPageSize);
    setPage(1);
  };

  const user = useAuthStore((state) => state.user);
  const router = useRouter();

  useEffect(() => {
    refetch();
  }, [refetch]);

  const columns: ColumnDef<Application>[] = [
    {
      accessorKey: 'name',
      header: 'Name',
      cell: ({ row }) => (
        <div className='font-medium'>{row.getValue('name')}</div>
      )
    },
    {
      accessorKey: 'product_name',
      header: 'Product Name',
      cell: ({ row }) => (
        <div>{row.getValue('product_name')}</div>
      )
    },
    // Conditionally add model_name and source_name columns
    ...(user?.isAdmin || user?.isAIEngineer
      ? [
          {
            accessorKey: 'model_name',
            header: 'Model Name',
            cell: ({ row }: { row: Row<Application> }) => (
              <div>{row.getValue('model_name')}</div>
            )
          }
        ]
      : []),
    {
      id: 'actions',
      header: 'Actions',
      cell: ({ row }) => (
        <div className='flex gap-2'>
          <Button
            onClick={() =>
              router.push(`/dashboard/chat?config_id=${row.original.id}`)
            }
          >
            <span className='mr-2 text-sm'>Chat</span>
            <LucideMessageCircleReply className='h-4 w-4' />
          </Button>
          {(user?.isAdmin || user?.isAIEngineer) && (
            <DeleteConfirmationDialog
              open={
                isDeleteDialogOpen &&
                applicationToDelete?.id === row.original.id
              }
              onOpenChange={(open) => {
                setIsDeleteDialogOpen(open);
                if (!open) {
                  setApplicationToDelete(null);
                }
              }}
              onConfirm={() => {
                if (applicationToDelete) {
                  handleDeleteApplication(applicationToDelete.id);
                }
                setIsDeleteDialogOpen(false);
                setApplicationToDelete(null);
              }}
              itemName={`application "${row.original.name}"`}
              triggerComponent={
                <Button
                  size={'icon'}
                  variant='ghost'
                  onClick={() => openDeleteDialog(row.original)}
                >
                  <LucideTrash className='h-4 w-4' />
                </Button>
              }
              title='Delete Application?'
              description='This action cannot be undone. This will permanently delete the application and all associated chat data for the user.'
            />
          )}
        </div>
      ),
      enableSorting: false,
      enableHiding: false
    }
  ];

  return (
    <>
      <div className='mb-2 flex flex-wrap items-center justify-between gap-x-4 space-y-2'>
        <div>
          <h2 className='text-2xl font-bold tracking-tight'>Applications</h2>
          <p className='text-muted-foreground'>
            Here&apos;s a list of your apps
          </p>
        </div>
        {(user?.isAdmin || user?.isAIEngineer) && (
          <div className='flex gap-2'>
            <Button
              className='space-x-1'
              onClick={() => router.push('/dashboard/chat?mode=create')}
            >
              <span>Create</span> <PlusCircle size={18} />
            </Button>
          </div>
        )}
      </div>
      <div className='-mx-4 flex-1 overflow-auto px-4 py-1 lg:flex-row lg:space-x-12 lg:space-y-0'>
        <DataTable
          data={applications || []}
          columns={columns}
          pagination={{
            page,
            pageCount: totalPages,
            pageSize,
            onPageChange: setPage,
            onPageSizeChange: handlePageSizeChange
          }}
        />
      </div>
    </>
  );
}
