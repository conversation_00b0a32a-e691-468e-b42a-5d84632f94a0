import { type ClassValue, clsx } from 'clsx';
import { twMerge } from 'tailwind-merge';

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export function formatBytes(
  bytes: number,
  opts: {
    decimals?: number;
    sizeType?: 'accurate' | 'normal';
  } = {}
) {
  const { decimals = 0, sizeType = 'normal' } = opts;

  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
  const accurateSizes = ['Bytes', 'KiB', 'MiB', 'GiB', 'TiB'];
  if (bytes === 0) return '0 Byte';
  const i = Math.floor(Math.log(bytes) / Math.log(1024));
  return `${(bytes / Math.pow(1024, i)).toFixed(decimals)} ${
    sizeType === 'accurate'
      ? (accurateSizes[i] ?? 'Bytest')
      : (sizes[i] ?? 'Bytes')
  }`;
}


export function formatDateTime(dateString: string): string {
  const date = new Date(dateString);
  
  // Handle invalid dates
  if (isNaN(date.getTime())) {
    return 'Invalid date';
  }

  const formatter = new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: 'numeric',
    minute: 'numeric',
    hour12: true
  });

  return formatter.format(date);
}

/**
 * Constructs a complete image URL from a relative path
 * @param imagePath - The relative path to the image
 * @returns Full URL to the image or null if path is invalid
 */
export function getImageUrl(
  imagePath: string | null | undefined
): string | null {
  if (!imagePath) return null;

  // If imagePath is already an absolute URL (like an S3 pre-signed URL), return it directly.
  if (imagePath.startsWith('http://') || imagePath.startsWith('https://')) {
    return imagePath;
  }
  
  // Fallback for relative paths (if any are still used elsewhere)
  // Use NEXT_PUBLIC_API_URL for consistency or define a specific assets URL
  const baseUrl = process.env.NEXT_PUBLIC_API_URL?.replace('/api2', '') || '';
  
  // Ensure clean path joining
  return `${baseUrl.replace(/\/$/, '')}${imagePath.startsWith('/') ? '' : '/'}${imagePath}`;
}

/**
 * Program status utility functions
 */
export type ProgramStatus = 'Draft' | 'In Progress' | 'Ready for Testing' | 'Tested' | 'Verified';

export const statusOptions: ProgramStatus[] = ['Draft', 'In Progress', 'Ready for Testing', 'Tested', 'Verified'];

/**
 * Get the color class for a program status dot
 * @param status - The program status
 * @returns Tailwind CSS color class for the status dot
 */
export function getStatusDotColor(status: string | null | undefined): string {
  switch (status) {
    case 'Draft':
      return 'bg-gray-400';
    case 'In Progress':
      return 'bg-blue-500';
    case 'Ready for Testing':
      return 'bg-yellow-500';
    case 'Tested':
      return 'bg-orange-500';
    case 'Verified':
      return 'bg-green-500';
    default:
      return 'bg-gray-400';
  }
}

/**
 * Get the badge variant for a program status (for UI components that use badges)
 * @param status - The program status
 * @returns Badge variant string
 */
export function getStatusBadgeVariant(status: string | null | undefined): 'default' | 'secondary' | 'outline' {
  switch (status) {
    case 'Draft':
      return 'secondary';
    case 'In Progress':
      return 'default';
    case 'Ready for Testing':
      return 'outline';
    case 'Tested':
      return 'secondary';
    case 'Verified':
      return 'default';
    default:
      return 'secondary';
  }
}
