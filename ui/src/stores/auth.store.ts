import { create } from 'zustand'
import { persist } from 'zustand/middleware'

interface User {
  email: string
  username: string
  cognito_id: string
  isAdmin: boolean
  isAIEngineer: boolean
}

interface AuthState {
  user: User | null
  accessToken: string | null
  setUser: (user: User | null) => void
  setAccessToken: (token: string | null) => void
  reset: () => void
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set) => ({
      user: null,
      accessToken: null,
      setUser: (user) => set({ user }),
      setAccessToken: (accessToken) => set({ accessToken }),
      reset: () => set({ user: null, accessToken: null }),
    }),
    {
      name: 'auth-storage',
    }
  )
)