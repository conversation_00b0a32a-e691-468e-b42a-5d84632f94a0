import { create } from 'zustand'
import { SourceResponse } from 'types'

interface SourceState {
  sources: SourceResponse[]
  selectedSource: SourceResponse | null
  setSources: (sources: SourceResponse[]) => void
  addSource: (source: SourceResponse) => void
  removeSource: (sourceId: number) => void
  updateSource: (source: SourceResponse) => void
  setSelectedSource: (source: SourceResponse | null) => void
}

export const useSourceStore = create<SourceState>((set) => ({
  sources: [],
  selectedSource: null,

  setSources: (sources) => set({ sources }),

  addSource: (source) =>
    set((state) => ({
      sources: [...state.sources, source],
    })),

  removeSource: (sourceId) =>
    set((state) => ({
      sources: state.sources.filter((source) => source.id !== sourceId),
      selectedSource: state.selectedSource?.id === sourceId ? null : state.selectedSource,
    })),

  updateSource: (updatedSource) =>
    set((state) => ({
      sources: state.sources.map((source) =>
        source.id === updatedSource.id ? updatedSource : source
      ),
      selectedSource:
        state.selectedSource?.id === updatedSource.id
          ? updatedSource
          : state.selectedSource,
    })),

  setSelectedSource: (source) => set({ selectedSource: source }),
}))
