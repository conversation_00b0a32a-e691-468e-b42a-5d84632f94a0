import { NavItem } from 'types';

export type Product = {
  photo_url: string;
  name: string;
  description: string;
  created_at: string;
  price: number;
  id: number;
  category: string;
  updated_at: string;
};

//Info: The following data is used for the sidebar navigation and Cmd K bar.
export const navItems: NavItem[] = [
  {
    title: 'Dashboard',
    url: '/dashboard/overview',
    icon: 'dashboard',
    isActive: false,
    shortcut: ['d', 'd'],
    items: [] // Empty array as there are no child items for Dashboard
  },
  {
    title: 'User Management',
    url: '/dashboard/user-management',
    icon: 'usersettings',
    shortcut: ['u', 'm'],
    isActive: false,
    items: []
  },
  {
    title: 'Products',
    url: '/dashboard/product',
    icon: 'product',
    shortcut: ['p', 'p'],
    isActive: false,
    items: [] // No child items
  },
  // {
  //   title: 'Account',
  //   url: '#', // Placeholder as there is no direct link for the parent
  //   icon: 'billing',
  //   isActive: true,

  //   items: [
  //     {
  //       title: 'Profile',
  //       url: '/dashboard/profile',
  //       icon: 'userPen',
  //       shortcut: ['m', 'm']
  //     },
  //     {
  //       title: 'Login',
  //       shortcut: ['l', 'l'],
  //       url: '/',
  //       icon: 'login'
  //     }
  //   ]
  // },
  // {
  //   title: 'Kanban',
  //   url: '/dashboard/kanban',
  //   icon: 'kanban',
  //   shortcut: ['k', 'k'],
  //   isActive: false,
  //   items: [] // No child items
  // },
  {
    title: 'Data Sources',
    url: '/dashboard/data-sources',
    icon: 'database',
    shortcut: ['d', 's'],
    isActive: false,
    items: []
  },

  
  // {
  //   title: 'Chat Creation',
  //   url: '/dashboard/chat',
  //   icon: 'kanban',
  //   shortcut: ['k', 'c'],
  //   isActive: false,
  //   items: []
  // },
  {
    title: 'Applications',
    url: '/dashboard/applications',
    icon: 'bot',
    shortcut: ['a', 'p'],
    isActive: false,
    items: []
  },
  {
    title: 'Chat History',
    url: '/dashboard/history',
    icon: 'message',
    shortcut: ['c', 'h'],
    isActive: false,
    items: []
  },
  {
    title: 'Program Segments',
    url: '/dashboard/segments',
    icon: 'layers',
    shortcut: ['p', 's'],
    isActive: false,
    items: []
  },
  {
    title: 'Program Analysis',
    url: '/dashboard/program-analysis',
    icon: 'activity',
    shortcut: ['p', 'a'],
    isActive: false,
    items: []
  }
];


export const ROUTES = {
  DASHBOARD: '/dashboard',
  OVERVIEW: '/dashboard/overview',
  DATA_SOURCES: '/dashboard/data-sources',
  USER_MANAGEMENT: '/dashboard/user-management',
  APPLICATIONS: '/dashboard/applications',
  CHAT: '/dashboard/chat',
  CHAT_HISTORY: '/dashboard/history',
  PRODUCTS: '/dashboard/products',
  SEGMENTS: '/dashboard/segments',
  PROGRAM_ANALYSIS: '/dashboard/program-analysis',
};


export const ROLE_NAV_ACCESS = {
  ADMIN: ['*'], // '*' means access to all routes
  AI_ENGINEER: [
    ROUTES.OVERVIEW,
    ROUTES.APPLICATIONS,
    ROUTES.CHAT,
    ROUTES.CHAT_HISTORY
  ],
  USER: [
    ROUTES.APPLICATIONS,
    ROUTES.CHAT,
    ROUTES.CHAT_HISTORY
  ]
} as const;

// Helper function to filter nav items based on allowed routes
export const getNavItemsByRole = (
  items: NavItem[],
  role?: 'ADMIN' | 'AI_ENGINEER' | 'USER'
): NavItem[] => {
  if (!role || role === 'ADMIN') return items;

  const allowedRoutes = ROLE_NAV_ACCESS[role];
  return items.filter(item => allowedRoutes.includes(item.url));
};