import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { fetchAuthSession, getCurrentUser, fetchUserAttributes } from 'aws-amplify/auth'
import { useAuthStore } from '@/stores/auth.store'
import { userService } from '@/services/user.service'
import { useAuthenticator } from '@aws-amplify/ui-react';

export const useUser = () => {
  
  const queryClient = useQueryClient()
  const { setUser, setAccessToken, user: storedUser } = useAuthStore()  // rename to avoid shadowing

  return useQuery({
    queryKey: ['user'],
    queryFn: async () => {
      try {
        // if (storedUser) return storedUser

        const currentUser = await getCurrentUser()
        const userAttributes = await fetchUserAttributes()
        const session = await fetchAuthSession()
        
        if (!session?.tokens || !session.userSub) throw new Error('No session tokens found')
        setAccessToken(session.tokens.accessToken.toString())
        const groups = session.tokens.accessToken.payload['cognito:groups'] as string[] | undefined
        const isAdmin = Boolean(groups?.includes('Admins'))
        const isAIEngineer = Boolean(groups?.includes('AIEngineers'))
        
        // Get user from your backend
        const accountData = await userService.getAccount(session.userSub)
        
        const user = {
          ...currentUser,
          ...userAttributes,
          ...accountData,
          isAdmin,
          isAIEngineer,
        }
        
        setUser(user)
        
        return user
      } catch (error) {
        setUser(null)
        setAccessToken(null)
        throw error
      }
    },
    retry: false,
  })
}

// Logout
export const useSignOut = () => {
  const { signOut } = useAuthenticator((context) => [context.user]);

  const queryClient = useQueryClient()
  const { setUser, setAccessToken } = useAuthStore()

  return useMutation({
    mutationKey: ['logout'],
    mutationFn: async () => {
      try {
        await signOut()
        setUser(null)
        setAccessToken(null)
        queryClient.clear()
      } catch (error) {
        console.log(error)
      }
    },
    retry: false,
  })
}