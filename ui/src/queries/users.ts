import { useQuery, useMutation } from '@tanstack/react-query';
import { userService } from '@/services/user.service';

export const useListAccounts = () => {
  return useQuery({
    queryKey: ['users'],
    queryFn: userService.listAccounts
  });
};


export const useUpdateAccountRoles = () => {
  return useMutation({
    mutationFn: ({ cognitoId, roles }: { cognitoId: string; roles: string[] }) =>
      userService.updateAccountRoles(cognitoId, roles)
  });
};
