import { useQuery, UseQueryOptions, useQueryClient, useMutation } from '@tanstack/react-query'
import { programService } from '@/services/program.service'
import { queryKeys } from './query-keys'
import {
  ProgramCategoryWithCount,
  ProgramListResponse,
  SubRoutineListResponse,
  SubRoutine,
  ProgramDescription
} from 'types'; // Corrected path to use alias

/**
 * Hook to fetch program categories with counts for a specific product.
 */
export const useGetProductProgramCategories = (
  productId: number | null | undefined, // Allow null/undefined ID
  options?: Omit<
    UseQueryOptions<ProgramCategoryWithCount[], Error>,
    'queryKey' | 'queryFn'
  >,
) => {
  return useQuery<ProgramCategoryWithCount[], Error>({
    queryKey: [...queryKeys.programCategories, productId], // Use a specific key
    queryFn: () => {
      if (typeof productId !== 'number') {
        return Promise.reject(
          new Error('Attempted to fetch program categories with invalid Product ID.'),
        );
      }
      return programService.getProductProgramCategories(productId);
    },
    enabled: typeof productId === 'number', // Only run query if productId is a valid number
    staleTime: 5 * 60 * 1000, // Example: 5 minutes stale time
    ...options,
  });
};

/**
 * Hook to fetch a paginated list of subroutines with flexible parameters.
 * Supports both getting all subroutines for a specific product and
 * general pagination of all subroutines.
 */
import type { ListSubroutinesParams } from 'types'; // Corrected path to use alias

export const useListSubroutines = (params: ListSubroutinesParams = {}) => {
  const { productId, ...otherParams } = params;

  // Only include the product ID if it's a valid number
  const finalParams: ListSubroutinesParams = {
    ...otherParams,
    ...(typeof productId === 'number' ? { productId } : {})
  };

  return useQuery<SubRoutineListResponse>({
    queryKey: [...queryKeys.subRoutines, finalParams],
    queryFn: () => programService.listSubroutines(finalParams),
    staleTime: 5 * 60 * 1000, // 5 minutes stale time
    enabled: productId === undefined || productId === null || typeof productId === 'number'
  });
};

/**
 * Hook to fetch programs for a specific category with pagination.
 */
export const useListProgramsByCategory = (params: {
  categoryId: number;
  skip?: number;
  limit?: number;
  productId?: number | null | undefined;
  search?: string;
  sortField?: string;
  sortDirection?: 'asc' | 'desc';
}) => {
  const { categoryId, productId, search, sortField, sortDirection, ...otherParams } = params;
  
  // Only include the product ID if it's a valid number
  const finalParams = {
    categoryId,
    ...otherParams,
    ...(typeof productId === 'number' ? { productId } : {}),
    ...(search ? { search } : {}),
    ...(sortField ? { sortField } : {}),
    ...(sortDirection ? { sortDirection } : {})
  };
  
  return useQuery<ProgramListResponse>({
    queryKey: [...queryKeys.programs, categoryId, finalParams],
    queryFn: () => programService.listProgramsByCategory(finalParams),
    staleTime: 5 * 60 * 1000, // 5 minutes stale time
    enabled: !!categoryId && (productId === undefined || productId === null || typeof productId === 'number')
  });
};


// Get a specific program by ID
import { ProgramDetail } from 'types'; // Corrected path to use alias

export const useGetProgramById = (
  programId: number,
  options?: Omit<UseQueryOptions<ProgramDetail, Error>, 'queryKey' | 'queryFn'>
) => {
  return useQuery<ProgramDetail, Error>({
    queryKey: [...queryKeys.programs, 'detail', programId],
    queryFn: () => programService.getProgramById(programId),
    enabled: !!programId,
    ...options
  })
}

/**
 * Hook to fetch a single subroutine by ID.
 */

export const useGetSubroutineById = (
  subroutineId: number,
  options?: Omit<UseQueryOptions<SubRoutine, Error>, 'queryKey' | 'queryFn'>
) => {
  return useQuery<SubRoutine, Error>({
    queryKey: [...queryKeys.subRoutines, 'detail', subroutineId],
    queryFn: () => programService.getSubroutineById(subroutineId),
    enabled: !!subroutineId,
    ...options
  });
};

// Get a specific program version by ID and version number
export const useGetProgramVersion = (
  programId: number,
  versionNumber: number,
  options?: Omit<UseQueryOptions<any, Error>, 'queryKey' | 'queryFn'>
) => {
  return useQuery<any, Error>({
    queryKey: [...queryKeys.programs, 'version', programId, versionNumber],
    queryFn: () => programService.getProgramVersion(programId, versionNumber),
    enabled: !!programId && !!versionNumber,
    ...options
  });
}

// Update a program
export const useUpdateProgram = (options?: {
  onSuccess?: (data: any) => void;
  onError?: (error: Error) => void;
}) => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ programId, programData }: {
      programId: number;
      programData: {
        steps: any[],
        name?: string,
        program_title?: string,
        logic_technique?: string,
        program_description?: ProgramDescription,
        version_notes?: string,
        save_action?: 'new' | 'overwrite'
      }
    }) => programService.updateProgram(programId, programData),
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: queryKeys.programs });
      options?.onSuccess?.(data);
    },
    onError: (error: Error) => {
      options?.onError?.(error);
    }
  });
}

// Create a program category
export const useCreateProgramCategory = (options?: {
  onSuccess?: (data: any) => void;
  onError?: (error: Error) => void;
}) => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: ({ name, description }: { name: string; description: string }) =>
      programService.createProgramCategory({ name, description }),
    onSuccess: (data) => {
      // Invalidate relevant queries if needed, e.g., program categories
      queryClient.invalidateQueries({ queryKey: queryKeys.programCategories });
      options?.onSuccess?.(data);
    },
    onError: (error: Error) => {
      options?.onError?.(error);
    }
  });
};

export const useDeleteProgramCategory = (options?: {
  onSuccess?: (data: any) => void;
  onError?: (error: Error) => void;
}) => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (categoryId: number) =>
      programService.deleteProgramCategory(categoryId),
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: queryKeys.programCategories });
      options?.onSuccess?.(data);
    },
    onError: (error: Error) => {
      options?.onError?.(error);
    }
  });
};

// Delete a specific program version
export const useDeleteProgramVersion = (options?: {
  onSuccess?: () => void;
  onError?: (error: Error) => void;
}) => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: ({ programId, versionNumber }: { programId: number; versionNumber: number }) =>
      programService.deleteProgramVersion(programId, versionNumber),
    onSuccess: (data, variables) => {
      // Invalidate the program details query to refresh the versions list
      queryClient.invalidateQueries({
        queryKey: [...queryKeys.programs, 'detail', variables.programId],
      });
      options?.onSuccess?.();
    },
    onError: (error: Error) => {
      options?.onError?.(error);
    },
  });
};

/**
 * Hook to export a program's steps as an Excel file via S3 presigned URL
 */
export const useExportProgram = (options?: {
  onSuccess?: (presigned_url: string, filename: string) => void;
  onError?: (error: Error) => void;
}) => {
  return useMutation({
    mutationFn: async ({
      programId,
      // programName is still useful for context or if the backend didn't provide a filename
      programName 
    }: {
      programId: number;
      programName?: string;
    }) => {
      
      // Call the service, which now returns an object with presigned_url and filename
      const exportData = await programService.exportProgram(programId); 
      
      
      // Ensure a filename is available, generate a fallback if necessary
      // The backend now provides a filename, so this is more of a safeguard.
      const filename = exportData.filename || (programName
        ? `${programName.replace(/[\\/\\\\:*?"<>|]/g, '_')}_steps.xlsx`
        : `program_${programId}_steps.xlsx`);
      
      return { presigned_url: exportData.presigned_url, filename };
    },
    onSuccess: (data) => {
      
      if (options?.onSuccess) {
        // Let consumer handle the presigned URL and filename
        options.onSuccess(data.presigned_url, data.filename);
      } else {
        // Default behavior: open the presigned URL, which should trigger download
        // The filename from `data.filename` is used by the server's Content-Disposition
        // The `programService.downloadFileFromUrl` can be used if more client-side control is needed,
        // but opening the URL directly is often simpler for S3 presigned URLs.
        try {
          window.open(data.presigned_url, '_blank');
          // Alternatively, use the service's helper if it offers more control or fallbacks:
          // programService.downloadFileFromUrl(data.presigned_url, data.filename);
        } catch (err) {
          console.error('Error opening presigned URL:', err);
          // Potentially call options.onError here or show a toast
        }
      }
    },
    onError: (error: Error) => {
      console.error('S3 Export error in mutation:', error);
      options?.onError?.(error);
    }
  });
};

/**
 * Hook to export a program's info as a Word document via S3 presigned URL
 */
export const useExportProgramInfo = (options?: {
  onSuccess?: (presigned_url: string, filename: string) => void;
  onError?: (error: Error) => void;
}) => {
  return useMutation({
    mutationFn: async ({
      programId,
      programName
    }: {
      programId: number;
      programName?: string;
    }) => {

      // Call the service, which now returns an object with presigned_url and filename
      const exportData = await programService.exportProgramInfo(programId);

      // Ensure a filename is available, generate a fallback if necessary
      const filename = exportData.filename || (programName
        ? `${programName.replace(/[\\/\\\\:*?"<>|]/g, '_')}_info.docx`
        : `program_${programId}_info.docx`);

      return { presigned_url: exportData.presigned_url, filename };
    },
    onSuccess: (data) => {

      if (options?.onSuccess) {
        // Let consumer handle the presigned URL and filename
        options.onSuccess(data.presigned_url, data.filename);
      } else {
        // Default behavior: open the presigned URL, which should trigger download
        try {
          window.open(data.presigned_url, '_blank');
        } catch (err) {
          console.error('Error opening presigned URL:', err);
        }
      }
    },
    onError: (error: Error) => {
      console.error('S3 Export error in mutation:', error);
      options?.onError?.(error);
    }
  });
};

// Update program status
export const useUpdateProgramStatus = (options?: {
  onSuccess?: (data: any) => void;
  onError?: (error: Error) => void;
}) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ programId, status }: { programId: number; status: string }) =>
      programService.updateProgramStatus(programId, status),
    onSuccess: (data, variables) => {
      // Invalidate all program-related queries to refresh the data
      queryClient.invalidateQueries({
        queryKey: queryKeys.programs,
        exact: false // This will invalidate all queries that start with queryKeys.programs
      });
      options?.onSuccess?.(data);
    },
    onError: (error: Error) => {
      options?.onError?.(error);
    }
  });
};
