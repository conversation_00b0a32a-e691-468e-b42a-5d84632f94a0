import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { applicationService } from '@/services/application.service';
import { ApplicationListResponse } from 'types';

export const useListApplications = (params: { skip?: number; limit?: number } = {}) => {
  return useQuery<ApplicationListResponse>({
    queryKey: ['applications', params],
    queryFn : () => applicationService.listApplications(params)
  });
};

export const useDeleteApplication = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ id }: { id: number }) => applicationService.deleteApplication(id),
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['applications'] })
    }
  })
};
