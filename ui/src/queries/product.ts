import { useMutation, useQuery, UseQueryOptions, useQueryClient } from '@tanstack/react-query'
import { productService, ProductMutationPayload } from '@/services/product.service'
import { queryKeys } from './query-keys'
import { ProductListResponse, ProductResponse } from 'types'

export const useListProducts = (params: { skip?: number; limit?: number } = {}) => {
  return useQuery<ProductListResponse>({
    queryKey: [...queryKeys.products, params],
    queryFn: () => productService.listProducts(params)
  })
}


export const useGetProduct = (
  id: number | null | undefined, // Allow null or undefined ID
  options?: Omit<UseQueryOptions<ProductResponse, Error>, 'queryKey' | 'queryFn'>
) => {
  return useQuery({
    queryKey: [...queryKeys.products, id],
    queryFn: () => {
      // Add check to satisfy TypeScript, although 'enabled' prevents execution with null/undefined
      if (typeof id !== 'number') {
        return Promise.reject(new Error('Attempted to fetch product with invalid ID.'));
      }
      return productService.getProduct(id);
    },
    enabled: !!id, // Only run query if id is a valid number (truthy)
    ...options
  })
}

export const useCreateProduct = (options?: {
  onSuccess?: (response: ProductResponse) => void;
  onError?: (error: Error) => void;
}) => {
  const queryClient = useQueryClient();
  
  return useMutation<ProductResponse, Error, ProductMutationPayload>({ // Specify types
    mutationFn: (payload: ProductMutationPayload) => productService.createProduct(payload), // Use new payload type
    onSuccess: (response) => {
      queryClient.invalidateQueries({ queryKey: queryKeys.products });
      options?.onSuccess?.(response);
    },
    onError: (error: Error) => {
      options?.onError?.(error);
    }
  });
}

export const useUpdateProduct = (options?: {
  onSuccess?: (response: ProductResponse) => void;
  onError?: (error: Error) => void;
}) => {
  const queryClient = useQueryClient();
  
  // Specify types for mutation function arguments and response/error
  return useMutation<ProductResponse, Error, { id: number; payload: ProductMutationPayload }>({
    mutationFn: ({ id, payload }: { id: number; payload: ProductMutationPayload }) => // Use new payload type
      productService.updateProduct(id, payload),
    onSuccess: (response, variables) => { // variables contains { id, payload }
      // Invalidate specific product query as well as the list
      queryClient.invalidateQueries({ queryKey: [...queryKeys.products, variables.id] });
      queryClient.invalidateQueries({ queryKey: queryKeys.products, exact: true }); // Invalidate the list query
      options?.onSuccess?.(response);
    },
    // Removed the incorrect onError block that was here
    onError: (error: Error) => { // Keep this onError block
      options?.onError?.(error);
    }
  });
}

export const useDeleteProduct = (options?: {
  onSuccess?: () => void;
  onError?: (error: Error) => void;
}) => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: productService.deleteProduct,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.products });
      options?.onSuccess?.();
    },
    onError: (error: Error) => {
      options?.onError?.(error);
    }
  });
}