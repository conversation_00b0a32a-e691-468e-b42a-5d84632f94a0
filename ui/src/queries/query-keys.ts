export const queryKeys = {
  sources: ['sources'],
  chatModels: ['chat'],
  chatConfigs: ['chatConfigs'],
  chatThreads: ['chatThreads'],
  products: ['products'],
  programCategories: ['programCategories'],
  programs: ['programs'],
  subRoutines: ['subRoutines'],
  segments: ['segments'],
dashboard: {
    all: ['dashboard'] as const,
    stats: () => [...queryKeys.dashboard.all, 'stats'] as const,
    chatsPerDay: (days?: number) => [...queryKeys.dashboard.all, 'chatsPerDay', { days }] as const,
    productChatActivity: (
      period?: string,
      granularity?: string,
      product_ids?: string[] | string,
      limit?: number
    ) =>
      [
        ...queryKeys.dashboard.all,
        'productChatActivity',
        { period, granularity, product_ids, limit },
      ] as const,
  },
}

