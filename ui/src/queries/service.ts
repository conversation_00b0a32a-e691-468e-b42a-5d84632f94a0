import axiosInstance from '@/lib/axios';
import { PineconeInvokeRequest, PineconeStreamLog } from 'types';

const BASE_URL = 'api2/pinecone'

export const getPineconeInputSchema = async () => {
  const response = await axiosInstance.get(BASE_URL + '/input_schema');
  return response.data;
};


export const getPineconeOutputSchema = async () => {
  const response = await axiosInstance.get(BASE_URL + '/output_schema');
  return response.data;
};


export const getPineconeConfigSchema = async () => {
  const response = await axiosInstance.get(BASE_URL + '/config_schema');
  return response.data;
};


export const invokePinecone = async (data: PineconeInvokeRequest) => {
  const response = await axiosInstance.post(BASE_URL + '/invoke', data);
  return response.data;
};

export const invokePineconeBatch = async (data: PineconeInvokeRequest) => {
  const response = await axiosInstance.post(BASE_URL + '/batch', data);
  return response.data;
};


export const invokePineconeStream = async (data: PineconeInvokeRequest) => {
  const response = await axiosInstance.post(BASE_URL + '/stream', data);
  return response.data;
};


export const invokePineconeStreamLog = async (data: PineconeStreamLog) => {
  const response = await axiosInstance.post(BASE_URL + '/stream_log', data);
  return response.data;
};
