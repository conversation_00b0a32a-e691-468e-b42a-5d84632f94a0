import { useQuery, useMutation, useQueryClient, UseQueryOptions } from '@tanstack/react-query';
import {
  segmentService,
  ProgramSegment,
  SegmentHierarchy,
  ListSegmentsParams,
  SegmentationJob,
  SegmentationJobListResponse,
  SegmentationJobStatus,
  ProgramSegmentListResponse,
  SegmentStats,
  SegmentStatsParams,
  SegmentCleanupResponse,
  ProductSegmentationJob,
  ProductSegmentationJobListResponse
} from '@/services/segment.service';
import { toast } from 'sonner';
import { queryKeys } from './query-keys';

/**
 * Hook to fetch a paginated list of segments with flexible parameters.
 * Returns the full pagination response with metadata.
 */
export const useListSegmentsPaginated = (params: ListSegmentsParams = {}) => {
  return useQuery<ProgramSegmentListResponse>({
    queryKey: [...queryKeys.segments, 'list-paginated', params],
    queryFn: () => segmentService.listSegments(params),
    staleTime: 5 * 60 * 1000, // 5 minutes stale time
  });
};

/**
 * Hook to fetch segment statistics with flexible parameters.
 * Returns aggregated stats without fetching all segment data.
 */
export const useGetSegmentStats = (params: SegmentStatsParams = {}) => {
  return useQuery<SegmentStats>({
    queryKey: [...queryKeys.segments, 'stats', params],
    queryFn: () => segmentService.getSegmentStats(params),
    staleTime: 5 * 60 * 1000, // 5 minutes stale time
  });
};

/**
 * Hook to fetch a list of segments with flexible parameters.
 * Returns only the segments array for backward compatibility.
 */
export const useListSegments = (params: ListSegmentsParams = {}) => {
  return useQuery<ProgramSegment[]>({
    queryKey: [...queryKeys.segments, 'list', params],
    queryFn: async () => {
      const response = await segmentService.listSegments(params);
      return response.segments;
    },
    staleTime: 5 * 60 * 1000, // 5 minutes stale time
  });
};

/**
 * Hook to fetch a specific segment by ID.
 */
export const useGetSegmentById = (
  segmentId: number,
  options?: Omit<UseQueryOptions<ProgramSegment, Error>, 'queryKey' | 'queryFn'>
) => {
  return useQuery<ProgramSegment, Error>({
    queryKey: [...queryKeys.segments, 'detail', segmentId],
    queryFn: () => segmentService.getSegmentById(segmentId),
    enabled: !!segmentId,
    ...options
  });
};

/**
 * Hook to fetch all segments for a specific program.
 */
export const useGetProgramSegments = (
  programId: number,
  includeMicroChunks: boolean = true,
  options?: Omit<UseQueryOptions<ProgramSegment[], Error>, 'queryKey' | 'queryFn'>
) => {
  return useQuery<ProgramSegment[], Error>({
    queryKey: [...queryKeys.segments, 'program', programId, { includeMicroChunks }],
    queryFn: () => segmentService.getProgramSegments(programId, includeMicroChunks),
    enabled: !!programId,
    staleTime: 5 * 60 * 1000,
    ...options
  });
};

/**
 * Hook to fetch segment hierarchy for a specific program.
 */
export const useGetProgramSegmentHierarchy = (
  programId: number,
  options?: Omit<UseQueryOptions<SegmentHierarchy, Error>, 'queryKey' | 'queryFn'>
) => {
  return useQuery<SegmentHierarchy, Error>({
    queryKey: [...queryKeys.segments, 'hierarchy', programId],
    queryFn: () => segmentService.getProgramSegmentHierarchy(programId),
    enabled: !!programId,
    staleTime: 5 * 60 * 1000,
    ...options
  });
};

/**
 * Hook to fetch macro phases only for a program.
 */
export const useGetMacroPhases = (
  programId: number,
  options?: Omit<UseQueryOptions<ProgramSegment[], Error>, 'queryKey' | 'queryFn'>
) => {
  return useQuery<ProgramSegment[], Error>({
    queryKey: [...queryKeys.segments, 'macro-phases', programId],
    queryFn: () => segmentService.getMacroPhases(programId),
    enabled: !!programId,
    staleTime: 5 * 60 * 1000,
    ...options
  });
};

/**
 * Hook to fetch micro chunks for a specific macro phase.
 */
export const useGetMicroChunks = (
  macroPhaseId: number,
  options?: Omit<UseQueryOptions<ProgramSegment[], Error>, 'queryKey' | 'queryFn'>
) => {
  return useQuery<ProgramSegment[], Error>({
    queryKey: [...queryKeys.segments, 'micro-chunks', macroPhaseId],
    queryFn: () => segmentService.getMicroChunks(macroPhaseId),
    enabled: !!macroPhaseId,
    staleTime: 5 * 60 * 1000,
    ...options
  });
};

/**
 * Hook to search segments by tags.
 */
export const useSearchSegmentsByTags = (
  searchTerm: string,
  productId?: number,
  options?: Omit<UseQueryOptions<ProgramSegment[], Error>, 'queryKey' | 'queryFn'>
) => {
  return useQuery<ProgramSegment[], Error>({
    queryKey: [...queryKeys.segments, 'search', searchTerm, productId],
    queryFn: () => segmentService.searchSegmentsByTags(searchTerm, productId),
    enabled: !!searchTerm && searchTerm.length > 2, // Only search if term is longer than 2 chars
    staleTime: 2 * 60 * 1000, // 2 minutes for search results
    ...options
  });
};

/**
 * Hook to fetch segments by phase.
 */
export const useGetSegmentsByPhase = (
  phase: string,
  productId?: number,
  options?: Omit<UseQueryOptions<ProgramSegment[], Error>, 'queryKey' | 'queryFn'>
) => {
  return useQuery<ProgramSegment[], Error>({
    queryKey: [...queryKeys.segments, 'phase', phase, productId],
    queryFn: () => segmentService.getSegmentsByPhase(phase, productId),
    enabled: !!phase,
    staleTime: 5 * 60 * 1000,
    ...options
  });
};

// --- Segmentation Job Hooks ---

/**
 * Hook to start segmentation for a program.
 */
export const useStartSegmentation = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (programId: number) => segmentService.startProgramSegmentation(programId),
    onSuccess: (data: SegmentationJob) => {
      toast.success('Segmentation started successfully');
      // Invalidate and refetch segmentation jobs
      queryClient.invalidateQueries({ queryKey: [...queryKeys.segments, 'jobs'] });
      queryClient.invalidateQueries({ queryKey: [...queryKeys.segments, 'program-jobs'] });
    },
    onError: (error: any) => {
      const message = error?.response?.data?.detail || 'Failed to start segmentation';
      toast.error(message);
    },
  });
};

/**
 * Hook to cancel a running segmentation job.
 */
export const useCancelSegmentation = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (jobId: number) => segmentService.cancelSegmentationJob(jobId),
    onSuccess: (data: SegmentationJob) => {
      toast.success('Segmentation cancelled successfully');
      // Invalidate and refetch segmentation jobs
      queryClient.invalidateQueries({ queryKey: [...queryKeys.segments, 'jobs'] });
      queryClient.invalidateQueries({ queryKey: [...queryKeys.segments, 'program-jobs'] });
      queryClient.invalidateQueries({ queryKey: [...queryKeys.segments, 'job', data.id] });
    },
    onError: (error: any) => {
      const message = error?.response?.data?.detail || 'Failed to cancel segmentation';
      toast.error(message);
    },
  });
};

/**
 * Hook to clear segments and segmentation jobs with optional filtering.
 */
export const useClearSegmentsAndJobs = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (params?: { product_id?: number; program_id?: number }) =>
      segmentService.clearSegmentsAndJobs(params),
    onSuccess: (data: SegmentCleanupResponse) => {
      const { statistics } = data;
      toast.success(
        `Successfully cleared ${statistics.segments_deleted} segments and ${statistics.jobs_deleted} jobs`
      );

      // Invalidate all segment-related queries to refresh the UI
      queryClient.invalidateQueries({ queryKey: [...queryKeys.segments] });
    },
    onError: (error: any) => {
      const message = error?.response?.data?.detail || 'Failed to clear segments and jobs';
      toast.error(message);
    },
  });
};

/**
 * Hook to get a specific segmentation job.
 */
export const useGetSegmentationJob = (
  jobId: number,
  options?: { enabled?: boolean; refetchInterval?: number }
) => {
  return useQuery<SegmentationJob, Error>({
    queryKey: [...queryKeys.segments, 'job', jobId],
    queryFn: () => segmentService.getSegmentationJob(jobId),
    enabled: options?.enabled !== false && !!jobId,
    refetchInterval: options?.refetchInterval || (
      // Auto-refresh every 2 seconds for running jobs
      (query) => {
        const data = query.state.data as SegmentationJob | undefined;
        if (data?.status === SegmentationJobStatus.RUNNING || data?.status === SegmentationJobStatus.PENDING) {
          return 2000;
        }
        return false;
      }
    ),
  });
};

/**
 * Hook to get segmentation jobs for a program.
 */
export const useGetProgramSegmentationJobs = (
  programId: number,
  options?: {
    skip?: number;
    limit?: number;
    enabled?: boolean;
    refetchInterval?: number;
  }
) => {
  const { skip = 0, limit = 10, enabled = true, refetchInterval } = options || {};

  return useQuery<SegmentationJobListResponse, Error>({
    queryKey: [...queryKeys.segments, 'program-jobs', programId, skip, limit],
    queryFn: () => segmentService.getProgramSegmentationJobs(programId, skip, limit),
    enabled: enabled && !!programId,
    refetchInterval: refetchInterval || (
      // Auto-refresh every 5 seconds if there are running jobs
      (query) => {
        const data = query.state.data as SegmentationJobListResponse | undefined;
        const hasRunningJobs = data?.jobs?.some(job =>
          job.status === SegmentationJobStatus.RUNNING ||
          job.status === SegmentationJobStatus.PENDING
        );
        return hasRunningJobs ? 5000 : false;
      }
    ),
  });
};

/**
 * Hook to get the latest segmentation job for a program.
 */
export const useGetLatestSegmentationJob = (programId: number, options?: { enabled?: boolean }) => {
  const { data: jobsResponse, ...queryResult } = useGetProgramSegmentationJobs(
    programId,
    {
      limit: 1,
      enabled: options?.enabled,
      refetchInterval: 3000 // Refresh every 3 seconds for latest job
    }
  );

  return {
    ...queryResult,
    data: jobsResponse?.jobs?.[0] || null,
  };
};

/**
 * Hook to check if segmentation is available for a program (no running jobs).
 */
export const useCanStartSegmentation = (programId: number) => {
  const { data: latestJob } = useGetLatestSegmentationJob(programId);

  const canStart = !latestJob || (
    latestJob.status !== SegmentationJobStatus.RUNNING &&
    latestJob.status !== SegmentationJobStatus.PENDING
  );

  return {
    canStart,
    latestJob,
    isRunning: latestJob?.status === SegmentationJobStatus.RUNNING,
    isPending: latestJob?.status === SegmentationJobStatus.PENDING,
    isCompleted: latestJob?.status === SegmentationJobStatus.COMPLETED,
    isFailed: latestJob?.status === SegmentationJobStatus.FAILED,
    isCancelled: latestJob?.status === SegmentationJobStatus.CANCELLED,
  };
};

// --- Product Segmentation Hooks ---

/**
 * Hook to start segmentation for all programs in a product.
 */
export const useStartProductSegmentation = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (productId: number) => segmentService.startProductSegmentation(productId),
    onSuccess: (data: ProductSegmentationJob) => {
      toast.success('Product segmentation started successfully');
      // Invalidate product segmentation jobs
      queryClient.invalidateQueries({ queryKey: [...queryKeys.segments, 'product-jobs'] });
      // Also invalidate regular segmentation jobs as they will be created
      queryClient.invalidateQueries({ queryKey: [...queryKeys.segments, 'jobs'] });
      queryClient.invalidateQueries({ queryKey: [...queryKeys.segments, 'program-jobs'] });
    },
    onError: (error: any) => {
      const message = error?.response?.data?.detail || 'Failed to start product segmentation';
      toast.error(message);
    },
  });
};

/**
 * Hook to cancel a product segmentation job.
 */
export const useCancelProductSegmentation = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (jobId: number) => segmentService.cancelProductSegmentationJob(jobId),
    onSuccess: () => {
      toast.success('Product segmentation cancelled successfully');
      // Invalidate product segmentation jobs
      queryClient.invalidateQueries({ queryKey: [...queryKeys.segments, 'product-jobs'] });
      // Also invalidate regular segmentation jobs
      queryClient.invalidateQueries({ queryKey: [...queryKeys.segments, 'jobs'] });
      queryClient.invalidateQueries({ queryKey: [...queryKeys.segments, 'program-jobs'] });
    },
    onError: (error: any) => {
      const message = error?.response?.data?.detail || 'Failed to cancel product segmentation';
      toast.error(message);
    },
  });
};

/**
 * Hook to get a specific product segmentation job.
 */
export const useGetProductSegmentationJob = (
  jobId: number,
  options?: { enabled?: boolean; refetchInterval?: number }
) => {
  return useQuery<ProductSegmentationJob, Error>({
    queryKey: [...queryKeys.segments, 'product-job', jobId],
    queryFn: () => segmentService.getProductSegmentationJob(jobId),
    enabled: options?.enabled !== false && !!jobId,
    refetchInterval: options?.refetchInterval || (
      // Auto-refresh every 2 seconds for running jobs
      (query) => {
        const data = query.state.data as ProductSegmentationJob | undefined;
        if (data?.status === SegmentationJobStatus.RUNNING || data?.status === SegmentationJobStatus.PENDING) {
          return 2000;
        }
        return false;
      }
    ),
  });
};

/**
 * Hook to get the latest product segmentation job for a product.
 */
export const useGetLatestProductSegmentationJob = (productId: number) => {
  return useQuery<ProductSegmentationJob | null, Error>({
    queryKey: [...queryKeys.segments, 'latest-product-job', productId],
    queryFn: async () => {
      const response = await segmentService.getProductSegmentationJobs(productId, 0, 1);
      return response.jobs.length > 0 ? response.jobs[0] : null;
    },
    enabled: !!productId,
    refetchInterval: (query) => {
      const data = query.state.data as ProductSegmentationJob | null;
      if (data?.status === SegmentationJobStatus.RUNNING || data?.status === SegmentationJobStatus.PENDING) {
        return 5000; // Refresh every 5 seconds for running jobs
      }
      return false;
    },
  });
};

/**
 * Hook to list product segmentation jobs for a product.
 */
export const useListProductSegmentationJobs = (
  productId: number,
  options?: { skip?: number; limit?: number; enabled?: boolean; refetchInterval?: number }
) => {
  const { skip = 0, limit = 10, enabled = true, refetchInterval } = options || {};

  return useQuery<ProductSegmentationJobListResponse, Error>({
    queryKey: [...queryKeys.segments, 'product-jobs', productId, skip, limit],
    queryFn: () => segmentService.getProductSegmentationJobs(productId, skip, limit),
    enabled: enabled && !!productId,
    refetchInterval: refetchInterval || (
      // Auto-refresh every 5 seconds if there are running jobs
      (query) => {
        const data = query.state.data as ProductSegmentationJobListResponse | undefined;
        const hasRunningJobs = data?.jobs?.some(job =>
          job.status === SegmentationJobStatus.RUNNING ||
          job.status === SegmentationJobStatus.PENDING
        );
        return hasRunningJobs ? 5000 : false;
      }
    ),
  });
};

/**
 * Hook to check if product segmentation can be started for a product.
 */
export const useCanStartProductSegmentation = (productId: number) => {
  const { data: latestJob } = useGetLatestProductSegmentationJob(productId);

  const canStart = !latestJob || ![
    SegmentationJobStatus.PENDING,
    SegmentationJobStatus.RUNNING
  ].includes(latestJob.status);

  const isRunning = latestJob?.status === SegmentationJobStatus.RUNNING;
  const isPending = latestJob?.status === SegmentationJobStatus.PENDING;
  const isCompleted = latestJob?.status === SegmentationJobStatus.COMPLETED;
  const isFailed = latestJob?.status === SegmentationJobStatus.FAILED;
  const isCancelled = latestJob?.status === SegmentationJobStatus.CANCELLED;

  return {
    canStart,
    latestJob,
    isRunning,
    isPending,
    isCompleted,
    isFailed,
    isCancelled,
  };
};
