import { useMutation, useQuery, UseQueryOptions, useQueryClient } from '@tanstack/react-query'
import { chatService } from '@/services/chat.service'
import { queryKeys } from './query-keys'
import { ChatStreamResponse, CreateConfigRequest, SendMessageRequest, ThreadsResponse, CreateConfigResponse } from 'types'


export const useGetConfig = (
  id: number | undefined | null,
  options?: Omit<UseQueryOptions<CreateConfigResponse, Error>, 'queryKey' | 'queryFn'>
) => {
  return useQuery({
    queryKey: [...queryKeys.chatConfigs, id],
    queryFn: () => chatService.getConfig(id as number), // Assert id as number here as it's enabled only if id is truthy
    enabled: !!id, // Query will only run if id is a truthy value (not null, undefined, 0)
    ...options
  })
}

export const useListModels = () => {
  return useQuery({
    queryKey: queryKeys.chatModels,
    queryFn: chatService.listModels
  })
}

export const useCreateConfig = (options?: {
  onSuccess?: (response: CreateConfigResponse) => void;
  onError?: (error: Error) => void;
}) => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: chatService.createConfig,
    onSuccess: (response) => {
      queryClient.invalidateQueries({ queryKey: queryKeys.chatConfigs });
      queryClient.setQueryData([...queryKeys.chatConfigs, response.id], response);
      options?.onSuccess?.(response);
    },
    onError: (error: Error) => {
      options?.onError?.(error);
    }
  });
};


export const useUpdateConfig = () => {
  return useMutation({
    mutationFn: ({ id, payload }: { id: number; payload: CreateConfigRequest }) =>
      chatService.updateConfig(id, payload)
  })
}

export const useListConfigs = () => {
  return useQuery({
    queryKey: queryKeys.chatConfigs,
    queryFn: chatService.listConfigs
  })
}



export const useSendMessage = () => {
  return useMutation({
    mutationFn: async (params: SendMessageRequest & { 
      onToken?: (response: ChatStreamResponse) => void 
    }) => {
      return chatService.sendMessage(params)
    }
  })
}

export const useListThreads = (params: {
  skip?: number;
  limit?: number;
  search?: string;
  sortField?: string;
  sortDirection?: 'asc' | 'desc';
  starredOnly?: boolean;
} = {}) => {
  return useQuery<ThreadsResponse>({
    queryKey: [...queryKeys.chatThreads, params],
    queryFn: () => chatService.listThreads(params)
  })
}

export const useThreadMessages = (threadId: string, options?: { enabled?: boolean }) => {
  return useQuery({
    queryKey: [...queryKeys.chatThreads, threadId],
    queryFn: () => chatService.getThreadMessages(threadId),
    staleTime: 0,
    refetchOnMount: true,
    refetchOnWindowFocus: false,
    ...options
  });
};


export const useLikeProgram = (options?: {
  onSuccess?: (data: { success: boolean; liked: boolean; disliked: boolean; saved: boolean }) => void;
  onError?: (error: Error) => void;
}) => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: chatService.updateProgramLike,
    onSuccess: (data, programId) => {
      const threadQueries = queryClient.getQueriesData({ queryKey: queryKeys.chatThreads });
      for (const [queryKey] of threadQueries) {
        queryClient.invalidateQueries({ queryKey });
      }
      options?.onSuccess?.(data);
    },
    onError: (error: Error) => {
      options?.onError?.(error);
    }
  });
}

export const useDislikeProgram = (options?: {
  onSuccess?: (data: { success: boolean; liked: boolean; disliked: boolean; saved: boolean }) => void;
  onError?: (error: Error) => void;
}) => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: chatService.updateProgramDislike,
    onSuccess: (data, programId) => {
      const threadQueries = queryClient.getQueriesData({ queryKey: queryKeys.chatThreads });
      for (const [queryKey] of threadQueries) {
        queryClient.invalidateQueries({ queryKey });
      }
      options?.onSuccess?.(data);
    },
    onError: (error: Error) => {
      options?.onError?.(error);
    }
  });
}

export const useSaveProgram = (options?: {
  onSuccess?: (data: { success: boolean; liked: boolean; disliked: boolean; saved: boolean }) => void;
  onError?: (error: Error) => void;
}) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: chatService.updateProgramSave,
    onSuccess: (data, programId) => {
      const threadQueries = queryClient.getQueriesData({ queryKey: queryKeys.chatThreads });
      for (const [queryKey] of threadQueries) {
        queryClient.invalidateQueries({ queryKey });
      }
      options?.onSuccess?.(data);
    },
    onError: (error: Error) => {
      options?.onError?.(error);
    }
  });
}

export const useStarChat = (options?: {
  onSuccess?: (data: { success: boolean; is_starred: boolean }) => void;
  onError?: (error: Error) => void;
}) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: chatService.starChat,
    onSuccess: (data, chatId) => {
      // Invalidate all thread queries to refresh the data
      queryClient.invalidateQueries({ queryKey: queryKeys.chatThreads });
      options?.onSuccess?.(data);
    },
    onError: (error: Error) => {
      options?.onError?.(error);
    }
  });
}
