import { useQuery } from '@tanstack/react-query';
import { getDashboardStats, getChatsPerDay } from '@/services/dashboard.service';
import { queryKeys } from './query-keys';

export const useDashboardStats = () => {
  return useQuery({
    queryKey: queryKeys.dashboard.stats(),
    queryFn: getDashboardStats,
    // Optional: Add staleTime or other configurations if needed
    // staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

export const useChatsPerDay = (days?: number) => {
  return useQuery({
    queryKey: queryKeys.dashboard.chatsPerDay(days),
    queryFn: () => getChatsPerDay(days),
    // Optional: Add staleTime or other configurations if needed
    // staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

/**
 * React Query hook for product program generation activity.
 * @param params period, granularity, product_ids, limit
 */
import {
  getProductProgramGenerationActivity,
  ProductProgramGenerationActivityParams,
  ProductProgramGenerationActivityResponse,
} from '@/services/dashboard.service';

export const useProductProgramGenerationActivity = (
  params: ProductProgramGenerationActivityParams = {}
) => {
  const mergedParams = {
    period: "10d", // Default period
    granularity: "daily", // Default granularity
    ...params,
  };
  const { period, granularity, product_ids, limit } = mergedParams;
  return useQuery<ProductProgramGenerationActivityResponse>({
    queryKey: ['dashboard', 'productProgramGenerationActivity', { period, granularity, product_ids, limit }], // MODIFIED queryKey
    queryFn: () => getProductProgramGenerationActivity(mergedParams),
    // staleTime: 5 * 60 * 1000, // Optional: 5 minutes
  });
};