import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { sourceService } from '@/services/source.service'
import { CreateSourceRequest, UploadSourceFileRequest, SourceListResponse } from 'types'

// Define query keys as constants
export const sourceKeys = {
  all: ['sources'] as const,
  lists: () => [...sourceKeys.all, 'list'] as const,
  detail: (id: number) => [...sourceKeys.all, 'detail', id] as const,
  files: (sourceId: number) => [...sourceKeys.all, 'files', sourceId] as const,
  fileDetail: (sourceId: number, fileId: number) => [...sourceKeys.files(sourceId), fileId] as const,
}

export const useListSources = (params: { skip?: number; limit?: number } = {}) => {
  return useQuery<SourceListResponse>({
    queryKey: [...sourceKeys.lists(), params],
    queryFn: () => sourceService.listSources(params),
  })
}

export const useCreateSource = () => {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: sourceService.createSource,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: sourceKeys.lists() })
    }
  })
}

export const useUpdateSource = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ id, payload }: { id: number; payload: CreateSourceRequest }) => 
      sourceService.updateSource(id, payload),
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({ queryKey: sourceKeys.detail(id) })
      queryClient.invalidateQueries({ queryKey: sourceKeys.lists() })
    }
  })
}

export const useDeleteSource = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ sourceId }: { sourceId: number }) => 
      sourceService.deleteSource(sourceId),
    onSuccess: (_, { sourceId }) => {
      queryClient.invalidateQueries({ queryKey: sourceKeys.detail(sourceId) })
      queryClient.invalidateQueries({ queryKey: sourceKeys.lists() })
    }
  })
}

export const useGetFilesFromSource = (
  sourceId: number, 
  options?: { 
    refetchInterval?: number | (() => number | false) 
  }
) => {
  return useQuery({
    queryKey: sourceKeys.files(sourceId),
    queryFn: () => sourceId && sourceId !== 0 
      ? sourceService.getFilesFromSource(sourceId) 
      : null,
    enabled: Boolean(sourceId && sourceId !== 0),
    ...options
  })
}

export const useGetFileDetailFromSource = (sourceId: number, fileId: number) => {
  return useQuery({
    queryKey: sourceKeys.fileDetail(sourceId, fileId),
    queryFn: () => sourceId && fileId 
      ? sourceService.getFileDetailFromSource(sourceId, fileId) 
      : null,
    enabled: Boolean(sourceId && fileId)
  })
}

export const useUpdateFileToSource = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ id, file }: { id: number; file: UploadSourceFileRequest }) =>
      sourceService.updateFileToSource(id, file),
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({ queryKey: sourceKeys.files(id) })
    },
    retry: false
  })
}

export const useDeleteFileFromSource = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ sourceId, fileId }: { sourceId: number; fileId: number }) =>
      sourceService.deleteFileFromSource(sourceId, fileId),
    onSuccess: (_, { sourceId }) => {
      queryClient.invalidateQueries({ queryKey: sourceKeys.files(sourceId) })
    }
  })
}