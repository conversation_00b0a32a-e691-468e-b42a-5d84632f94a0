'use client';

import React from 'react';
import { ScrollArea } from '@/components/ui/scroll-area';
import { useSidebar } from '@/components/ui/sidebar';

export default function PageContainer({
  children,
  scrollable = true
}: {
  children: React.ReactNode;
  scrollable?: boolean;
}) {
  const sidebar = useSidebar();

  const contentClassName =
    sidebar?.open && !sidebar?.isMobile
      ? 'flex max-w-[98%] flex-1 p-4'
      : 'flex max-w-full flex-1 p-4';

  return (
    <>
      {scrollable ? (
        <ScrollArea className='h-[calc(100dvh-52px)]'>
          <div className={contentClassName}>{children}</div>
        </ScrollArea>
      ) : (
        <div
          className={
            sidebar?.open && !sidebar?.isMobile
              ? 'flex max-w-[98%] flex-1 p-4'
              : 'flex max-w-full flex-1 p-4'
          }
        >
          {children}
        </div>
      )}
    </>
  );
}
