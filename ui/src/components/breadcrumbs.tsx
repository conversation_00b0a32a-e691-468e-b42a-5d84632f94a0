'use client';
import {
  Breadcrumb,
  B<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  BreadcrumbPage
} from '@/components/ui/breadcrumb';
import { useBreadcrumbs } from '@/hooks/use-breadcrumbs';
import { useSourceStore } from '@/stores/source.store';
import { usePathname } from 'next/navigation';

export function Breadcrumbs() {
  const pathname = usePathname();
  const items = useBreadcrumbs();
  const selectedSource = useSourceStore((state) => state.selectedSource);

  // Hide breadcrumbs on product detail pages (e.g., /dashboard/product/[id])
  if (/^\/dashboard\/product\/\d+$/i.test(pathname)) return null;

  if (items.length === 0) return null;

  // Process items to replace data source IDs with names
  const processedItems = items.map((item) => {
    // Check if this is a data source ID path segment
    if (
      item.link.includes('/data-sources/') &&
      !isNaN(Number(item.title)) &&
      selectedSource
    ) {
      return {
        ...item,
        title: selectedSource.name || item.title
      };
    }
    return item;
  }) as import('@/hooks/use-breadcrumbs').BreadcrumbItem[];

  return (
    <Breadcrumb>
      <BreadcrumbList>
        <BreadcrumbPage>
          {(() => {
            const last = processedItems[processedItems.length - 1];
            // Prefer 'label', fallback to 'title', fallback to empty string
            return last?.label ?? last?.title ?? '';
          })()}
        </BreadcrumbPage>
      </BreadcrumbList>
    </Breadcrumb>
  );
}
