import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { LucideFileText, LucideTrash } from 'lucide-react';
import {
  Popover,
  PopoverContent,
  PopoverTrigger
} from '@/components/ui/popover';
import { formatDateTime } from '@/lib/utils';

interface FileItemProps {
  file: {
    id: number;
    name: string;
    size: number;
    created_at: string;
    status: string;
  };
  onFileClick: (file: { id: number }) => void;
  onDeleteFile: (fileId: number) => void;
  isSelected: boolean;
}

export function FileCard({
  file,
  onFileClick,
  onDeleteFile,
  isSelected
}: FileItemProps) {
  const [isDeleteOpen, setIsDeleteOpen] = useState(false);
  const truncateFileName = (fileName: string, maxLength: number = 20) => {
    if (fileName.length <= maxLength) return fileName;
    const extension = fileName.split('.').pop();
    const name = fileName.substring(0, fileName.lastIndexOf('.'));
    const truncated = name.substring(0, maxLength - 3) + '...';
    return `${truncated}.${extension}`;
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'text-yellow-500 bg-yellow-500/10';
      case 'active':
        return 'text-green-500 bg-green-500/10';
      case 'failed':
        return 'text-red-500 bg-red-500/10';
      default:
        return 'text-gray-500 bg-gray-500/10';
    }
  };

  return (
    <div
      className={`flex cursor-pointer items-center justify-between rounded-md border bg-background p-4 transition-all hover:border-primary/50 hover:shadow-md ${
        isSelected ? 'border-primary' : ''
      }`}
      onClick={() => onFileClick(file)}
    >
      <div className='flex items-center gap-4'>
        <div className='rounded-full bg-primary/10 p-2'>
          <LucideFileText className='h-5 w-5 text-primary' />
        </div>
        <div className='flex flex-col gap-2'>
          <span className='font-medium' title={file.name}>
            {truncateFileName(file.name)}
          </span>
          <div className='flex items-center gap-3 text-xs text-muted-foreground'>
            <span>{(file.size / 1024).toFixed(2)} KB</span>
            <span>•</span>
            <span>{formatDateTime(file.created_at)}</span>
            <span>•</span>
            <span
              className={`rounded-full px-2 py-1 text-xs ${getStatusColor(file.status)}`}
            >
              {file.status}
            </span>
          </div>
        </div>
      </div>
      <Popover open={isDeleteOpen} onOpenChange={setIsDeleteOpen}>
        <PopoverTrigger asChild>
          <Button
            variant='ghost'
            size='icon'
            className='transition-colors hover:bg-destructive/10 hover:text-destructive'
            onClick={(e) => e.stopPropagation()}
          >
            <LucideTrash className='h-4 w-4' />
          </Button>
        </PopoverTrigger>
        <PopoverContent className='w-80' align='end'>
          <div className='space-y-4'>
            <h4 className='font-medium leading-none'>Delete File</h4>
            <p className='text-sm text-muted-foreground'>
              Are you sure you want to delete this file? This action cannot be
              undone.
            </p>
            <div className='flex justify-end gap-4'>
              <Button
                variant='outline'
                onClick={(e) => {
                  e.stopPropagation();
                  setIsDeleteOpen(false);
                }}
              >
                Cancel
              </Button>
              <Button
                variant='destructive'
                onClick={(e) => {
                  e.stopPropagation();
                  onDeleteFile(file.id);
                  setIsDeleteOpen(false);
                }}
              >
                Delete
              </Button>
            </div>
          </div>
        </PopoverContent>
      </Popover>
    </div>
  );
}
