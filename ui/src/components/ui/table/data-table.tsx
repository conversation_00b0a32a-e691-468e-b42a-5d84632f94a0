'use client';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import {
  ColumnDef,
  flexRender,
  getCoreRowModel,
  useReactTable,
  getSortedRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  ColumnFiltersState,
  SortingState,
  VisibilityState,
  getFacetedUniqueValues,
  getFacetedRowModel
} from '@tanstack/react-table';
import { useState } from 'react';
import { DataTablePagination } from './data-table-pagination';
import DataTableToolbar from './data-table-toolbar';

interface DataTableProps<TData> {
  columns: ColumnDef<TData>[];
  data: TData[];
  searchKey?: string;
  filterableColumns?: {
    id: string;
    title: string;
    options: {
      label: string;
      value: string;
    }[];
  }[];
  isLoading?: boolean;
  pagination?: {
    page: number;
    pageCount: number;
    onPageChange: (page: number) => void;
    onPageSizeChange: (pageSize: number) => void;
    pageSize: number;
  };
  onRowClick?: (row: { original: TData }) => void;
  condensed?: boolean;
}

export function DataTable<TData>({
  columns,
  data,
  searchKey,
  filterableColumns,
  isLoading = false,
  pagination,
  onRowClick,
  condensed = false
}: DataTableProps<TData> & { isLoading?: boolean }) {
  const [sorting, setSorting] = useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({});
  const [rowSelection, setRowSelection] = useState({});

  const table = useReactTable({
    data,
    columns,
    state: {
      sorting,
      columnVisibility,
      rowSelection,
      columnFilters
    },
    enableRowSelection: true,
    onRowSelectionChange: setRowSelection,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    onColumnVisibilityChange: setColumnVisibility,
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFacetedRowModel: getFacetedRowModel(),
    getFacetedUniqueValues: getFacetedUniqueValues(),
    manualPagination: true,
    pageCount: pagination?.pageCount ?? -1
  });

  return (
    <div className={`space-y-${condensed ? '2' : '4'}`}>
      <DataTableToolbar
        table={table}
        filterableColumns={filterableColumns}
        searchKey={searchKey}
        condensed={condensed}
      />
      <div className='rounded-md border'>
        <Table className={condensed ? 'text-xs' : ''}>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id} className={condensed ? 'h-8' : ''}>
                {headerGroup.headers.map((header) => {
                  return (
                    <TableHead
                      key={header.id}
                      colSpan={header.colSpan}
                      className={condensed ? 'h-8 px-2 py-1' : ''}
                    >
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                            header.column.columnDef.header,
                            header.getContext()
                          )}
                    </TableHead>
                  );
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {isLoading ? (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className={`${condensed ? 'h-16' : 'h-24'} text-center`}
                >
                  Loading...
                </TableCell>
              </TableRow>
            ) : table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && 'selected'}
                  onClick={() => onRowClick && onRowClick(row)}
                  className={` ${onRowClick ? 'cursor-pointer hover:bg-muted/60' : ''} ${condensed ? 'h-8' : ''} `}
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell
                      key={cell.id}
                      className={condensed ? 'px-2 py-1' : ''}
                    >
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext()
                      )}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className={`${condensed ? 'h-16' : 'h-24'} text-center`}
                >
                  No results.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
      {pagination && (
        <div className='flex justify-end'>
          <DataTablePagination
            table={table}
            onPageChange={pagination.onPageChange}
            pageCount={pagination.pageCount}
            page={pagination.page}
            onPageSizeChange={pagination.onPageSizeChange}
            pageSize={pagination.pageSize}
          />
        </div>
      )}
    </div>
  );
}
