import { DataTableToolbarProps } from 'types/data-table-types';
import { Input } from '../input';
import { DataTableFacetedFilter } from './data-table-faceted-filter';
import { DataTableViewOptions } from './data-table-view-options';

interface ExtendedDataTableToolbarProps<TData>
  extends DataTableToolbarProps<TData> {
  condensed?: boolean;
}

const DataTableToolbar = <TData,>({
  table,
  filterableColumns,
  searchKey,
  condensed = false
}: ExtendedDataTableToolbarProps<TData>) => {
  return (
    <div className='flex items-center justify-between'>
      <div className='flex flex-1 items-center space-x-2'>
        {searchKey && (
          <Input
            placeholder='Search...'
            value={
              (table.getColumn(searchKey)?.getFilterValue() as string) ?? ''
            }
            onChange={(event) =>
              table.getColumn(searchKey)?.setFilterValue(event.target.value)
            }
            className={condensed ? 'h-8 text-xs' : ''}
          />
        )}
        {filterableColumns?.map((column) => (
          <DataTableFacetedFilter
            key={column.id}
            column={table.getColumn(column.id)}
            title={column.title}
            options={column.options}
          />
        ))}
      </div>
      {!condensed && <DataTableViewOptions table={table} />}
    </div>
  );
};

export default DataTableToolbar;
