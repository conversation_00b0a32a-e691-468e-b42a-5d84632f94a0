import { api } from '@/services/user.service';
import { CreateSourceRequest, DeleteFileFromSourceResponse, SourceResponse, SourceListResponse, UploadSourceFileRequest, UploadSourceFileResponse, FileDetailResponse } from 'types'


export const sourceService = {
  listSources: async (params: { skip?: number; limit?: number }= {}): Promise<SourceListResponse> => {
    const { skip = 0, limit = 20 } = params;
    const { data } = await api.get(`/source/list?skip=${skip}&limit=${limit}`)
    return data
  },

  createSource: async (payload: CreateSourceRequest): Promise<SourceResponse> => {
    const { data } = await api.post('/source/create', payload)
    return data
  },

  updateSource: async (id: number, payload: CreateSourceRequest): Promise<SourceResponse> => {
    const { data } = await api.put(`/source/${id}`, payload)
    return data
  },

  deleteSource: async (sourceId: number): Promise<void> => {
    await api.delete(`/source/${sourceId}/delete`)
  },

  getFilesFromSource: async (id: number): Promise<UploadSourceFileResponse> => {
    const { data } = await api.get(`/source/${id}/files`)
    return data
  },

  getFileDetailFromSource: async (sourceId: number, fileId: number): Promise<FileDetailResponse[]> => {
    const { data } = await api.get(`/source/${sourceId}/file/${fileId}`)
    return data
  },

  updateFileToSource: async (id: number, { files }: UploadSourceFileRequest): Promise<UploadSourceFileResponse> => {
    const formData = new FormData()

    const appendFile = (file: typeof files[0]) => {
      const content = file.content
      const fileObj = content instanceof File ? content : new File([content], file.name)
      formData.append('files', fileObj)
    }

    Array.isArray(files) ? files.forEach(appendFile) : appendFile(files)

    const { data } = await api.post(`/source/${id}/files/upload`, formData, {
      headers: { 'Content-Type': 'multipart/form-data' }
    })

    return data
  },

  deleteFileFromSource: async (sourceId: number, fileId: number): Promise<DeleteFileFromSourceResponse> => {
    const { data } = await api.delete(`/source/${sourceId}/${fileId}/delete`)
    return data
  }
}
