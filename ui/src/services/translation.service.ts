interface TranslateRequest {
  text: string | string[];
  sourceLang?: string;
  targetLang?: string;
}

interface TranslateResponse {
  translatedText: string | string[];
  originalText: string | string[];
}

class TranslationService {
  private readonly apiKey = process.env.NEXT_PUBLIC_GOOGLE_TRANSLATE_API_KEY || 'AIzaSyATBXajvzQLTDHEQbcpq0Ihe0vWDHmO520';
  private readonly apiUrl = 'https://translate-pa.googleapis.com/v1/translateHtml';

  async translateText({ text, sourceLang = 'ja', targetLang = 'en' }: TranslateRequest): Promise<TranslateResponse> {
    try {
      const isArray = Array.isArray(text);
      const textArray = isArray ? text : [text];
      
      // Use the exact API format you provided
      const response = await fetch(this.apiUrl, {
        method: 'POST',
        headers: {
          'Accept': '*/*',
          'Content-Type': 'application/json+protobuf',
          'X-Goog-API-Key': this.apiKey,
        },
        body: JSON.stringify([[textArray, sourceLang, targetLang], "wt_lib"])
      });

      if (!response.ok) {
        throw new Error(`Translation failed: ${response.status}`);
      }

      const data = await response.json();
      
      // Parse the response - the API returns format like [["translation1", "translation2", ...]]
      let translatedTexts: string[] = [];
      if (data && Array.isArray(data) && data.length > 0) {
        // The translations are in the first element of the response array
        if (Array.isArray(data[0])) {
          translatedTexts = data[0];
        } else {
          // Fallback if response structure is different
          translatedTexts = data.map((item: any) => {
            if (typeof item === 'string') return item;
            if (Array.isArray(item) && item.length > 0) return item[0];
            return '';
          });
        }
      }


      return {
        translatedText: isArray ? translatedTexts : translatedTexts[0] || text,
        originalText: text
      };
    } catch (error) {
      console.error('Translation error:', error);
      throw new Error('Failed to translate text');
    }
  }

  async translateCppComments(code: string): Promise<string> {
    // Regex to find Japanese characters
    const japanesePattern = /[\u3000-\u303F\u3040-\u309F\u30A0-\u30FF\uFF00-\uFFEF\u4E00-\u9FAF\u3400-\u4DBF]/;
    // Updated regex to better capture comments
    const commentPattern = /(\/\/[^\r\n]*|\/\*[\s\S]*?\*\/)/g;

    let modifiedCode = code;
    const matches = Array.from(code.matchAll(commentPattern));

    // Collect all Japanese comments for batch translation
    const commentsToTranslate: {
      match: RegExpMatchArray;
      commentText: string;
      fullComment: string;
    }[] = [];

    for (const match of matches) {
      const fullComment = match[0];

      // Skip comments that already have English translations
      if (fullComment.includes('// EN:') || fullComment.includes('/* EN:')) {
        continue;
      }

      // Extract the actual comment text more carefully
      let commentText = '';
      if (fullComment.startsWith('//')) {
        commentText = fullComment.slice(2).trim();
      } else if (fullComment.startsWith('/*') && fullComment.endsWith('*/')) {
        commentText = fullComment.slice(2, -2).trim();
      }

      // Check if the comment contains Japanese characters
      if (commentText && japanesePattern.test(commentText)) {
        commentsToTranslate.push({
          match,
          commentText,
          fullComment
        });
      }
    }

    if (commentsToTranslate.length === 0) {
      return code;
    }


    try {
      // Batch translate all Japanese comments at once
      const textsToTranslate = commentsToTranslate.map(item => item.commentText);
      const { translatedText: translatedTexts } = await this.translateText({ 
        text: textsToTranslate 
      });
      
      const translations = Array.isArray(translatedTexts) ? translatedTexts : [translatedTexts];
      
      // Process matches in reverse order to maintain string positions
      for (let i = commentsToTranslate.length - 1; i >= 0; i--) {
        const item = commentsToTranslate[i];
        const translatedText = translations[i];
        
        
        if (!translatedText || translatedText === item.commentText) {
          continue; // Skip if translation failed or same as original
        }

        let newComment;
        if (item.fullComment.startsWith('//')) {
          // For single line comments, append the translation
          newComment = `${item.fullComment} // EN: ${translatedText}`;
        } else {
          // For multi-line comments, add translation below
          const leadingWhitespace = code.substring(0, item.match.index!).match(/[ \t]*$/)?.[0] || '';
          newComment = `${item.fullComment}\n${leadingWhitespace}/* EN: ${translatedText} */`;
        }
        
        modifiedCode = modifiedCode.substring(0, item.match.index!) + 
                      newComment + 
                      modifiedCode.substring(item.match.index! + item.fullComment.length);
      }
      
      return modifiedCode;
      
    } catch (error) {
      return code; // Return original code if translation fails
    }
  }
}

export const translationService = new TranslationService(); 