import { api } from '@/services/user.service';
import {
  ProgramCategoryWithCount,
  SubRoutineListResponse,
  ProgramListResponse,
  ListSubroutinesParams
} from 'types'; // Path alias should resolve to ui/types/index.ts
  

export const programService = {
  // Get a specific program by ID
  getProgramById: async (programId: number): Promise<import('types').ProgramDetail> => {
    const { data } = await api.get(`/programs/${programId}`);
    return data as import('types').ProgramDetail;
  },

  // Update a program
  updateProgram: async (programId: number, programData: { 
    steps: any[], 
    name?: string, 
    program_title?: string, 
    logic_technique?: string, 
    program_description?: any,
    version_notes?: string,
    save_action?: 'new' | 'overwrite'
  }): Promise<any> => {
    try {
      const { data } = await api.put(`/programs/${programId}`, programData);
      return data;
    } catch (error) {
      console.error('Error updating program:', error);
      throw error; // Make sure to re-throw so the error is handled by the mutation
    }
  },

  // Export program steps as Excel file
  exportProgram: async (programId: number): Promise<{ presigned_url: string; filename: string }> => {
    try {
      // The response type is no longer 'blob', it's default JSON
      const response = await api.get(`/programs/${programId}/export`);

      if (!response.data || !response.data.presigned_url) {
        throw new Error('No presigned_url received from export endpoint');
      }

      return response.data as { presigned_url: string; filename: string };
    } catch (error) {
      throw error;
    }
  },

  // Export program info as Word document
  exportProgramInfo: async (programId: number): Promise<{ presigned_url: string; filename: string }> => {
    try {
      const response = await api.get(`/programs/${programId}/export-info`);

      if (!response.data || !response.data.presigned_url) {
        throw new Error('No presigned_url received from export-info endpoint');
      }

      return response.data as { presigned_url: string; filename: string };
    } catch (error) {
      throw error;
    }
  },

  // Helper method to download blob as file - this can now be simplified or adapted
  // For presigned URLs, we often just open the URL in a new tab.
  // However, to control the filename more directly from the frontend if needed,
  // or if we want to use fetch + download, we can adapt this.
  // For now, the primary download mechanism will be opening the presigned URL.
  downloadFileFromUrl: (url: string, filename?: string): void => {
    try {
      const a = document.createElement('a');
      a.style.display = 'none';
      a.href = url;
      // The 'download' attribute might not always work with presigned S3 URLs
      // depending on S3's Content-Disposition header and browser security.
      // The filename is mostly a hint here if the server set Content-Disposition.
      if (filename) {
        a.download = filename;
      }
      document.body.appendChild(a);
      a.click();
      setTimeout(() => {
        document.body.removeChild(a);
        // No need to revokeObjectURL for direct URLs
      }, 500);
    } catch (error) {
      console.error('Error during file download from URL:', error);
      // Fallback: try opening in new tab, browser might handle download
      window.open(url, '_blank');
    }
  },
  
  // Helper method to download blob as file (KEEPING FOR OTHER POTENTIAL USES, BUT NOT FOR S3 EXPORT)
  downloadBlob: (blob: Blob, filename?: string): void => {
    
    try {
      // For browsers that support the download attribute
      // Make sure we don't wrap the blob unnecessarily - it's already a blob
      const url = window.URL.createObjectURL(blob);
      
      // Create a temporary link element
      const a = document.createElement('a');
      a.style.display = 'none';
      a.href = url;
      
      // Set download attribute with filename
      a.download = filename || 'export.xlsx';
      
      // Append to body, click, and remove - needed for Firefox
      document.body.appendChild(a);
      a.click();
      
      // Use a longer timeout to ensure download starts before cleanup
      setTimeout(() => {
        document.body.removeChild(a);
        window.URL.revokeObjectURL(url);
      }, 500);
      
    } catch (error) {
      console.error('Error during file download:', error);
      // Fallback method for older browsers
      const reader = new FileReader();
      reader.onload = function() {
        const dataUrl = reader.result;
        const link = document.createElement('a');
        link.href = dataUrl as string;
        link.download = filename || 'export.xlsx';
        link.click();
      };
      reader.readAsDataURL(blob);
    }
  },

  getProductProgramCategories: async (
    productId: number,
  ): Promise<ProgramCategoryWithCount[]> => {
    const { data } = await api.get(
      `/products/${productId}/program_categories`,
    );
    if (!Array.isArray(data)) {
        throw new Error('Invalid data format received for program categories');
    }
    return data as ProgramCategoryWithCount[];
  },

  // Single method for subroutines with pagination support
  listSubroutines: async (params: ListSubroutinesParams = {}): Promise<SubRoutineListResponse> => {
    const {
      skip = 0,
      limit = 20,
      productId,
      search,
      sortField = 'created_at',
      sortDirection = 'desc'
    } = params;
    let url = `/subroutines/list?skip=${skip}&limit=${limit}`;

    if (productId) {
      url += `&product_id=${productId}`;
    }
    if (search) {
      url += `&search=${encodeURIComponent(search)}`;
    }
    url += `&sort_field=${sortField}&sort_direction=${sortDirection}`;

    const { data } = await api.get(url);
    return data as SubRoutineListResponse;
  },
  
  // Method to fetch programs by category with pagination, search, and sorting
  listProgramsByCategory: async (params: {
    categoryId: number;
    skip?: number;
    limit?: number;
    productId?: number;
    search?: string;
    sortField?: string;
    sortDirection?: 'asc' | 'desc';
  }): Promise<ProgramListResponse> => {
    const {
      categoryId,
      skip = 0,
      limit = 20,
      productId,
      search,
      sortField = 'created_at',
      sortDirection = 'desc'
    } = params;
    
    let url = `/categories/${categoryId}/programs?skip=${skip}&limit=${limit}`;
    
    if (productId) {
      url += `&product_id=${productId}`;
    }
    
    if (search) {
      url += `&search=${encodeURIComponent(search)}`;
    }
    
    // Add sorting parameters
    url += `&sort_field=${sortField}&sort_direction=${sortDirection}`;
    
    const { data } = await api.get(url);
    return data as ProgramListResponse;
  },
  // Get a specific subroutine by ID
  getSubroutineById: async (subroutineId: number): Promise<import('types').SubRoutine> => {
    const { data } = await api.get(`/subroutines/${subroutineId}`);
    return data as import('types').SubRoutine;
  },
  // Create a new program category
  createProgramCategory: async ({
    name,
    description,
  }: {
    name: string;
    description: string;
  }): Promise<any> => {
    const { data } = await api.post('/program-category/create', { name, description });
    return data;
  },
  // Delete a program category by ID
  deleteProgramCategory: async (categoryId: number): Promise<any> => {
    const { data } = await api.delete(`/program-category/${categoryId}/delete`);
    return data;
  },
  // Get a specific program version
  getProgramVersion: async (programId: number, versionNumber: number): Promise<any> => {
    const { data } = await api.get(`/programs/${programId}/versions/${versionNumber}`);
    return data;
  },
  // Delete a specific program version
  deleteProgramVersion: async (programId: number, versionNumber: number): Promise<void> => {
    await api.delete(`/programs/${programId}/versions/${versionNumber}`);
  },

  // Update program status
  updateProgramStatus: async (programId: number, status: string): Promise<any> => {
    try {
      const { data } = await api.patch(`/programs/${programId}/status`, { status });
      return data;
    } catch (error) {
      console.error('Error updating program status:', error);
      throw error;
    }
  },
}