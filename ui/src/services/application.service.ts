import { api } from '@/services/user.service';
import { ApplicationListResponse } from 'types'
export const applicationService = {
  listApplications: async (params: { skip?: number; limit?: number } = {}): Promise<ApplicationListResponse> => {
    const { skip = 0, limit = 20 } = params;
    const response = await api.get(`/chat/list?skip=${skip}&limit=${limit}`);
    return response.data;
  },

  deleteApplication: async (id: number): Promise<void> => {
    const response = await api.delete(`/chat/${id}`);
    return response.data;
  },

};
