import { api } from '@/services/user.service';

export interface ProgramSegment {
  id: number;
  original_program_id: number;
  phase: string;
  parent_segment_id?: number;
  entry_state: Record<string, any>;
  exit_state: Record<string, any>;
  purpose_tags?: string[];
  technique_tags?: string[];
  body_part_tags?: string[];
  intensity_score?: number;
  duration_seconds?: number;
  steps: Record<string, any>[];
}

export interface SegmentHierarchy {
  program_id: number;
  total_segments: number;
  macro_phases: {
    segment: ProgramSegment;
    micro_chunks: ProgramSegment[];
  }[];
  summary: {
    macro_phase_count: number;
    micro_chunk_count: number;
    phases: Record<string, {
      macro_phase_id: number;
      micro_chunk_count: number;
      intensity_score?: number;
      duration_seconds?: number;
    }>;
  };
}

export enum SegmentationJobStatus {
  PENDING = 'pending',
  RUNNING = 'running',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CANCELLED = 'cancelled'
}

export interface SegmentationJob {
  id: number;
  program_id: number;
  status: SegmentationJobStatus;
  progress_percentage: number;
  current_step?: string;
  total_steps: number;
  completed_steps: number;
  error_message?: string;
  started_at?: string;
  completed_at?: string;
  created_at: string;
  updated_at: string;
}

export interface SegmentationJobListResponse {
  jobs: SegmentationJob[];
  total: number;
  page: number;
  limit: number;
}

export interface SegmentCleanupStatistics {
  segments_deleted: number;
  micro_chunks_deleted: number;
  macro_phases_deleted: number;
  jobs_deleted: number;
  vectors_deleted_from_pinecone: number;
  sequences_reset: boolean;
}

export interface SegmentCleanupFilters {
  product_id?: number;
  program_id?: number;
}

export interface SegmentCleanupResponse {
  success: boolean;
  message: string;
  statistics: SegmentCleanupStatistics;
  filters_applied: SegmentCleanupFilters;
}

// --- Product Segmentation Types ---

export interface ProductSegmentationJob {
  id: number;
  product_id: number;
  status: SegmentationJobStatus;
  progress_percentage: number;
  current_step?: string;
  total_programs: number;
  completed_programs: number;
  failed_programs: number;
  error_message?: string;
  started_at?: string;
  completed_at?: string;
  created_at: string;
  updated_at: string;
}

export interface ProductSegmentationJobListResponse {
  jobs: ProductSegmentationJob[];
  total: number;
  page: number;
  limit: number;
}

export interface ProgramSegmentListResponse {
  segments: ProgramSegment[];
  total: number;
  page: number;
  limit: number;
}

export interface SegmentStats {
  total_segments: number;
  macro_phases: number;
  micro_chunks: number;
  avg_intensity: number;
  total_duration: number;
  analyzed_programs: number;
  phase_distribution: {
    front: number;
    main: number;
    cooling: number;
    discovered: number;
  };
}

export interface ListSegmentsParams {
  skip?: number;
  limit?: number;
  program_id?: number;
  phase?: string;
  is_macro_phase?: boolean;
  product_id?: number;
  search?: string;
  sort_field?: string;
  sort_direction?: 'asc' | 'desc';
}

export interface SegmentStatsParams {
  program_id?: number;
  phase?: string;
  is_macro_phase?: boolean;
  product_id?: number;
  search?: string;
}

export const segmentService = {
  // Get segment statistics
  getSegmentStats: async (params: SegmentStatsParams = {}): Promise<SegmentStats> => {
    const {
      program_id,
      phase,
      is_macro_phase,
      product_id,
      search
    } = params;

    let url = '/segments/stats';
    const queryParams = new URLSearchParams();

    if (program_id) {
      queryParams.append('program_id', program_id.toString());
    }
    if (phase) {
      queryParams.append('phase', phase);
    }
    if (is_macro_phase !== undefined) {
      queryParams.append('is_macro_phase', is_macro_phase.toString());
    }
    if (product_id) {
      queryParams.append('product_id', product_id.toString());
    }
    if (search) {
      queryParams.append('search', search);
    }

    if (queryParams.toString()) {
      url += `?${queryParams.toString()}`;
    }

    const { data } = await api.get(url);
    return data as SegmentStats;
  },

  // Get paginated list of segments
  listSegments: async (params: ListSegmentsParams = {}): Promise<ProgramSegmentListResponse> => {
    const {
      skip = 0,
      limit = 20,
      program_id,
      phase,
      is_macro_phase,
      product_id,
      search,
      sort_field = 'id',
      sort_direction = 'asc'
    } = params;

    let url = `/segments?skip=${skip}&limit=${limit}`;

    if (program_id) {
      url += `&program_id=${program_id}`;
    }
    if (phase) {
      url += `&phase=${encodeURIComponent(phase)}`;
    }
    if (is_macro_phase !== undefined) {
      url += `&is_macro_phase=${is_macro_phase}`;
    }
    if (product_id) {
      url += `&product_id=${product_id}`;
    }
    if (search) {
      url += `&search=${encodeURIComponent(search)}`;
    }
    url += `&sort_field=${sort_field}&sort_direction=${sort_direction}`;

    const { data } = await api.get(url);
    return data as ProgramSegmentListResponse;
  },

  // Get a specific segment by ID
  getSegmentById: async (segmentId: number): Promise<ProgramSegment> => {
    const { data } = await api.get(`/segments/${segmentId}`);
    return data as ProgramSegment;
  },

  // Get all segments for a specific program
  getProgramSegments: async (
    programId: number, 
    includeMicroChunks: boolean = true
  ): Promise<ProgramSegment[]> => {
    const { data } = await api.get(
      `/programs/${programId}/segments?include_micro_chunks=${includeMicroChunks}`
    );
    return data as ProgramSegment[];
  },

  // Get segment hierarchy for a specific program
  getProgramSegmentHierarchy: async (programId: number): Promise<SegmentHierarchy> => {
    const { data } = await api.get(`/programs/${programId}/segments/hierarchy`);
    return data as SegmentHierarchy;
  },

  // Get macro phases only for a program
  getMacroPhases: async (programId: number): Promise<ProgramSegment[]> => {
    const response = await segmentService.listSegments({
      program_id: programId,
      is_macro_phase: true,
      sort_field: 'id',
      sort_direction: 'asc'
    });
    return response.segments;
  },

  // Get micro chunks for a specific macro phase
  getMicroChunks: async (macroPhaseId: number): Promise<ProgramSegment[]> => {
    const response = await segmentService.listSegments({
      is_macro_phase: false,
      sort_field: 'id',
      sort_direction: 'asc'
    });
    return response.segments.filter(segment => segment.parent_segment_id === macroPhaseId);
  },

  // Search segments by tags
  searchSegmentsByTags: async (
    searchTerm: string,
    productId?: number
  ): Promise<ProgramSegment[]> => {
    const response = await segmentService.listSegments({
      search: searchTerm,
      product_id: productId,
      limit: 50
    });
    return response.segments;
  },

  // Get segments by phase
  getSegmentsByPhase: async (
    phase: string,
    productId?: number
  ): Promise<ProgramSegment[]> => {
    const response = await segmentService.listSegments({
      phase,
      product_id: productId,
      limit: 100
    });
    return response.segments;
  },

  // Segmentation Job functions
  startProgramSegmentation: async (programId: number): Promise<SegmentationJob> => {
    const { data } = await api.post(`/programs/${programId}/segmentation/start`);
    return data as SegmentationJob;
  },

  getSegmentationJob: async (jobId: number): Promise<SegmentationJob> => {
    const { data } = await api.get(`/segmentation-jobs/${jobId}`);
    return data as SegmentationJob;
  },

  getProgramSegmentationJobs: async (
    programId: number,
    skip: number = 0,
    limit: number = 10
  ): Promise<SegmentationJobListResponse> => {
    const { data } = await api.get(`/programs/${programId}/segmentation-jobs`, {
      params: { skip, limit }
    });
    return data as SegmentationJobListResponse;
  },

  cancelSegmentationJob: async (jobId: number): Promise<SegmentationJob> => {
    const { data } = await api.post(`/segmentation-jobs/${jobId}/cancel`);
    return data as SegmentationJob;
  },

  // Cleanup functions
  clearSegmentsAndJobs: async (params?: {
    product_id?: number;
    program_id?: number;
  }): Promise<SegmentCleanupResponse> => {
    let url = '/segments/clear';
    const queryParams = new URLSearchParams();

    if (params?.product_id) {
      queryParams.append('product_id', params.product_id.toString());
    }
    if (params?.program_id) {
      queryParams.append('program_id', params.program_id.toString());
    }

    if (queryParams.toString()) {
      url += `?${queryParams.toString()}`;
    }

    const { data } = await api.delete(url);
    return data as SegmentCleanupResponse;
  },

  // --- Product Segmentation Methods ---

  startProductSegmentation: async (productId: number): Promise<ProductSegmentationJob> => {
    const { data } = await api.post(`/products/${productId}/segmentation`);
    return data as ProductSegmentationJob;
  },

  cancelProductSegmentationJob: async (jobId: number): Promise<ProductSegmentationJob> => {
    const { data } = await api.post(`/product-jobs/${jobId}/cancel`);
    return data as ProductSegmentationJob;
  },

  getProductSegmentationJob: async (jobId: number): Promise<ProductSegmentationJob> => {
    const { data } = await api.get(`/product-jobs/${jobId}`);
    return data as ProductSegmentationJob;
  },

  getProductSegmentationJobs: async (
    productId: number,
    skip: number = 0,
    limit: number = 10
  ): Promise<ProductSegmentationJobListResponse> => {
    const { data } = await api.get(`/products/${productId}/jobs`, {
      params: { skip, limit }
    });
    return data as ProductSegmentationJobListResponse;
  }
};
