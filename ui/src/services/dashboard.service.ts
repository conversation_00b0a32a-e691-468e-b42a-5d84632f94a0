import { api } from './user.service'; // Use the shared api instance

export interface DashboardStats {
  totalPrograms: number;
  totalProducts: number;
  totalChatSessions: number;
  programsByCategory: Record<string, number>;
  totalUsers: number;
}

export const getDashboardStats = async (): Promise<DashboardStats> => {
  const response = await api.get<DashboardStats>('/dashboard/stats');
  return response.data;
};
export interface ChatPerDay {
  date: string;
  count: number;
}

export const getChatsPerDay = async (days = 30): Promise<ChatPerDay[]> => {
  const response = await api.get<ChatPerDay[]>('/dashboard/chats-per-day', {
    params: { days },
  });
  return response.data;
};


/**
 * Product Chat Activity API types and function
 */

export interface ProductChatActivityTimePoint {
  timestamp: string;
  product_chats: Record<string, number>;
  product_names: Record<string, string>;
}

export interface ProductChatActivityResponse {
  data: ProductChatActivityTimePoint[];
  query_details: {
    period: string;
    granularity: string;
    products_returned: string[];
    [key: string]: any;
  };
}

export interface ProductChatActivityParams {
  period?: string;
  granularity?: string;
  product_ids?: string[] | string;
  limit?: number;
}

/**
 * Fetches non-cumulative chat counts per product over time intervals.
 * @param params period, granularity, product_ids, limit
 */
export const getProductChatActivity = async (
  params: ProductChatActivityParams = {}
): Promise<ProductChatActivityResponse> => {
  const { product_ids, ...rest } = params;
  const period = rest.period ?? "10d";
  const granularity = rest.granularity ?? "daily";
  return (
    await api.get<ProductChatActivityResponse>('/dashboard/product-chat-activity', {
      params: {
        ...rest,
        period,
        granularity,
        ...(product_ids
          ? { product_ids: Array.isArray(product_ids) ? product_ids.join(',') : product_ids }
          : {}),
      },
    })
  ).data;
};

/**
 * Product Program Generation Activity API types and function
 */

export interface ProductProgramGenerationActivityTimePoint {
  timestamp: string;
  product_program_counts: Record<string, number>;
  product_names: Record<string, string>;
}

export interface ProductProgramGenerationActivityResponse {
  data: ProductProgramGenerationActivityTimePoint[];
  query_details: {
    period: string;
    granularity: string;
    products_returned: string[];
    [key: string]: any;
  };
}

export interface ProductProgramGenerationActivityParams {
  period?: string;
  granularity?: string;
  product_ids?: string[] | string;
  limit?: number;
}

/**
 * Fetches non-cumulative program generation counts per product over time intervals.
 * @param params period, granularity, product_ids, limit
 */
export const getProductProgramGenerationActivity = async (
  params: ProductProgramGenerationActivityParams = {}
): Promise<ProductProgramGenerationActivityResponse> => {
  const { product_ids, ...rest } = params;
  const period = rest.period ?? "10d";
  const granularity = rest.granularity ?? "daily";
  return (
    await api.get<ProductProgramGenerationActivityResponse>('/dashboard/product-program-generation-activity', {
      params: {
        ...rest,
        period,
        granularity,
        ...(product_ids
          ? { product_ids: Array.isArray(product_ids) ? product_ids.join(',') : product_ids }
          : {}),
      },
    })
  ).data;
};