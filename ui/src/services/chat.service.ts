import { ChatList, ChatStreamResponse, CreateConfigRequest, CreateConfigResponse, ListModelsResponse, SendMessageRequest, ThreadsResponse, ThreadWithMessagesResponse } from 'types'
import { api } from './user.service' // Import the shared, configured Axios instance

// Removed local api instance and interceptor

interface ExtendedXMLHttpRequest extends XMLHttpRequest {
  seenBytes?: number;
  buffer?: string;
}

// Removed local auth interceptor - it's now handled globally in user.service.ts

export const chatService = {
  listModels: async (): Promise<ListModelsResponse> => {
    const { data } = await api.get('/chat/models')
    return data
  },

  createConfig: async (payload: CreateConfigRequest): Promise<CreateConfigResponse> => {
    const { data } = await api.post('/chat/create_config', payload)
    return data
  },

  updateConfig: async (id: number, payload: CreateConfigRequest): Promise<CreateConfigResponse> => {
    const { data } = await api.put(`/chat/update_config/${id}`, payload)
    return data
  },

  listConfigs: async (): Promise<ChatList> => {
    const { data } = await api.get('/chat/list')
    return data
  },

  getConfig: async (id: number): Promise<CreateConfigResponse> => {
    const { data } = await api.get(`/chat/list/${id}`)
    return data
  },

  sendMessage: async ({ message, slug, chat_config_id, onToken }: SendMessageRequest & { 
    onToken?: (response: ChatStreamResponse) => void 
  }): Promise<ChatStreamResponse> => {
    const { data } = await api.post('/chat/program', 
      { message, slug, chat_config_id },
      {
        responseType: 'stream',
        timeout: 60000 * 5, // Increased timeout to 5 minutes
        onDownloadProgress: (progressEvent) => {
          const target = progressEvent.event.target as ExtendedXMLHttpRequest;
          const newData = target.response.slice(target.seenBytes || 0);
          target.seenBytes = target.response.length;
          
          // Initialize buffer if needed
          if (!target.buffer) target.buffer = '';
          
          // Append new data to buffer
          target.buffer += newData;
          
          // Split buffer by newline and process complete lines
          const lines = target.buffer.split('\n');
          
          // Keep the last (potentially incomplete) line in the buffer
          target.buffer = lines.pop() || '';
          
          for (const line of lines) {
            const trimmedLine = line.trim();
            if (trimmedLine.startsWith('data: ')) {
              try {
                const response = JSON.parse(trimmedLine.replace('data: ', '')) as ChatStreamResponse;
                onToken?.(response);
              } catch (e) {
                console.warn('Failed to parse streaming response:', e);
              }
            }
          }
        }
      }
    );

    return data;
  },

  listThreads: async (params: { skip?: number; limit?: number; search?: string; sortField?: string; sortDirection?: 'asc' | 'desc'; starredOnly?: boolean; } = {}): Promise<ThreadsResponse> => {
    const { skip = 0, limit = 20, search, sortField = 'updated_at', sortDirection = 'desc', starredOnly = false } = params;
    let url = `/chat/threads?skip=${skip}&limit=${limit}&sort_field=${sortField}&sort_direction=${sortDirection}`;

    if (search) {
      url += `&search=${encodeURIComponent(search)}`;
    }

    if (starredOnly) {
      url += `&starred_only=true`;
    }

    const { data } = await api.get(url);
    return data
  },


  getThreadMessages: async (threadId: string): Promise<ThreadWithMessagesResponse> => {
    const { data } = await api.get(`/chat/thread/${threadId}/messages`);
    return data;
  },

  // New program interaction methods
  updateProgramLike: async (programId: number): Promise<{ success: boolean; liked: boolean; disliked: boolean; saved: boolean }> => {
    const { data } = await api.post('/program/like', { program_id: programId });
    return data;
  },
  
  updateProgramDislike: async (programId: number): Promise<{ success: boolean; liked: boolean; disliked: boolean; saved: boolean }> => {
    const { data } = await api.post('/program/dislike', { program_id: programId });
    return data;
  },
  
  updateProgramSave: async (programId: number): Promise<{ success: boolean; liked: boolean; disliked: boolean; saved: boolean }> => {
    const { data } = await api.post('/program/save', { program_id: programId });
    return data;
  },

  // Chat star functionality
  starChat: async (chatId: number): Promise<{ success: boolean; is_starred: boolean }> => {
    const { data } = await api.post('/chat/star', { chat_id: chatId });
    return data;
  },

}
