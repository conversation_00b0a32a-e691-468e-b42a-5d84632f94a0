import { api } from '@/services/user.service';
import { ProductCreateRequest, ProductListResponse, ProductResponse } from 'types'

// Add this type definition near the top or in a types file
export interface ProductMutationPayload extends Omit<ProductCreateRequest, 'features'> {
  features?: string[]; // Keep features as array for frontend state
  image?: File | null; // Add optional image file
  image_url?: string | null; // To signal removal on update
}


export const productService = {
  listProducts: async (params: { skip?: number; limit?: number } = {}): Promise<ProductListResponse> => {
    const { skip = 0, limit = 20 } = params;
    const { data } = await api.get(`/product/list?skip=${skip}&limit=${limit}`);
    return data
  },

  // Fetch all products (no pagination)
  getAllProducts: async (): Promise<Array<{ id: number; name: string }>> => {
    const { data } = await api.get('/products');
    // If the API returns more fields, that's fine; we only need id and name for the widget.
    return data;
  },

  getProduct: async (id: number): Promise<ProductResponse> => {
    const { data } = await api.get(`/product/${id}`);
    return data
  },

  createProduct: async (payload: ProductMutationPayload): Promise<ProductResponse> => {
    const formData = new FormData();
    // Append all fields except the image file itself
    Object.entries(payload).forEach(([key, value]) => {
      if (key !== 'image' && value !== undefined && value !== null) {
        // Convert features array to JSON string if backend expects it that way
        // Handle features array: append each feature individually
        if (key === 'features' && Array.isArray(value)) {
          value.forEach(feature => {
            formData.append('features', feature); // Append each feature with the same key name
          });
        } else {
          // Append other non-null, non-undefined values as strings
          formData.append(key, String(value));
        }
      }
    });

    // Append the image file if it exists
    if (payload.image) {
      formData.append('image', payload.image);
    }

    // Axios automatically sets Content-Type to multipart/form-data for FormData
    const { data } = await api.post('/product/create', formData);
    return data
  },

  updateProduct: async (id: number, payload: ProductMutationPayload): Promise<ProductResponse> => {
     const formData = new FormData();
     // Append all fields except the image file itself
     Object.entries(payload).forEach(([key, value]) => {
       // Handle image_url specifically for removal signal
       if (key === 'image_url' && value === null) {
         // Send the key 'image_url' with an empty string value.
         // The backend view checks `product.image_url is None` after Pydantic parsing.
         // Pydantic might interpret an empty string as None for an Optional[str] field,
         // or we might need adjustment based on testing. Let's assume empty string works.
         formData.append(key, '');
       } else if (key !== 'image' && value !== undefined && value !== null) {
         // Append other non-null, non-undefined values
         // Handle features array: append each feature individually
         if (key === 'features' && Array.isArray(value)) {
           value.forEach(feature => {
             formData.append('features', feature); // Append each feature with the same key name
           });
         } else {
           // Append other non-null, non-undefined values as strings
           formData.append(key, String(value));
         }
       }
     });

     // Append the new image file if it exists
     if (payload.image) {
       formData.append('image', payload.image);
     }
     // Note: If payload.image is null or undefined, and image_url is not explicitly set to null/empty,
     // the existing image on the backend will be retained.

     const { data } = await api.put(`/product/${id}`, formData);
     return data
  },

  deleteProduct: async (id: number): Promise<{ status: string }> => {
    const { data } = await api.delete(`/product/${id}/delete`);
    return data
  }
}