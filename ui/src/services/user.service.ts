import axios from 'axios';
import { fetchAuthSession } from 'aws-amplify/auth';
import { useAuthStore } from '@/stores/auth.store';

export const api = axios.create({
  baseURL: process.env.NEXT_PUBLIC_API_URL || '/api2',
  timeout: 30000,
});

// Request interceptor to inject and potentially refresh the token
api.interceptors.request.use(
  async (config) => {
    try {
      // fetchAuthSession automatically handles refresh if needed
      const session = await fetchAuthSession({ forceRefresh: false });
      const accessToken = session.tokens?.accessToken?.toString();

      if (accessToken) {
        config.headers.Authorization = `Bearer ${accessToken}`;
        // Update the store with the latest token (optional but good practice)
        useAuthStore.getState().setAccessToken(accessToken);
      } else {
        // Handle case where session exists but no tokens (shouldn't typically happen with Cognito)
        console.warn('Amplify session fetched but no access token found.');
        // Optionally clear auth state or throw error if token is strictly required
        // useAuthStore.getState().reset();
        // throw new Error('Authentication token not available.');
      }
    } catch (error) {
      // This catch block handles errors during fetchAuthSession (e.g., refresh token expired)
      console.error('Error fetching auth session or refreshing token:', error);
      // Clear auth state as the session is likely invalid
      useAuthStore.getState().reset();
      // Re-throw the error or throw a custom one to prevent the request from proceeding
      throw new Error('Failed to authenticate session.');
    }
    return config;
  },
  (error) => {
    // Handle request configuration errors
    return Promise.reject(error);
  }
);

// Response interceptor to handle 401 errors globally
api.interceptors.response.use(
  (response) => {
    // Any status code that lie within the range of 2xx cause this function to trigger
    return response;
  },
  (error) => {
    // Any status codes that falls outside the range of 2xx cause this function to trigger
    if (error.response?.status === 401) {
      console.error('Received 401 Unauthorized. Clearing auth state.');
      // Clear the user session data from the store.
      // This should trigger the UI (via Authenticator or other logic) to prompt for login.
      useAuthStore.getState().reset();
      // Optionally redirect or perform other logout actions here if needed,
      // but clearing the store might be sufficient depending on AppProvider setup.
    }
    // Return the error so that calling code can handle it if needed
    return Promise.reject(error);
  }
);


export const userService = {
  getAccount: async (cognitoId: string) => {
    const { data } = await api.get(`/account/get/${cognitoId}`)
    return data
  },
  listAccounts: async () => {
    const { data } = await api.get('/account/list')
    return data
  },
  updateAccountRoles: async (cognitoId: string, roles: string[]) => {
    const { data } = await api.put(`/account/${cognitoId}/roles`, { 
      cognito_id: cognitoId,
      roles 
    })
    return data
  }
}
