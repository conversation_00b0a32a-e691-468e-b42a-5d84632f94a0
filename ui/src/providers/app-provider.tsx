'use client';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import { Authenticator } from '@aws-amplify/ui-react';
import ThemeProvider from './theme-provider';
import { Amplify } from 'aws-amplify';
import '@aws-amplify/ui-react/styles.css';
import { useUser } from '@/hooks/auth.hooks';
import '../app/globals.css';

Amplify.configure({
  Auth: {
    Cognito: {
      userPoolId: 'us-east-1_Qkklle9VL',
      userPoolClientId: '5lnolt60mqg5un19kimnekapb'
    }
  }
});

const formFields = {
  signUp: {
    username: {
      order: 1,
      placeholder: 'Choose a username',
      label: 'Username',
      inputprops: { required: true }
    },
    email: {
      order: 1,
      placeholder: 'Enter your email address',
      label: 'Email',
      inputprops: { type: 'email', required: true }
    },
    password: {
      order: 3,
      placeholder: 'Enter your password',
      label: 'Password',
      inputprops: { type: 'password', required: true }
    },
    confirm_password: {
      order: 4,
      placeholder: 'Confirm your password',
      label: 'Confirm Password',
      inputprops: { type: 'password', required: true }
    }
  }
};

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 1000 * 60 * 5, // 5 minutes
      refetchOnWindowFocus: false
    }
  }
});

function AuthenticatedContent({ children }: { children: React.ReactNode }) {
  const { isLoading, error, data: user } = useUser();

  // Add check for existing user data to prevent unnecessary loading states
  if (user) {
    return <>{children}</>;
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
      </div>
    )
  }

  if (error) {
    return <div>Error loading user data</div>;
  }

  return <>{children}</>;
}

export function AppProvider({ children }: { children: React.ReactNode }) {
  return (
    <QueryClientProvider client={queryClient}>
      <ThemeProvider attribute='class' defaultTheme='light' enableSystem>
        <Authenticator
          formFields={formFields}
          variation="modal"
          className="!bg-white"
        >
          {({ user }) =>
            user ? (
              <AuthenticatedContent>{children}</AuthenticatedContent>
            ) : (
              <div className="flex flex-col items-center justify-center min-h-screen p-4">
                <h1 className="text-xl font-semibold mb-4">Please sign in</h1>
              </div>
            )
          }
        </Authenticator>
        <ReactQueryDevtools />
      </ThemeProvider>
    </QueryClientProvider>
  );
}
