# Stage 1: Builder
FROM node:20-slim AS builder

WORKDIR /app

# Copy package manager files and .npmrc
# Prefer package-lock.json or pnpm-lock.yaml for reproducible builds
COPY package.json pnpm-lock.yaml* ./ 
# If you use .npmrc for private registries or specific configs
COPY .npmrc* ./

# Install all dependencies (including devDependencies needed for build)
# Use `npm ci` for faster, more reliable builds if you have a package-lock.json
# Or, if using pnpm (as pnpm-lock.yaml suggests you might be):
RUN corepack enable && pnpm install --frozen-lockfile
# RUN npm ci

# Copy the rest of the application source code
COPY . .

# Build the Next.js application
RUN pnpm run build # Assuming your build script is defined in package.json and runnable with pnpm

# Optional: Prune devDependencies if they are not needed by pm2 or the runtime
# but often `npm run build` output is self-contained or pm2 needs some parts of node_modules.
# If your build output is entirely standalone in .next, then this is safer:
# RUN pnpm prune --prod 

# Stage 2: Production
FROM node:20-slim AS production

ENV NODE_ENV=production
ENV HOME=/home/<USER>

WORKDIR /app

# Create a non-root user and group for better security
RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 --home /home/<USER>
RUN mkdir -p /home/<USER>/.pm2 && chown -R nextjs:nodejs /home/<USER>

# Copy only necessary files from the builder stage
COPY --from=builder /app/package.json /app/pnpm-lock.yaml* ./ 
# COPY --from=builder /app/.npmrc* ./ # If .npmrc is needed for prod dependencies for pm2, unlikely

# Install production dependencies only, ignoring scripts (like husky prepare)
# If using pnpm:
RUN corepack enable && pnpm install --prod --frozen-lockfile --ignore-scripts
# RUN npm ci --omit=dev 

COPY --from=builder --chown=nextjs:nodejs /app/.next ./.next
COPY --from=builder /app/public ./public
# Copy any other static assets or necessary config files that `next start` needs
# For example, if next.config.js is used by next start, copy it.
COPY --from=builder /app/next.config.js ./

# Install pm2 globally
# Note: pm2 will be installed using npm here, as it's a global tool.
# If you want to manage pm2 with pnpm, you might need a different approach
# or ensure pnpm is available in the PATH for the nextjs user.
RUN npm install -g pm2

# Ensure PM2 directories exist and have proper permissions
RUN mkdir -p /home/<USER>/.pm2/logs /home/<USER>/.pm2/pids /home/<USER>/.pm2/modules && \
    touch /home/<USER>/.pm2/pm2.pid /home/<USER>/.pm2/module_conf.json && \
    chown -R nextjs:nodejs /home/<USER>/.pm2

USER nextjs

EXPOSE 3000

# CMD for pm2. Ensure your package.json "start" script is `next start`
# and is runnable by pnpm if you change "npm" to "pnpm" here.
CMD ["pm2-runtime", "start", "pnpm", "--name", "client", "--", "start"]

# Tip:
# 1. Ensure your .dockerignore file is comprehensive to keep build context small.
#    (e.g., ignore .git, node_modules in host, .env, local build artifacts)
# 2. Use Docker BuildKit for faster builds: Set DOCKER_BUILDKIT=1 environment variable when building.