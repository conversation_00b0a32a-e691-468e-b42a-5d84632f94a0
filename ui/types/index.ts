import { Icons } from '@/components/icons';

export interface NavItem {
  title: string;
  url: string;
  disabled?: boolean;
  external?: boolean;
  shortcut?: [string, string];
  icon?: keyof typeof Icons;
  label?: string;
  description?: string;
  isActive?: boolean;
  items?: NavItem[];
}

export interface ListSubroutinesParams {
  skip?: number;
  limit?: number;
  productId?: number;
  search?: string;
  sortField?: string;
  sortDirection?: 'asc' | 'desc';
}

export interface NavItemWithChildren extends NavItem {
    items: NavItemWithChildren[];
}

export interface NavItemWithOptionalChildren extends NavItem {
  items?: NavItemWithChildren[];
}

export interface FooterItem {
  title: string;
  items: {
    title: string;
    href: string;
    external?: boolean;
  }[];
}

export interface PineconeInvokeRequest {
  input: string;
  config: any;
  kwargs: any;
}

export interface PineconeInputSchemaResponse {
  title: string;
  type: string;
}

export interface PineconeOutputSchemaResponse {
  title: string;
  type: string;
}

export interface PineconeConfigSchemaResponse {
  properties: object;
  title: string;
  type: string;
}


export interface PineconeResponse {
  output: string[];
  metadata: PineconeMetadata;
}


export interface PineconeMetadata {
  responses: Array<{
    run_id: string;
    feedback_tokens: any[];
  }>;
  run_ids: string[];
}


export interface PineconeStreamLog {
  input: string;
  config: any;
  include_names?: string[];
  include_types?: string[];
  include_tags?: string[];
  exclude_names?: string[];
  exclude_types?: string[];
  exclude_tags?: string[];
  kwargs: any;
}


export interface PineconeStreamResponse {
  id: string;
  streamed_output: string[];
  final_output: string | null;
  logs: any;
  name: string;
  type: string;
}

// Data Sources
export interface SourceResponse {
  id: number;
  name: string;
  description?: string;
  status: string;
  created_at: string;
  s3_bucket_url?: string;
}

export interface SourceListResponse {
  sources: SourceResponse[];
  total: number;
}

export interface CreateSourceRequest {
  name: string;
  description: string;
  s3_bucket_url: string;
}


export interface UploadSourceFileRequest {
  files: Array<{
    name: string;
    file_type: string;
    vector_id?: string;
    content: string | File;
  }>;
}
export interface UploadSourceFileResponse {
  source: {
    id: number;
    name: string;
    description: string;
    created_at: string;
  };
  files: Array<{
    id: number;
    name: string;
    status: string;
    file_type: string;
    size: number;
    created_at: string;
  }>;
}

export interface FileDetailResponse {
  page_content: string;
  metadata: any;
}

export interface DeleteFileFromSourceResponse {
  status: string;
}


// Chat
export interface ChatModel {
  id: number;
  name: string;
  provider: string;
}

export interface ListModelsResponse {
  models: ChatModel[];
}


export interface CreateConfigRequest {
  name: string;
  model_id: number;
  product_id?: number | null;
  sft_model_name?: string | null;
  synthesis_strategy?: string | null;
}


export interface CreateConfigResponse {
  id: number;
  name: string;
  url: string;
  model_id: number;
  product_id?: number | null;
  created_at: string;
  updated_at: string;
  sft_model_name?: string | null;
  use_sft_direct_generation: boolean;
  synthesis_strategy?: string | null;
}

export interface ChatList extends Array<CreateConfigResponse> {}
export type Application = {
  id: number;
  name: string;
}

export interface SendMessageRequest {
  slug: string;
  chat_config_id: number;
  message: string;
}

export interface ChatStreamResponse {
  message_id: number;
  type:
    | 'token'
    // Document[] will use the more specific Document type defined below
    | 'documents'
    // Program[] will use the more specific Program type defined below
    | 'programs'
    | 'target_sequence'
    | 'synthesized_steps' // Added synthesized_steps
    | 'program_plan' // Added for segment-aware synthesis program plan

    | 'error' // Assuming an error type might exist
    | 'info' // Assuming an info type might exist
    | 'massage_program_details'; // Added for massage_program_details type
  slug?: string; // Thread slug, usually sent with the first token
  content:
    | string // For 'token', 'massage_program_details' (JSON string) type
    | Document[] // For 'documents' type, uses the Document interface below
    | Program[] // For 'programs' type, uses the Program interface below
    | TargetSequence // For 'target_sequence' type
    | HighEndProductStep[] // Changed for 'synthesized_steps' type
    | ProgramPlan // For 'program_plan' type

  program_id?: number; // New field, sent with 'synthesized_steps'
}

// This is the primary Document interface
export interface Document {
  page_content: string;
  metadata: {
    file_id: string;
    file_name: string;
    page: number;
    page_label: string;
    source: string;
    source_id: string;
  };
}

export interface Thread {
  id: number;
  slug: string;
  first_message: string | null;
  config_id: number;
  config_name: string;
  is_starred: boolean;
  created_at: string;
  updated_at: string;
}


export interface ThreadsResponse {
  threads: Thread[];
  total: number;
}

export interface ApplicationListResponse {
  chat_configs: Application[];
  total: number;
}

export interface Message {
  id: number;
  content: string;
  role:
    | 'user'
    | 'assistant'
    | 'programs'
    | 'target_sequence'
    | 'synthesized_steps'
    | 'error'
    | 'massage_program_details'
    | 'program_plan'
; // Added for segment-aware synthesis message types
  documents?: Document[];
  programs?: Program[];
  target_sequence?: TargetSequence;
  program_id?: number;
  synthesized_steps?: HighEndProductStep[];
  massageProgramDetails?: MassageProgramDetails; // Added for massage_program_details data
  program_plan?: ProgramPlan; // Added for program_plan data

  program_liked?: boolean;
  program_disliked?: boolean;
  program_saved?: boolean;
}

export interface ThreadWithMessagesResponse {
  thread: Thread;
  messages: Message[];
}

export type SequenceAction = {
  id?: number;
  order: number;
  action: string;
  params: number | null;
  speed?: string | null;
  position3D?: number | null;
  width?: string | null;
  hasError?: boolean;
  errorMessage?: string;
};

export type SubroutineData = {
  id: string;
  name: string;
  actions: SequenceAction[];
};

export type bodyPositionMapType = {
  [key: string]: { top: number; left: number };
};


export interface Product {
  id: number;
  name: string;
  description: string;
  model: string;
  series?: string;
  type?: string;
  subtype?: string;
  features?: string[];
  image_url?: string | null; // Added optional image URL
  created_at: string;
  updated_at?: string; // Also add updated_at if backend sends it
}

export interface ProductCreateRequest {
  name: string;
  description: string;
  model: string;
  series?: string;
  type?: string;
  subtype?: string;
  features?: string[];
}

export interface ProductResponse extends Product {}

export interface ProductListResponse {
  products: Product[];
  total: number;
}


export interface SequenceSetResponse {
  id: number;
  name: string;
  description: string;
  user_id: number;
  product_id: number;
  product_name: string;
  created_at: string;
}

export interface SequenceListResponse {
  sequences: SequenceAction[];
  total: number;
}

export interface SequenceSetListResponse {
  sequence_sets: SequenceSetResponse[];
  total: number;
}

// Add the new type here
export interface SubroutineListResponse {
  subroutines: SubroutineData[];
}


export type MainNavItem = NavItemWithOptionalChildren;

export type SidebarNavItem = NavItemWithChildren;

// Program & Subroutine Types
export interface ProgramCategoryWithCount {
  id: number;
  name: string;
  description?: string | null;
  createdAt: string; // Or Date
  updatedAt: string; // Or Date
  programCount: number;
}

// Simple Subroutine type for list view
export interface SubRoutine {
  id: number;
  name: string;
  subroutineIdJson: string; // Matches alias from SubRoutineListSchema
  product_id: number;
  product_name?: string;
  actions_count?: number;
  created_at: string;
  updated_at?: string;
}

export interface SubRoutineListResponse {
  subroutines: SubRoutine[];
  total: number;
}

export interface Program {
  id: number;
  name: string;
  program_title: string;
  source_folder: string;
  source_filename: string;
  relevance_score?: number;
  target_objective_text?: string;
  target_objective_json?: string;
  intensity_text?: string;
  intensity_json?: number;
  targeted_acupressure_points?: string[];
  signature_moves?: string[];
  logic_technique?: string;
  source_notes?: string;
  overall_settings?: any;
  step_count?: number;
  status: 'Draft' | 'In Progress' | 'Ready for Testing' | 'Tested' | 'Verified';
}

// Program version type
export interface ProgramVersion {
  id: number;
  program_id: number;
  version_number: number;
  steps: any[];
  created_at: string;
  created_by_user_id: number;
  created_by_username?: string;
  version_notes?: string;
}

// Full detail for a single program (matches backend ProgramSchema)
export interface ProgramDetail {
  id: number;
  name?: string;
  source_folder: string;
  source_filename: string;
  logic_technique?: string;
  program_title: string;
  source_notes?: string;
  program_description?: ProgramDescription | null;
  overall_settings?: Record<string, any> | null;
  steps: any[];
  column_mapping_used?: Record<string, any>;
  product_id: number;
  category_id: number;
  subRoutines: any[]; // Standardizing on camelCase naming
  current_version_number: number; // Add current version number
  versions: ProgramVersion[]; // Add versions array
  created_at?: string;
  updated_at?: string;
  product_name?: string | null;
  product_model?: string | null;
  category_name?: string | null;
  status: 'Draft' | 'In Progress' | 'Ready for Testing' | 'Tested' | 'Verified';
}

export interface ProgramListResponse {
  programs: Program[];
  total: number;
}

export interface ProgramDescription {
  intensity?: number;
  program_name?: string;
  recommended_position?: string;
  target_group_objective?: string;
  programme_sequence?: {
    [key: string]: string;
  };
  targeted_acupressure_points?: string[];
  signature_moves?: string[];
  limitations?: string;
}

// Add new TargetSequence interface
export interface TargetSequence {
  name: string; // Added missing name property
  objective: string;
  programme_sequence: {
    front_sequence: string;
    main_sequence: string;
    cooling_sequence: string;
  };
  signature_moves: string[];
  targeted_acupressure_points: string[];
}

// Added for synthesized steps
export interface HighEndProductStep {
  step_number?: string | null;
  roller_action_description?: string | null;
  air_action_description?: string | null;
  kneading_speed?: number | null;
  tapping_speed?: number | null;
  position_3d?: (string | number | null)[] | null;
  width?: string | null;
  seat_program?: string | null;
  scent?: string | null;
  light?: string | null;
  type?: string | null;
  subroutine_id?: string | null;
}

export interface HighEndProductSteps {
  steps: HighEndProductStep[];
}

// --- Massage Program Details Types (updated for new schema) ---

// Enum types matching the backend
export type DurationEnum = '15' | '30';
export type IntensityEnum = 'weak' | 'medium' | 'strong';
export type WarmAirSettingEnum = 'Off' | 'Low' | 'High';
export type VibrationSettingEnum = 'Low' | 'High' | 'Auto' | 'Off';
export type AirBagsEnum = 'Wide' | 'Narrow';

export interface ProgramFlowDetails {
  highlights?: string | null;
  signature_moves?: string | null;
  logic_techniques?: string | null;
  intensity?: IntensityEnum | null;
}

export interface MassageSettingsDetails {
  warm_air?: WarmAirSettingEnum | null;
  vibration?: VibrationSettingEnum | null;
  air_bags?: AirBagsEnum | null;
  scent?: boolean | null;
  mood_light?: boolean | null;
}

export interface MassageProgramDetails {
  target_market: string; // Now required with default "18 to 65"
  duration: DurationEnum; // Now required enum
  focus_area: string[]; // Now required with default ["Full body"]
  problem_desired_outcome: string; // Now required
  program_flow?: ProgramFlowDetails | null;
  massage_settings?: MassageSettingsDetails | null;
}

// --- Segment-aware synthesis types ---

export interface PhaseSearchInstruction {
  body_parts?: string[];
  techniques?: string[];
  purposes?: string[];
  intensity_range?: string;
  description_keywords?: string[];
}

export interface ProgramPlan {
  program_strategy: string;
  overall_intensity?: string;
  front_phase: PhaseSearchInstruction;
  main_phase: PhaseSearchInstruction;
  cooling_phase: PhaseSearchInstruction;
  focus_priorities?: string[];
  avoid_characteristics?: string[];
  special_requirements?: string;
}



