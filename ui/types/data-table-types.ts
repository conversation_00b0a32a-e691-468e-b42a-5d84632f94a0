import { ColumnDef, Table} from "@tanstack/react-table"

export type DataTableProps<TData> = {
  data: TData[]
  columns: ColumnDef<TData>[]
  searchKey?: string
  filterableColumns?: {
    id: string
    title: string
    options: {
      label: string
      value: string
    }[]
  }[]
}



export interface DataTableToolbarProps<TData> {
  table: Table<TData>
  filterableColumns?: {
    id: string
    title: string
    options: {
      label: string
      value: string
    }[]
  }[]
  searchKey?: string
}
